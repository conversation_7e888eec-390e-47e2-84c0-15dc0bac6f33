// d8b48670129f6fc7f85977e021ec56da
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class ChatRequest : TSNSerializable {
    /**
     * 填当前 Conversation 唯一的 UUID，如果是新的 Conversation 则为空
     */
    var conversationId: String? = null

    /**
     * 填上一个消息的 UUID
     */
    var parentMessageId: String? = null

    /**
     * 消息列表
     */
    var messages: MutableList<ChatMessage>? = null

    /**
     * system_prompt,eg:{\"mode\":\"潮玩二创\"}
     */
    var systemPrompt: String? = null

    /**
     * 版本
     */
    var version: String? = NuroSetting.version
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.conversationId = TSNJSONObject.getString(map, "conversation_id") ?: null
        this.parentMessageId = TSNJSONObject.getString(map, "parent_message_id") ?: null
        this.messages = (this.optMapList(1, map.opt("messages"), { return@optMapList (it as? TSNJSONMap)?.let { ChatMessage(JSONObject = TSNJSONObject(it)) } }) as? MutableList<ChatMessage>?)
        this.systemPrompt = TSNJSONObject.getString(map, "system_prompt") ?: null
        this.version = TSNJSONObject.getString(map, "version") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["conversation_id"] = this.conversationId
        this.encodeData["parent_message_id"] = this.parentMessageId
        this.encodeData["messages"] = this.messages
        this.encodeData["system_prompt"] = this.systemPrompt
        this.encodeData["version"] = this.version
    }
}