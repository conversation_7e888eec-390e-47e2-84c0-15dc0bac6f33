// 0844da4c90b32a1becb84826db91f9f1
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class SSEDeltaMessage : TSNSerializable {
    var op: String? = null

    var path: String? = null

    var value: String? = null

    var original_value: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.op = TSNJSONObject.getString(map, "op") ?: null
        this.path = TSNJSONObject.getString(map, "path") ?: null
        this.value = TSNJSONObject.getString(map, "value") ?: null
        this.original_value = TSNJSONObject.getString(map, "original_value") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["op"] = this.op
        this.encodeData["path"] = this.path
        this.encodeData["value"] = this.value
        this.encodeData["original_value"] = this.original_value
    }
}

class SSEFinalMessage : TSNSerializable {
    var ret: String? = null

    var errmsg: String? = null

    var systime: String? = null

    var logid: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.ret = TSNJSONObject.getString(map, "ret") ?: null
        this.errmsg = TSNJSONObject.getString(map, "errmsg") ?: null
        this.systime = TSNJSONObject.getString(map, "systime") ?: null
        this.logid = TSNJSONObject.getString(map, "logid") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["ret"] = this.ret
        this.encodeData["errmsg"] = this.errmsg
        this.encodeData["systime"] = this.systime
        this.encodeData["logid"] = this.logid
    }
}