// 27b7dece0686331dcabf60f97a5dd099
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class HistoryConversation : TSNSerializable {
    var title: String? = null

    var create_time: Int = 0

    var update_time: Int = 0

    var conversation_id: String = ""

    var conversationMapping: MutableMap<String, ChatMessage>? = null

    var summary: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.title = TSNJSONObject.getString(map, "title") ?: null
        this.create_time = TSNJSONObject.getInt(map, "create_time") ?: 0
        this.update_time = TSNJSONObject.getInt(map, "update_time") ?: 0
        this.conversation_id = TSNJSONObject.getString(map, "conversation_id") ?: ""
        this.conversationMapping = (this.optMapList(1, map.opt("mapping"), { return@optMapList (it as? TSNJSONMap)?.let { ChatMessage(JSONObject = TSNJSONObject(it)) } }) as? MutableMap<String, ChatMessage>?)
        this.summary = TSNJSONObject.getString(map, "summary") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["title"] = this.title
        this.encodeData["create_time"] = this.create_time
        this.encodeData["update_time"] = this.update_time
        this.encodeData["conversation_id"] = this.conversation_id
        this.encodeData["mapping"] = this.conversationMapping
        this.encodeData["summary"] = this.summary
    }
}

class HistoryMessages : TSNSerializable {
    var conversation: HistoryConversation? = null

    var total: Int? = null

    var hasMore: Boolean? = null

    fun getChatMessages(): MutableList<ChatMessage> {
        var result: MutableList<ChatMessage> = mutableListOf()
        var _mapping = this.conversation?.conversationMapping
        val __mapping = _mapping
        if (__mapping != null) {
            var toolCallRoleMessages: MutableList<ChatMessage> = mutableListOf()
            TSNMapUtils.forEach(
                __mapping,
                f0@{ id, message ->
                    var role = message.author?.role
                    val _role = role
                    if (_role != null && _role == "tool") {
                        toolCallRoleMessages.push(message)
                        return@f0
                    }
                    result.push(message)
                },
            )
            toolCallRoleMessages.forEach(
                f0@{ message ->
                    result.push(message)
                },
            )
        } else {
            return mutableListOf()
        }
        return result
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.conversation = if (map.has("conversation")) HistoryConversation(JSONObject = TSNJSONObject(map.optJSONObject("conversation"))) else null
        this.total = TSNJSONObject.getInt(map, "total") ?: null
        this.hasMore = TSNJSONObject.getBool(map, "has_more") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["conversation"] = this.conversation
        this.encodeData["total"] = this.total
        this.encodeData["has_more"] = this.hasMore
    }
}