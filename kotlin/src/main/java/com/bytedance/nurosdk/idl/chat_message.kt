// ab21eb3f2258539eb69dd597f0bfe67f
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

open class ChatPatchable : TSNSerializable {
    open fun applyPatch(
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>?,
    ): Unit {
        var _pathComponents =
            pathComponents ?: (
                f0@{ val deltaPath = delta.path
                    if (deltaPath == null) {
                        var v: MutableList<String> = mutableListOf()
                        return@f0 v
                    }
                    var p = deltaPath.split("/").toMutableList()
                    if (p.tsn_safeGet(0) == "") {
                        p.shift()
                    }
                    if (p.tsn_safeGet(0) == "message") {
                        p.shift()
                    }
                    return@f0 p
                }
            )()
        val currentPath = _pathComponents.shift()
        if (currentPath != null) {
            this.applyPatchPath(currentPath, delta, _pathComponents)
        }
    }

    open fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
    }

    open fun <T> applyToArrayAny(
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
        newValueBuilder: (String?) -> T,
        originValue: MutableList<T>?,
    ): MutableList<T> {
        var _pathComponents = pathComponents
        val indexPath = _pathComponents.shift()
        if (indexPath == null) {
            return mutableListOf()
        }
        val index = parseInt(indexPath)
        var parts = originValue ?: mutableListOf()
        if (parts.tsn_safeGet(index) == null) {
            for (j in parts.length until index + 1) {
                if (parts.tsn_safeGet(j) == null) {
                    parts.push(newValueBuilder(null))
                }
            }
        }
        if ((delta.op == "replace" || delta.op == "add") && _pathComponents.length == 0) {
            parts[index] = newValueBuilder(delta.value)
        }
        val newValue = parts.tsn_safeGet(index)
        if (newValue is ChatPatchable) {
            newValue.applyPatch(delta, _pathComponents)
        }
        return parts
    }

    open fun applyToArrayString(
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
        originValue: MutableList<String>?,
    ): MutableList<String> {
        var _pathComponents = pathComponents
        val indexPath = _pathComponents.shift()
        if (indexPath == null) {
            return mutableListOf()
        }
        val index = parseInt(indexPath)
        var parts = originValue ?: mutableListOf()
        if (parts.tsn_safeGet(index) == null) {
            for (j in parts.length until index + 1) {
                if (parts.tsn_safeGet(j) == null) {
                    parts.push("")
                }
            }
        }
        if ((delta.op == "replace" || delta.op == "add") && _pathComponents.length == 0) {
            val _delta_value = delta?.value
            if (_delta_value != null) {
                parts[index] = _delta_value
            }
        } else if (delta.op == "append") {
            parts[index] = (parts.tsn_safeGet(index) ?: "") + (delta.value ?: "")
        }
        return parts
    }

    open fun applyToString(
        delta: SSEDeltaMessage,
        originValue: String?,
    ): String {
        if (delta.op == "replace") {
            val _delta_value = delta?.value
            if (_delta_value != null) {
                return _delta_value
            }
        } else if (delta.op == "append") {
            return (originValue ?: "") + (delta.value ?: "")
        }
        return originValue ?: ""
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
    }

    override fun unmapping() {
        super.unmapping()
    }
}

enum class ChatMessageStatus(
    val value: String,
) {
    /**
     * 消息状态, finished_successfully
     */
    finished_successfully("finished_successfully"),
    /**
     * 消息状态,  in_progress
     */
    in_progress("in_progress"),
    /**
     * 推流前异常错误
     * 推流失败，比如第一条事件就推送失败了
     */
    interrupt_status("interrupt_status"),
    /**
     * 推流前异常错误
     * pe策略失败
     */
    pe_policy_failed_status("pe_policy_failed_status"),
    /**
     * 推流前异常错误
     * 获取流失败
     */
    chat_stream_failed_status("chat_stream_failed_status"),
    /**
     * 安全审核拦截
     * 输入文本审核拦截
     */
    input_text_block_status("input_text_block_status"),
    /**
     * 安全审核拦截
     * 输出文本审核拦截
     */
    output_text_block_status("output_text_block_status"),
    /**
     * 推流异常状态
     * 推送思考内容截止
     */
    send_reasoning_content_stop_status("send_reasoning_content_stop_status"),
    /**
     * 推流异常状态
     * 推送内容截止
     */
    send_content_stop_status("send_content_stop_status"),
}

fun ChatMessageStatus.valueOf(): String = this.value

enum class ChatContentType(
    val value: String,
) {
    text("text"),
    image_url("image_url"),
}

fun ChatContentType.valueOf(): String = this.value

enum class ChatMessageFileType(
    val value: Int,
) {
    /**
     * txt
     */
    TXT(1),
    /**
     * pdf
     */
    PDF(2),
    /**
     * doc
     */
    DOC(3),
    /**
     * docx
     */
    DOCX(4),
    /**
     * image
     */
    IMAGE(5),
    /**
     * video
     */
    VIDEO(6),
    /**
     * audio
     */
    AUDIO(7),
}

fun ChatMessageFileType.valueOf(): Int = this.value

class ChatMessageImageMetadata : ChatPatchable {
    var image_width: Int? = null

    var image_height: Int? = null

    var image_format: String? = null

    var image_prompt: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.image_width = TSNJSONObject.getInt(map, "image_width") ?: null
        this.image_height = TSNJSONObject.getInt(map, "image_height") ?: null
        this.image_format = TSNJSONObject.getString(map, "image_format") ?: null
        this.image_prompt = TSNJSONObject.getString(map, "image_prompt") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["image_width"] = this.image_width
        this.encodeData["image_height"] = this.image_height
        this.encodeData["image_format"] = this.image_format
        this.encodeData["image_prompt"] = this.image_prompt
    }
}

class ChatMessageFileURI : ChatPatchable {
    var uri: String? = null

    var file_type: ChatMessageFileType? = null

    var file_name: String? = null

    var url: String? = null

    var image_metadata: ChatMessageImageMetadata? = null

    var extra: MutableMap<String, Any>? = null

    var file_description: String? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "uri") {
            this.uri = this.applyToString(delta, this.uri)
        } else if (name == "file_type") {
            if (delta.op == "replace") {
                val _delta_value = delta?.value
                if (_delta_value != null) {
                    this.file_type = ChatMessage.convertValueToChatMessageFileType(parseInt(_delta_value))
                }
            }
        } else if (name == "file_name") {
            this.file_name = this.applyToString(delta, this.file_name)
        } else if (name == "url") {
            this.url = this.applyToString(delta, this.url)
        } else if (name == "image_metadata") {
            if (this.image_metadata == null) {
                this.image_metadata = ChatMessageImageMetadata()
            }
            this.image_metadata?.applyPatch(delta, pathComponents)
        } else if (name == "file_description") {
            this.file_description = this.applyToString(delta, this.file_description)
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.uri = TSNJSONObject.getString(map, "uri") ?: null
        ChatMessageFileType.values().firstOrNull { it.value == map.opt("file_type") }?.let { this.file_type = it }
        this.file_name = TSNJSONObject.getString(map, "file_name") ?: null
        this.url = TSNJSONObject.getString(map, "url") ?: null
        this.image_metadata = if (map.has("image_metadata")) ChatMessageImageMetadata(JSONObject = TSNJSONObject(map.optJSONObject("image_metadata"))) else null
        this.extra = (this.optMapList(1, map.opt("extra"), { TSNJSONObject.getPlainValue(it) }) as? MutableMap<String, Any>?)
        this.file_description = TSNJSONObject.getString(map, "file_description") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["uri"] = this.uri
        this.encodeData["file_type"] = this.file_type?.value
        this.encodeData["file_name"] = this.file_name
        this.encodeData["url"] = this.url
        this.encodeData["image_metadata"] = this.image_metadata
        this.encodeData["extra"] = this.extra
        this.encodeData["file_description"] = this.file_description
    }
}

class ChatContentPart : ChatPatchable {
    var text: String? = null

    var file: ChatMessageFileURI? = null

    var is_referenced: Boolean? = null

    var reasoning_content: String? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "text") {
            this.text = this.applyToString(delta, this.text)
        } else if (name == "reasoning_content") {
            this.reasoning_content = this.applyToString(delta, this.reasoning_content)
        } else if (name == "file") {
            if (this.file == null) {
                this.file = ChatMessageFileURI(JSONString = delta.value ?: "{}")
            }
            this.file?.applyPatch(delta, pathComponents)
        } else if (name == "is_referenced") {
            if (delta.value == "true") {
                this.is_referenced = true
            }
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.text = TSNJSONObject.getString(map, "text") ?: null
        this.file = if (map.has("file")) ChatMessageFileURI(JSONObject = TSNJSONObject(map.optJSONObject("file"))) else null
        this.is_referenced = TSNJSONObject.getBool(map, "is_referenced") ?: null
        this.reasoning_content = TSNJSONObject.getString(map, "reasoning_content") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["text"] = this.text
        this.encodeData["file"] = this.file
        this.encodeData["is_referenced"] = this.is_referenced
        this.encodeData["reasoning_content"] = this.reasoning_content
    }
}

class ChatContent : ChatPatchable {
    /**
     * 内容类型：text、image_url
     */
    var content_type: ChatContentType? = null

    /**
     * 消息内容
     */
    var content_parts: MutableList<ChatContentPart>? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "content_parts") {
            this.content_parts =
                this.applyToArrayAny(
                    delta,
                    pathComponents,
                    f0@{ value ->
                        val _value = value
                        if (_value != null && _value.indexOf("{") == 0) {
                            return@f0 ChatContentPart(JSONString = _value ?: "{}")
                        } else {
                            return@f0 ChatContentPart()
                        }
                    },
                    this.content_parts,
                )
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        ChatContentType.values().firstOrNull { it.value == map.opt("content_type") }?.let { this.content_type = it }
        this.content_parts = (this.optMapList(1, map.opt("content_parts"), { return@optMapList (it as? TSNJSONMap)?.let { ChatContentPart(JSONObject = TSNJSONObject(it)) } }) as? MutableList<ChatContentPart>?)
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["content_type"] = this.content_type?.value
        this.encodeData["content_parts"] = this.content_parts
    }
}

enum class ChatToolCallType(
    val value: String,
) {
    client_function("client_function"),
    server_function("server_function"),
}

fun ChatToolCallType.valueOf(): String = this.value

class ChatToolCallFunc : ChatPatchable {
    /**
     * 函数名称
     */
    var name: String? = null

    /**
     * 函数调用参数
     */
    var arguments: String? = null

    /**
     * 内部使用，上一次成功修复的 JSON 参数，流式传输使用。
     */
    var _lastRepairedArguments: String? = "{}"

    /**
     * 其他附带的内容
     */
    var extra: MutableMap<String, String>? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "arguments") {
            this.arguments = this.applyToString(delta, this.arguments)
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.name = TSNJSONObject.getString(map, "name") ?: null
        this.arguments = TSNJSONObject.getString(map, "arguments") ?: null
        this.extra = (this.optMapList(1, map.opt("extra"), { return@optMapList it as? String }) as? MutableMap<String, String>?)
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["name"] = this.name
        this.encodeData["arguments"] = this.arguments
        this.encodeData["extra"] = this.extra
    }
}

/**
 * 工具调用定义
 */
class ChatToolCall : ChatPatchable {
    /**
     * 工具调用ID
     */
    var id: String? = null

    /**
     * 类型：client_function、server_function
     */
    var type: ChatToolCallType? = null

    /**
     * 是否正在流式返回工具参数
     */
    var streaming: Boolean? = null

    /**
     * func
     */
    var _func: ChatToolCallFunc? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "func") {
            if (this._func == null) {
                this._func = ChatToolCallFunc()
            }
            this._func?.applyPatch(delta, pathComponents)
        } else if (name == "streaming") {
            if (delta.op == "replace") {
                val _delta_value = delta?.value
                if (_delta_value != null) {
                    this.streaming = _delta_value == "true"
                }
            }
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.id = TSNJSONObject.getString(map, "id") ?: null
        ChatToolCallType.values().firstOrNull { it.value == map.opt("type") }?.let { this.type = it }
        this.streaming = TSNJSONObject.getBool(map, "streaming") ?: null
        this._func = if (map.has("func")) ChatToolCallFunc(JSONObject = TSNJSONObject(map.optJSONObject("func"))) else null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["id"] = this.id
        this.encodeData["type"] = this.type?.value
        this.encodeData["streaming"] = this.streaming
        this.encodeData["func"] = this._func
    }
}

enum class ChatToolType(
    val value: String,
) {
    client_function("client_function"),
    server_function("server_function"),
}

fun ChatToolType.valueOf(): String = this.value

class ChatTool : ChatPatchable {
    /**
     * 工具ID
     */
    var id: String? = null

    /**
     * 工具类型
     */
    var type: ChatToolType? = null

    /**
     * 工具名称
     */
    var name: String? = null

    /**
     * 工具描述
     */
    var description: String? = null

    /**
     * 工具参数（JSONSchema）
     */
    var parameters: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.id = TSNJSONObject.getString(map, "id") ?: null
        ChatToolType.values().firstOrNull { it.value == map.opt("type") }?.let { this.type = it }
        this.name = TSNJSONObject.getString(map, "name") ?: null
        this.description = TSNJSONObject.getString(map, "description") ?: null
        this.parameters = TSNJSONObject.getString(map, "parameters") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["id"] = this.id
        this.encodeData["type"] = this.type?.value
        this.encodeData["name"] = this.name
        this.encodeData["description"] = this.description
        this.encodeData["parameters"] = this.parameters
    }
}

class ChatAuthor : ChatPatchable {
    /**
     * 角色
     */
    var role: String? = null

    /**
     * 名称
     */
    var name: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.role = TSNJSONObject.getString(map, "role") ?: null
        this.name = TSNJSONObject.getString(map, "name") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["role"] = this.role
        this.encodeData["name"] = this.name
    }
}

class FinishDetails : ChatPatchable {
    /**
     * 类型,eg: stop, interrupted
     */
    var type: String = ""

    fun isEqualToObject(obj: Any): Boolean {
        if (obj is FinishDetails) {
            return obj.type == this.type
        }
        return false
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.type = TSNJSONObject.getString(map, "type") ?: ""
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["type"] = this.type
    }
}

class ChatMessageMetadata : ChatPatchable {
    /**
     * 是否在会话中隐藏，有些消息不需要展示
     */
    var is_visually_hidden_from_conversation: Boolean = false

    /**
     * 对话ID
     */
    var conversation_id: String = ""

    /**
     * 父消息ID
     */
    var parent_message_id: String = ""

    /**
     * 工具调用ID
     */
    var tool_call_id: String? = null

    /**
     * 完成详情(FinishDetails结构见公共结构)
     */
    var finish_details: FinishDetails? = null

    /**
     * 用户GUI操作
     */
    var action: String? = null

    /**
     * 回复类型：follow_up、answer
     * 1. follow_up: 表示推荐问
     * 2. answer表示：模型正常回答
     */
    var message_type: String? = null

    /**
     * 模型想起
     */
    var model_detail: String? = null

    /**
     * payload
     */
    var payload: String? = null

    /**
     * 是否对话已经完结，不是的话需要 续连
     */
    var end_turn: Boolean? = null

    /**
     * 埋点透传字段
     */
    var metricsExtra: String? = null

    fun isEqualToObject(obj: Any): Boolean {
        if (obj is ChatMessageMetadata) {
            var finish_details_is_equal = true
            val _obj_finish_details = obj?.finish_details
            val _this_finish_details = this?.finish_details
            if (_obj_finish_details != null && _this_finish_details != null) {
                finish_details_is_equal = _obj_finish_details.isEqualToObject(_this_finish_details)
            } else if (_obj_finish_details != null) {
                finish_details_is_equal = false
            } else if (_this_finish_details != null) {
                finish_details_is_equal = false
            }
            return (obj.conversation_id == this.conversation_id && obj.parent_message_id == this.parent_message_id && obj.tool_call_id == this.tool_call_id && obj.is_visually_hidden_from_conversation == this.is_visually_hidden_from_conversation && obj.action == this.action && obj.message_type == this.message_type && obj.model_detail == this.model_detail && obj.payload == this.payload && obj.end_turn == this.end_turn && finish_details_is_equal == true)
        }
        return false
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.is_visually_hidden_from_conversation = TSNJSONObject.getBool(map, "is_visually_hidden_from_conversation") ?: false
        this.conversation_id = TSNJSONObject.getString(map, "conversation_id") ?: ""
        this.parent_message_id = TSNJSONObject.getString(map, "parent_message_id") ?: ""
        this.tool_call_id = TSNJSONObject.getString(map, "tool_call_id") ?: null
        this.finish_details = if (map.has("finish_details")) FinishDetails(JSONObject = TSNJSONObject(map.optJSONObject("finish_details"))) else null
        this.action = TSNJSONObject.getString(map, "action") ?: null
        this.message_type = TSNJSONObject.getString(map, "message_type") ?: null
        this.model_detail = TSNJSONObject.getString(map, "model_detail") ?: null
        this.payload = TSNJSONObject.getString(map, "payload") ?: null
        this.end_turn = TSNJSONObject.getBool(map, "end_turn") ?: null
        this.metricsExtra = TSNJSONObject.getString(map, "metrics_extra") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["is_visually_hidden_from_conversation"] = this.is_visually_hidden_from_conversation
        this.encodeData["conversation_id"] = this.conversation_id
        this.encodeData["parent_message_id"] = this.parent_message_id
        this.encodeData["tool_call_id"] = this.tool_call_id
        this.encodeData["finish_details"] = this.finish_details
        this.encodeData["action"] = this.action
        this.encodeData["message_type"] = this.message_type
        this.encodeData["model_detail"] = this.model_detail
        this.encodeData["payload"] = this.payload
        this.encodeData["end_turn"] = this.end_turn
        this.encodeData["metrics_extra"] = this.metricsExtra
    }
}

class ChatMessage : ChatPatchable {
    companion object {
        fun convertValueToChatMessageStatus(value: String): ChatMessageStatus {
            when (value) {
                "finished_successfully" -> {
                    return ChatMessageStatus.finished_successfully
                }
                "in_progress" -> {
                    return ChatMessageStatus.in_progress
                }
                "interrupt_status" -> {
                    return ChatMessageStatus.interrupt_status
                }
                "pe_policy_failed_status" -> {
                    return ChatMessageStatus.pe_policy_failed_status
                }
                "chat_stream_failed_status" -> {
                    return ChatMessageStatus.chat_stream_failed_status
                }
                "input_text_block_status" -> {
                    return ChatMessageStatus.input_text_block_status
                }
                "output_text_block_status" -> {
                    return ChatMessageStatus.output_text_block_status
                }
                "send_reasoning_content_stop_status" -> {
                    return ChatMessageStatus.send_reasoning_content_stop_status
                }
                "send_content_stop_status" -> {
                    return ChatMessageStatus.send_content_stop_status
                }
                else -> {
                    return ChatMessageStatus.finished_successfully
                }
            }
        }

        fun convertValueToChatMessageFileType(value: Int): ChatMessageFileType {
            when (value) {
                1 -> {
                    return ChatMessageFileType.TXT
                }
                2 -> {
                    return ChatMessageFileType.PDF
                }
                3 -> {
                    return ChatMessageFileType.DOC
                }
                4 -> {
                    return ChatMessageFileType.DOCX
                }
                5 -> {
                    return ChatMessageFileType.IMAGE
                }
                6 -> {
                    return ChatMessageFileType.VIDEO
                }
                7 -> {
                    return ChatMessageFileType.AUDIO
                }
                else -> {
                    return ChatMessageFileType.IMAGE
                }
            }
        }
    }

    /**
     * 消息作者
     */
    var author: ChatAuthor? = null

    /**
     * 消息元数据
     */
    var metadata: ChatMessageMetadata? = null

    /**
     * 消息状态, finished_successfully, in_progress
     */
    var status: ChatMessageStatus? = null

    /**
     * 消息ID, UUID
     */
    var id: String? = null

    /**
     * 消息内容, 展示给用户查看
     */
    var content: ChatContent? = null

    /**
     * 更新时间，unix毫秒时间戳
     */
    var update_time: Int? = null

    /**
     * 创建时间, unix毫秒时间戳
     */
    var create_time: Long? = 0

    /**
     * 是否结束本轮对话，一轮对话可能由多个message构成
     */
    var end_turn: Boolean? = null

    /**
     * 下发到端上需要执行的操作，端上被视为Agent的工具，可以被进行工具调用
     */
    var tool_calls: MutableList<ChatToolCall>? = null

    var tools: MutableList<ChatTool>? = null

    override fun applyPatchPath(
        name: String,
        delta: SSEDeltaMessage,
        pathComponents: MutableList<String>,
    ): Unit {
        if (name == "content") {
            if (this.content == null) {
                this.content = ChatContent()
            }
            this.content?.applyPatch(delta, pathComponents)
        } else if (name == "status") {
            if (delta.op == "replace") {
                val _delta_value = delta?.value
                if (_delta_value != null) {
                    this.status = ChatMessage.convertValueToChatMessageStatus(_delta_value)
                }
            }
        } else if (name == "tool_calls") {
            this.tool_calls =
                this.applyToArrayAny(
                    delta,
                    pathComponents,
                    f0@{ value ->
                        val _value = value
                        if (_value != null && _value.indexOf("{") == 0) {
                            return@f0 ChatToolCall(JSONString = _value)
                        }
                        return@f0 ChatToolCall()
                    },
                    this.tool_calls,
                )
        } else if (name == "end_turn") {
            if (delta.op == "replace") {
                if (delta.value == "true") {
                    this.end_turn = true
                } else {
                    this.end_turn = false
                }
            }
        }
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.author = if (map.has("author")) ChatAuthor(JSONObject = TSNJSONObject(map.optJSONObject("author"))) else null
        this.metadata = if (map.has("metadata")) ChatMessageMetadata(JSONObject = TSNJSONObject(map.optJSONObject("metadata"))) else null
        ChatMessageStatus.values().firstOrNull { it.value == map.opt("status") }?.let { this.status = it }
        this.id = TSNJSONObject.getString(map, "id") ?: null
        this.content = if (map.has("content")) ChatContent(JSONObject = TSNJSONObject(map.optJSONObject("content"))) else null
        this.update_time = TSNJSONObject.getInt(map, "update_time") ?: null
        this.create_time = TSNJSONObject.getInt64(map, "create_time") ?: null
        this.end_turn = TSNJSONObject.getBool(map, "end_turn") ?: null
        this.tool_calls = (this.optMapList(1, map.opt("tool_calls"), { return@optMapList (it as? TSNJSONMap)?.let { ChatToolCall(JSONObject = TSNJSONObject(it)) } }) as? MutableList<ChatToolCall>?)
        this.tools = (this.optMapList(1, map.opt("tools"), { return@optMapList (it as? TSNJSONMap)?.let { ChatTool(JSONObject = TSNJSONObject(it)) } }) as? MutableList<ChatTool>?)
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["author"] = this.author
        this.encodeData["metadata"] = this.metadata
        this.encodeData["status"] = this.status?.value
        this.encodeData["id"] = this.id
        this.encodeData["content"] = this.content
        this.encodeData["update_time"] = this.update_time
        this.encodeData["create_time"] = this.create_time
        this.encodeData["end_turn"] = this.end_turn
        this.encodeData["tool_calls"] = this.tool_calls
        this.encodeData["tools"] = this.tools
    }
}