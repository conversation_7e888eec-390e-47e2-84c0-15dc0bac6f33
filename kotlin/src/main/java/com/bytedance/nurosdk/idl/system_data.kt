// e81cceb8c502bdd83da6d0f4fde1f647
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

enum class SystemDataType(
    val value: String,
) {
    title_generation("title_generation"),
    stream_complete("stream_complete"),
    heartbeat("heartbeat"),
    summary("summary"),
}

fun SystemDataType.valueOf(): String = this.value

class SystemData : TSNSerializable {
    var type: SystemDataType? = null

    var conversation_id: String? = null

    var title: String? = null

    var content: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        SystemDataType.values().firstOrNull { it.value == map.opt("type") }?.let { this.type = it }
        this.conversation_id = TSNJSONObject.getString(map, "conversation_id") ?: null
        this.title = TSNJSONObject.getString(map, "title") ?: null
        this.content = TSNJSONObject.getString(map, "content") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["type"] = this.type?.value
        this.encodeData["conversation_id"] = this.conversation_id
        this.encodeData["title"] = this.title
        this.encodeData["content"] = this.content
    }
}