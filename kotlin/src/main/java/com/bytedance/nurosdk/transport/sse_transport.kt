// b7fa2ea0d6cff66833e4010e747ec5cd
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

open class SSETransport(
    endpoint: String,
    headers: MutableMap<String, String>?,
) : CommonTransport() {
    private var _conversationManager: NuroConversationManager? = null

    private lateinit var endpoint: String

    private var headers: MutableMap<String, String> = mutableMapOf()

    private var currentChunked: String = ""

    private var currentChatMessage: ChatMessage? = null

    private var messageDataType: String = "" // reasoning, text, tool.

    init {
        this.endpoint = endpoint
        val _headers = headers
        if (_headers != null) {
            this.headers = _headers
        }
    }

    open override fun setConversationManager(conversationManager: NuroConversationManager): Unit {
        this._conversationManager = conversationManager
    }

    open override fun sendMessage(
        message: NuroMessage,
        tools: MutableList<NuroMCPToolItem>,
    ): Unit {
        this.sendMessages(mutableListOf(message), tools)
    }

    open override fun resumeMessage() {
        var headers: MutableMap<String, String> = mutableMapOf("Content-Type" to "application/json")
        for ((key, _) in this.headers) {
            headers[key] = this.headers.tsn_safeGet(key) ?: ""
        }
        var resumeData: ResumeData = ResumeData()
        resumeData.conversationId = this._conversationManager?.conversation?.conversationId ?: ""
        var length =
            this._conversationManager
                ?.conversation
                ?.messages
                ?.length ?: 0
        if (length > 0) {
            var message =
                this._conversationManager
                    ?.conversation
                    ?.messages
                    ?.tsn_safeGet(length - 1)
            var rawId = ""
            val _message = message
            if (_message != null) {
                rawId = _message.getResumeMsgId()
            }
            resumeData.messageId = rawId
        }
        this.currentChunked = ""
        this.sendSSERequest(
            EventStreamAdapter.reconnectEndpoint,
            resumeData.toJSONString(),
            headers,
            f0@{ -> },
            f0@{ MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager)
                this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
            },
            f0@{ MessageProcessor.markInProgressMessagesAsCancel(this._conversationManager)
                this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
            },
        )
    }

    open override fun sendMessages(
        messages: MutableList<NuroMessage>,
        tools: MutableList<NuroMCPToolItem>,
    ): Unit {
        if (messages.length == 0) {
            this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
            return
        }
        this._conversationManager?.conversation?.messages?.forEach(
            f0@{ message ->
                if (message.isFinalStatus() == false) {
                    if (message is NuroToolCallMessage) {
                        message.setMsgStatus(NuroToolCallMessageStatus.skipped)
                    }
                }
            },
        )
        for (index in 0 until messages.length) {
            val msg = messages[index]
            if (msg is NuroUserMessage) {
                msg.setMsgStatus(NuroUserMessageStatus.sending)
            }
        }
        val chatRequest = ChatRequest()
        chatRequest.conversationId = this._conversationManager?.conversation?.conversationId
        chatRequest.parentMessageId =
            (
                f0@{ val messages = this._conversationManager?.conversation?.messages
                    if (messages != null && messages.length > 0) {
                        run {
                            var index = messages.length - 1
                            var __first__ = true
                            while (index >= 0) {
                                if (!__first__) {
                                    index = index - 1
                                }
                                __first__ = false
                                if (index >= 0) {
                                    val msg = messages.tsn_safeGet(index)
                                    if (msg != null) {
                                        if (msg is NuroAssistantMessage || msg is NuroToolCallMessage) {
                                            return@f0 msg._rawId
                                        }
                                    }
                                } else {
                                    break
                                }
                            }
                        }
                    }
                    return@f0 null
                }
            )()
        if (chatRequest.parentMessageId == null) {
            chatRequest.systemPrompt = this._conversationManager?.conversation?.systemPrompt
        }
        chatRequest.messages = mutableListOf()
        var previousMessageId: String? = null
        for (index in 0 until messages.length) {
            val message = messages[index]
            val chatMessage = ChatMessage()
            chatMessage.id = message.id
            val chatAuthor = ChatAuthor()
            chatAuthor.role = if (message is NuroToolCallMessage) "tool" else "user"
            chatMessage.author = chatAuthor
            val content = ChatContent()
            val metadata = ChatMessageMetadata()
            if (message is NuroUserMessage) {
                var contentParts: MutableList<ChatContentPart> = mutableListOf()
                run {

                    val _message_text = message?.text
                    if (_message_text != null && _message_text.length > 0) {
                        var contentPart: ChatContentPart = ChatContentPart()
                        contentPart.text = _message_text
                        contentParts.push(contentPart)
                    }
                }
                run {

                    val _message_files = message?.files
                    if (_message_files != null && _message_files.length > 0) {
                        _message_files.forEach(
                            f0@{ it ->
                                var contentPart: ChatContentPart = ChatContentPart()
                                var file = ChatMessageFileURI()
                                file.file_type = if (it.type == NuroFileType.image) ChatMessageFileType.IMAGE else ChatMessageFileType.VIDEO
                                file.url = it.url
                                file.uri = it.uri
                                file.extra = it.extra
                                file.file_description = it.file_description
                                var nuroImageMetadata = it.metadata?.image_metadata ?: null
                                val _nuroImageMetadata = nuroImageMetadata
                                if (_nuroImageMetadata != null && ChatMessageFileType.IMAGE) {
                                    var imageMetadata = ChatMessageImageMetadata()
                                    imageMetadata.image_width = _nuroImageMetadata.width
                                    imageMetadata.image_height = _nuroImageMetadata.height
                                    imageMetadata.image_prompt = _nuroImageMetadata.prompt
                                    imageMetadata.image_format = _nuroImageMetadata.format
                                    file.image_metadata = imageMetadata
                                }
                                contentPart.file = file
                                contentParts.push(contentPart)
                            },
                        )
                    }
                }
                metadata.metricsExtra = message.metadata.metricsExtra
                run {

                    val _message_referenceInfo = message?.referenceInfo
                    if (_message_referenceInfo != null && _message_referenceInfo.length > 0) {
                        _message_referenceInfo.forEach(
                            f0@{ it ->
                                var contentPart: ChatContentPart = ChatContentPart()
                                run {

                                    val _it_text = it?.text
                                    if (_it_text != null && _it_text.length > 0) {
                                        contentPart.text = _it_text
                                    }
                                }
                                run {

                                    val _it_file = it?.file
                                    if (_it_file != null) {
                                        var file = ChatMessageFileURI()
                                        file.file_type = if (_it_file.type == NuroFileType.image) ChatMessageFileType.IMAGE else ChatMessageFileType.VIDEO
                                        file.url = _it_file.url
                                        file.uri = _it_file.uri
                                        file.extra = _it_file.extra
                                        file.file_description = _it_file.file_description
                                        var nuroImageMetadata = _it_file.metadata?.image_metadata ?: null
                                        val _nuroImageMetadata = nuroImageMetadata
                                        if (_nuroImageMetadata != null && ChatMessageFileType.IMAGE) {
                                            var imageMetadata = ChatMessageImageMetadata()
                                            imageMetadata.image_width = _nuroImageMetadata.width
                                            imageMetadata.image_height = _nuroImageMetadata.height
                                            imageMetadata.image_prompt = _nuroImageMetadata.prompt
                                            imageMetadata.image_format = _nuroImageMetadata.format
                                            file.image_metadata = imageMetadata
                                        }
                                        contentPart.file = file
                                    }
                                }
                                contentPart.is_referenced = true
                                contentParts.push(contentPart)
                            },
                        )
                    }
                }
                content.content_parts = contentParts
            } else if (message is NuroToolCallMessage) {
                var tollmsg: NuroToolCallMessage = message as NuroToolCallMessage
                var contentPart: ChatContentPart = ChatContentPart()
                contentPart.text = tollmsg.toolResult ?: ""
                content.content_parts = mutableListOf(contentPart)
                metadata.tool_call_id = tollmsg.toolCallId
            }
            chatMessage.content = content
            chatMessage.tools =
                tools
                    .map(
                        f0@{ it ->
                            val toolItem = ChatTool()
                            toolItem.id = it.name
                            toolItem.type = ChatToolType.client_function
                            toolItem.name = it.serverName + "_" + it.name
                            toolItem.description = it.description
                            toolItem.parameters = it.inputSchema
                            return@f0 toolItem
                        },
                    ).toMutableList()
            metadata.conversation_id = chatRequest.conversationId ?: ""
            metadata.parent_message_id = previousMessageId ?: chatRequest.parentMessageId ?: ""
            chatMessage.metadata = metadata
            chatMessage.create_time = message.createTime
            chatRequest.messages?.push(chatMessage)
            previousMessageId = message.id
        }
        var headers: MutableMap<String, String> = mutableMapOf("Content-Type" to "application/json")
        for ((key, _) in this.headers) {
            headers[key] = this.headers.tsn_safeGet(key) ?: ""
        }
        this.currentChunked = ""
        this.sendSSERequest(
            this.endpoint,
            chatRequest.toJSONString(),
            headers,
            f0@{ -> },
            f0@{ MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager)
                this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
            },
            f0@{ MessageProcessor.markInProgressMessagesAsCancel(this._conversationManager)
                this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
            },
        )
    }

    open fun sendSSERequest(
        endpoint: String?,
        chatRequest: String?,
        headers: MutableMap<String, String>,
        onChunkStart: () -> Unit,
        onMessageFailed: () -> Unit,
        onConversationCancel: () -> Unit,
    ): Unit {
        if (NuroMockManager.isMocking()) {
            return
        }
        if (endpoint == null) {
            return
        }
        val esConfig = EventStreamConfig()
        esConfig.endpoint = endpoint
        esConfig.method = "POST"
        esConfig.headers = headers
        esConfig.data = chatRequest ?: ""
        var chunkedStarted = false
        esConfig.onChunk = f0@{ text: String ->
            NuroLogger.debug(
                "SSETransport",
                f1@{ "received chunk, $text" },
            )
            if (chunkedStarted == false) {
                chunkedStarted = true
                onChunkStart()
            }
            this.currentChunked = this.currentChunked + text
            this.flushChuck(false)
        }
        esConfig.onFinish = f0@{  NuroLogger.debug(
            "SSETransport",
            f1@{ "received finish" },
        )
            this.flushChuck(true)
        }
        esConfig.onError = f0@{ code: Int, message: String? ->
            NuroLogger.debug(
                "SSETransport",
                f1@{ "received error, code = $code, message = ${message ?: ""}" },
            )
            this.flushChuck(true)
            onMessageFailed()
        }
        esConfig.onCancel = f0@{ code: Int, message: String? ->
            NuroLogger.debug(
                "SSETransport",
                f1@{ "received cancel" },
            )
            this.flushChuck(true)
            onConversationCancel()
        }
        NuroLogger.debug(
            "SSETransport",
            f0@{ "send sse request, $chatRequest" },
        )
        this.token = SSEImpl.fetch(esConfig)
    }

    open fun flushChuck(ended: Boolean): Unit {
        var originalParts = this.currentChunked.split("\n\n").toMutableList()
        var partsLength = originalParts.length
        var currentIndex = -1
        originalParts.forEach(
            f0@{ part ->
                currentIndex = currentIndex + 1
                if (currentIndex == partsLength - 1) {
                    if (ended == false) {
                        this.currentChunked = part
                        return@f0
                    }
                }
                if (part.indexOf("logid") > 0 && part.indexOf("ret") > 0 && part.indexOf("{") == 0) {
                    var res: SSEFinalMessage = SSEFinalMessage(JSONString = part)
                    val _res_ret = res?.ret
                    if (_res_ret != null) {
                        if (_res_ret == "0") {
                            MessageProcessor.markLastUserMessageAsFinished(this._conversationManager)
                        } else {
                            MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager)
                        }
                        this._conversationManager?.conversation?.updateState(NuroConversationState.readyToSendMessage)
                        return@f0
                    }
                }
                var id: String = ""
                var event: String = ""
                var data: String = ""
                part.split("\n").toMutableList().forEach(
                    f1@{ line ->
                        if (line.indexOf("id:") == 0) {
                            id = line.substr(3).trim()
                        } else if (line.indexOf("event:") == 0) {
                            event = line.substr(6).trim()
                        } else if (line.indexOf("data:") == 0) {
                            data = line.substr(5).trim()
                        }
                        if (id != "" && event != "" && data != "") {
                            this.onSSEEvent(id, event, data)
                        }
                    },
                )
            },
        )
        if (ended == true) {
            this.currentChunked = ""
        }
    }

    open fun onSSEEvent(
        id: String,
        event: String,
        data: String,
    ): Unit {
        if (event == "message") {
            NuroLogger.debug(
                "SSETransport",
                f0@{ "received message, $data" },
            )
            val _chatMessage = ChatMessage(JSONString = data)
            val coversationId = _chatMessage.metadata?.conversation_id
            this.currentChatMessage = _chatMessage
            run {

                val _this__conversationManager = this?._conversationManager
                if (_this__conversationManager != null && coversationId != null) {
                    _this__conversationManager.conversation.conversationId = coversationId
                }
            }
            val contentParts = _chatMessage.content?.content_parts
            if (contentParts != null && contentParts.length > 0) {
                MessageProcessor.convertChatMessageToNuroMessage(this._conversationManager, _chatMessage, ConvertType.new_message).forEach(
                    f0@{ it ->
                        MessageProcessor.markLastUserMessageAsFinished(this._conversationManager)
                        this._conversationManager?.receivedMessage(it)
                    },
                )
            }
        } else if (event == "delta") {
            NuroLogger.debug(
                "SSETransport",
                f0@{ "received delta, $data" },
            )
            var delta = SSEDeltaMessage(JSONString = data)
            var deltaPath = delta.path
            run {

                val _deltaPath = deltaPath
                if (_deltaPath != null && _deltaPath.indexOf("/message/content/content_parts/") >= 0) {
                    this.messageDataType = "assistant"
                } else if (_deltaPath != null && _deltaPath.indexOf("/message/tool_calls/") >= 0) {
                    MessageProcessor.markMessagesAsFinished(this._conversationManager)
                    this.messageDataType = "tool_call"
                }
            }
            run {

                val _this_currentChatMessage = this?.currentChatMessage
                if (_this_currentChatMessage != null) {
                    _this_currentChatMessage.applyPatch(delta, null)
                    MessageProcessor.convertChatMessageToNuroMessage(this._conversationManager, _this_currentChatMessage, ConvertType.new_message).forEach(
                        f0@{ it ->
                            MessageProcessor.markLastUserMessageAsFinished(this._conversationManager)
                            this._conversationManager?.receivedMessage(it)
                        },
                    )
                }
            }
        } else if (event == "system" && data.indexOf("stream_error") > 0) {
            NuroLogger.error(
                "SSETransport",
                f0@{ "received system stream_error, $data" },
            )
            MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager)
        } else if (event == "system") {
            var sysData = SystemData(JSONString = data)
            NuroLogger.debug(
                "SSETransport",
                f0@{ "received system data, $data" },
            )
            if (sysData.type == SystemDataType.stream_complete) {
                MessageProcessor.markMessagesAsFinished(this._conversationManager)
            }
            run {

                val _this__conversationManager = this?._conversationManager
                val _this__conversationManager_conversation = this?._conversationManager?.conversation
                if (_this__conversationManager != null && _this__conversationManager_conversation != null && SystemDataType.summary) {
                    __this__conversationManager_conversation.summary = sysData.content
                }
            }
            var listeners = this._conversationManager?.conversation?.systemDataListeners
            run {

                val _listeners = listeners
                if (_listeners != null) {
                    TSNMapUtils.forEach(
                        _listeners,
                        f0@{ key, listener ->
                            listener(sysData)
                        },
                    )
                }
            }
        }
    }
}