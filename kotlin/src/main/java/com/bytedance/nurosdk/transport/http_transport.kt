// dce77b036c1459553a6257edb84975b7
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class HttpTransport {
    private var currentChunked: String = ""

    fun sendRequest(
        endpoint: String?,
        data: String? = "",
        headers: MutableMap<String, String> = mutableMapOf(),
        successCallback: (String) -> Unit,
        failCallback: (String, String?) -> Unit,
    ): Unit {
        if (endpoint == null) {
            return
        }
        val esConfig = EventStreamConfig()
        esConfig.endpoint = endpoint
        esConfig.method = "POST"
        esConfig.headers = headers
        esConfig.data = data ?: ""
        esConfig.onChunk = f0@{ text: String ->
            this.currentChunked = this.currentChunked + text
        }
        esConfig.onFinish = f0@{ var result = HttpData(JSONString = this.currentChunked)
            if (result.ret == "0") {
                NuroLogger.debug(
                    "HttpTransport",
                    f1@{ "sendRequest: onFinish: ret = ${result.ret}, endpoint = $endpoint, data = $data" },
                )
                successCallback(this.currentChunked)
            } else {
                NuroLogger.error(
                    "HttpTransport",
                    f1@{ "sendRequest: onFinish: ret = ${result.ret}, errmsg = ${result.errmsg}, endpoint = $endpoint, data = $data" },
                )
                failCallback(result.ret, result.errmsg)
            }
        }
        esConfig.onError = f0@{ code: Int, message: String? ->
            NuroLogger.error(
                "HttpTransport",
                f1@{ "sendRequest: onError: code = $code, message = $message, endpoint = $endpoint, data = $data" },
            )
            failCallback(code.toString(), message)
        }
        NuroLogger.debug(
            "HttpTransport",
            f0@{ "sendRequest: endpoint = $endpoint, data = $data, method = ${esConfig.method}" },
        )
        SSEImpl.fetch(esConfig)
    }
}

class HttpData : TSNSerializable {
    var ret: String = "0"

    var errmsg: String? = null

    var systime: String? = null

    var logid: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.ret = TSNJSONObject.getString(map, "ret") ?: ""
        this.errmsg = TSNJSONObject.getString(map, "errmsg") ?: null
        this.systime = TSNJSONObject.getString(map, "systime") ?: null
        this.logid = TSNJSONObject.getString(map, "logid") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["ret"] = this.ret
        this.encodeData["errmsg"] = this.errmsg
        this.encodeData["systime"] = this.systime
        this.encodeData["logid"] = this.logid
    }
}