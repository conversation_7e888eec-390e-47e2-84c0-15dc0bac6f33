// 872c04339a8fcc7a37e613955b7a6f7e
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class EventStreamConfig {
    var endpoint: String = ""

    var method: String = "POST"

    var headers: MutableMap<String, String>? = null

    var data: String? = null

    var onStart: (() -> Unit)? = null

    var onChunk: ((String) -> Unit)? = null

    var onError: ((Int, String?) -> Unit)? = null

    var onFinish: (() -> Unit)? = null

    var onCancel: ((Int, String?) -> Unit)? = null
}

object EventStreamAdapter {
    var fetch: ((EventStreamConfig) -> String)? = null

    var cancel: ((String) -> Unit)? = null

    var reconnectEndpoint: String? = null // 重连的 endpoint

    var interruptEndpoint: String? = null // 中断的 endpoint

    var payloadEndpoint: String? = null // payload// 的 endpoint
}

object SSEImpl {
    fun fetch(config: EventStreamConfig): String? {
        val fetcher = EventStreamAdapter.fetch
        if (fetcher != null) {
            return fetcher(config)
        }
        return null
    }

    fun cancel(cancelToken: String): Unit {
        val canceler = EventStreamAdapter.cancel
        if (canceler != null) {
            canceler(cancelToken)
        }
    }
}