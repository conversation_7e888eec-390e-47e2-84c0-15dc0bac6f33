// 8532db10a1e7be43741959b62d7bfc71
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

open abstract class CommonTransport {
    open var token: String? = null

    open abstract fun setConversationManager(conversationManager: NuroConversationManager): Unit

    open abstract fun sendMessage(
        message: NuroMessage,
        tools: MutableList<NuroMCPToolItem>,
    ): Unit

    open abstract fun sendMessages(
        messages: MutableList<NuroMessage>,
        tools: MutableList<NuroMCPToolItem>,
    ): Unit

    open abstract fun resumeMessage(): Unit
}