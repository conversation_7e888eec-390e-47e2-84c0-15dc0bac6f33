// 1e2290d4eb1c10bffd53bc5fd8ac813e
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class TOSFileUploadConfig(
    nuroFile: NuroFile,
    localFile: NuroLocalFile,
) {
    lateinit var nuroFile: NuroFile

    lateinit var localFile: NuroLocalFile

    var onFinish: (() -> Unit)? = null

    var onError: ((Int, String?) -> Unit)? = null

    var onCancel: ((Int, String?) -> Unit)? = null

    init {
        this.nuroFile = nuroFile
        this.localFile = localFile
    }
}

object TOSFileUploadAdapter {
    var upload: ((TOSFileUploadConfig) -> String)? = null

    var cancel: ((String) -> Unit)? = null
}

object TOSFileUploadImpl {
    fun upload(config: TOSFileUploadConfig): String? {
        val uploader = TOSFileUploadAdapter.upload
        if (uploader != null) {
            return uploader(config)
        }
        return null
    }

    fun cancel(cancelToken: String): Unit {
        val canceler = TOSFileUploadAdapter.cancel
        if (canceler != null) {
            canceler(cancelToken)
        }
    }
}