// a84dc7053395f3a49bcdc75481466755
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

// Define log levels
enum class NuroLogLevel(
    val value: Int,
) {
    DEBUG(0),
    INFO(1),
    ERROR(2),
    NONE(3), // No logs
}

fun NuroLogLevel.valueOf(): Int = this.value

object NuroLoggerAdapter {
    var debug: ((String, String) -> Unit)? = null

    var info: ((String, String) -> Unit)? = null

    var error: ((String, String) -> Unit)? = null
}

object NuroLogger {
    private var currentLevel: NuroLogLevel = NuroLogLevel.ERROR // Default log level

    fun setLogLevel(level: NuroLogLevel): Unit {
        NuroLogger.currentLevel = level
    }

    fun debug(
        tag: String,
        msg: () -> String,
    ): Unit {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.DEBUG.valueOf()) {
            val logger = NuroLoggerAdapter.debug
            if (logger != null) {
                logger(tag, "[DEBUG] ${msg()}")
            }
        }
    }

    fun info(
        tag: String,
        msg: () -> String,
    ): Unit {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.INFO.valueOf()) {
            val logger = NuroLoggerAdapter.info
            if (logger != null) {
                logger(tag, "[INFO] ${msg()}")
            }
        }
    }

    fun error(
        tag: String,
        msg: () -> String,
    ): Unit {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.ERROR.valueOf()) {
            val logger = NuroLoggerAdapter.error
            if (logger != null) {
                logger(tag, "[ERROR] ${msg()}")
            }
        }
    }
}