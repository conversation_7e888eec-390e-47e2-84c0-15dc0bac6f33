package com.bytedance.nurosdk

import org.json.JSONArray
import org.json.JSONObject

class JSONVerifier {
    companion object {
        /**
         * 验证输入的字符串是否为有效的 JSON 格式。
         * 
         * @param json 待验证的字符串。
         * @return 如果是有效的 JSON 格式则返回 true，否则返回 false。
         */
        fun isValid(json: String): <PERSON><PERSON><PERSON> {
            return try {
                when {
                    json.trim().startsWith("{") -> JSONObject(json)
                    json.trim().startsWith("[") -> JSONArray(json)
                    else -> return false
                }
                true
            } catch (e: Exception) {
                false
            }
        }
    }
}
