// 42e9dd08c16360808dd7383ff3efc5dd
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

object NuroSetting {
    /**
     * 是否需要展示 Server 端的 Function 调用消息
     * 如果为 true，则会在 Conversation.messages 中带有对应的 ToolCallMessage。
     */
    var needDisplayServerFunctionMessage: Boolean = true

    /**
     * 请求版本
     */
    var version: String = "3.0.0"

    /**
     *
     */
    var canvasSettings: CanvasSettings =
        mutableMapOf(
            "startNode" to "",
            "endNode" to "",
            "nodes" to mutableListOf(),
        )

    /**
     * agent之间的屏蔽toolCall
     */
    var shieldToolCall: MutableList<String> = mutableListOf("handoff_to_planner", "handoff_to_host")
}

interface CanvasSettings {
    var startNode: String
    var endNode: String
    var nodes: MutableList<String>
}