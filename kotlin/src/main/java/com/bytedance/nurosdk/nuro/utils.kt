// 0c3941972c809085adb7fc69132f4a90
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*
import java.util.UUID

object NuroUtils {
    fun randomUUIDString(): String = UUID.randomUUID().toString()

    fun isJSONValid(json: String): Boolean = JSONVerifier.isValid(json)

    fun stringEndsWith(
        str: String,
        suffix: String,
    ): Boolean {
        if (str.length < suffix.length) {
            return false
        }
        return str.substr(str.length - suffix.length) == suffix
    }

    // for Canvas做的临时逻辑
    fun completeQuotes(str: String): String? {
        val len = str.length
        if (len == 0) {
            return ""
        }
        var output: MutableList<String> = mutableListOf()
        var inString = false
        for (i in 0 until len) {
            val char = str.substring(i, i + 1)
            if (char == "\"" && (i == 0 || str.substring(i - 1, i) != "\\")) {
                if (inString) {
                    // Potentially the end of a string.
                    // Peek ahead to see if the next non-whitespace character is a valid JSON delimiter.
                    var nextNonWhitespaceChar: String? = null
                    for (j in i + 1 until len) {
                        val nextChar = str.substring(j, j + 1)
                        if (nextChar != " " && nextChar != "\n" && nextChar != "\r" && nextChar != "\t") {
                            nextNonWhitespaceChar = nextChar
                            break
                        }
                    }
                    val _nextNonWhitespaceChar = nextNonWhitespaceChar
                    if (_nextNonWhitespaceChar != null && _nextNonWhitespaceChar != ":" && _nextNonWhitespaceChar != "]" && _nextNonWhitespaceChar != "}" && _nextNonWhitespaceChar != ",") {
                        output.push("\\", char)
                    } else {
                        output.push(char)
                        inString = false
                    }
                } else {
                    output.push(char)
                    inString = true
                }
            } else {
                output.push(char)
            }
        }
        if (inString) {
            output.push("\"")
        }
        val result = output.join("")
        return result
    }

    fun jsonrepair(str: String): String? {
        if (this.isJSONValid(str) == true) {
            return str
        }
        // Heuristic to remove a single trailing comma (Chinese or ASCII)
        // if it's the very last character of the trimmed string.
        var tempStr = str.trim()
        if (tempStr.length > 0 && NuroUtils.stringEndsWith(tempStr, ",") == true) {
            tempStr = tempStr.substring(0, tempStr.length - 1)
        }
        val parseStr = tempStr
        val len = parseStr.length
        if (len == 0) {
            return ""
        }
        var output: MutableList<String> = mutableListOf()
        val stack: MutableList<String> = mutableListOf() // Stack will store '{', '[', or '"'
        var inString = false
        for (i in 0 until len) {
            val char = parseStr.substring(i, i + 1)
            output.push(char)
            if (char == "\"" && (i == 0 || parseStr.substring(i - 1, i) != "\\")) {
                if (inString) {
                    if (stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "\"") {
                        stack.pop()
                    }
                    inString = false
                } else {
                    stack.push("\"")
                    inString = true
                }
            } else if (!inString) {
                if (char == "{") {
                    stack.push("{")
                } else if (char == "[") {
                    stack.push("[")
                } else if (char == "}") {
                    if (stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "{") {
                        stack.pop()
                    }
                } else if (char == "]") {
                    if (stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "[") {
                        stack.pop()
                    }
                }
            }
        }
        while (stack.length > 0) {
            val openSymbol = stack.pop()
            if (openSymbol == "\"") {
                output.push("\"")
            } else if (openSymbol == "{") {
                val currentOutputStr = output.join("")
                if (this.stringEndsWith(currentOutputStr, ":") == true) {
                    output.push("\"\"")
                }
                output.push("}")
            } else if (openSymbol == "[") {
                output.push("]")
            }
        }
        var repairedString = output.join("")
        if (this.isJSONValid(repairedString) == true) {
            return repairedString
        } else {
            // 处理一下转义
            var quoteRepairedString = this.completeQuotes(repairedString) ?: ""
            if (this.isJSONValid(quoteRepairedString) == true) {
                return quoteRepairedString
            }
        }
        return null
    }
}