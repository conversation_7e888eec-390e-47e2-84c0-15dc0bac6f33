// 84a119bb90cbd0ad3a34eb16be49640f
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

enum class NuroMessageOp(
    val value: String,
) {
    add("add"),
    update("update"),
    delete("delete"),
}

fun NuroMessageOp.valueOf(): String = this.value

enum class NuroTaskOp(
    val value: String,
) {
    add("add"),
    update("update"),
    delete("delete"),
}

fun NuroTaskOp.valueOf(): String = this.value

enum class NuroTaskStatus(
    val value: String,
) {
    none("none"),
    running("running"),
    finished("finished"),
    failed("failed"),
    cancelled("cancelled"),
}

fun NuroTaskStatus.valueOf(): String = this.value

class NuroMessageOut(
    message: NuroMessage,
    op: NuroMessageOp,
) {
    lateinit var message: NuroMessage

    lateinit var messageOp: NuroMessageOp

    init {
        this.message = message
        this.messageOp = op
    }
}

class NuroTaskOut(
    task: NuroTask,
    taskOp: NuroTaskOp,
) {
    lateinit var task: NuroTask

    lateinit var taskOp: NuroTaskOp

    var messages: MutableList<NuroMessageOut> = mutableListOf()

    init {
        this.task = task
        this.taskOp = taskOp
    }

    fun updateMessage(messages: MutableList<NuroMessageOut>) {
        messages.forEach(
            f0@{ msg ->
                if (this.task.containMessage(msg.message)) {
                    this.messages.push(msg)
                } else {
                    this.messages.push(NuroMessageOut(msg.message, NuroMessageOp.delete))
                }
            },
        )
    }
}

class NuroTask(
    taskId: String,
) {
    /**
     * 任务ID
     */
    lateinit var taskId: String

    /**
     * 任务状态
     */
    var taskStatus: NuroTaskStatus = NuroTaskStatus.none

    /**
     * 消息哈希
     * 用于判断消息是否发生了变化
     */
    var messageHash: String = ""

    /**
     * 用户输入的问题
     * 可能包含多个 UserMessage
     */
    var promptMessages: MutableList<NuroMessage> = mutableListOf()

    /**
     * 中间消息
     * 可能包含多个 ReasoningMessage、AssistantMessage、ToolCallMessage
     * 这些消息表示了生成产物的过程
     */
    var middlewareMessages: MutableList<NuroMessage> = mutableListOf()

    /**
     * 产物消息
     * 可能包含多个 AssistantMessage、ToolCallMessage
     */
    var artifactMessages: MutableList<NuroMessage> = mutableListOf()

    /**
     *
     * 屏蔽掉的消息信息
     */
    var shieldMessages: MutableList<NuroMessage> = mutableListOf()

    init {
        this.taskId = taskId
    }

    fun recalculateMessageHash() {
        var messageHashPart =
            this.promptMessages
                .concat(this.middlewareMessages)
                .concat(this.shieldMessages)
                .concat(this.artifactMessages)
        val messageHash =
            messageHashPart
                .map(
                    f0@{ it ->
                        it.id + "_" + it.updated.toString(10)
                    },
                ).toMutableList()
                .join(",")
        this.messageHash = messageHash
    }

    fun displayingMiddlewareMessages(): MutableList<NuroMessage> {
        var addFirstAssistant: Boolean = false
        return this.middlewareMessages
            .filter(
                f0@{ msg: NuroMessage ->
                    if (msg is NuroAssistantMessage) {
                        if (addFirstAssistant == false) {
                            addFirstAssistant = true
                            return@f0 true
                        }
                    } else if (msg is NuroToolCallMessage) {
                        if (msg.toolType == ChatToolCallType.client_function.valueOf()) {
                            return@f0 true
                        }
                    }
                    return@f0 false
                },
            ).toMutableList()
    }

    fun isDisplayingMiddlewareMessage(msg: NuroMessage): Boolean {
        var showMsgs: MutableList<NuroMessage> = this.displayingMiddlewareMessages()
        return showMsgs.some(
            f0@{ it: NuroMessage ->
                it.id == msg.id
            },
        )
    }

    fun addMessage(
        msg: NuroMessage,
        taskMsgType: NuroTaskMessageType,
    ) {
        msg._task = this
        msg.taskMessageType = taskMsgType
        if (taskMsgType == NuroTaskMessageType.promptMessage) {
            this.promptMessages.push(msg)
        } else if (taskMsgType == NuroTaskMessageType.middlewareMessage) {
            this.middlewareMessages.push(msg)
        } else if (taskMsgType == NuroTaskMessageType.artifactMessage) {
            this.artifactMessages.push(msg)
        } else if (taskMsgType == NuroTaskMessageType.shieldMessage) {
            this.shieldMessages.push(msg)
        }
    }

    fun containMessage(msg: NuroMessage): Boolean {
        for (i in 0 until this.promptMessages.length) {
            if (this.promptMessages.tsn_safeGet(i)?.id == msg.id) {
                return true
            }
        }
        for (i in 0 until this.middlewareMessages.length) {
            if (this.middlewareMessages.tsn_safeGet(i)?.id == msg.id) {
                return true
            }
        }
        for (i in 0 until this.artifactMessages.length) {
            if (this.artifactMessages.tsn_safeGet(i)?.id == msg.id) {
                return true
            }
        }
        for (i in 0 until this.shieldMessages.length) {
            if (this.shieldMessages.tsn_safeGet(i)?.id == msg.id) {
                return true
            }
        }
        return false
    }

    fun needResume(): Boolean = this.taskStatus == NuroTaskStatus.running
}

open class NuroTaskChecker {
    open fun isPromptMessage(message: NuroMessage): Boolean = message is NuroUserMessage

    open fun isArtifactMessage(message: NuroMessage): Boolean = !(message is NuroUserMessage)

    open fun isShieldMessage(message: NuroMessage): Boolean {
        if (message is NuroToolCallMessage) {
            return (NuroSetting.shieldToolCall.indexOf((message as NuroToolCallMessage).toolName) >= 0)
        }
        return false
    }
}

class NuroCanvasChecker {
    public fun isNuroCanvasMessage(message: NuroToolCallMessage): Boolean = false
}