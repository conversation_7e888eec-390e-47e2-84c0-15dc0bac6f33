//2186c1b646472c87deaa6e5153fcea0c
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

enum class ChatMessageError(val value: String)  {
    /**
       * 推流前异常错误
       * 推流失败，比如第一条事件就推送失败了
       */
    interrupt_status("interrupt_status"),
    /**
       * 推流前异常错误
       * pe策略失败
       */
    pe_policy_failed_status("pe_policy_failed_status"),
    /**
       * 推流前异常错误
       * 获取流失败
       */
    chat_stream_failed_status("chat_stream_failed_status"),
    /**
       * 安全审核拦截
       * 输入文本审核拦截
       */
    input_text_block_status("input_text_block_status"),
    /**
       * 安全审核拦截
       * 输出文本审核拦截
       */
    output_text_block_status("output_text_block_status"),
    /**
       * 推流异常状态
       * 推送思考内容截止
       */
    send_reasoning_content_stop_status("send_reasoning_content_stop_status"),
    /**
       * 推流异常状态
       * 推送内容截止
       */
    send_content_stop_status("send_content_stop_status"),
    /**
       * sdk返回值错误
       * sse请求错误
       */
    send_failed("send_failed"),
    /**
       * sdk返回值错误
       * 上传文件失败
       */
    upload_failed("upload_failed"),
}

fun ChatMessageError.valueOf(): String { return this.value }

enum class NuroUserMessageStatus(val value: String)  {
    /**
       * 刚创建状态
       */
    none("none"),
    /**
       * 正在上传图片/视频
       */
    uploading_files("uploading_files"),
    /**
       * 正在发送
       */
    sending("sending"),
    /**
       * 收到返回的 AssistantMessage
       * 收到一点点立即变状态，不会等AssistantMessage完整收到再改变改状态
       */
    finished_successfully("finished_successfully"),
    /**
       * 失败
       */
    failed("failed"),
    /**
       * 取消
       */
    cancelled("cancelled"),
}

fun NuroUserMessageStatus.valueOf(): String { return this.value }

enum class NuroAssistantMessageStatus(val value: String)  {
    /**
       * 刚创建状态
       */
    none("none"),
    /**
       * 正在回传数据流
       */
    streaming("streaming"),
    /**
       * 数据流返回完成
       */
    finished_successfully("finished_successfully"),
    /**
       * 失败
       */
    failed("failed"),
    /**
       * 取消
       */
    cancelled("cancelled"),
}

fun NuroAssistantMessageStatus.valueOf(): String { return this.value }

enum class NuroReasoningMessageStatus(val value: String)  {
    /**
       * 刚创建状态
       */
    none("none"),
    /**
       * 正在回传数据流
       */
    streaming("streaming"),
    /**
       * 数据流返回完成
       */
    finished_successfully("finished_successfully"),
    /**
       * 失败
       */
    failed("failed"),
    /**
       * 取消
       */
    cancelled("cancelled"),
}

fun NuroReasoningMessageStatus.valueOf(): String { return this.value }

enum class NuroToolCallMessageStatus(val value: String)  {
    /**
       * 刚创建状态
       */
    none("none"),
    /**
       * 工具参数正在流式传输中，上层业务不应允许用户操作本工具。
       */
    streaming("streaming"),
    /**
       * 流式传输失败，可能是 LLM 或 Host Agent 或网络异常原因。
       */
    streaming_failed("streaming_failed"),
    /**
       * 流式传输取消，可能是用户主动取消
       */
    streaming_cancelled("streaming_cancelled"),
    /**
       * 正在调用本地或远端方法
       * 此处执行过程中，toolResult 有可能被流式更新。
       */
    invoking("invoking"),
    /**
       * 本地方法无法直接返回结果
       * 等 sendToolResultMessage 方法调用返回结果
       */
    wait_user_response("wait_user_response"),
    /**
       * 用户发送其他消息，不再通过sendToolResultMessage 回调结果
       */
    skipped("skipped"),
    /**
       * 方法成功回调结果
       */
    finished_successfully("finished_successfully"),
}

fun NuroToolCallMessageStatus.valueOf(): String { return this.value }

enum class NuroMessageType(val value: String)  {
    user("user"),
    assistant("assistant"),
    reasoning("reasoning"),
    toolcall("tool_call"),
    canvas("canvas"),
}

fun NuroMessageType.valueOf(): String { return this.value }

/**
 *  PayLoadData
 */
class PayLoadData: TSNSerializable {
    
    var conversationId: String = ""

    var messageId: String = ""

    var payload: String = "" //传submit_id
    constructor() : super() { doInit(false) }
    constructor(JSONString: String) : super(JSONString) { doInit(true) }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) { doInit(true) }
    
    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.conversationId = TSNJSONObject.getString(map, "conversation_id") ?: ""
        this.messageId = TSNJSONObject.getString(map, "message_id") ?: ""
        this.payload = TSNJSONObject.getString(map, "payload") ?: ""
    }
    
    override fun unmapping() {
        super.unmapping()
        this.encodeData["conversation_id"] = this.conversationId
        this.encodeData["message_id"] = this.messageId
        this.encodeData["payload"] = this.payload
    }


}

/**
 *  InterruptData
 */
class InterruptData: TSNSerializable {
    
    var conversationId: String = ""

    var messageId: String = ""
    constructor() : super() { doInit(false) }
    constructor(JSONString: String) : super(JSONString) { doInit(true) }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) { doInit(true) }
    
    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.conversationId = TSNJSONObject.getString(map, "conversation_id") ?: ""
        this.messageId = TSNJSONObject.getString(map, "message_id") ?: ""
    }
    
    override fun unmapping() {
        super.unmapping()
        this.encodeData["conversation_id"] = this.conversationId
        this.encodeData["message_id"] = this.messageId
    }


}

/**
 *  ResumeData
 */
class ResumeData: TSNSerializable {
    
    var conversationId: String = ""

    var messageId: String = ""

    var from_first_message: Boolean? = true
    constructor() : super() { doInit(false) }
    constructor(JSONString: String) : super(JSONString) { doInit(true) }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) { doInit(true) }
    
    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.conversationId = TSNJSONObject.getString(map, "conversation_id") ?: ""
        this.messageId = TSNJSONObject.getString(map, "message_id") ?: ""
        this.from_first_message = TSNJSONObject.getBool(map, "from_first_message") ?: null
    }
    
    override fun unmapping() {
        super.unmapping()
        this.encodeData["conversation_id"] = this.conversationId
        this.encodeData["message_id"] = this.messageId
        this.encodeData["from_first_message"] = this.from_first_message
    }


}

enum class NuroTaskMessageType(val value: String)  {
    promptMessage("promptMessage"),
    middlewareMessage("middlewareMessage"),
    artifactMessage("artifactMessage"),
    shieldMessage("shieldMessage"),
}

fun NuroTaskMessageType.valueOf(): String { return this.value }

open class NuroMessage(id: String, type: String) {
    
    lateinit open var id: String

    lateinit open var type: String

    open var updated: Int = 0

    open var createTime: Long = 0

    open var errorMsg: String? = null

    open var metadata: ChatMessageMetadata = ChatMessageMetadata()

    open var taskMessageType: NuroTaskMessageType? = null

    // 此消息是否为最后一轮
    open var endTurn: Boolean? = null

    /**
       * 原始的消息 id，用于标识服务端消息的唯一性，请勿在前端使用！！！
       */
    open var _rawId: String = ""

    /**
       * 会话管理器，用于发送消息，上层业务勿用。
       */
    open var _conversationManager: NuroConversationManager? = null

    /**
       * 消息对应的task
       */
    open var _task: NuroTask? = null

    /**
       * 消息索引值，用于排序
       */
    open var _messageIndex: Int = 0

    init {
        this.id = id
        this.type = type
        this.updated = 0
    }

    open fun setMessagePayload(payload: String, successCallback: (String) -> Unit, failCallback: (String, String?) -> Unit): Unit {
        if (EventStreamAdapter.payloadEndpoint == null) {
            return
        }
        this.metadata.payload = payload
        var payLoadData: PayLoadData = PayLoadData()
        payLoadData.messageId = this._rawId
        payLoadData.conversationId = this._conversationManager?.conversation?.conversationId ?: ""
        payLoadData.payload = payload
        HttpTransport().sendRequest(EventStreamAdapter.payloadEndpoint, payLoadData.toJSONString() ?: "", mutableMapOf(), successCallback, f0@{ code: String, reason: String? ->
              failCallback(code, reason)
        })
    }

    open fun baseCopy(message: NuroMessage): NuroMessage {
        message.updated = this.updated
        message.createTime = this.createTime
        message.errorMsg = this.errorMsg
        message.metadata = this.metadata
        message._rawId = this._rawId
        message._conversationManager = this._conversationManager
        message.taskMessageType = this.taskMessageType
        message._task = this._task
        message.endTurn = this.endTurn
        return message
    }

    open fun copy(): NuroMessage {
        val message = NuroMessage(this.id, this.type)
        this.baseCopy(message)
        return message
    }

    open fun isDisplay(): Boolean {
        if (this.taskMessageType == NuroTaskMessageType.middlewareMessage) {
            var show = this._task?.isDisplayingMiddlewareMessage(this) ?: true
                return show
        }
        else {
            return true
        }
    }

    open fun isFinalStatus(): Boolean {
        return true
    }

    open fun isCancelledStatus(): Boolean {
        return true
    }

    open fun isFailedStatus(): Boolean {
        return true
    }

    open fun isEqualToObject(message: Any): Boolean {
        if (message is NuroMessage) {
            return (this.id == message.id && this.type == message.type && this.createTime == message.createTime && this.errorMsg == message.errorMsg && this.metadata.isEqualToObject(message.metadata) == true && this.taskMessageType == message.taskMessageType && this.endTurn == message.endTurn)
        }
        return false
    }

    /**
       * 获取这条消息 对应的 这轮回话里面的 相关消息（包含 NuroUserMessage）
       */
    open fun getMessageGroup(): MutableList<NuroMessage> {
        var msgs: MutableList<NuroMessage> = this._conversationManager?.conversation?.messages ?: mutableListOf()
        var targetIndex = -1
        run {
          var i = 0
          var __first__ = true
          while (i < msgs.length) {
            if (!__first__) {
              i = i + 1
            }
            __first__ = false
            if (i < msgs.length) {
                  var id = msgs.tsn_safeGet(i)?.id ?: ""
                if (id == this.id) {
                    targetIndex = i
                        break
                }
            }
            else {
              break
            }
          }
        }
        if (targetIndex == -1) {
            return mutableListOf()
        }
        // 向前查找：包含第一个遇到的 type='a'
            var startIndex = 0
        run {
          var i = targetIndex
          var __first__ = true
          while (i >= 0) {
            if (!__first__) {
              i = i - 1
            }
            __first__ = false
            if (i >= 0) {
                  if (msgs.tsn_safeGet(i) is NuroUserMessage) {
                    startIndex = i
                        break
                }
                if (i == 0) {
                    startIndex = 0
                }
            }
            else {
              break
            }
          }
        }
        // 向后查找：不包含第一个遇到的 type='a'
            var endIndex = msgs.length
        run {
          var i = targetIndex + 1
          var __first__ = true
          while (i < msgs.length) {
            if (!__first__) {
              i = i + 1
            }
            __first__ = false
            if (i < msgs.length) {
                  if (msgs.tsn_safeGet(i) is NuroUserMessage) {
                    endIndex = i
                        break
                }
            }
            else {
              break
            }
          }
        }
        return msgs.slice(startIndex, endIndex)
    }

    open fun needResume(): Boolean {
        if (this == null) {
            return false
        }
        if (this is NuroUserMessage) {
            return false
        }
        return !this.isFinalStatus()
    }

    open fun getResumeMsgId(): String {
        return this._rawId
    }

}

enum class NuroFileType(val value: String)  {
    image("image"),
    video("video"),
}

fun NuroFileType.valueOf(): String { return this.value }

class NuroLocalFile(localPath: String, localFileObject: Any?) {
    
    lateinit var localPath: String

    var localFileObject: Any? = null

    init {
        this.localPath = localPath
        this.localFileObject = localFileObject
    }

    fun isEqualToObject(other: Any): Boolean {
        if (other is NuroLocalFile) {
            return this.localPath == other.localPath
        }
        return false
    }

}

class NuroImageMetadata {
    
    var width: Int? = null

    var height: Int? = null

    var format: String? = null

    var prompt: String? = null

    fun isEqualToObject(other: Any): Boolean {
        if (other is NuroImageMetadata) {
            return (this.width == other.width && this.height == other.height && this.format == other.format && this.prompt == other.prompt)
        }
        return false
    }

}

class NuroFileMetadata(imageMetadata: NuroImageMetadata) {
    
    // 图片
    var image_metadata: NuroImageMetadata? = null

    init {
        this.image_metadata = imageMetadata
    }

    fun isEqualToObject(other: Any): Boolean {
        if (other is NuroFileMetadata) {
            val _this_image_metadata = this?.image_metadata
                val _other_image_metadata = other?.image_metadata
                if (this.image_metadata == null && other.image_metadata == null) {
                    return true
                }
                else if (_this_image_metadata != null && _other_image_metadata != null) {
                    return (_this_image_metadata?.isEqualToObject(_other_image_metadata) == true)
                }
                else {
                    return false
                }
        }
        return false
    }

}

class NuroFile(type: NuroFileType, url: String?, mimeType: String?, localFile: NuroLocalFile? = null, file_description: String?) {
    
    /**
       * image, video, etc.
       */
    lateinit var type: NuroFileType

    /**
       * url of the file.
       */
    var url: String? = null

    /**
       * uri of the file.
       */
    var uri: String? = null

    /**
       *
       */
    var extra: MutableMap<String, Any>? = null

    /**
       * 本地文件信息，当文件被用户选择后，并且未被上传时，会有该字段。
       * 该信息将透传至 TOS Uploader 方法，用于上传文件，文件上传后，url 字段会被更新。
       */
    var localFile: NuroLocalFile? = null

    /**
       * 增加imagemeta信息
       */
    var mimeType: String? = null

    /**
       * 文件描述, 业务需要添加的描述，可以用来描述相对关系
       */
    var file_description: String? = null

    /**
       * metadata, 用来存prompt等信息
       */
    var metadata: NuroFileMetadata? = null

    init {
        this.type = type
        this.url = url
        this.mimeType = mimeType
        this.localFile = localFile
        this.file_description = file_description
    }

    fun isEqualToObject(other: Any): Boolean {
        if (other is NuroFile) {
            var localFileEqual = false
                run{
                      
                val _this_localFile = this?.localFile
                val _other_localFile = other?.localFile
                if (_this_localFile != null && _other_localFile != null) {
                    localFileEqual = _this_localFile.isEqualToObject(_other_localFile) == true
                }
                else if (this.localFile == null && other.localFile == null) {
                    localFileEqual = true
                }
                      }
                var localMetadataEqual = false
                run{
                      
                val _this_metadata = this?.metadata
                val _other_metadata = other?.metadata
                if (_this_metadata != null && _other_metadata != null) {
                    localMetadataEqual = _this_metadata.isEqualToObject(_other_metadata) == true
                }
                else if (this.metadata == null && other.metadata == null) {
                    localMetadataEqual = true
                }
                      }
                return (this.type == other.type && this.url == other.url && this.uri == other.uri && this.file_description == other.file_description && this.mimeType == other.mimeType && localMetadataEqual && localFileEqual)
        }
        return false
    }

}

enum class ReferenceRole(val value: Int)  {
    User(0),
    Assistant(1),
}

fun ReferenceRole.valueOf(): Int { return this.value }

class RefContent(text: String?, file: NuroFile?) {
    
    var text: String? = null

    var file: NuroFile? = null

    init {
        this.text = text
        this.file = file
    }

}

class NuroUserMessage(id: String, text: String?, files: MutableList<NuroFile>? = null, messageStatus: NuroUserMessageStatus = NuroUserMessageStatus.none, referenceInfo: MutableList<RefContent>? = null): NuroMessage(id, NuroMessageType.user) {
    
    var text: String? = null

    var files: MutableList<NuroFile>? = null

    lateinit var messageStatus: NuroUserMessageStatus

    var referenceInfo: MutableList<RefContent>? = null

    init {
        this.text = text
        this.files = files
        this.createTime = TSNNumberConverter.toInt64(Date.now())
        this.messageStatus = messageStatus
        this.referenceInfo = referenceInfo
    }

    override fun copy(): NuroMessage {
        val message = NuroUserMessage(this.id, this.text, this.files, this.messageStatus, this.referenceInfo)
        this.baseCopy(message)
        return message
    }

    override fun isEqualToObject(other: Any): Boolean {
        if (other is NuroUserMessage) {
            var filesEqual = true
                val _this_files = this?.files
                val _other_files = other?.files
                if (_this_files != null && _other_files != null && _this_files.length == _other_files.length) {
                    for (i in 0 until _this_files.length) {
                              if (_this_files[i].isEqualToObject(_other_files[i]) != true) {
                                    filesEqual = false
                                        break
                                }
                        }
                }
                else if (this.files?.length != other.files?.length) {
                    filesEqual = false
                }
                return (super.isEqualToObject(other) == true && this.text == other.text && this.messageStatus == other.messageStatus && filesEqual)
        }
        return false
    }

    fun setMsgStatus(status: NuroUserMessageStatus): Unit {
        var newmsg = this.copy()
        if (newmsg is NuroUserMessage) {
            newmsg.messageStatus = status
        }
        this._conversationManager?.receivedMessage(newmsg)
    }

    override fun isFinalStatus(): Boolean {
        return (this.messageStatus == NuroUserMessageStatus.finished_successfully || this.messageStatus == NuroUserMessageStatus.failed || this.messageStatus == NuroUserMessageStatus.cancelled)
    }

    override fun isCancelledStatus(): Boolean {
        return this.messageStatus == NuroUserMessageStatus.cancelled
    }

    override fun isFailedStatus(): Boolean {
        return this.messageStatus == NuroUserMessageStatus.failed
    }

}

class NuroReasoningMessage(id: String, text: String, messageStatus: NuroReasoningMessageStatus = NuroReasoningMessageStatus.none): NuroMessage(id, NuroMessageType.reasoning) {
    
    lateinit var text: String

    lateinit var messageStatus: NuroReasoningMessageStatus

    init {
        this.text = text
        this.messageStatus = messageStatus
    }

    override fun copy(): NuroMessage {
        val message = NuroReasoningMessage(this.id, this.text, this.messageStatus)
        this.baseCopy(message)
        return message
    }

    override fun isEqualToObject(other: Any): Boolean {
        if (other is NuroReasoningMessage) {
            return (super.isEqualToObject(other) == true && this.text == other.text && this.messageStatus == other.messageStatus)
        }
        return false
    }

    override fun isFinalStatus(): Boolean {
        return (this.messageStatus == NuroReasoningMessageStatus.finished_successfully || this.messageStatus == NuroReasoningMessageStatus.failed || this.messageStatus == NuroReasoningMessageStatus.cancelled)
    }

    override fun isCancelledStatus(): Boolean {
        return this.messageStatus == NuroReasoningMessageStatus.cancelled
    }

    override fun isFailedStatus(): Boolean {
        return this.messageStatus == NuroReasoningMessageStatus.failed
    }

    fun setMsgStatus(status: NuroReasoningMessageStatus): Unit {
        var newmsg = this.copy()
        if (newmsg is NuroReasoningMessage) {
            newmsg.messageStatus = status
        }
        this._conversationManager?.receivedMessage(newmsg)
    }

    fun setStatus(status: ChatMessageStatus?): Unit {
        if (status == null) {
            return
        }
        when (status) { 
            ChatMessageStatus.finished_successfully -> {
                this.messageStatus = NuroReasoningMessageStatus.finished_successfully}
            ChatMessageStatus.in_progress -> {
                this.messageStatus = NuroReasoningMessageStatus.streaming}
            else -> {
                this.messageStatus = NuroReasoningMessageStatus.failed
                    this.errorMsg = status.valueOf()}
        }
    }

}

class NuroAssistantMessage(id: String, name: String?, text: String?, files: MutableList<NuroFile>? = null, messageStatus: NuroAssistantMessageStatus = NuroAssistantMessageStatus.none): NuroMessage(id, NuroMessageType.assistant) {
    
    var name: String? = null

    var text: String? = null

    var files: MutableList<NuroFile>? = null

    lateinit var messageStatus: NuroAssistantMessageStatus

    var relateToolCalls: MutableList<NuroToolCallMessage> = mutableListOf()

    init {
        this.text = text
        this.files = files
        this.name = name
        this.messageStatus = messageStatus
    }

    override fun copy(): NuroMessage {
        val message = NuroAssistantMessage(this.id, this.name, this.text, this.files, this.messageStatus)
        message.relateToolCalls = this.relateToolCalls
        this.baseCopy(message)
        return message
    }

    override fun isEqualToObject(other: Any): Boolean {
        if (other is NuroAssistantMessage) {
            var filesEqual = true
                val _this_files = this?.files
                val _other_files = other?.files
                if (_this_files != null && _other_files != null && _this_files.length == _other_files.length) {
                    for (i in 0 until _this_files.length) {
                              if (_this_files[i].isEqualToObject(_other_files[i]) != true) {
                                    filesEqual = false
                                        break
                                }
                        }
                }
                else if (this.files?.length != other.files?.length) {
                    filesEqual = false
                }
                return (super.isEqualToObject(other) == true && this.name == other.name && this.text == other.text && this.messageStatus == other.messageStatus && filesEqual)
        }
        return false
    }

    fun setMsgStatus(status: NuroAssistantMessageStatus): Unit {
        var newmsg = this.copy()
        if (newmsg is NuroAssistantMessage) {
            newmsg.messageStatus = status
        }
        this._conversationManager?.receivedMessage(newmsg)
    }

    override fun isFinalStatus(): Boolean {
        return (this.messageStatus == NuroAssistantMessageStatus.finished_successfully || this.messageStatus == NuroAssistantMessageStatus.failed || this.messageStatus == NuroAssistantMessageStatus.cancelled)
    }

    override fun isCancelledStatus(): Boolean {
        return this.messageStatus == NuroAssistantMessageStatus.cancelled
    }

    override fun isFailedStatus(): Boolean {
        return this.messageStatus == NuroAssistantMessageStatus.failed
    }

    fun setStatus(status: ChatMessageStatus?): Unit {
        if (status == null) {
            return
        }
        when (status) { 
            ChatMessageStatus.finished_successfully -> {
                this.messageStatus = NuroAssistantMessageStatus.finished_successfully}
            ChatMessageStatus.in_progress -> {
                this.messageStatus = NuroAssistantMessageStatus.streaming}
            else -> {
                this.messageStatus = NuroAssistantMessageStatus.failed
                    this.errorMsg = status.valueOf()}
        }
    }

    // 标识是否是对话结束
    fun isFinalTurn(): Boolean {
        if (this.endTurn == true || this.metadata?.end_turn == true) {
            return true
        }
        return false
    }

}

open class NuroToolCallMessage(id: String, toolCallId: String, toolType: String, toolName: String, toolArgs: String?, toolExtra: MutableMap<String, String>?, toolResult: String?, messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none): NuroMessage(id, NuroMessageType.toolcall) {
    
    /**
       * functionType: server_function, client_function.
       */
    lateinit open var toolCallId: String

    lateinit open var toolType: String

    lateinit open var toolName: String // text2image_form

    open var toolArgs: String? = null // <- json

    open var toolResult: String? = null

    open var toolExtra: MutableMap<String, String>? = null

    lateinit open var messageStatus: NuroToolCallMessageStatus

    init {
        this.toolCallId = toolCallId
        this.toolType = toolType
        this.toolName = toolName
        this.toolArgs = toolArgs
        this.toolExtra = toolExtra
        this.toolResult = toolResult
        this.messageStatus = messageStatus
        val _toolResult = toolResult
        if (_toolResult != null && _toolResult.length > 0) {
            val toolCallResult = MCPToolCallResult(JSONString = _toolResult)
                if (toolCallResult.content.length > 0 && this.messageStatus != NuroToolCallMessageStatus.invoking) {
                    this.messageStatus = NuroToolCallMessageStatus.finished_successfully
                }
        }
    }

    open fun sendToolCallResult(result: String, userMessage: NuroUserMessage? = null): Unit {
        this.toolResult = result
        val _this_toolResult = this?.toolResult
        val _this__conversationManager = this?._conversationManager
        if (_this_toolResult != null && _this__conversationManager != null && _this_toolResult != "") {
            this.setMsgStatus(NuroToolCallMessageStatus.finished_successfully)
        }
        this._conversationManager?.sendToolResultMessage(this, userMessage)
    }

    open fun sendToolCallResultFromMCPFormat(callResult: MCPToolCallResult, userMessage: NuroUserMessage? = null): Unit {
        if (NuroMockManager.isMocking()) {
            return
        }
        this.sendToolCallResult(callResult.toJSONString() ?: "", userMessage)
    }

    open fun decodeToolCallResultAsPlainText(): String? {
        val _this_toolResult = this?.toolResult
        if (_this_toolResult != null) {
            if (_this_toolResult.trim().indexOf("{") == 0) {
                    val formattedResult = this.decodeToolCallResultToMCPFormat()
                        var content = ""
                        for (index in 0 until formattedResult.content.length) {
                              val element = formattedResult.content.tsn_safeGet(index)
                                if (element is MCPToolCallTextContent) {
                                    content += element.text
                                }
                        }
                        return content
                }
                else {
                    return _this_toolResult
                }
        }
        else {
            return null
        }
    }

    open fun decodeToolCallResultToMCPFormat(): MCPToolCallResult {
        return MCPToolCallResult(JSONString = this.toolResult ?: "{}")
    }

    open fun setMsgStatus(status: NuroToolCallMessageStatus): Unit {
        var newmsg = this.copy()
        if (newmsg is NuroToolCallMessage) {
            newmsg.messageStatus = status
        }
        this._conversationManager?.receivedMessage(newmsg)
    }

    override open fun isFinalStatus(): Boolean {
        return (this.messageStatus == NuroToolCallMessageStatus.skipped || this.messageStatus == NuroToolCallMessageStatus.finished_successfully || this.messageStatus == NuroToolCallMessageStatus.streaming_failed || this.messageStatus == NuroToolCallMessageStatus.streaming_cancelled)
    }

    override open fun isCancelledStatus(): Boolean {
        return this.messageStatus == NuroToolCallMessageStatus.streaming_cancelled
    }

    override open fun isFailedStatus(): Boolean {
        return this.messageStatus == NuroToolCallMessageStatus.streaming_failed
    }

    override open fun copy(): NuroToolCallMessage {
        val message = NuroToolCallMessage(this.id, this.toolCallId, this.toolType, this.toolName, this.toolArgs, this.toolExtra, this.toolResult, this.messageStatus)
        this.baseCopy(message)
        return message
    }

    override open fun isEqualToObject(other: Any): Boolean {
        if (other is NuroToolCallMessage) {
            return (super.isEqualToObject(other) == true && this.toolCallId == other.toolCallId && this.toolType == other.toolType && this.toolName == other.toolName && this.toolArgs == other.toolArgs && this.toolExtra == other.toolExtra && this.toolResult == other.toolResult && this.messageStatus == other.messageStatus && this.endTurn == other.endTurn)
        }
        return false
    }

    open fun isClientToolSkipped(): Boolean {
        if (this.toolType == ChatToolCallType.client_function) {
            return this.messageStatus == NuroToolCallMessageStatus.skipped
        }
        return false
    }

}

val CANVAS_DEFAULT: Int = -10000

val CANVAS_ADD_TO_END: Int = -10001

fun getNodeFromNuroToolCallMsg(id: String, nodeIndex: Int, toolCall: NuroToolCallMessage): NuroCanvasNode {
    val node = NuroCanvasNode(id, nodeIndex, toolCall.toolCallId, toolCall.toolType, toolCall.toolName, toolCall.toolArgs, toolCall.toolExtra, toolCall.toolResult, toolCall.messageStatus)
    node.nodeIndex = nodeIndex
    node._rawId = toolCall._rawId ?: ""
    node._conversationManager = toolCall._conversationManager
    node.createTime = toolCall.createTime ?: 0
    node._messageIndex = toolCall._messageIndex
    node.messageStatus = toolCall.messageStatus
    node.endTurn = toolCall.endTurn
    return node
}

// NuroCanvasNode，代表中间插入的节点信息
class NuroCanvasNode(id: String, nodeIndex: Int, toolCallId: String, toolType: String, toolName: String, toolArgs: String?, toolExtra: MutableMap<String, String>?, toolResult: String?, messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none): NuroToolCallMessage(id, toolCallId, toolType, toolName, toolArgs, toolExtra, toolResult, messageStatus) {
    
    var nodeIndex: Int = CANVAS_DEFAULT // 标识插入节点的相对位置

    init {
        this.nodeIndex = nodeIndex
    }

    override fun copy(): NuroCanvasNode {
        val message = NuroCanvasNode(this.id, this.nodeIndex, this.toolCallId, this.toolType, this.toolName, this.toolArgs, this.toolExtra, this.toolResult, this.messageStatus)
        this.baseCopy(message)
        return message
    }

    override fun isCancelledStatus(): Boolean {
        // 生成节点没有取消状态
        return super.isCancelledStatus()
    }

    override fun isFailedStatus(): Boolean {
        // 生成节点没有fail状态
        return super.isFailedStatus()
    }

    override fun isFinalStatus(): Boolean {
        return super.isFinalStatus()
    }

    override fun getResumeMsgId(): String {
        return this._rawId
    }

    override fun isEqualToObject(other: Any): Boolean {
        if (other is NuroCanvasNode) {
            return (super.isEqualToObject(other) == true && this.nodeIndex == other.nodeIndex)
        }
        return false
    }

}

enum class NuroCanvasStatus(val value: Int)  {
    none(0),
    init(1),
    streaming(2),
    cancel(3), // 用户取消
    end(4),
}

fun NuroCanvasStatus.valueOf(): Int { return this.value }

class NuroCanvasMessage(id: String): NuroMessage(id, NuroMessageType.canvas) {
    
    var status: NuroCanvasStatus = NuroCanvasStatus.none

    var startNode: NuroToolCallMessage? = null

    var endNode: NuroToolCallMessage? = null

    // 标识对应的图片
    var nodes: MutableList<NuroCanvasNode>? = mutableListOf()

    init {
    
    }

    fun updateStartNode(node: NuroToolCallMessage) {
        this.startNode = node
    }

    fun updateCanvasStatus(status: NuroCanvasStatus) {
        this.status = status
        this.setMsgStatus(this.status)
    }

    fun updateEndNode(node: NuroToolCallMessage) {
        this.endNode = node
    }

    /*
       ** 增加一个finish接口，因为收到end的时候，有可能还在streaming
       */
    fun finish() {
        this.status = NuroCanvasStatus.end
        this.setMsgStatus(NuroCanvasStatus.end)
    }

    fun addOrReplaceNode(node: NuroCanvasNode): Unit {
        if (this.nodes == null) {
            this.nodes = mutableListOf()
        }
        if (node.nodeIndex > this.nodes?.length) {
            this.nodes.push(node)
        }
        else {
            if (node.nodeIndex == CANVAS_ADD_TO_END) {
                    var length = this.nodes.length
                        this.nodes[length] = node
                }
                else {
                    var index = node.nodeIndex - 1
                        this.nodes[index] = node
                }
        }
    }

    fun findNodeByToolCallId(toolCallId: String): NuroCanvasNode? {
        if (this.nodes == null) {
            return null
        }
        var targetNode: NuroCanvasNode? = null
        this.nodes.forEach(f0@{ it ->
              if (it.toolCallId == toolCallId) {
                    targetNode = it
                }
        })
        return targetNode
    }

    fun findEndNode(toolCallId: String): NuroToolCallMessage? {
        if (this.endNode == null) {
            return null
        }
        if (this.endNode.toolCallId == toolCallId) {
            return this.endNode
        }
        return null
    }

    fun findStartNode(toolCallId: String): NuroToolCallMessage? {
        if (this.startNode == null) {
            return null
        }
        if (this.startNode.toolCallId == toolCallId) {
            return this.startNode
        }
        return null
    }

    fun isCanvasOpen(): Boolean {
        return (this.status != NuroCanvasStatus.end && this.status != NuroCanvasStatus.cancel)
    }

    override fun copy(): NuroCanvasMessage {
        val message = NuroCanvasMessage(this.id)
        message.status = this.status
        message.nodes = this.nodes?.map(f0@{ it ->
              it.copy()
        })?.toMutableList()
        message.startNode = this.startNode?.copy()
        message.endNode = this.endNode?.copy()
        this.baseCopy(message)
        return message
    }

    override fun isCancelledStatus(): Boolean {
        // 没有cancel状态，生成过程中，交互需要禁止
        return this.status == NuroCanvasStatus.cancel
    }

    override fun isFailedStatus(): Boolean {
        // 画布消息不维护失败状态，由业务自行判断。
        return false
    }

    override fun isFinalStatus(): Boolean {
        return this.status == NuroCanvasStatus.end
    }

    fun setMsgStatus(status: NuroCanvasStatus): Unit {
        this.status = status
        var newmsg = this.copy()
        if (newmsg is NuroCanvasMessage) {
            newmsg.status = status
        }
        this._conversationManager?.receivedMessage(newmsg)
    }

    override fun getResumeMsgId(): String {
        var result = ""
        run{
              
        val _this_startNode = this?.startNode
        if (_this_startNode != null) {
            if (_this_startNode.needResume()) {
                    result = _this_startNode._rawId
                }
        }
              }
        run{
              
        val _this_nodes = this?.nodes
        if (_this_nodes != null) {
            _this_nodes.forEach(f0@{ node ->
                      val _node = node
                        if (_node != null) {
                            if (_node.needResume()) {
                                    result = _node._rawId
                                }
                        }
                })
        }
              }
        run{
              
        val _this_endNode = this?.endNode
        if (_this_endNode != null) {
            if (_this_endNode.needResume()) {
                    result = _this_endNode._rawId
                }
        }
              }
        if (result == "") {
            val _this_startNode = this?.startNode
                if (_this_startNode != null) {
                    result = _this_startNode._rawId
                }
        }
        return result
    }

    override fun isEqualToObject(message: Any): Boolean {
        if (message is NuroCanvasMessage) {
            var result = super.isEqualToObject(message) == true && this.status == message.status && this.startNode?.isEqualToObject(message.startNode) == true && this.endNode?.isEqualToObject(message.endNode) == true
                if (result == false) {
                    return false
                }
                if (this.nodes == null) {
                    val _message_nodes = message?.nodes
                        if (_message_nodes != null) {
                            return false
                        }
                        else {
                            return true
                        }
                }
                else if (this.nodes.length == 0) {
                    if (message.nodes == null) {
                            return false
                        }
                        else if (message.nodes.length > 0) {
                            return false
                        }
                        return true
                }
                else {
                    if (message.nodes == null) {
                            return false
                        }
                        else if (message.nodes.length == 0) {
                            return false
                        }
                        else if (this.nodes.length != message.nodes.length) {
                            return false
                        }
                        else {
                            for (i in 0 until this.nodes.length) {
                                      if (this.nodes.tsn_safeGet(i)?.isEqualToObject(message.nodes.tsn_safeGet(i)) == false) {
                                            return false
                                        }
                                }
                                return true
                        }
                }
        }
        return false
    }

}