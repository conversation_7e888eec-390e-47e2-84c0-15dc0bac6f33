// 8763eafdfdd9d4ef7297fbac4cfc8442
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class NuroConversationManager(
    conversationId: String = NuroUtils.randomUUIDString(),
) {
    val conversation: NuroConversation

    private var activeTransport: CommonTransport? = null

    /**
     * Client Side MCP
     */
    var mcpManager: NuroMCPManager? = null

    private var toolCalled: MutableMap<String, Boolean> = mutableMapOf()

    init {
        this.conversation = NuroConversation(conversationId)
    }

    /**
     * 注册需要链接的后端通道
     * @param transport
     */
    fun connect(transport: CommonTransport): Unit {
        this.activeTransport = transport
        transport.setConversationManager(this)
        this.conversation.conversationState = NuroConversationState.readyToSendMessage
    }

    /**
     * 使能本地工具调用
     */
    fun enableMCPTools(): Unit {
        var _self: NuroConversationManager? = this
        this.conversation.addStateUpdateListener(
            f0@{ state ->
                if (_self == null) {
                    return@f0
                }
                if (state == NuroConversationState.readyToSendMessage) {
                    var msgs: MutableList<NuroToolCallMessage> = mutableListOf()
                    this.conversation.messages.forEach(
                        f1@{ msg ->
                            if (msg is NuroToolCallMessage) {
                                if (msg.messageStatus == NuroToolCallMessageStatus.invoking) {
                                    if (msg.toolType == ChatToolCallType.client_function.valueOf()) {
                                        if (msg.toolResult == "") {
                                            msgs.push(msg)
                                        } else if (msg.toolResult == null) {
                                            msgs.push(msg)
                                        }
                                    }
                                }
                            }
                        },
                    )
                    if (msgs.length > 0) {
                        _self.onToolCall(msgs)
                    }
                }
            },
        )
    }

    /**
     * 解析会话历史数据
     * @param str 会话历史数据 jsonstr
     */
    fun decodeConversationFromJSONString(
        str: String,
        needResume: Boolean = false,
    ): Unit {
        var history = HistoryMessages(JSONString = str)
        this.conversation.conversationId = history.conversation?.conversation_id ?: ""
        var messages = history.getChatMessages()
        this.conversation.summary = history.conversation?.summary ?: ""
        messages.sort(
            f0@{ a, b ->
                val timeA = TSNNumberConverter.toDouble(a.create_time ?: 0)
                val timeB = TSNNumberConverter.toDouble(b.create_time ?: 0)
                if (timeA < timeB) {
                    return@f0 -1
                } else if (timeA > timeB) {
                    return@f0 1
                } else {
                    return@f0 0
                }
            },
        )
        var nuroMessagesR: MutableMap<String, NuroMessage> = mutableMapOf()
        messages.forEach(
            f0@{ message ->
                var msgs = MessageProcessor.convertChatMessageToNuroMessage(this, message, ConvertType.history)
                msgs.forEach(
                    f1@{ message ->
                        nuroMessagesR[message.id] = message
                    },
                )
                var nuroMessages: MutableList<NuroMessage> = TSNMapUtils.values(nuroMessagesR)
                var length: Int = nuroMessages.length
                if (length > 1) {
                    var parentid = nuroMessages.tsn_safeGet(0)?.id ?: ""
                    run {
                        var index = 1
                        var __first__ = true
                        while (index < length) {
                            if (!__first__) {
                                index = index + 1
                            }
                            __first__ = false
                            if (index < length) {
                                var msg = nuroMessages.tsn_safeGet(index)
                                val _msg = msg
                                if (_msg != null) {
                                    _msg.metadata.parent_message_id = parentid
                                    parentid = _msg.id
                                }
                            } else {
                                break
                            }
                        }
                    }
                }
                nuroMessages.forEach(
                    f1@{ msg ->
                        this.receivedMessage(msg, false)
                    },
                )
            },
        )
        if (needResume) {
            this.resumeMessageIfNeed()
        }
    }

    /**
     * 会话开始时需要提前填入的信息
     * @param value
     */
    fun setSystemPrompt(value: String): Unit {
        this.conversation.systemPrompt = value
    }

    fun resumeMessageIfNeed(): Unit {
        if (this.conversation.needResume() == false) {
            return
        }
        this.conversation.updateState(NuroConversationState.streamingResponse)
        this.activeTransport?.resumeMessage()
    }

    fun sendUserMessage(message: NuroUserMessage?): Unit {
        if (message == null) {
            return
        }
        if (NuroMockManager.checkMock(message, this)) {
            return
        }
        if (message._conversationManager == null) {
            message._conversationManager = this
        }
        this.conversation.updateState(NuroConversationState.streamingResponse)
        message.setMsgStatus(NuroUserMessageStatus.none)
        run {

            val _message_files = message?.files
            if (_message_files != null && _message_files.length > 0) {
                var _files: MutableList<NuroFile> = mutableListOf()
                for (index in 0 until _message_files.length) {
                    val file = _message_files[index]
                    _files.push(file)
                }
                message.setMsgStatus(NuroUserMessageStatus.uploading_files)
                this.uploadFiles(
                    _files,
                    f0@{ this._doSendUserMessage(message) },
                    f0@{ message.errorMsg = ChatMessageError.upload_failed.valueOf()
                        message.setMsgStatus(NuroUserMessageStatus.failed)
                        this.conversation.updateState(NuroConversationState.readyToSendMessage)
                    },
                )
            } else {
                this._doSendUserMessage(message)
            }
        }
    }

    private fun _doSendUserMessage(message: NuroUserMessage): Unit {
        val _this_mcpManager = this?.mcpManager
        if (_this_mcpManager != null) {
            _this_mcpManager.getAllTools(
                f0@{ tools ->
                    this.activeTransport?.sendMessage(message, tools)
                },
            )
        } else {
            this.activeTransport?.sendMessage(message, mutableListOf())
        }
    }

    private fun noUpload(file: NuroFile): Boolean = file.url != null && file.uri != null

    private fun uploadFiles(
        files: MutableList<NuroFile>,
        successCallback: () -> Unit,
        failCallback: () -> Unit,
    ): Unit {
        var uploadQueueFiles = files
        var file = uploadQueueFiles.tsn_safeGet(0)
        val _file = file
        if (_file != null) {
            var noUp: Boolean = this.noUpload(_file)
            if (noUp == true) {
                uploadQueueFiles.shift()
                this.uploadFiles(uploadQueueFiles, successCallback, failCallback)
            } else {
                val __file_localFile = _file?.localFile
                if (__file_localFile != null) {
                    var config = TOSFileUploadConfig(_file, __file_localFile)
                    config.onFinish = f0@{ uploadQueueFiles.shift()
                        this.uploadFiles(uploadQueueFiles, successCallback, failCallback)
                    }
                    config.onError = f0@{ code, message ->
                        failCallback()
                    }
                    TOSFileUploadImpl.upload(config)
                } else {
                    uploadQueueFiles.shift()
                    this.uploadFiles(uploadQueueFiles, successCallback, failCallback)
                }
            }
        } else {
            successCallback()
        }
    }

    /**
     * 本地工具方法 结果回调
     * @param message 回调的工具
     * @param userMessage 额外需要带的用户消息
     */
    fun sendToolResultMessage(
        message: NuroToolCallMessage,
        userMessage: NuroUserMessage? = null,
    ): Unit {
        this.conversation.updateState(NuroConversationState.streamingResponse)
        var msgs: MutableList<NuroMessage> = mutableListOf()
        msgs.push(message)
        run {

            val _userMessage = userMessage
            if (_userMessage != null) {
                msgs.push(_userMessage)
            }
        }
        run {

            val _this_mcpManager = this?.mcpManager
            if (_this_mcpManager != null) {
                _this_mcpManager.getAllTools(
                    f0@{ tools ->
                        this.activeTransport?.sendMessages(msgs, tools)
                    },
                )
            } else {
                this.activeTransport?.sendMessages(msgs, mutableListOf())
            }
        }
    }

    fun sendToolResultMessages(messages: MutableList<NuroToolCallMessage>): Unit {
        messages.forEach(
            f0@{ message ->
                this.receivedMessage(message)
            },
        )
        this.conversation.updateState(NuroConversationState.streamingResponse)
        val _this_mcpManager = this?.mcpManager
        if (_this_mcpManager != null) {
            _this_mcpManager.getAllTools(
                f0@{ tools ->
                    this.activeTransport?.sendMessages(messages as MutableList<NuroMessage>, tools)
                },
            )
        } else {
            this.activeTransport?.sendMessages(messages as MutableList<NuroMessage>, mutableListOf())
        }
    }

    /**
     * 重试操作
     * @param message 需要被重试的消息
     *
     * @param message 是否需要删除重试消息
     */
    fun regenerateMessage(
        message: NuroMessage,
        needDelete: Boolean,
    ): Unit {
        var hasTool = false
        var index: Int = -1
        var userMessage: NuroUserMessage? = null
        var indexuser: Int = -1
        run {
            var i = this.conversation.messages.length - 1
            var __first__ = true
            while (i > -1) {
                if (!__first__) {
                    i = i - 1
                }
                __first__ = false
                if (i > -1) {
                    var msg = this.conversation.messages.tsn_safeGet(i)
                    if (msg == null) {
                        continue
                    }
                    if (msg.id == message.id && msg.type == message.type) {
                        index = i
                    }
                    if (index > -1) {
                        if (msg is NuroToolCallMessage) {
                            hasTool = true
                        } else if (msg is NuroUserMessage) {
                            userMessage = msg
                            indexuser = i
                            break
                        }
                    }
                } else {
                    break
                }
            }
        }
        if (index != -1) {
            val _userMessage = userMessage
            if (_userMessage != null) {
                if (needDelete == true) {
                    this.conversation.messages.splice(index, 1)
                    this.notifyConversationUpdate(message, NuroMessageOp.delete)
                }
                if (needDelete == true && index != indexuser) {
                    this.conversation.messages.splice(indexuser, 1)
                    this.notifyConversationUpdate(_userMessage, NuroMessageOp.delete)
                }
                var newMessage: NuroUserMessage? = null
                if (hasTool == true && needDelete == false) {
                    newMessage = NuroUserMessage(NuroUtils.randomUUIDString(), "再次生成")
                } else {
                    newMessage = _userMessage.copy() as NuroUserMessage
                    val _newMessage = newMessage
                    if (_newMessage != null) {
                        _newMessage.id = NuroUtils.randomUUIDString()
                    }
                }
                this.sendUserMessage(newMessage)
            }
        }
    }

    /**
     * 中断 当前会话请求
     */
    fun interruptResponse(): Unit {
        var token = this.activeTransport?.token
        MessageProcessor.markInProgressMessagesAsCancel(this)
        val _token = token
        if (_token != null) {
            SSEImpl.cancel(_token)
        }
        var interruptData: InterruptData = InterruptData()
        interruptData.conversationId = this.conversation.conversationId ?: ""
        var lastUserMessage = this.conversation.findLastUserMessage()
        interruptData.messageId = lastUserMessage?.id ?: ""
        HttpTransport().sendRequest(
            EventStreamAdapter.interruptEndpoint,
            interruptData.toJSONString(),
            mutableMapOf(),
            f0@{ result: String ->
            },
            f0@{ code: String, reason: String? ->
            },
        )
    }

    fun receivedMessage(
        _message: NuroMessage,
        fromSSE: Boolean = true,
    ): Unit {
        var message = _message.copy()
        message._conversationManager = this
        var index = -1
        for (i in 0 until this.conversation.messages.length) {
            if (this.conversation.messages
                    .tsn_safeGet(i)
                    ?.id == message.id &&
                this.conversation.messages
                    .tsn_safeGet(i)
                    ?.type == message.type
            ) {
                index = i
                break
            }
        }
        if (index >= 0) {
            // message already received, current is a streaming message.
            var oldmsg = this.conversation.messages.tsn_safeGet(index)
            val _oldmsg = oldmsg
            if (_oldmsg != null) {
                if (_oldmsg.isEqualToObject(message) == true) {
                    return
                }
                if (_oldmsg.isFinalStatus() == true && fromSSE == true) {
                    return
                }
                message.updated = _oldmsg.updated + 1
            }
            this.conversation.messages[index] = message
            this.notifyConversationUpdate(message, NuroMessageOp.update)
        } else {
            var length: Int = this.conversation.messages.length
            if (length > 0) {
                message.metadata.parent_message_id = this.conversation.messages
                    .tsn_safeGet(length - 1)
                    ?.id ?: ""
            }
            this.conversation.messages.push(message)
            this.notifyConversationUpdate(message, NuroMessageOp.add)
        }
    }

    fun notifyConversationUpdate(
        message: NuroMessage,
        op: NuroMessageOp,
    ): Unit {
        this.conversation.updateMessage(message, op)
    }

    fun onToolCall(toolCallMessages: MutableList<NuroToolCallMessage>): Unit {
        var _toolCallMessages = toolCallMessages
        var toolCallCount = toolCallMessages.length
        for (index in 0 until toolCallMessages.length) {
            val toolCallMessage = toolCallMessages[index]
            if (this.toolCalled.tsn_safeGet(toolCallMessage.id) == true) {
                toolCallCount = toolCallCount - 1
                return
            }
            this.toolCalled[toolCallMessage.id] = true
            this.mcpManager?.callTool(
                toolCallMessage.toolName,
                toolCallMessage.toolArgs,
                toolCallMessage.id,
                f0@{ result ->
                    val toolCallResult = MCPToolCallResult(JSONString = result)
                    if (toolCallResult.content.length <= 0) {
                        toolCallMessage._conversationManager = this
                        toolCallMessage.setMsgStatus(NuroToolCallMessageStatus.wait_user_response)
                    } else {
                        var toolMsgCopy = toolCallMessage.copy() as NuroToolCallMessage
                        toolMsgCopy.toolResult = result
                        toolMsgCopy.messageStatus = NuroToolCallMessageStatus.finished_successfully
                        _toolCallMessages[index] = toolMsgCopy
                        toolCallCount = toolCallCount - 1
                        if (toolCallCount <= 0) {
                            this.sendToolResultMessages(_toolCallMessages)
                        }
                    }
                },
            )
        }
    }
}