// bd574c1c9f0fb9a30ebc30771bc16c4b
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

enum class NuroConversationState(
    val value: String,
) {
    /**
     * The conversation is preparing to send the first message.
     * UI should show a loading indicator, should not allow user to send a message.
     */
    preparing("preparing"),
    /**
     * The conversation is streaming the response.
     * UI should show the response with streaming, should not allow user to send new message.
     * UI can display a pause button allow user to stop streaming.
     */
    streamingResponse("streamingResponse"),
    /**
     * The conversation is ready to send the first message.
     * UI should allow the user to send the first message.
     */
    readyToSendMessage("readyToSendMessage"),
}

fun NuroConversationState.valueOf(): String = this.value

class NuroConversation(
    conversationId: String,
) {
    lateinit var conversationId: String

    var systemPrompt: String? = null

    var conversationState: NuroConversationState = NuroConversationState.preparing

    var messages: Mu<PERSON>List<NuroMessage> = mutableListOf()

    var tasks: MutableList<NuroTask> = mutableListOf()

    var taskChecker: NuroTaskChecker? = null

    var summary: String? = null

    private var stateUpdateListeners: MutableMap<String, (NuroConversationState) -> Unit> = mutableMapOf()

    private var messageUpdateListeners: MutableMap<String, (NuroMessage, NuroMessageOp) -> Unit> = mutableMapOf()

    private var taskUpdateListeners: MutableMap<String, (MutableList<NuroTaskOut>) -> Unit> = mutableMapOf()

    var systemDataListeners: MutableMap<String, (SystemData) -> Unit> = mutableMapOf()

    init {
        this.conversationId = conversationId
    }

    fun addStateUpdateListener(listener: (NuroConversationState) -> Unit): String {
        val uuid = NuroUtils.randomUUIDString()
        this.stateUpdateListeners[uuid] = listener
        return uuid
    }

    fun removeStateUpdateListener(token: String) {
        this.stateUpdateListeners.remove(token)
    }

    fun removeAllStateUpdateListeners() {
        this.stateUpdateListeners = mutableMapOf()
    }

    fun addSystemDataListener(listener: (SystemData) -> Unit): String {
        val uuid = NuroUtils.randomUUIDString()
        this.systemDataListeners[uuid] = listener
        return uuid
    }

    fun removeSystemDataListener(token: String) {
        this.systemDataListeners.remove(token)
    }

    fun removeSystemDataListeners() {
        this.systemDataListeners = mutableMapOf()
    }

    fun addMessageUpdateListener(listener: (NuroMessage, NuroMessageOp) -> Unit): String {
        val uuid = NuroUtils.randomUUIDString()
        this.messageUpdateListeners[uuid] = listener
        return uuid
    }

    fun removeMessageUpdateListener(token: String) {
        this.messageUpdateListeners.remove(token)
    }

    fun removeAllMessageUpdateListeners() {
        this.messageUpdateListeners = mutableMapOf()
    }

    fun addTaskUpdateListener(listener: (MutableList<NuroTaskOut>) -> Unit): String {
        val uuid = NuroUtils.randomUUIDString()
        this.taskUpdateListeners[uuid] = listener
        return uuid
    }

    fun removeTaskUpdateListener(token: String) {
        this.taskUpdateListeners.remove(token)
    }

    fun removeAllTaskUpdateListeners() {
        this.taskUpdateListeners = mutableMapOf()
    }

    fun needResume(): Boolean {
        if (EventStreamAdapter.reconnectEndpoint == null) {
            return false
        }
        if (NuroSetting.version == "3.0.0") {
            // 3.0.0按任务来分
            var length = this.tasks.length
            if (length <= 0) {
                return false
            }
            var lastTask = this.tasks.tsn_safeGet(length - 1)
            return lastTask?.taskStatus == NuroTaskStatus.running
        } else {
            var length = this.messages.length
            if (length <= 0) {
                return false
            }
            var lastMessage = this.messages.tsn_safeGet(length - 1)
            return lastMessage?.needResume() ?: false
        }
    }

    fun updateState(state: NuroConversationState): Unit {
        NuroLogger.info(
            "NuroConversation",
            f0@{ "updateConversationState: state = $state, conversationId = ${this.conversationId}" },
        )
        this.conversationState = state
        TSNMapUtils.forEach(
            this.stateUpdateListeners,
            f0@{ key, listener ->
                listener(state)
            },
        )
    }

    fun updateMessage(
        message: NuroMessage,
        op: NuroMessageOp,
    ): Unit {
        NuroLogger.info(
            "NuroConversation",
            f0@{ "updateMessage: conversationId = ${this.conversationId}, msgType = ${message.type}, id = ${message.id}, op = $op" },
        )
        TSNMapUtils.forEach(
            this.messageUpdateListeners,
            f0@{ key, listener ->
                listener(message, op)
            },
        )
        var messageOut = NuroMessageOut(message, op)
        this.processTask(mutableListOf(messageOut), true)
    }

    fun processTask(
        msgOut: MutableList<NuroMessageOut>,
        needUpdate: Boolean = true,
    ): Unit {
        val taskChecker = this.taskChecker
        if (taskChecker != null) {
            val newTasks: MutableList<NuroTask> = mutableListOf()
            var currentTask: NuroTask? = null
            this.messages.forEach(
                f0@{ it, index ->
                    if (taskChecker.isArtifactMessage(it)) {
                        if (currentTask == null) {
                            val newTask = NuroTask(it.id)
                            currentTask = newTask
                            newTask.taskStatus = NuroTaskStatus.running
                            newTasks.push(newTask)
                        }
                        if (taskChecker.isShieldMessage(it)) {
                            currentTask?.addMessage(it, NuroTaskMessageType.shieldMessage)
                        } else {
                            currentTask?.addMessage(it, NuroTaskMessageType.artifactMessage)
                        }
                    } else {
                        run {

                            val _currentTask = currentTask
                            if (_currentTask != null && _currentTask.artifactMessages.length > 0) {
                                currentTask = null
                            }
                        }
                        if (currentTask == null) {
                            val newTask = NuroTask(it.id)
                            currentTask = newTask
                            newTask.taskStatus = NuroTaskStatus.running
                            newTasks.push(newTask)
                        }
                        run {

                            val _currentTask = currentTask
                            if (_currentTask != null) {
                                if (taskChecker.isPromptMessage(it) == true) {
                                    if (_currentTask.middlewareMessages.length <= 0) {
                                        _currentTask.addMessage(it, NuroTaskMessageType.promptMessage)
                                    } else {
                                        val newTask = NuroTask(it.id)
                                        newTask.taskStatus = NuroTaskStatus.running
                                        newTask.addMessage(it, NuroTaskMessageType.promptMessage)
                                        newTasks.push(newTask)
                                        currentTask = newTask
                                    }
                                } else {
                                    _currentTask.addMessage(it, NuroTaskMessageType.middlewareMessage)
                                }
                            }
                        }
                    }
                    run {

                        val _currentTask = currentTask
                        if (_currentTask != null) {
                            if (it.isCancelledStatus()) {
                                _currentTask.taskStatus = NuroTaskStatus.cancelled
                                currentTask = null
                            } else if (
                                it.isFailedStatus()
                            ) {
                                _currentTask.taskStatus = NuroTaskStatus.failed
                                currentTask = null
                            } else {
                                if (index == this.messages.length - 1) {
                                    if (it is NuroAssistantMessage) {
                                        if (it.isFinalTurn()) {
                                            _currentTask.taskStatus = NuroTaskStatus.finished
                                        }
                                    } else if (it is NuroToolCallMessage) {
                                        if ((it as NuroToolCallMessage)?.isClientToolSkipped()) {
                                            _currentTask.taskStatus = NuroTaskStatus.finished
                                        }
                                    }
                                }
                            }
                        }
                    }
                },
            )
            newTasks.forEach(
                f0@{ task ->
                    task.recalculateMessageHash()
                },
            )
            val newTasksLength = newTasks.length
            var changeTasks: MutableList<NuroTaskOut> = mutableListOf()
            for (i in 0 until newTasksLength) {
                if (i < this.tasks.length) {
                    if (newTasks[i].messageHash != this.tasks[i].messageHash) {
                        this.tasks[i] = newTasks[i]
                        if (needUpdate) {
                            var taskOut = NuroTaskOut(this.tasks[i], NuroTaskOp.update)
                            changeTasks.push(taskOut)
                        }
                    }
                } else {
                    this.tasks.push(newTasks[i])
                    if (needUpdate) {
                        var taskOut = NuroTaskOut(this.tasks[this.tasks.length - 1], NuroTaskOp.add)
                        changeTasks.push(taskOut)
                    }
                }
            }
            if (this.tasks.length > newTasksLength) {
                val tasksToRemoveCount = this.tasks.length - newTasksLength
                val removedTasks = this.tasks.splice(newTasksLength, tasksToRemoveCount)
                removedTasks.forEach(
                    f0@{ removedTask ->
                        if (needUpdate) {
                            var taskOut = NuroTaskOut(removedTask, NuroTaskOp.delete)
                            changeTasks.push(taskOut)
                        }
                    },
                )
            }
            changeTasks.forEach(
                f0@{ changeTask ->
                    changeTask.updateMessage(msgOut)
                },
            )
            TSNMapUtils.forEach(
                this.taskUpdateListeners,
                f0@{ key, listener ->
                    listener(changeTasks)
                },
            )
        }
    }

    fun findToolCallMessageByToolCallId(toolCallId: String): NuroToolCallMessage? {
        var result: NuroToolCallMessage? = null
        this.messages.forEach(
            f0@{ it ->
                if (it is NuroToolCallMessage) {
                    if (it.toolCallId == toolCallId) {
                        result = it
                    }
                }
            },
        )
        return result
    }

    fun findLastOpenNuroCanvasMessage(messageList: MutableList<NuroMessage>?): NuroCanvasMessage? {
        if (messageList == null) {
            TSNConsole.log(" message list is empty! ")
            return null
        }
        var result: NuroCanvasMessage? = null
        var index = messageList.length - 1
        while (index >= 0) {
            if (messageList.tsn_safeGet(index) is NuroCanvasMessage) {
                var message = messageList.tsn_safeGet(index) as NuroCanvasMessage
                if (message.isCanvasOpen()) {
                    result = message
                    break
                }
                break
            }
            index--
        }
        return result
    }

    fun findLOpenNuroCanvasMessage(messageList: MutableList<NuroMessage>?): MutableList<NuroCanvasMessage>? {
        if (messageList == null) {
            TSNConsole.log(" message list is empty! ")
            return null
        }
        var result: MutableList<NuroCanvasMessage>? = null
        var index = messageList.length - 1
        while (index >= 0) {
            if (messageList.tsn_safeGet(index) is NuroCanvasMessage) {
                var message = messageList.tsn_safeGet(index) as NuroCanvasMessage
                if (message.isCanvasOpen()) {
                    if (result == null) {
                        result = mutableListOf()
                    }
                    result.push(message)
                }
            }
            index--
        }
        return result
    }

    fun findOpenNuroCanvasMessageById(
        canvasMsgId: String,
        messageList: MutableList<NuroMessage>?,
    ): NuroCanvasMessage? {
        if (messageList == null) {
            TSNConsole.log(" message list is empty! ")
            return null
        }
        var result: NuroCanvasMessage? = null
        var index = this.messages.length - 1
        while (index >= 0) {
            if (this.messages.tsn_safeGet(index) is NuroCanvasMessage) {
                var message = this.messages.tsn_safeGet(index) as NuroCanvasMessage
                if (message.id == canvasMsgId) {
                    result = message
                    break
                }
            }
            index--
        }
        return result
    }

    fun findLastUserMessage(): NuroUserMessage? {
        var length = this.messages.length
        run {
            var i = length - 1
            var __first__ = true
            while (i >= 0) {
                if (!__first__) {
                    i = i - 1
                }
                __first__ = false
                if (i >= 0) {
                    var msg = this.messages.tsn_safeGet(i)
                    if (msg is NuroUserMessage) {
                        return msg
                    }
                } else {
                    break
                }
            }
        }
        return null
    }
}