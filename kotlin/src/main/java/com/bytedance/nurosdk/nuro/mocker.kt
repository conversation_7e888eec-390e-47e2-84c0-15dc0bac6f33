// 7673d5ecb503b5589945cd6ce576adc0
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

interface INuroMocker {
    fun checkMock(
        userMessage: NuroUserMessage,
        manager: NuroConversationManager,
    ): <PERSON><PERSON><PERSON>
}

object NuroMockManager {
    private var mocker: INuroMocker? = null

    fun setMocker(mocker: INuroMocker?) {
        NuroMockManager.mocker = mocker
    }

    fun checkMock(
        userMessage: NuroUserMessage,
        manager: NuroConversationManager,
    ): Boolean = NuroMockManager.mocker?.checkMock(userMessage, manager) ?: false

    fun isMocking(): <PERSON><PERSON>an = NuroMockManager.mocker != null
}