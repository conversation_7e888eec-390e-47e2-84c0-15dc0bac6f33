// 82830d76e487552206e47099287b6db3
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

enum class ConvertType(
    val value: String,
) {
    new_message("new_message"),
    history("history"),
}

fun ConvertType.valueOf(): String = this.value

object MessageProcessor {
    fun convertChatMessageToNuroMessage(
        conversationManager: NuroConversationManager?,
        chatMessage: ChatMessage,
        type: ConvertType,
    ): MutableList<NuroMessage> {
        val is_visually_hidden_from_conversation = chatMessage.metadata?.is_visually_hidden_from_conversation
        if (is_visually_hidden_from_conversation != null && is_visually_hidden_from_conversation == true) {
            // Server 端要求该消息不展示在会话中，所以不返回任何消息。
            return mutableListOf()
        }
        val role = chatMessage.author?.role
        val name = chatMessage.author?.name
        var msgs: MutableList<NuroMessage> = mutableListOf()
        if (role != null) {
            if (role == "user") {
                var text = ""
                var files: MutableList<NuroFile> = mutableListOf()
                var referenceInfo: MutableList<RefContent> = mutableListOf()
                chatMessage.content?.content_parts?.forEach(
                    f0@{ it ->
                        run {

                            val _it_text = it?.text
                            if (_it_text != null && _it_text.length > 0) {
                                if (it.is_referenced == true) {
                                    referenceInfo.push(text = _it_text)
                                } else {
                                    text = text + _it_text.trim()
                                }
                            }
                        }
                        run {

                            val _it_file = it?.file
                            if (_it_file != null) {
                                var file = NuroFile(NuroFileType.image, _it_file.url, null)
                                file.uri = _it_file.uri
                                run {

                                    val __it_file_image_metadata = it?.file?.image_metadata
                                    if (__it_file_image_metadata != null) {
                                        var fileImageMetadata = NuroImageMetadata()
                                        fileImageMetadata.width = __it_file_image_metadata.image_width
                                        fileImageMetadata.height = __it_file_image_metadata.image_height
                                        fileImageMetadata.prompt = __it_file_image_metadata.image_prompt
                                        fileImageMetadata.format = __it_file_image_metadata.image_format
                                        var fileMetadata = NuroFileMetadata(fileImageMetadata)
                                        file.metadata = fileMetadata
                                    }
                                }
                                if (it.is_referenced == true) {
                                    referenceInfo.push(file = file)
                                } else {
                                    files.push(file)
                                }
                            }
                        }
                    },
                )
                var msg = NuroUserMessage(chatMessage.id ?: "", text, if (files.length > 0) files else null, null, if (referenceInfo.length > 0) referenceInfo else null)
                msg._conversationManager = conversationManager
                msg.createTime = chatMessage.create_time ?: 0
                msg._rawId = chatMessage.id ?: ""
                msg._messageIndex = msgs.length
                if (chatMessage.status == ChatMessageStatus.in_progress) {
                    msg.messageStatus = NuroUserMessageStatus.sending
                } else {
                    msg.messageStatus = NuroUserMessageStatus.finished_successfully
                }
                run {

                    val _chatMessage_metadata = chatMessage?.metadata
                    if (_chatMessage_metadata != null) {
                        msg.metadata = _chatMessage_metadata
                    }
                }
                msgs.push(msg)
            } else if (role == "assistant") {
                var reasoning = ""
                var text = ""
                var files: MutableList<NuroFile> = mutableListOf()
                chatMessage.content?.content_parts?.forEach(
                    f0@{ it ->
                        run {

                            val _it_reasoning_content = it?.reasoning_content
                            if (_it_reasoning_content != null && _it_reasoning_content.length > 0) {
                                reasoning = reasoning + _it_reasoning_content
                            }
                        }
                        run {

                            val _it_text = it?.text
                            if (_it_text != null && _it_text.length > 0) {
                                text = text + _it_text.trim()
                            }
                        }
                        run {

                            val _it_file = it?.file
                            if (_it_file != null) {
                                var file = NuroFile(NuroFileType.image, _it_file.url, null)
                                file.uri = _it_file.uri
                                val __it_file_image_metadata = it?.file?.image_metadata
                                if (__it_file_image_metadata != null) {
                                    var fileImageMetadata = NuroImageMetadata()
                                    fileImageMetadata.width = __it_file_image_metadata.image_width
                                    fileImageMetadata.height = __it_file_image_metadata.image_height
                                    fileImageMetadata.prompt = __it_file_image_metadata.image_prompt
                                    fileImageMetadata.format = __it_file_image_metadata.image_format
                                    var fileMetadata = NuroFileMetadata(fileImageMetadata)
                                    file.metadata = fileMetadata
                                }
                                files.push(file)
                            }
                        }
                    },
                )
                if (reasoning.length > 0) {
                    var reasoningMsg = NuroReasoningMessage((chatMessage.id ?: "") + "_reasoning", reasoning)
                    reasoningMsg._conversationManager = conversationManager
                    reasoningMsg.createTime = chatMessage.create_time ?: 0
                    reasoningMsg._rawId = chatMessage.id ?: ""
                    reasoningMsg._messageIndex = msgs.length
                    if (text.length > 0 || files.length > 0) {
                        reasoningMsg.messageStatus = NuroReasoningMessageStatus.finished_successfully
                    } else {
                        reasoningMsg.setStatus(chatMessage.status)
                    }
                    run {

                        val _chatMessage_metadata = chatMessage?.metadata
                        if (_chatMessage_metadata != null) {
                            reasoningMsg.metadata = ChatMessageMetadata(JSONString = _chatMessage_metadata.toJSONString() ?: "{}")
                        }
                    }
                    msgs.push(reasoningMsg)
                }
                var assistantMsg: NuroAssistantMessage? = null
                var isLastTurnMsg = false
                if (chatMessage.end_turn == true) {
                    isLastTurnMsg = true
                    var canvasMsgList = conversationManager?.conversation?.findLOpenNuroCanvasMessage(msgs) ?: conversationManager?.conversation?.findLOpenNuroCanvasMessage(conversationManager?.conversation?.messages)
                    val _canvasMsgList = canvasMsgList
                    if (_canvasMsgList != null) {
                        _canvasMsgList.forEach(
                            f0@{ it ->
                                it.setMsgStatus(NuroCanvasStatus.end)
                                msgs.push(it)
                            },
                        )
                    }
                }
                if (text.length > 0 || files.length > 0 || isLastTurnMsg == true) {
                    var msg = NuroAssistantMessage((chatMessage.id ?: "") + "_assistant", name, text, if (files.length > 0) files else null)
                    msg._conversationManager = conversationManager
                    msg.createTime = chatMessage.create_time ?: 0
                    msg._rawId = chatMessage.id ?: ""
                    msg._messageIndex = msgs.length
                    msg.setStatus(chatMessage.status)
                    run {

                        val _chatMessage_metadata = chatMessage?.metadata
                        if (_chatMessage_metadata != null) {
                            msg.metadata = ChatMessageMetadata(JSONString = _chatMessage_metadata.toJSONString() ?: "{}")
                        }
                    }
                    if (isLastTurnMsg) {
                        msg.endTurn = true
                    }
                    assistantMsg = msg
                    msgs.push(msg)
                }
                chatMessage.tool_calls?.forEach(
                    f0@{ it ->
                        var itFunc = it._func
                        if (itFunc == null) {
                            return@f0
                        }
                        if (it.type == ChatToolCallType.server_function && NuroSetting.needDisplayServerFunctionMessage == false) {
                            return@f0
                        }
                        var jsonrepairSrc = itFunc.arguments ?: ""
                        var jsonrepairResult = NuroUtils.jsonrepair(jsonrepairSrc)
                        if (jsonrepairResult == null) {
                            jsonrepairResult = itFunc._lastRepairedArguments ?: jsonrepairSrc
                        } else if (jsonrepairResult == "") {
                            jsonrepairResult = itFunc._lastRepairedArguments ?: jsonrepairSrc
                        } else {
                            itFunc._lastRepairedArguments = jsonrepairResult
                        }
                        var toolName = itFunc.name ?: ""
                        // 先统一转成toolCallMessage
                        var toolCall = NuroToolCallMessage((chatMessage.id ?: "") + "_toolcall_" + (it.id ?: ""), it.id ?: "", it.type?.valueOf() ?: ChatToolCallType.client_function.valueOf(), toolName, jsonrepairResult, itFunc.extra, "")
                        toolCall._conversationManager = conversationManager
                        toolCall.createTime = chatMessage.create_time ?: 0
                        toolCall._rawId = chatMessage.id ?: ""
                        toolCall._messageIndex = msgs.length
                        if (type == ConvertType.new_message) {
                            toolCall.messageStatus = NuroToolCallMessageStatus.invoking
                            if (it.streaming == true) {
                                if (chatMessage.status == ChatMessageStatus.send_content_stop_status) {
                                    toolCall.messageStatus = NuroToolCallMessageStatus.streaming_failed
                                } else {
                                    toolCall.messageStatus = NuroToolCallMessageStatus.streaming
                                }
                            }
                        } else {
                            toolCall.messageStatus = NuroToolCallMessageStatus.skipped
                        }
                        if (toolName.indexOf(NuroSetting.canvasSettings.startNode) >= 0) {
                            // 如果收到一个打开画布的指令，就新建一个画布消息 等到streaming=false的时候，才能新建,否则流失输出会收到多条消息
                            var canvasMsgId = (chatMessage.id ?: "") + "_canvas"
                            var existCanvasMsg = conversationManager?.conversation?.findOpenNuroCanvasMessageById(canvasMsgId, msgs) ?: conversationManager?.conversation?.findOpenNuroCanvasMessageById(canvasMsgId, conversationManager?.conversation?.messages)
                            var msg: NuroCanvasMessage? = null
                            if (existCanvasMsg == null) {
                                msg = NuroCanvasMessage(canvasMsgId)
                                msg._conversationManager = conversationManager
                                msg.createTime = chatMessage.create_time ?: 0
                                msg._rawId = chatMessage.id ?: ""
                                msg._messageIndex = msgs.length
                            } else {
                                msg = existCanvasMsg.copy()
                            }
                            run {

                                val _chatMessage_metadata = chatMessage?.metadata
                                if (_chatMessage_metadata != null) {
                                    msg.metadata = ChatMessageMetadata(JSONString = _chatMessage_metadata.toJSONString() ?: "{}")
                                }
                            }
                            if (type == ConvertType.history) {
                                msg.status = NuroCanvasStatus.streaming
                            } else {
                                msg.updateCanvasStatus(NuroCanvasStatus.streaming)
                            }
                            msg.updateStartNode(toolCall)
                            msgs.push(msg)
                        } else if (toolName.indexOf(NuroSetting.canvasSettings.endNode) >= 0) {
                            // 如果收到一个关闭画布的指令，找到最近的NuroCanvasMessage,设置它的status为end
                            var msg = conversationManager?.conversation?.findLastOpenNuroCanvasMessage(msgs) ?: conversationManager?.conversation?.findLastOpenNuroCanvasMessage(conversationManager?.conversation?.messages)
                            val _msg = msg
                            if (_msg != null) {
                                var update_msg = _msg.copy() as NuroCanvasMessage
                                _msg.endNode = toolCall
                                update__msg.updateEndNode(toolCall)
                                _msgs.push(update__msg)
                            }
                        } else if (
                            NuroSetting.canvasSettings.nodes.some(
                                f1@{ node ->
                                    node.indexOf(toolName) >= 0
                                },
                            )
                        ) {
                            // 如果收到一个插入内容的指令，找到最近的NuroCanvasMessage,执行插入Node操作
                            var msg = conversationManager?.conversation?.findLastOpenNuroCanvasMessage(msgs) ?: conversationManager?.conversation?.findLastOpenNuroCanvasMessage(conversationManager?.conversation?.messages)
                            val _msg = msg
                            if (_msg != null) {
                                var update_msg = _msg.copy() as NuroCanvasMessage
                                var nodeIndex = CANVAS_DEFAULT
                                run {

                                    val _itFunc_arguments = itFunc?.arguments
                                    if (_itFunc_arguments != null) {
                                        if (NuroUtils.isJSONValid(_itFunc_arguments)) {
                                            nodeIndex = JSON.parse(_itFunc_arguments).canvas_content_id ?: CANVAS_DEFAULT
                                        } else {
                                            // 有可能被转义影响到了，处理一下, 临时修复逻辑，单独起一个分支处理，减少影响, 后面随着canvas的升级干掉
                                            var repairResult = NuroUtils.completeQuotes(_itFunc_arguments) ?: ""
                                            if (NuroUtils.isJSONValid(repairResult)) {
                                                nodeIndex = JSON.parse(_itFunc_arguments).canvas_content_id ?: CANVAS_DEFAULT
                                            }
                                        }
                                    }
                                }
                                if (nodeIndex != CANVAS_DEFAULT) {
                                    val targetNode = getNodeFromNuroToolCallMsg(toolCall.id, nodeIndex, toolCall)
                                    update__msg.addOrReplaceNode(targetNode)
                                    _msgs.push(update__msg)
                                } else {
                                    if (type == ConvertType.history) {
                                        val node = getNodeFromNuroToolCallMsg(toolCall.id, CANVAS_ADD_TO_END, toolCall)
                                        update__msg.addOrReplaceNode(node)
                                        _msgs.push(update__msg)
                                    }
                                }
                            } else {
                                assistantMsg?.relateToolCalls?.push(toolCall)
                                msgs.push(toolCall)
                            }
                        } else {
                            assistantMsg?.relateToolCalls?.push(toolCall)
                            msgs.push(toolCall)
                        }
                    },
                )
            } else if (role == "tool") {
                var content_parts = chatMessage.content?.content_parts ?: mutableListOf()
                var toolCallId = chatMessage.metadata?.tool_call_id
                var canvasMessageList = conversationManager?.conversation?.findLOpenNuroCanvasMessage(msgs) ?: conversationManager?.conversation?.findLOpenNuroCanvasMessage(conversationManager?.conversation?.messages)
                var len = content_parts.length
                if (len > 0) {
                    val _toolCallId = toolCallId
                    if (_toolCallId != null) {
                        var part = content_parts.tsn_safeGet(0)
                        val _part = part
                        if (_part != null) {
                            var toolResult: String? = _part.text
                            // 找到对应的工具调用消息，更新它的结果
                            var updateCanvasMsg: NuroCanvasMessage? = null
                            run {

                                val _canvasMessageList = canvasMessageList
                                if (_canvasMessageList != null) {
                                    _canvasMessageList.forEach(
                                        f0@{ lastCanvasMsg ->
                                            var endNode = lastCanvasMsg.findEndNode(_toolCallId ?: "")
                                            var startNode = lastCanvasMsg.findStartNode(_toolCallId ?: "")
                                            val _endNode = endNode
                                            val _startNode = startNode
                                            if (_endNode != null) {
                                                updateCanvasMsg = lastCanvasMsg.copy() as NuroCanvasMessage
                                                run {

                                                    val _toolResult = toolResult
                                                    if (_toolResult != null) {
                                                        _endNode.toolResult = _toolResult
                                                    }
                                                }
                                                updateCanvasMsg.updateEndNode(_endNode.copy())
                                                if (chatMessage.status == ChatMessageStatus.finished_successfully) {
                                                    if (type == ConvertType.history) {
                                                        updateCanvasMsg.status = NuroCanvasStatus.end
                                                    } else {
                                                        updateCanvasMsg.finish()
                                                    }
                                                }
                                                msgs.push(updateCanvasMsg)
                                            } else if (_startNode != null) {
                                                updateCanvasMsg = lastCanvasMsg.copy() as NuroCanvasMessage
                                                val _toolResult = toolResult
                                                if (_toolResult != null) {
                                                    _startNode.toolResult = _toolResult
                                                }
                                                updateCanvasMsg.updateStartNode(_startNode.copy())
                                                msgs.push(updateCanvasMsg)
                                            } else {
                                                var node = lastCanvasMsg.findNodeByToolCallId(_toolCallId ?: "")
                                                val _node = node
                                                if (_node != null) {
                                                    updateCanvasMsg = lastCanvasMsg.copy() as NuroCanvasMessage
                                                    var targetNode = _node.copy() as NuroCanvasNode
                                                    run {

                                                        val _toolResult = toolResult
                                                        if (_toolResult != null && _toolResult.trim().indexOf("{") != 0) {
                                                            // 不是一个合法的 JSON，兼容处理成 mcp text
                                                            var r = MCPToolCallResult()
                                                            r.content = mutableListOf(MCPToolCallTextContent.create(_toolResult ?: ""))
                                                            targetNode.toolResult = r.toJSONString()
                                                        } else {
                                                            val firstJson = this.extractFirstJsonObject(toolResult ?: "")
                                                            targetNode.toolResult = firstJson ?: ""
                                                        }
                                                    }
                                                    if (chatMessage.status == ChatMessageStatus.in_progress) {
                                                        targetNode.messageStatus = NuroToolCallMessageStatus.invoking
                                                    } else {
                                                        targetNode.messageStatus = NuroToolCallMessageStatus.finished_successfully
                                                    }
                                                    updateCanvasMsg.addOrReplaceNode(targetNode)
                                                    msgs.push(updateCanvasMsg)
                                                } else {
                                                    TSNConsole.log("[NuroCanvasMsgToolCall] cannot find node with _toolCallId " + _toolCallId)
                                                }
                                            }
                                        },
                                    )
                                }
                            }
                            if (updateCanvasMsg == null) {
                                var toolMsg = conversationManager?.conversation?.findToolCallMessageByToolCallId(_toolCallId)
                                val _toolMsg = toolMsg
                                if (_toolMsg != null && _toolMsg is NuroToolCallMessage) {
                                    val _toolMsgCopy = _toolMsg.copy() as NuroToolCallMessage
                                    run {

                                        val _toolResult = toolResult
                                        if (_toolResult != null && _toolResult.trim().indexOf("{") != 0) {
                                            // 不是一个合法的 JSON，兼容处理成 mcp text
                                            var r = MCPToolCallResult()
                                            r.content = mutableListOf(MCPToolCallTextContent.create(_toolResult))
                                            _toolMsgCopy.toolResult = r.toJSONString()
                                        } else {
                                            _toolMsgCopy.toolResult = toolResult
                                        }
                                    }
                                    if (chatMessage.status == ChatMessageStatus.in_progress) {
                                        _toolMsgCopy.messageStatus = NuroToolCallMessageStatus.invoking
                                    } else {
                                        _toolMsgCopy.messageStatus = NuroToolCallMessageStatus.finished_successfully
                                    }
                                    msgs.push(_toolMsgCopy)
                                }
                            }
                        }
                    }
                }
            }
        }
        return msgs
    }

    fun markMessagesAsFinished(conversationManager: NuroConversationManager?): Unit {
        if (conversationManager == null) {
            return
        }
        conversationManager.conversation.messages.forEach(
            f0@{ it ->
                if (it.isFinalStatus() == false) {
                    if (it is NuroReasoningMessage) {
                        it.setMsgStatus(NuroReasoningMessageStatus.finished_successfully)
                    }
                    if (it is NuroAssistantMessage) {
                        it.setMsgStatus(NuroAssistantMessageStatus.finished_successfully)
                    }
                }
            },
        )
    }

    fun markLastUserMessageAsFinished(conversationManager: NuroConversationManager?): Unit {
        if (conversationManager == null) {
            return
        }
        conversationManager.conversation.messages.forEach(
            f0@{ it ->
                if (it is NuroUserMessage) {
                    if (it.isFinalStatus() == false) {
                        it.setMsgStatus(NuroUserMessageStatus.finished_successfully)
                    }
                }
            },
        )
    }

    fun markInProgressMessagesAsFailed(conversationManager: NuroConversationManager?): Unit {
        if (conversationManager == null) {
            return
        }
        var errorMsg = ChatMessageError.send_failed.valueOf()
        conversationManager.conversation.messages.forEach(
            f0@{ it ->
                if (it.isFinalStatus() == false) {
                    it.errorMsg = errorMsg
                    if (it is NuroUserMessage) {
                        it.setMsgStatus(NuroUserMessageStatus.failed)
                    }
                    if (it is NuroReasoningMessage) {
                        it.setMsgStatus(NuroReasoningMessageStatus.failed)
                    }
                    if (it is NuroAssistantMessage) {
                        it.setMsgStatus(NuroAssistantMessageStatus.failed)
                    }
                    if (it is NuroToolCallMessage) {
                        if (it.messageStatus == NuroToolCallMessageStatus.streaming) {
                            it.setMsgStatus(NuroToolCallMessageStatus.streaming_failed)
                        } else {
                            it.setMsgStatus(NuroToolCallMessageStatus.skipped)
                        }
                    }
                }
            },
        )
    }

    fun markInProgressMessagesAsCancel(conversationManager: NuroConversationManager?): Unit {
        if (conversationManager == null) {
            return
        }
        conversationManager.conversation.messages.forEach(
            f0@{ it ->
                if (it.isFinalStatus() == false) {
                    if (it is NuroUserMessage) {
                        it.setMsgStatus(NuroUserMessageStatus.cancelled)
                    }
                    if (it is NuroReasoningMessage) {
                        it.setMsgStatus(NuroReasoningMessageStatus.cancelled)
                    }
                    if (it is NuroAssistantMessage) {
                        it.setMsgStatus(NuroAssistantMessageStatus.cancelled)
                    }
                    if (it is NuroToolCallMessage) {
                        if (it.messageStatus == NuroToolCallMessageStatus.streaming || it.messageStatus == NuroToolCallMessageStatus.streaming_cancelled || it.messageStatus == NuroToolCallMessageStatus.wait_user_response) {
                            it.setMsgStatus(NuroToolCallMessageStatus.streaming_cancelled)
                        } else {
                            it.setMsgStatus(NuroToolCallMessageStatus.skipped)
                        }
                    }
                    if (it is NuroCanvasMessage) {
                        it.setMsgStatus(NuroCanvasStatus.cancel)
                    }
                }
            },
        )
    }

    fun isUserMessage(message: NuroMessage): Boolean = message is NuroUserMessage

    fun isToolCallMessage(message: NuroMessage): Boolean = message is NuroToolCallMessage

    fun extractFirstJsonObject(jsonString: String): String {
        var balance = 0
        var startIndex = -1
        for (i in 0 until jsonString.length) {
            if (jsonString.charAt(i) == "{") {
                if (balance == 0) {
                    startIndex = i
                }
                balance++
            } else if (jsonString.charAt(i) == "}") {
                balance--
                if (balance == 0 && startIndex != -1) {
                    return jsonString.substring(startIndex, i + 1)
                }
            }
        }
        return "" // 没有找到完整的 JSON 对象
    }
}