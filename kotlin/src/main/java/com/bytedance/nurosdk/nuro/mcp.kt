// aec8f65e595c65c73b74ce82f75563bc
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

interface NuroMCPClientAdapter {
    fun listTools(callback: (MutableList<NuroMCPToolItem>) -> Unit): Unit

    fun callTool(
        toolCallId: String,
        toolName: String,
        toolArgs: String?,
        callback: (String) -> Unit,
    ): Unit
}

class NuroMCPServerConfig(
    name: String,
    adapter: NuroMCPClientAdapter,
) {
    lateinit var name: String

    lateinit var adapter: NuroMCPClientAdapter

    init {
        this.name = name
        this.adapter = adapter
    }
}

class NuroMCPToolItem(
    serverName: String,
    name: String,
    description: String,
    inputSchema: String,
) {
    /**
     * 服务名称
     */
    lateinit var serverName: String

    /**
     * 工具名称
     */
    lateinit var name: String

    /**
     * 工具描述
     */
    lateinit var description: String

    /**
     * JSON Schema
     */
    lateinit var inputSchema: String

    init {
        this.serverName = serverName
        this.name = name
        this.description = description
        this.inputSchema = inputSchema
    }
}

class NuroMCPManager {
    private var servers: MutableMap<String, NuroMCPServerConfig> = mutableMapOf()

    private var toolsServerMapping: MutableMap<String, String> = mutableMapOf()

    fun registerServer(config: NuroMCPServerConfig): Unit {
        this.servers[config.name] = config
    }

    fun getAllTools(callback: (MutableList<NuroMCPToolItem>) -> Unit): Unit {
        val allTools: MutableList<NuroMCPToolItem> = mutableListOf()
        var taskCount = TSNMapUtils.size(this.servers)
        if (taskCount == 0) {
            callback(allTools)
            return
        }
        TSNMapUtils.forEach(
            this.servers,
            f0@{ serverName, server ->
                server.adapter.listTools(
                    f1@{ tools ->
                        tools.forEach(
                            f1@{ tool ->
                                allTools.push(tool)
                                this.toolsServerMapping[serverName + "_" + tool.name] = serverName
                            },
                        )
                        taskCount = taskCount - 1
                        if (taskCount == 0) {
                            TSNConsole.log(allTools)
                            callback(allTools)
                        }
                    },
                )
            },
        )
    }

    fun callTool(
        functionNameWithPrefix: String,
        functionCallArguments: String?,
        functionCallId: String,
        callback: (String) -> Unit,
    ): Unit {
        val serverName = this.toolsServerMapping.tsn_safeGet(functionNameWithPrefix)
        if (serverName == null) {
            return
        }
        val server = this.servers.tsn_safeGet(serverName)
        if (server == null) {
            return
        }
        server.adapter.callTool(
            functionCallId,
            functionNameWithPrefix.substr(serverName.length + 1),
            functionCallArguments,
            f0@{ result ->
                callback(result)
            },
        )
    }
}