package com.bytedance.nurosdk.mcp

import com.bytedance.nurosdk.MCPToolCallResult
import com.bytedance.nurosdk.MCPToolDefineProperty
import com.bytedance.nurosdk.NuroMCPClientAdapter
import com.bytedance.nurosdk.NuroMCPServerConfig
import com.bytedance.nurosdk.NuroMCPToolItem

import org.json.JSONObject

class MCPLiteServer(
    val name: String,
    val version: String
) : NuroMCPClientAdapter {

    private val tools: MutableMap<String, MCPLiteTool> = mutableMapOf()

    /**
     * Define a tool
     */
    fun tool(
        name: String,
        description: String,
        inputSchema: MCPToolDefineProperty?,
        toolCallHandler: MCPLiteTool.ToolCallHandler
    ) {
        tools[name] = MCPLiteTool(name, description, inputSchema, toolCallHandler)
    }

    override fun listTools(callback: (MutableList<NuroMCPToolItem>) -> Unit) {
        callback(tools.values.map { tool ->
            NuroMCPToolItem(
                this.name,
                tool.name,
                tool.description,
                tool.inputSchema?.toJSONString() ?: "{}"
            )
        }.toMutableList())
    }

    override fun callTool(
        toolCallId: String,
        toolName: String,
        toolArgs: String?,
        callback: (String) -> Unit
    ) {
        val tool = tools[toolName]
        if (tool != null) {
            var params: Any? = null
            try {
                if (toolArgs != null) {
                    params = JSONObject(toolArgs)
                }
            } catch (_: Exception) {
            }
            tool.toolCallHandler.invoke(params) { result ->
                val resultString = result.toJSONString()
                if (resultString != null) {
                    callback(resultString)
                } else {
                    callback("{}")
                }
            }
        }
    }

    fun asNuroMCPServerConfig(): NuroMCPServerConfig {
        return NuroMCPServerConfig(this.name, this)
    }
}

class MCPLiteTool(
    val name: String,
    val description: String,
    val inputSchema: MCPToolDefineProperty?,
    val toolCallHandler: ToolCallHandler
) {
    fun interface ToolCallHandler {
        fun invoke(params: Any?, resultCallback: (MCPToolCallResult) -> Unit)
    }
}