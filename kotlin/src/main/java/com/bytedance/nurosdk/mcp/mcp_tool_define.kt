// e6d64305c35056bef193bd20aab871e0
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

class MCPToolDefine(
    name: String,
    description: String,
    inputSchema: MCPToolDefineObjectProperty?,
) {
    lateinit var name: String

    lateinit var description: String

    var inputSchema: MCPToolDefineObjectProperty? = null

    init {
        this.name = name
        this.description = description
        this.inputSchema = inputSchema
    }
}

open class MCPToolDefineProperty : TSNSerializable {
    open var type: String = ""

    open var title: String? = null

    open var description: String? = null

    open var deprecated: Boolean? = null

    open fun defTitle(value: String): MCPToolDefineProperty {
        this.title = value
        return this
    }

    open fun defDescription(value: String): MCPToolDefineProperty {
        this.description = value
        return this
    }

    open fun defDeprecated(value: Boolean): MCPToolDefineProperty {
        this.deprecated = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.type = TSNJSONObject.getString(map, "type") ?: ""
        this.title = TSNJSONObject.getString(map, "title") ?: null
        this.description = TSNJSONObject.getString(map, "description") ?: null
        this.deprecated = TSNJSONObject.getBool(map, "deprecated") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["type"] = this.type
        this.encodeData["title"] = this.title
        this.encodeData["description"] = this.description
        this.encodeData["deprecated"] = this.deprecated
    }
}

class MCPToolDefineObjectProperty : MCPToolDefineProperty {
    var properties: MutableMap<String, MCPToolDefineProperty> = mutableMapOf()

    var required: MutableList<String> = mutableListOf()

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "object"
    }

    public override fun defDescription(value: String): MCPToolDefineObjectProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineObjectProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineObjectProperty {
        this.deprecated = value
        return this
    }

    public fun defProperty(
        name: String,
        value: MCPToolDefineProperty,
    ): MCPToolDefineObjectProperty {
        this.properties[name] = value
        return this
    }

    public fun defRequired(required: MutableList<String>): MCPToolDefineObjectProperty {
        this.required = required
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.properties = (this.optMapList(1, map.opt("properties"), { return@optMapList (it as? TSNJSONMap)?.let { MCPToolDefineProperty(JSONObject = TSNJSONObject(it)) } }) as? MutableMap<String, MCPToolDefineProperty>) ?: mutableMapOf()
        this.required = (this.optMapList(1, map.opt("required"), { return@optMapList it as? String }) as? MutableList<String>) ?: mutableListOf()
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["properties"] = this.properties
        this.encodeData["required"] = this.required
    }
}

class MCPToolDefineStringProperty : MCPToolDefineProperty {
    var _default: String? = null

    var examples: MutableList<String>? = null

    var _enum: MutableList<String>? = null

    var _const: String? = null

    var minLength: Int? = null

    var maxLength: Int? = null

    var pattern: String? = null

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "string"
    }

    public override fun defDescription(value: String): MCPToolDefineStringProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineStringProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineStringProperty {
        this.deprecated = value
        return this
    }

    public fun defDefault(value: String): MCPToolDefineStringProperty {
        this._default = value
        return this
    }

    public fun defExamples(values: MutableList<String>): MCPToolDefineStringProperty {
        this.examples = values
        return this
    }

    public fun defEnum(values: MutableList<String>): MCPToolDefineStringProperty {
        this._enum = values
        return this
    }

    public fun defConst(value: String): MCPToolDefineStringProperty {
        this._const = value
        return this
    }

    public fun defMinLength(value: Int): MCPToolDefineStringProperty {
        this.minLength = value
        return this
    }

    public fun defMaxLength(value: Int): MCPToolDefineStringProperty {
        this.maxLength = value
        return this
    }

    public fun defPattern(value: String): MCPToolDefineStringProperty {
        this.pattern = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this._default = TSNJSONObject.getString(map, "default") ?: null
        this.examples = (this.optMapList(1, map.opt("examples"), { return@optMapList it as? String }) as? MutableList<String>?)
        this._enum = (this.optMapList(1, map.opt("enum"), { return@optMapList it as? String }) as? MutableList<String>?)
        this._const = TSNJSONObject.getString(map, "const") ?: null
        this.minLength = TSNJSONObject.getInt(map, "minLength") ?: null
        this.maxLength = TSNJSONObject.getInt(map, "maxLength") ?: null
        this.pattern = TSNJSONObject.getString(map, "pattern") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["default"] = this._default
        this.encodeData["examples"] = this.examples
        this.encodeData["enum"] = this._enum
        this.encodeData["const"] = this._const
        this.encodeData["minLength"] = this.minLength
        this.encodeData["maxLength"] = this.maxLength
        this.encodeData["pattern"] = this.pattern
    }
}

class MCPToolDefineIntegerProperty : MCPToolDefineProperty {
    var _default: Int? = null

    var examples: MutableList<Int>? = null

    var _enum: MutableList<Int>? = null

    var _const: Int? = null

    var minimum: Int? = null

    var maximum: Int? = null

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "integer"
    }

    public override fun defDescription(value: String): MCPToolDefineIntegerProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineIntegerProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineIntegerProperty {
        this.deprecated = value
        return this
    }

    public fun defDefault(value: Int): MCPToolDefineIntegerProperty {
        this._default = value
        return this
    }

    public fun defExamples(values: MutableList<Int>): MCPToolDefineIntegerProperty {
        this.examples = values
        return this
    }

    public fun defEnum(values: MutableList<Int>): MCPToolDefineIntegerProperty {
        this._enum = values
        return this
    }

    public fun defConst(value: Int): MCPToolDefineIntegerProperty {
        this._const = value
        return this
    }

    public fun defMinimum(value: Int): MCPToolDefineIntegerProperty {
        this.minimum = value
        return this
    }

    public fun defMaximum(value: Int): MCPToolDefineIntegerProperty {
        this.maximum = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this._default = TSNJSONObject.getInt(map, "default") ?: null
        this.examples = (this.optMapList(1, map.opt("examples"), { return@optMapList (it as? Number)?.toInt() }) as? MutableList<Int>?)
        this._enum = (this.optMapList(1, map.opt("enum"), { return@optMapList (it as? Number)?.toInt() }) as? MutableList<Int>?)
        this._const = TSNJSONObject.getInt(map, "const") ?: null
        this.minimum = TSNJSONObject.getInt(map, "minimum") ?: null
        this.maximum = TSNJSONObject.getInt(map, "maximum") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["default"] = this._default
        this.encodeData["examples"] = this.examples
        this.encodeData["enum"] = this._enum
        this.encodeData["const"] = this._const
        this.encodeData["minimum"] = this.minimum
        this.encodeData["maximum"] = this.maximum
    }
}

class MCPToolDefineNumberProperty : MCPToolDefineProperty {
    var _default: Double? = null

    var examples: MutableList<Double>? = null

    var _enum: MutableList<Double>? = null

    var _const: Double? = null

    var minimum: Double? = null

    var maximum: Double? = null

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "number"
    }

    public override fun defDescription(value: String): MCPToolDefineNumberProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineNumberProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineNumberProperty {
        this.deprecated = value
        return this
    }

    public fun defDefault(value: Double): MCPToolDefineNumberProperty {
        this._default = value
        return this
    }

    public fun defExamples(values: MutableList<Double>): MCPToolDefineNumberProperty {
        this.examples = values
        return this
    }

    public fun defEnum(values: MutableList<Double>): MCPToolDefineNumberProperty {
        this._enum = values
        return this
    }

    public fun defConst(value: Double): MCPToolDefineNumberProperty {
        this._const = value
        return this
    }

    public fun defMinimum(value: Double): MCPToolDefineNumberProperty {
        this.minimum = value
        return this
    }

    public fun defMaximum(value: Double): MCPToolDefineNumberProperty {
        this.maximum = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this._default = TSNJSONObject.getDouble(map, "default") ?: null
        this.examples = (this.optMapList(1, map.opt("examples"), { return@optMapList (it as? Number)?.toDouble() }) as? MutableList<Double>?)
        this._enum = (this.optMapList(1, map.opt("enum"), { return@optMapList (it as? Number)?.toDouble() }) as? MutableList<Double>?)
        this._const = TSNJSONObject.getDouble(map, "const") ?: null
        this.minimum = TSNJSONObject.getDouble(map, "minimum") ?: null
        this.maximum = TSNJSONObject.getDouble(map, "maximum") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["default"] = this._default
        this.encodeData["examples"] = this.examples
        this.encodeData["enum"] = this._enum
        this.encodeData["const"] = this._const
        this.encodeData["minimum"] = this.minimum
        this.encodeData["maximum"] = this.maximum
    }
}

class MCPToolDefineBooleanProperty : MCPToolDefineProperty {
    var _default: Boolean? = null

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "boolean"
    }

    public override fun defDescription(value: String): MCPToolDefineBooleanProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineBooleanProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineBooleanProperty {
        this.deprecated = value
        return this
    }

    fun defDefault(value: Boolean): MCPToolDefineBooleanProperty {
        this._default = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this._default = TSNJSONObject.getBool(map, "default") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["default"] = this._default
    }
}

class MCPToolDefineArrayProperty : MCPToolDefineProperty {
    var items: MCPToolDefineProperty? = null

    var _enum: MutableList<Any>? = null

    var maxItems: Int? = null

    var minItems: Int? = null

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "array"
    }

    public override fun defDescription(value: String): MCPToolDefineArrayProperty {
        this.description = value
        return this
    }

    public override fun defTitle(value: String): MCPToolDefineArrayProperty {
        this.title = value
        return this
    }

    public override fun defDeprecated(value: Boolean): MCPToolDefineArrayProperty {
        this.deprecated = value
        return this
    }

    public fun defItems(value: MCPToolDefineProperty): MCPToolDefineArrayProperty {
        this.items = value
        return this
    }

    public fun defEnum(values: MutableList<Any>): MCPToolDefineArrayProperty {
        this._enum = values
        return this
    }

    public fun defMaxItems(value: Int): MCPToolDefineArrayProperty {
        this.maxItems = value
        return this
    }

    public fun defMinItems(value: Int): MCPToolDefineArrayProperty {
        this.minItems = value
        return this
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.items = if (map.has("items")) MCPToolDefineProperty(JSONObject = TSNJSONObject(map.optJSONObject("items"))) else null
        this._enum = (this.optMapList(1, map.opt("enum"), { TSNJSONObject.getPlainValue(it) }) as? MutableList<Any>?)
        this.maxItems = TSNJSONObject.getInt(map, "maxItems") ?: null
        this.minItems = TSNJSONObject.getInt(map, "minItems") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["items"] = this.items
        this.encodeData["enum"] = this._enum
        this.encodeData["maxItems"] = this.maxItems
        this.encodeData["minItems"] = this.minItems
    }
}

fun transformToZodSchema(
    Zod: Any,
    value: MCPToolDefineProperty?,
    isRoot: Boolean = true,
): MutableMap<String, Any> {
    if (value == null) {
        return mutableMapOf()
    }
    return mutableMapOf()
}