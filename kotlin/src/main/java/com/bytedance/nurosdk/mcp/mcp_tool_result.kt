// 96a106bfbe6bd5dd1870412e8adc3f45
// This file is generated by tsn.
package com.bytedance.nurosdk
import byted.tsn.foundation.*

open class MCPToolCallContent : TSNSerializable {
    open var type: String = "text"
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.type = TSNJSONObject.getString(map, "type") ?: ""
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["type"] = this.type
    }
}

class MCPToolCallResource : MCPToolCallContent {
    var uri: String? = null

    var name: String? = null

    var text: String? = null

    var mimeType: String? = null
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.uri = TSNJSONObject.getString(map, "uri") ?: null
        this.name = TSNJSONObject.getString(map, "name") ?: null
        this.text = TSNJSONObject.getString(map, "text") ?: null
        this.mimeType = TSNJSONObject.getString(map, "mimeType") ?: null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["uri"] = this.uri
        this.encodeData["name"] = this.name
        this.encodeData["text"] = this.text
        this.encodeData["mimeType"] = this.mimeType
    }
}

class MCPToolCallResourceContent : MCPToolCallContent {
    companion object {
        fun create(
            uri: String,
            name: String,
            text: String?,
            mimeType: String?,
        ): MCPToolCallResourceContent {
            val content = MCPToolCallResourceContent()
            val resource = MCPToolCallResource()
            resource.uri = uri
            resource.name = name
            resource.text = text
            resource.mimeType = mimeType
            content.resource = resource
            return content
        }
    }

    var resource: MCPToolCallResource? = MCPToolCallResource()

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "resource"
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.resource = if (map.has("resource")) MCPToolCallResource(JSONObject = TSNJSONObject(map.optJSONObject("resource"))) else null
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["resource"] = this.resource
    }
}

class MCPToolCallResult : TSNSerializable {
    var content: MutableList<MCPToolCallContent> = mutableListOf()

    override fun afterParse(): Unit {
        this.content =
            this.content
                .map(
                    f0@{ it ->
                        if (it.type == "text") {
                            return@f0 MCPToolCallTextContent(JSONObject = it.rawData)
                        } else if (it.type == "resource") {
                            return@f0 MCPToolCallResourceContent(JSONObject = it.rawData)
                        } else {
                            return@f0 it
                        }
                    },
                ).toMutableList()
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.content = (this.optMapList(1, map.opt("content"), { return@optMapList (it as? TSNJSONMap)?.let { MCPToolCallContent(JSONObject = TSNJSONObject(it)) } }) as? MutableList<MCPToolCallContent>) ?: mutableListOf()
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["content"] = this.content
    }
}

class MCPToolCallTextContent : MCPToolCallContent {
    companion object {
        fun create(text: String): MCPToolCallTextContent {
            val content = MCPToolCallTextContent()
            content.text = text
            return content
        }
    }

    var text: String = ""

    override fun afterInit(): Unit {
        super.afterInit()
        this.type = "text"
    }
    constructor() : super() {
        doInit(false)
    }
    constructor(JSONString: String) : super(JSONString) {
        doInit(true)
    }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) {
        doInit(true)
    }

    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.text = TSNJSONObject.getString(map, "text") ?: ""
    }

    override fun unmapping() {
        super.unmapping()
        this.encodeData["text"] = this.text
    }
}