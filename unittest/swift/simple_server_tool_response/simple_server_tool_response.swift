//2a22ea2cd3b787e8c682e43316e36b94
// This file is generated by tsn.
import TSNFoundation
import NuroSDK
import ObjectMapper

class SimpleServerToolWeatherArgs: TSNSerializable {

    var city: String? = nil

    var date: String? = nil

    override func mapping(map: Map) {
        super.mapping(map: map)
        city <- map["city"]
        date <- map["date"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == SimpleServerToolWeatherArgs.self { afterParse();deferInitAndParse() }
    }

}

public class SimpleServerToolResponse: TestCase {

    public init() {
        super.init("simple_server_tool_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleServerToolEndpoint()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"))
        let lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(conversationManager.conversation)
        guard let lastAssistantMessage = lastAssistantMessage else {
            self.reject("The last message should be assistant message")
            return
        }
        if lastAssistantMessage.text != "广州今天的天气是下雪。" {
            self.reject("The last message content should be '广州今天的天气是下雪。'")
            return
        }
        let lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
        guard let lastToolCallMessage = lastToolCallMessage else {
            self.reject("The last message should be tool call message")
            return
        }
        let toolArgs = lastToolCallMessage.toolArgs
        if let _toolArgs = toolArgs {
            let _toolArgsObject = SimpleServerToolWeatherArgs(JSONString: _toolArgs)
            if _toolArgsObject.city != "广州" {
                self.reject("The tool args city should be '广州'")
                return
            }
            if _toolArgsObject.date != "今天" {
                self.reject("The tool args date should be '今天'")
                return
            }
        }
        else {
            self.reject("The tool args should not be undefined")
            return
        }
        let toolResult = lastToolCallMessage.decodeToolCallResultToMCPFormat()
        let contentFirst = toolResult.content.tsn_safeGet(0)
        if let contentFirst = contentFirst as? MCPToolCallTextContent {
            if contentFirst.text != "The 广州 weather is snowy in 今天." {
                self.reject("The tool result content should be 'The 广州 weather is snowy in 今天.'")
                return
            }
        }
        else {
            self.reject("The tool result content should be MCPToolCallTextContent")
            return
        }
        self.resolve()
    }

}
