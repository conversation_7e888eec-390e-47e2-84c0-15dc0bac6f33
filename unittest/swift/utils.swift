//7c11973e86fd1add6d558f01c27677b4
// This file is generated by tsn.
import TSNFoundation
import NuroSDK

public class FailedTestInfo {

    public var functionName: String

    public var errorMessage: String

    public init(_ functionName: String, _ errorMessage: String) {
        self.functionName = functionName
        self.errorMessage = errorMessage
    }

}

public class SuccessTestInfo {

    public var functionName: String

    public init(_ functionName: String) {
        self.functionName = functionName
    }

}

public class TestContext {

    static public var globalContext = TestContext()

    private var failedTests: [FailedTestInfo] = []

    private var succeededTests: [SuccessTestInfo] = []

    public func reportFailure(_ functionName: String, _ errorMessage: String) -> Void {
        self.failedTests.push(FailedTestInfo(functionName, errorMessage))
    }

    public func reportSuccess(_ functionName: String) -> Void {
        self.succeededTests.push(SuccessTestInfo(functionName))
    }

    public func getFailedTests() -> [FailedTestInfo] {
        return self.failedTests
    }

    public func getSucceededTests() -> [SuccessTestInfo] {
        return self.succeededTests
    }

    public init() {}

}

public class TestCase {

    public var name: String

    public var resolve: () -> Void = { () in
        
    }

    public var reject: (String) -> Void = { (_) in
        
    }

    public init(_ name: String) {
        self.name = name
    }

    public func run() -> Void {
    
    }

    public func delay(_ seconds: Double, _ cb: @escaping () -> Void) -> Void {
        TSNTimer(seconds, cb)
    }

}

public class TestDevice {

    static public var testMCPManager: NuroMCPManager? = nil

    private var definedTests: [TestCase] = []

    public func defineTest(_ item: TestCase) -> Void {
        self.definedTests.push(item)
    }

    public func runNextTest(_ onFinish: @escaping () -> Void) -> Void {
        let currentTest = self.definedTests.shift()
        if let _currentTest = currentTest {
            _currentTest.resolve = { () in
                TestContext.globalContext.reportSuccess(_currentTest.name)
                self.runNextTest(onFinish)
            }
            _currentTest.reject = { (errorMessage) in
                TestContext.globalContext.reportFailure(_currentTest.name, errorMessage)
                self.runNextTest(onFinish)
            }
            _currentTest.run()
        }
        else {
            onFinish()
        }
    }

    public init() {}

}

public class TestChunkedServer {

    static public var singleton = TestChunkedServer()

    public var chunksParts: [[String]] = []

    public var _pausedConfig: EventStreamConfig? = nil

    public func installNuroSDK() -> Void {
        NuroSetting.version = "3.0.0"
        NuroSetting.canvasSettings = [
            "startNode": "canvas_open_new_canvas", 
            "endNode": "close_canvas", 
            "nodes": [
            "creative_agent_mcp_gen_text2image_v3", 
            "creative_agent_mcp_gen_image2image_v3", 
            "creative_agent_mcp_gen_image2video_v3", 
            "creative_agent_mcp_gen_text2video_v3", 
            "creative_agent_mcp_gen_start_end2video_v3"
        ]
        ]
        EventStreamAdapter.fetch = { (config: EventStreamConfig) in
            self.start(config)
            return ""
        }
    }

    public func setText(_ text: String) -> Void {
        self.installNuroSDK()
        var chunks: [String] = []
        var remainingText = text
        while remainingText.length > 0 {
            let chunkSize = TSNNumberConverter.toInt(Math.floor(Math.random() * TSNNumberConverter.toDouble(remainingText.length)) + 1)
            chunks.push(remainingText.substring(0, chunkSize))
            remainingText = remainingText.substring(chunkSize, remainingText.length)
        }
        self.chunksParts.push(chunks)
    }

    public func setTexts(_ texts: [String]) -> Void {
        for index in 0..<texts.length {
            let element = texts[index]
            self.setText(element)
        }
    }

    public func start(_ config: EventStreamConfig, _ fromResume: Bool = false) -> Void {
        var currentChunks = self.chunksParts.shift()
        guard let currentChunks = currentChunks else {
            return
        }
        if let _config_onStart = config.onStart, fromResume != true {
            _config_onStart()
        }
        for index in 0..<currentChunks.length {
            let element = currentChunks[index]
            if let _config_onChunk = config.onChunk {
                _config_onChunk(element)
            }
        }
        if currentChunks.join("").indexOf("stream_complete") < 0 {
            self._pausedConfig = config
            return
        }
        if let _config_onFinish = config.onFinish {
            _config_onFinish()
        }
    }

    public func resume() -> Void {
        if let _this__pausedConfig = self._pausedConfig {
            self.start(_this__pausedConfig, true)
            self._pausedConfig = nil
        }
    }

    public init() {}

}

public class TestConversationUtils {

    static public func findLastAssistantMessage(_ conversation: NuroConversation?) -> NuroAssistantMessage? {
        var messages = conversation?.messages
        guard let messages = messages else {
            return nil
        }
        var lastAssistantMessage: NuroAssistantMessage? = nil
        do {
          var index = messages.length - 1
          var __first__ = true
          while index >= 0 {
            if !__first__ {
              index = index - 1
            }
            __first__ = false
            if index >= 0 {
                  let element = messages[index]
            if let element = element as? NuroAssistantMessage {
                lastAssistantMessage = element
            }
            }
            else {
              break
            }
          }
        }
        return lastAssistantMessage
    }

    static public func findLastToolCallMessage(_ conversation: NuroConversation?) -> NuroToolCallMessage? {
        var messages = conversation?.messages
        guard let messages = messages else {
            return nil
        }
        var lastToolCallMessage: NuroToolCallMessage? = nil
        do {
          var index = messages.length - 1
          var __first__ = true
          while index >= 0 {
            if !__first__ {
              index = index - 1
            }
            __first__ = false
            if index >= 0 {
                  let element = messages[index]
            if let element = element as? NuroToolCallMessage {
                lastToolCallMessage = element
            }
            }
            else {
              break
            }
          }
        }
        return lastToolCallMessage
    }

    static public func findLastTask(_ conversation: NuroConversation?) -> NuroTask? {
        var tasks = conversation?.tasks
        guard let tasks = tasks else {
            return nil
        }
        var lastTask: NuroTask? = nil
        do {
          var index = tasks.length - 1
          var __first__ = true
          while index >= 0 {
            if !__first__ {
              index = index - 1
            }
            __first__ = false
            if index >= 0 {
                  let element = tasks[index]
            lastTask = element
            }
            else {
              break
            }
          }
        }
        return lastTask
    }

    static public func findLastCanvasMessage(_ conversation: NuroConversation?) -> NuroCanvasMessage? {
        var tasks = conversation?.tasks
        guard let tasks = tasks else {
            return nil
        }
        // Nuro 3.0只有一个turn
            var lastTask = tasks.tsn_safeGet(0)
        var resultMessage: NuroCanvasMessage? = nil
        lastTask?.artifactMessages.forEach({ (artifactMessage) in
            if let artifactMessage = artifactMessage as? NuroCanvasMessage {
                resultMessage = artifactMessage
            }
        })
        return resultMessage
    }

    public init() {}

}
