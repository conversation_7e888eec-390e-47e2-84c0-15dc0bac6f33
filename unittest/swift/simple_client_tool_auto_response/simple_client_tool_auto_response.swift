//0082b16434aa9bb9c13b7f1acb033b3f
// This file is generated by tsn.
import TSNFoundation
import NuroSDK

public class SimpleClientToolAutoResponse: TestCase {

    public init() {
        super.init("simple_client_tool_auto_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleClientToolAutoEndpoint()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        self.delay(1.0, { () in
            let lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
            guard let lastToolCallMessage = lastToolCallMessage else {
                self.reject("The last message should be tool call message")
                return
            }
            if lastToolCallMessage.messageStatus != NuroToolCallMessageStatus.finished_successfully {
                self.reject("The last message status should be finished successfully")
                return
            }
            if lastToolCallMessage.decodeToolCallResultAsPlainText() != "佛山市" {
                self.reject("The last message tool result should be '佛山市'")
                return
            }
            self.resolve()
        })
    }

}
