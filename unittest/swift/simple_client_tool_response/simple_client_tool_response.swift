//7f3aa4a457aec01c76f0e3667e1dd125
// This file is generated by tsn.
import TSNFoundation
import NuroSDK
import ObjectMapper

class SimpleClientToolStruct: TSNSerializable {

    var reason: String? = nil

    override func mapping(map: Map) {
        super.mapping(map: map)
        reason <- map["reason"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == SimpleClientToolStruct.self { afterParse();deferInitAndParse() }
    }

}

public class SimpleClientToolResponse: TestCase {

    public init() {
        super.init("simple_client_tool_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleClientToolEndpoint()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        self.delay(1.0, { () in
            let lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
            guard let lastToolCallMessage = lastToolCallMessage else {
                self.reject("The last message should be tool call message")
                return
            }
            let toolArgsObject = SimpleClientToolStruct(JSONString: lastToolCallMessage.toolArgs ?? "{}")
            if toolArgsObject.reason != "确定用户当前所在位置" {
                self.reject("The last message reason should be '确定用户当前所在位置'")
                return
            }
            let tcMCPFormat = MCPToolCallResult()
            tcMCPFormat.content = [MCPToolCallTextContent.create("广州市")]
            lastToolCallMessage.sendToolCallResultFromMCPFormat(tcMCPFormat)
            let lastToolCallMessage2 = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
            guard let lastToolCallMessage2 = lastToolCallMessage2 else {
                self.reject("The last message should be tool call message")
                return
            }
            if lastToolCallMessage2.messageStatus != NuroToolCallMessageStatus.finished_successfully {
                self.reject("The last message status should be finished successfully")
                return
            }
            if lastToolCallMessage2.decodeToolCallResultAsPlainText() != "广州市" {
                self.reject("The last message tool result should be '广州市'")
                return
            }
            self.resolve()
        })
    }

}
