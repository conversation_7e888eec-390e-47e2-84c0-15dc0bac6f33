//8971ecbc44a8f72d824c5f2e6836f67f
// This file is generated by tsn.
import TSNFoundation

public func runTests(_ finishCallback: @escaping (TestDevice) -> Void) -> Void {
    let testDevice = TestDevice()
    testDevice.defineTest(SimpleAssistantResponse())
    testDevice.defineTest(SimpleServerToolResponse())
    testDevice.defineTest(SimpleClientToolResponse())
    testDevice.defineTest(SimpleClientToolAutoResponse())
    testDevice.defineTest(SimpleTaskResponse())
    testDevice.defineTest(ConversationManagerResponse())
    testDevice.defineTest(SimpleJsonRepairResponse())
    testDevice.runNextTest({ () in
        var failedTests = TestContext.globalContext.getFailedTests()
        var succeededTests = TestContext.globalContext.getSucceededTests()
        if failedTests.length > 0 {
            TSNConsole.log("Test failed:")
            failedTests.forEach({ (test) in
                TSNConsole.log("Test \(test.functionName) failed: \(test.errorMessage)")
            })
        }
        else {
            TSNConsole.log("All tests passed!")
        }
        finishCallback(testDevice)
    })
}
