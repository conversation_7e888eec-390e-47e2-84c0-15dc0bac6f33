//4ce05af1a6dd25788507a3a51edff218
// This file is generated by tsn.
import TSNFoundation
import NuroSDK

public class SimpleJsonRepairResponse: TestCase {

    public init() {
        super.init("simple_task_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleJsonRepairServer()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.conversation.taskChecker = NuroTaskChecker()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"))
        let lastTask = TestConversationUtils.findLastTask(conversationManager.conversation)
        guard let lastTask = lastTask else {
            self.reject("Last task is undefined")
            return
        }
        let promptMessage = lastTask.promptMessages.tsn_safeGet(0)
        guard let promptMessage = promptMessage else {
            self.reject("The prompt message is undefined, the task should have prompt message")
            return
        }
        if promptMessage is NuroUserMessage == false {
            self.reject("The prompt message is not a NuroUserMessage, the task should have prompt message")
            return
        }
        let toolMessage = lastTask.middlewareMessages.tsn_safeGet(0)
        if NuroSetting.version != "3.0.0" {
            guard let toolMessage = toolMessage else {
                self.reject("The middlewareMessages tool message is undefined, the task should have tool message")
                return
            }
        }
        TestChunkedServer.singleton.resume()
        let lastTask2 = TestConversationUtils.findLastTask(conversationManager.conversation)
        guard let lastTask2 = lastTask2 else {
            self.reject("Last task is undefined")
            return
        }
        let lastIndex = lastTask2.artifactMessages.length - 1
        let artifactMessage = lastTask2.artifactMessages.tsn_safeGet(lastIndex < 0 ? 0 : lastIndex)
        guard let artifactMessage = artifactMessage else {
            self.reject("The artifactMessages tool message is undefined, the task should have artifact message")
            return
        }
        if artifactMessage is NuroAssistantMessage == false {
            self.reject("The artifactMessages tool message is not a NuroAssistantMessageStatus, the task should have artifact message")
            return
        }
        lastTask2.artifactMessages.forEach({ (message, index) in
            if let message = message as? NuroToolCallMessage {
                TSNConsole.log("message is " + (message as! NuroToolCallMessage).toolArgs)
            }
        })
        self.resolve()
    }

}
