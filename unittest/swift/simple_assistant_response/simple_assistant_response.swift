//48e58e829b666954d85fec63f7c8a406
// This file is generated by tsn.
import TSNFoundation
import NuroSDK

public class SimpleAssistantResponse: TestCase {

    public init() {
        super.init("simple_assistant_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleAssistantServer()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "你好"))
        let lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(conversationManager.conversation)
        guard let lastAssistantMessage = lastAssistantMessage else {
            self.reject("The last message should be assistant message")
            return
        }
        if lastAssistantMessage.text != "你好！有什么可以帮您的吗？😊" {
            self.reject("The last message content should be '你好！有什么可以帮您的吗？😊'")
            return
        }
        if lastAssistantMessage.id != "oqoj15qm9e_assistant" {
            self.reject("The last message id should be 'oqoj15qm9e_assistant'")
            return
        }
        if lastAssistantMessage.messageStatus != NuroAssistantMessageStatus.finished_successfully {
            self.reject("The last message status should be finished_successfully")
            return
        }
        self.resolve()
    }

}
