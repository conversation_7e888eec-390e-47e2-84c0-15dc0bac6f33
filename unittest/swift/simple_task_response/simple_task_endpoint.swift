//9c9eec8fab018479b09aafd613795200
// This file is generated by tsn.
import TSNFoundation

public class SimpleTaskServer {

    public var res_0 = """
    id:6bv80kpv9dh
    event:message
    data: {"author":{"role":"assistant"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849","parent_message_id":""},"status":"in_progress","id":"7i44woc6vf","create_time":0}
    
    id:7kyo8euj5ai
    event:delta
    data: {"op":"add","path":"/message/tool_calls/0","value":"{\\"id\\":\\"call_hig0u4dzh6e04ruge88um09u\\",\\"type\\":\\"server_function\\",\\"streaming\\":true,\\"func\\":{\\"name\\":\\"weather_mock_get_weather_7e03a363\\",\\"arguments\\":\\"\\"}}"}
    
    id:4nr496c27q3
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"{\\""}
    
    id:2g6n3tm37vq
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"city"}
    
    id:bwmy136gjwo
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\":\\""}
    
    id:f13d62fq0oa
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"广州"}
    
    id:zrhkwp8x3i
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\",\\""}
    
    id:1zyj3sqoy71
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"date"}
    
    id:gprkiuenttr
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\":\\""}
    
    id:gkjezljbwdc
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"202"}
    
    id:ot5ghgurun9
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"3"}
    
    id:i1zkk6kjpkg
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"-"}
    
    id:0fzu7yiofm78
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"11"}
    
    id:wzx39dr5ws
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"-"}
    
    id:qonhx0jv9an
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"01"}
    
    id:yydp5bszz99
    event:delta
    data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\"}"}
    
    id:xiiij2mmi4e
    event:delta
    data: {"op":"replace","path":"/message/tool_calls/0","value":"{\\"id\\":\\"call_hig0u4dzh6e04ruge88um09u\\",\\"type\\":\\"server_function\\",\\"streaming\\":false,\\"func\\":{\\"name\\":\\"weather_mock_get_weather_7e03a363\\",\\"arguments\\":\\"{\\\\\\"city\\\\\\":\\\\\\"广州\\\\\\",\\\\\\"date\\\\\\":\\\\\\"2023-11-01\\\\\\"}\\"}}"}
    
    
    """

    public var res_1 = """
    id:iu6pn0nq3zn
    event:message
    data: {"author":{"role":"tool"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849","parent_message_id":"","tool_call_id":"call_hig0u4dzh6e04ruge88um09u"},"status":"finished_successfully","id":"tool_result_call_hig0u4dzh6e04ruge88um09u","content":{"content_type":"text","content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"The 广州 weather is sunny in 2023-11-01.\\"}]}"}]},"create_time":0}
    
    id:4kekhybiyx5
    event:message
    data: {"author":{"role":"assistant"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849","parent_message_id":""},"status":"in_progress","id":"i257rknhdqd","create_time":0}
    
    id:r4l96op31ob
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"广州"}
    
    id:nc0eblotl2
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"今天的"}
    
    id:u0q59kz9wwp
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"天气"}
    
    id:xq795ns5wd
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"是"}
    
    id:o85kdcw2bv
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"晴天"}
    
    id:k92kvbkj23j
    event:delta
    data: {"op":"append","path":"/message/content/content_parts/0/text","value":"。"}
    
    id:74bzp8imb6k
    event:delta
    data: {"op":"replace","path":"/message/status","value":"finished_successfully"}
    
    id:rldmj2i15xm
    event:system
    data: {"type":"stream_complete","conversation_id":"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849"}
    
    {"ret":"0","logid":"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849"}
    
    
    """

    public func injectRes0() -> Void {
        TestChunkedServer.singleton.setTexts([self.res_0, self.res_1])
    }

    public init() {}

}
