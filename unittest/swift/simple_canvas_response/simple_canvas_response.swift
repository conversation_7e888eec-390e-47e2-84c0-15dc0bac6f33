//992f3f4ea82ec41d4bb119da2ba68cbc
// This file is generated by tsn.
import TSNFoundation
import NuroSDK

public class SimpleCanvasResponse: TestCase {

    public init() {
        super.init("simple_client_tool_auto_response")
    }

    override public func run() -> Void {
        // Setup
            let server = SimpleCanvasEndPoint()
        server.injectRes0()
        // Run
            let conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", nil))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        self.delay(1.0, { () in
            let lastTask = TestConversationUtils.findLastTask(conversationManager.conversation)
            guard let lastTask = lastTask else {
                self.reject("Last task is undefined")
                return
            }
            if lastTask.artifactMessages.length == 0 {
                self.reject("Last task artifact messages is empty")
                return
            }
            if lastTask.artifactMessages.length != 5 {
                self.reject("Last task artifact messages number wrong, should be 5 but current number is " + lastTask.artifactMessages.length)
                return
            }
            if lastTask.taskStatus != NuroTaskStatus.finished {
                self.reject("Last task artifact messages task status wrong, should be finished but current task status is " + lastTask.taskStatus)
                return
            }
            self.resolve()
        })
    }

}
