import {
  MCPToolCallT<PERSON>t<PERSON>ontent,
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroSetting,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { SimpleServerToolEndpoint } from "./simple_server_tool_endpoint";
import { TestCase, TestConversationUtils } from "../utils";
import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

class SimpleServerToolWeatherArgs extends TSNSerializable {
  @TSNJSON("city") city?: string = undefined;
  @TSNJSON("date") date?: string = undefined;
}

export class SimpleServerToolResponse extends TestCase {
  constructor() {
    super("simple_server_tool_response");
  }

  run(): void {
    // Setup
    const server = new SimpleServerToolEndpoint();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));
    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"),
    );
    const lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(
      conversationManager.conversation,
    );
    if (lastAssistantMessage === undefined) {
      this.reject("The last message should be assistant message");
      return;
    }
    if (lastAssistantMessage.text !== "广州今天的天气是下雪。") {
      this.reject(
        "The last message content should be '广州今天的天气是下雪。'",
      );
      return;
    }
    const lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(
      conversationManager.conversation,
    );
    if (lastToolCallMessage === undefined) {
      this.reject("The last message should be tool call message");
      return;
    }
    const toolArgs = lastToolCallMessage.toolArgs;
    if (toolArgs !== undefined) {
      const toolArgsObject = new SimpleServerToolWeatherArgs({
        JSONString: toolArgs,
      });
      if (toolArgsObject.city !== "广州") {
        this.reject("The tool args city should be '广州'");
        return;
      }
      if (toolArgsObject.date !== "今天") {
        this.reject("The tool args date should be '今天'");
        return;
      }
    } else {
      this.reject("The tool args should not be undefined");
      return;
    }
    const toolResult = lastToolCallMessage.decodeToolCallResultToMCPFormat();
    const contentFirst = toolResult.content[0];
    if (contentFirst instanceof MCPToolCallTextContent) {
      if (contentFirst.text !== "The 广州 weather is snowy in 今天.") {
        this.reject(
          "The tool result content should be 'The 广州 weather is snowy in 今天.'",
        );
        return;
      }
    } else {
      this.reject("The tool result content should be MCPToolCallTextContent");
      return;
    }
    this.resolve();
  }
}
