import {
  NuroMCPClientAdapterImpl,
  NuroMCPManager,
  NuroSetting,
} from "@byted/nurosdk";
import { IS_JS } from "@byted/tsnfoundation";
const { McpServer } = require("@modelcontextprotocol/sdk/server/mcp.js");
const { z } = require("zod");
import { TestDevice } from "./utils";

export function installDevice(): void {
  NuroSetting.needDisplayServerFunctionMessage = true;
  const mcpManager = new NuroMCPManager();
  if (IS_JS) {
    const localServer = new McpServer({ name: "local", version: "1.0.0" });
    localServer.tool(
      "get_location",
      "获取用户当前位置",
      { reason: z.string().describe("为什么需要获取地理位置，理由是什么。") },
      async (args: any) => {
        if (args.reason === "不需要用户确认") {
          return { content: [{ type: "text", text: "佛山市" }] };
        }
        return { content: [] }; // 留空是因为我们需要通过自定义工具界面，请求用户授权获取用户的地理位置信息。
      },
    );
    mcpManager.registerServer({
      name: "local",
      adapter: new NuroMCPClientAdapterImpl("local", localServer),
    });
  }
  TestDevice.testMCPManager = mcpManager;
}

if (IS_JS) {
  installDevice();
}
