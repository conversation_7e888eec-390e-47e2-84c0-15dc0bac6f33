import {
  MCPToolCallResult,
  MCPToolCallTextContent,
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroSetting,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { TestCase, TestConversationUtils, TestDevice } from "../utils";
import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";
import { SimpleClientToolEndpoint } from "./simple_client_tool_endpoint";

class SimpleClientToolStruct extends TSNSerializable {
  @TSNJSON("reason") reason?: string = undefined;
}

export class SimpleClientToolResponse extends TestCase {
  constructor() {
    super("simple_client_tool_response");
  }

  run(): void {
    // Setup
    const server = new SimpleClientToolEndpoint();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));

    conversationManager.mcpManager = TestDevice.testMCPManager;
    conversationManager.enableMCPTools();
    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"),
    );

    this.delay(1.0, () => {
      const lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(
        conversationManager.conversation,
      );
      if (lastToolCallMessage === undefined) {
        this.reject("The last message should be tool call message");
        return;
      }
      const toolArgsObject = new SimpleClientToolStruct({
        JSONString: lastToolCallMessage.toolArgs ?? "{}",
      });
      if (toolArgsObject.reason !== "确定用户当前所在位置") {
        this.reject("The last message reason should be '确定用户当前所在位置'");
        return;
      }

      const tcMCPFormat = new MCPToolCallResult();
      tcMCPFormat.content = [MCPToolCallTextContent.create("广州市")];
      lastToolCallMessage.sendToolCallResultFromMCPFormat(tcMCPFormat);

      const lastToolCallMessage2 =
        TestConversationUtils.findLastToolCallMessage(
          conversationManager.conversation,
        );

      if (lastToolCallMessage2 === undefined) {
        this.reject("The last message should be tool call message");
        return;
      }

      if (
        lastToolCallMessage2.messageStatus !==
        NuroToolCallMessageStatus.finished_successfully
      ) {
        this.reject("The last message status should be finished successfully");
        return;
      }
      if (lastToolCallMessage2.decodeToolCallResultAsPlainText() !== "广州市") {
        this.reject("The last message tool result should be '广州市'");
        return;
      }

      this.resolve();
    });
  }
}
