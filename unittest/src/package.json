{"name": "@byted/nurosdk-unittest", "version": "1.0.0", "description": "", "main": "runTests.js", "scripts": {"build": "tsnc && tsc && npm run format", "dev": "tsc -w", "format": "prettier --ignore-path .gitignore --write \"**/*.+(js|ts|json)\"", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@byted/tsncompiler": "0.2.19", "@byted/tsnfoundation": ">=0.2.7", "@byted/nurosdk": "file:../../js", "@modelcontextprotocol/sdk": "^1.10.2", "fetch-event-stream": "^0.1.5", "typescript": "4.9.4", "zod": "^3.24.2"}, "devDependencies": {"prettier": "^3.5.1"}}