import {
  NuroAssistantMessage,
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroSetting,
  NuroTaskChecker,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { SimpleTaskServer } from "./simple_task_endpoint";
import { TestCase, TestChunkedServer, TestConversationUtils } from "../utils";

export class SimpleTaskResponse extends TestCase {
  constructor() {
    super("simple_task_response");
  }

  run(): void {
    // Setup
    const server = new SimpleTaskServer();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));

    conversationManager.conversation.taskChecker = new NuroTaskChecker();

    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"),
    );

    const lastTask = TestConversationUtils.findLastTask(
      conversationManager.conversation,
    );
    if (lastTask === undefined) {
      this.reject("Last task is undefined");
      return;
    }
    const promptMessage = lastTask.promptMessages[0];
    if (promptMessage === undefined) {
      this.reject(
        "The prompt message is undefined, the task should have prompt message",
      );
      return;
    }
    if (promptMessage instanceof NuroUserMessage === false) {
      this.reject(
        "The prompt message is not a NuroUserMessage, the task should have prompt message",
      );
      return;
    }

    const toolMessage = lastTask.middlewareMessages[0];
    if (NuroSetting.version !== "3.0.0") {
      if (toolMessage === undefined) {
        this.reject(
          "The middlewareMessages tool message is undefined, the task should have tool message",
        );
        return;
      }
    }

    TestChunkedServer.singleton.resume();

    const lastTask2 = TestConversationUtils.findLastTask(
      conversationManager.conversation,
    );
    if (lastTask2 === undefined) {
      this.reject("Last task is undefined");
      return;
    }
    const lastIndex = lastTask2.artifactMessages.length - 1;

    const artifactMessage =
      lastTask2.artifactMessages[lastIndex < 0 ? 0 : lastIndex];
    if (artifactMessage === undefined) {
      this.reject(
        "The artifactMessages tool message is undefined, the task should have artifact message",
      );
      return;
    }
    if (artifactMessage instanceof NuroAssistantMessage === false) {
      this.reject(
        "The artifactMessages tool message is not a NuroAssistantMessageStatus, the task should have artifact message",
      );
      return;
    }

    this.resolve();
  }
}
