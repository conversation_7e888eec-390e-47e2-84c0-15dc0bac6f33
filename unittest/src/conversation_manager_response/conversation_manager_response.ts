import {
  NuroConversationManager as ConversationManager,
  NuroTaskStatus,
  NuroSetting,
  NuroTaskChecker,
  NuroCanvasMessage,
  NuroCanvasStatus,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
  NuroCanvasNode,
  NuroUserMessage,
  NuroAssistantMessage,
} from "@byted/nurosdk";
import {
  mockConversationJSONStr,
  mockLocalConsumptionStr,
  mockLocalServerContinueGenerateStr,
} from "./conversation_manager_endpoint";
import { TestCase } from "../utils";
import { TSNConsole } from "@byted/tsnfoundation";

export class ConversationManagerResponse extends TestCase {
  constructor() {
    super("conversation_manager_response");
    this.initSettings();
  }

  initSettings() {
    NuroSetting.version = "3.0.0";
    NuroSetting.canvasSettings = {
      startNode: "canvas_open_new_canvas",
      endNode: "close_canvas",
      nodes: [
        "creative_agent_mcp_gen_text2image_v3",
        "creative_agent_mcp_gen_image2image_v3",
        "creative_agent_mcp_gen_image2video_v3",
        "creative_agent_mcp_gen_text2video_v3",
        "creative_agent_mcp_gen_start_end2video_v3",
      ],
    };
  }

  run(): void {
    // 普通画布消息
    this.testCanvasMessage();
    // 本地工具调用，没有继续生成
    this.testLocalMessage();
    // 本地工具调用，有继续生成
    this.testLocalContinueGenerateMessage();

    this.resolve();
  }

  private testCanvasMessage(): void {
    const conversationManager = new ConversationManager();
    conversationManager.conversation.taskChecker = new NuroTaskChecker();
    conversationManager.decodeConversationFromJSONString(
      mockConversationJSONStr,
      false,
    );
    if (
      conversationManager.conversation.conversationId !==
      "78556148-a721-4d50-2188-5f047bbef9b3"
    ) {
      this.reject(
        "conversationId should be '78556148-a721-4d50-2188-5f047bbef9b3' but now is " +
          conversationManager.conversation.conversationId,
      );
    }
    if (
      conversationManager.conversation.summary !== "已生成3张治愈系小猫场景图"
    ) {
      this.reject("已生成3张治愈系小猫场景图'");
    }
    if (conversationManager.conversation.messages.length !== 5) {
      this.reject("should have 5 messages");
      return;
    }

    const tasks = conversationManager.conversation.tasks;
    if (tasks.length !== 1) {
      this.reject("should have 1 task");
      return;
    }

    const task = tasks[0];
    if (task?.taskStatus !== NuroTaskStatus.finished) {
      this.reject("task status should be finished'");
    }

    if (task?.artifactMessages.length !== 4) {
      this.reject("task artifact messages should be 4'");
    }

    const firstMessage = task?.artifactMessages[0];
    if (firstMessage === undefined) {
      this.reject("first messages should be not be empty! ");
    }
    if (!(firstMessage instanceof NuroAssistantMessage)) {
      this.reject("first messages should be assistantMessage! ");
    }
    if (
      (firstMessage as NuroAssistantMessage).text !==
      "我将为您生成3张不同场景的小猫图片，包括花园玩耍、窗台打盹和纸箱探险的温馨画面，保持一致的治愈系风格。"
    ) {
      this.reject(
        "first messages text failed, should be 我将为您生成3张不同场景的小猫图片，包括花园玩耍、窗台打盹和纸箱探险的温馨画面，保持一致的治愈系风格。 but now is " +
          (firstMessage as NuroAssistantMessage).text,
      );
    }

    const secondMessage = task?.artifactMessages[1];
    if (secondMessage === undefined) {
      this.reject("canvas messages should be not be empty! ");
    }
    if (!(secondMessage instanceof NuroCanvasMessage)) {
      this.reject("first messages should be NuroCanvasMessage! ");
    }

    if ((secondMessage as NuroCanvasMessage).status !== NuroCanvasStatus.end) {
      this.reject(
        "first messages status failed, should be end but now is " +
          (secondMessage as NuroCanvasMessage).status,
      );
    }

    if ((secondMessage as NuroCanvasMessage).nodes === undefined) {
      this.reject("first messages nodes should not be empty! ");
    }

    if ((secondMessage as NuroCanvasMessage).nodes?.length !== 3) {
      this.reject(
        "first messages nodes length should be 3 , but now is " +
          (secondMessage as NuroCanvasMessage).nodes?.length,
      );
    }

    (secondMessage as NuroCanvasMessage).nodes?.forEach((node, index) => {
      if (node?.toolResult === undefined) {
        this.reject(
          "first messages nodes should have result ! but now have no result ! index is " +
            index,
        );
      }
    });
    TSNConsole.log("pass canvas message test!");
  }

  private testLocalMessage(): void {
    TSNConsole.log("begin local tool message test!");
    const conversationManager = new ConversationManager();
    conversationManager.conversation.taskChecker = new NuroTaskChecker();
    conversationManager.decodeConversationFromJSONString(
      mockLocalConsumptionStr,
      false,
    );

    if (
      conversationManager.conversation.conversationId !==
      "420ce080-7411-e56a-4d3b-cb8aa992b5ca"
    ) {
      this.reject(
        "conversationId should be '420ce080-7411-e56a-4d3b-cb8aa992b5ca'",
      );
    }
    if (
      conversationManager.conversation.summary !==
      "生成10个猫主题视频需消耗较多积分"
    ) {
      this.reject("生成10个猫主题视频需消耗较多积分'");
    }

    if (conversationManager.conversation.messages.length !== 2) {
      this.reject("should have 2 messages");
      return;
    }

    const tasks = conversationManager.conversation.tasks;
    if (tasks.length !== 1) {
      this.reject("should have 1 task");
      return;
    }

    const task = tasks[0];
    if (task?.taskStatus !== NuroTaskStatus.finished) {
      this.reject("task status should be finished'");
    }

    if (task?.artifactMessages.length !== 1) {
      this.reject("task artifact messages should be 1 ");
    }

    const firstMessage = task?.artifactMessages[0];
    if (firstMessage === undefined) {
      this.reject("canvas messages should be not be empty! ");
    }
    if (!(firstMessage instanceof NuroToolCallMessage)) {
      this.reject("first messages should be NuroToolCallMessage! ");
    }

    if (
      (firstMessage as NuroToolCallMessage)?.messageStatus !==
      NuroToolCallMessageStatus.skipped
    ) {
      this.reject(
        "first messages status failed, should be skipped but now is " +
          (firstMessage as NuroToolCallMessage)?.messageStatus,
      );
    }

    if ((firstMessage as NuroToolCallMessage)?.toolType !== "client_function") {
      this.reject(
        "first messages toolType failed, should be client_function but now is " +
          (firstMessage as NuroToolCallMessage)?.toolType,
      );
    }
    TSNConsole.log("pass local tool message test!");
  }

  private testLocalContinueGenerateMessage(): void {
    TSNConsole.log("begin local tool message continue generate test!");
    const conversationManager = new ConversationManager();
    conversationManager.conversation.taskChecker = new NuroTaskChecker();
    conversationManager.decodeConversationFromJSONString(
      mockLocalServerContinueGenerateStr,
      false,
    );

    if (
      conversationManager.conversation.conversationId !==
      "cf3af5c9-ce37-ac43-0a30-b8361b28baf3"
    ) {
      this.reject(
        "conversationId should be 'cf3af5c9-ce37-ac43-0a30-b8361b28baf3'",
      );
    }
    if (
      conversationManager.conversation.summary !==
      "因未获取到具体生成的视频相关资源等详细产物信息，暂无法准确按要求总结，可补充更多生成结果相关内容再进一步总结。"
    ) {
      this.reject(
        "因未获取到具体生成的视频相关资源等详细产物信息，暂无法准确按要求总结，可补充更多生成结果相关内容再进一步总结。'",
      );
    }

    if (conversationManager.conversation.messages.length !== 6) {
      this.reject("should have 6 messages");
      return;
    }

    const tasks = conversationManager.conversation.tasks;
    if (tasks.length !== 1) {
      this.reject("should have 1 task");
      return;
    }

    const task = tasks[0];
    if (task?.taskStatus !== NuroTaskStatus.finished) {
      this.reject("task status should be finished'");
    }

    if (task?.artifactMessages.length !== 5) {
      this.reject("task artifact messages should be 5 ");
    }

    const firstMessage = task?.artifactMessages[0];
    if (firstMessage === undefined) {
      this.reject("canvas messages should be not be empty! ");
    }
    if (!(firstMessage instanceof NuroToolCallMessage)) {
      this.reject("first messages should be NuroToolCallMessage! ");
    }

    if (
      (firstMessage as NuroToolCallMessage)?.messageStatus !==
      NuroToolCallMessageStatus.finished_successfully
    ) {
      this.reject(
        "first messages status failed, should be skipped but now is " +
          (firstMessage as NuroToolCallMessage)?.messageStatus,
      );
    }

    if ((firstMessage as NuroToolCallMessage)?.toolType !== "client_function") {
      this.reject(
        "first messages toolType failed, should be client_function but now is " +
          (firstMessage as NuroToolCallMessage)?.toolType,
      );
    }

    if (
      (firstMessage as NuroToolCallMessage)?.toolResult !==
      '{"content":[{"type":"text","text":"继续生成"}]}'
    ) {
      this.reject(
        "first messages toolResult not matched ! now tool result is " +
          (firstMessage as NuroToolCallMessage)?.toolResult,
      );
    }

    if (
      (firstMessage as NuroToolCallMessage)?.toolResult !==
      '{"content":[{"type":"text","text":"继续生成"}]}'
    ) {
      this.reject(
        "first messages toolResult not matched ! now tool result is " +
          (firstMessage as NuroToolCallMessage)?.toolResult,
      );
    }

    const secondMessage = task?.artifactMessages[1];
    if (secondMessage === undefined) {
      this.reject("second messages should be not be empty! ");
    }
    if (!(secondMessage instanceof NuroCanvasMessage)) {
      this.reject("second messages should be NuroCanvasMessage! ");
    }
    if ((secondMessage as NuroCanvasMessage)?.status !== NuroCanvasStatus.end) {
      this.reject(
        "second messages status not matched ! should be end , now status is " +
          (secondMessage as NuroCanvasMessage)?.status,
      );
    }
    if ((secondMessage as NuroCanvasMessage)?.nodes?.length !== 2) {
      this.reject(
        "second messages nodes length not matched ! should be 2 , now length is " +
          (secondMessage as NuroCanvasMessage)?.nodes?.length,
      );
    }
    (secondMessage as NuroCanvasMessage)?.nodes?.forEach((node, index) => {
      if ((node as NuroCanvasNode)?.toolResult === "") {
        this.reject(
          "second messages nodes toolResult should not be empty! index is " +
            index,
        );
      }
    });

    const thirdMessage = task?.artifactMessages[2];
    if (thirdMessage === undefined) {
      this.reject("second messages should be not be empty! ");
    }
    if (!(thirdMessage instanceof NuroCanvasMessage)) {
      this.reject("second messages should be NuroCanvasMessage! ");
    }
    if ((thirdMessage as NuroCanvasMessage)?.status !== NuroCanvasStatus.end) {
      this.reject(
        "second messages status not matched ! should be end , now status is " +
          (secondMessage as NuroCanvasMessage)?.status,
      );
    }
    if ((thirdMessage as NuroCanvasMessage)?.nodes?.length !== 2) {
      this.reject(
        "second messages nodes length not matched ! should be 2 , now length is " +
          (secondMessage as NuroCanvasMessage)?.nodes?.length,
      );
    }
    (thirdMessage as NuroCanvasMessage)?.nodes?.forEach((node, index) => {
      if ((node as NuroCanvasNode)?.toolResult === "") {
        this.reject(
          "second messages nodes toolResult should not be empty! index is " +
            index,
        );
      }
    });

    TSNConsole.log("pass local tool message test!");
  }
}
