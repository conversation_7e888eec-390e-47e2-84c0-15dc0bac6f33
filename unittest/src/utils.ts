import { TSNNumberConverter, TSNTimer } from "@byted/tsnfoundation";
import {
  EventStreamAdapter,
  EventStreamConfig,
  NuroAssistantMessage,
  NuroConversation,
  NuroMCPManager,
  NuroTask,
  NuroToolCallMessage,
  NuroSetting,
  NuroCanvasMessage,
} from "@byted/nurosdk";

export class FailedTestInfo {
  functionName: string;
  errorMessage: string;

  constructor(functionName: string, errorMessage: string) {
    this.functionName = functionName;
    this.errorMessage = errorMessage;
  }
}

export class SuccessTestInfo {
  functionName: string;

  constructor(functionName: string) {
    this.functionName = functionName;
  }
}

export class TestContext {
  static globalContext = new TestContext();
  private failedTests: FailedTestInfo[] = [];
  private succeededTests: SuccessTestInfo[] = [];

  reportFailure(functionName: string, errorMessage: string): void {
    this.failedTests.push(new FailedTestInfo(functionName, errorMessage));
  }

  reportSuccess(functionName: string): void {
    this.succeededTests.push(new SuccessTestInfo(functionName));
  }

  getFailedTests(): FailedTestInfo[] {
    return this.failedTests;
  }

  getSucceededTests(): SuccessTestInfo[] {
    return this.succeededTests;
  }
}

export class TestCase {
  name: string;
  resolve: () => void = () => {};
  reject: (errorMessage: string) => void = (_) => {};

  constructor(name: string) {
    this.name = name;
  }

  run(): void {}

  delay(seconds: Double, cb: () => void): void {
    new TSNTimer(seconds, cb);
  }
}

export class TestDevice {
  static testMCPManager: Optional<NuroMCPManager> = undefined;

  private definedTests: TestCase[] = [];

  defineTest(item: TestCase) {
    this.definedTests.push(item);
  }

  runNextTest(onFinish: () => void) {
    const currentTest = this.definedTests.shift();
    if (currentTest !== undefined) {
      currentTest.resolve = () => {
        TestContext.globalContext.reportSuccess(currentTest.name);
        this.runNextTest(onFinish);
      };
      currentTest.reject = (errorMessage) => {
        TestContext.globalContext.reportFailure(currentTest.name, errorMessage);
        this.runNextTest(onFinish);
      };
      currentTest.run();
    } else {
      onFinish();
    }
  }
}

export class TestChunkedServer {
  static singleton = new TestChunkedServer();

  chunksParts: Array<Array<string>> = [];
  _pausedConfig: Optional<EventStreamConfig> = undefined;

  installNuroSDK(): void {
    NuroSetting.version = "3.0.0";
    NuroSetting.canvasSettings = {
      startNode: "canvas_open_new_canvas",
      endNode: "close_canvas",
      nodes: [
        "creative_agent_mcp_gen_text2image_v3",
        "creative_agent_mcp_gen_image2image_v3",
        "creative_agent_mcp_gen_image2video_v3",
        "creative_agent_mcp_gen_text2video_v3",
        "creative_agent_mcp_gen_start_end2video_v3",
      ],
    };
    EventStreamAdapter.fetch = (config: EventStreamConfig) => {
      this.start(config);
      return "";
    };
  }

  setText(text: string): void {
    this.installNuroSDK();
    const chunks: Array<string> = [];
    let remainingText = text;
    while (remainingText.length > 0) {
      const chunkSize = TSNNumberConverter.toInt(
        Math.floor(
          Math.random() * TSNNumberConverter.toDouble(remainingText.length),
        ) + 1,
      );
      chunks.push(remainingText.substring(0, chunkSize));
      remainingText = remainingText.substring(chunkSize, remainingText.length);
    }
    this.chunksParts.push(chunks);
  }

  setTexts(texts: string[]): void {
    for (let index = 0; index < texts.length; index++) {
      const element = texts[index]!;
      this.setText(element);
    }
  }

  start(config: EventStreamConfig, fromResume: boolean = false): void {
    const currentChunks = this.chunksParts.shift();
    if (currentChunks === undefined) {
      return;
    }
    if (config.onStart !== undefined && fromResume !== true) {
      config.onStart();
    }
    for (let index = 0; index < currentChunks.length; index++) {
      const element = currentChunks[index]!;
      if (config.onChunk !== undefined) {
        config.onChunk(element);
      }
    }
    if (currentChunks.join("").indexOf("stream_complete") < 0) {
      // 中断回复，等候 resume
      this._pausedConfig = config;
      return;
    }
    if (config.onFinish !== undefined) {
      config.onFinish();
    }
  }

  resume(): void {
    if (this._pausedConfig !== undefined) {
      this.start(this._pausedConfig, true);
      this._pausedConfig = undefined;
    }
  }
}

export class TestConversationUtils {
  static findLastAssistantMessage(
    conversation: Optional<NuroConversation>,
  ): Optional<NuroAssistantMessage> {
    const messages = conversation?.messages;
    if (messages === undefined) {
      return undefined;
    }
    let lastAssistantMessage: Optional<NuroAssistantMessage> = undefined;
    for (let index = messages.length - 1; index >= 0; index = index - 1) {
      const element = messages[index]!;
      if (element instanceof NuroAssistantMessage) {
        lastAssistantMessage = element;
      }
    }
    return lastAssistantMessage;
  }

  static findLastToolCallMessage(
    conversation: Optional<NuroConversation>,
  ): Optional<NuroToolCallMessage> {
    const messages = conversation?.messages;
    if (messages === undefined) {
      return undefined;
    }
    let lastToolCallMessage: Optional<NuroToolCallMessage> = undefined;
    for (let index = messages.length - 1; index >= 0; index = index - 1) {
      const element = messages[index]!;
      if (element instanceof NuroToolCallMessage) {
        lastToolCallMessage = element;
      }
    }
    return lastToolCallMessage;
  }

  static findLastTask(
    conversation: Optional<NuroConversation>,
  ): Optional<NuroTask> {
    const tasks = conversation?.tasks;
    if (tasks === undefined) {
      return undefined;
    }
    let lastTask: Optional<NuroTask> = undefined;
    for (let index = tasks.length - 1; index >= 0; index = index - 1) {
      const element = tasks[index]!;
      lastTask = element;
    }
    return lastTask;
  }

  static findLastCanvasMessage(
    conversation: Optional<NuroConversation>,
  ): Optional<NuroCanvasMessage> {
    const tasks = conversation?.tasks;
    if (tasks === undefined) {
      return undefined;
    }
    // Nuro 3.0只有一个turn
    let lastTask = tasks[0];
    let resultMessage: Optional<NuroCanvasMessage> = undefined;
    lastTask?.artifactMessages.forEach((artifactMessage) => {
      if (artifactMessage instanceof NuroCanvasMessage) {
        resultMessage = artifactMessage;
      }
    });
    return resultMessage;
  }
}
