import {
  MCP<PERSON>ool<PERSON>allR<PERSON>ult,
  MCPToolCallTextContent,
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroSetting,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { TestCase, TestConversationUtils, TestDevice } from "../utils";
import { SimpleClientToolAutoEndpoint } from "./simple_client_tool_auto_endpoint";

export class SimpleClientToolAutoResponse extends TestCase {
  constructor() {
    super("simple_client_tool_auto_response");
  }

  run(): void {
    // Setup
    const server = new SimpleClientToolAutoEndpoint();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));

    conversationManager.mcpManager = TestDevice.testMCPManager;
    conversationManager.enableMCPTools();
    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"),
    );

    this.delay(1.0, () => {
      const lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(
        conversationManager.conversation,
      );
      if (lastToolCallMessage === undefined) {
        this.reject("The last message should be tool call message");
        return;
      }

      if (
        lastToolCallMessage.messageStatus !==
        NuroToolCallMessageStatus.finished_successfully
      ) {
        this.reject("The last message status should be finished successfully");
        return;
      }
      if (lastToolCallMessage.decodeToolCallResultAsPlainText() !== "佛山市") {
        this.reject("The last message tool result should be '佛山市'");
        return;
      }

      this.resolve();
    });
  }
}
