import {
  MCPTool<PERSON>allResult,
  MCPToolCallText<PERSON>ontent,
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroSetting,
  NuroTaskStatus,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { TestCase, TestConversationUtils, TestDevice } from "../utils";
import { SimpleCanvasEndPoint } from "./simple_canvas_endpoint";

export class SimpleCanvasResponse extends TestCase {
  constructor() {
    super("simple_client_tool_auto_response");
  }

  run(): void {
    // Setup
    const server = new SimpleCanvasEndPoint();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));

    conversationManager.mcpManager = TestDevice.testMCPManager;
    conversationManager.enableMCPTools();
    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"),
    );

    this.delay(1.0, () => {
      const lastTask = TestConversationUtils.findLastTask(
        conversationManager.conversation,
      );
      if (lastTask === undefined) {
        this.reject("Last task is undefined");
        return;
      }
      if (lastTask.artifactMessages.length === 0) {
        this.reject("Last task artifact messages is empty");
        return;
      }
      if (lastTask.artifactMessages.length !== 5) {
        this.reject(
          "Last task artifact messages number wrong, should be 5 but current number is " +
            lastTask.artifactMessages.length,
        );
        return;
      }
      if (lastTask.taskStatus !== NuroTaskStatus.finished) {
        this.reject(
          "Last task artifact messages task status wrong, should be finished but current task status is " +
            lastTask.taskStatus,
        );
        return;
      }

      this.resolve();
    });
  }
}
