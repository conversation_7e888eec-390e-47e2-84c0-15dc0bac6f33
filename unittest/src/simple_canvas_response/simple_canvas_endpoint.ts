import { TestChunkedServer } from "../utils";

export class SimpleCanvasEndPoint {
  res_0 = `id:1
event:message
data:{"status":"in_progress","id":"efa647a2-4ff3-4bd3-b460-8d49e6bb29b4","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"a77fee98-726c-af3a-c5ba-099e55ceb471","tool_call_id":"","message_type":"answer"},"create_time":1752116569738,"update_time":1752116569738}

id:2
event:delta
data:{"op":"append","value":"我","path":"/message/content/content_parts/0/text"}

id:3
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"将"}

id:4
event:delta
data:{"value":"为","path":"/message/content/content_parts/0/text","op":"append"}

id:5
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"您"}

id:6
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"生成"}

id:7
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"3"}

id:8
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"张"}

id:9
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"不同"}

id:10
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风格"}

id:11
event:delta
data:{"op":"append","value":"的","path":"/message/content/content_parts/0/text"}

id:12
event:delta
data:{"value":"小猫","path":"/message/content/content_parts/0/text","op":"append"}

id:13
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"图片"}

id:14
event:delta
data:{"value":"，","path":"/message/content/content_parts/0/text","op":"append"}

id:15
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"包括"}

id:16
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"可爱"}

id:17
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"卡通"}

id:18
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风格"}

id:19
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"、"}

id:20
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"写实"}

id:21
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"摄影"}

id:22
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风格"}

id:23
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"和"}

id:24
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"水彩"}

id:25
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"艺术"}

id:26
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风格"}

id:27
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"，"}

id:28
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"每张"}

id:29
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"都"}

id:30
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"有"}

id:31
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"独特"}

id:32
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"的"}

id:33
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"场景"}

id:34
event:delta
data:{"op":"append","value":"和","path":"/message/content/content_parts/0/text"}

id:35
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"动态"}

id:36
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"表现"}

id:37
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"。"}

id:38
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"\\n\\n"}

id:39
event:delta
data:{"path":"/message/tool_calls/0","op":"add","value":"{\\\"id\\\":\\\"call_g0k39dqqe17h8p84rstd31t8\\\",\\\"type\\\":\\\"server_function\\\",\\\"func\\\":{\\\"name\\\":\\\"canvas_open_new_canvas\\\",\\\"arguments\\\":\\\"\\\"},\\\"streaming\\\":true}"}

id:40
event:delta
data:{"op":"append","value":"{\"","path":"/message/tool_calls/0/func/arguments"}

id:41
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"canvas"}

id:42
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"_type"}

id:43
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"\":\""}

id:44
event:delta
data:{"value":"multi","path":"/message/tool_calls/0/func/arguments","op":"append"}

id:45
event:delta
data:{"op":"append","value":"_image","path":"/message/tool_calls/0/func/arguments"}

id:46
event:delta
data:{"value":"\",\"","path":"/message/tool_calls/0/func/arguments","op":"append"}

id:47
event:delta
data:{"value":"num","path":"/message/tool_calls/0/func/arguments","op":"append"}

id:48
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"_contents"}

id:49
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"\\":"}

id:50
event:delta
data:{"op":"append","value":"3","path":"/message/tool_calls/0/func/arguments"}

id:51
event:delta
data:{"value":",\"","path":"/message/tool_calls/0/func/arguments","op":"append"}

id:52
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"ratio"}

id:53
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"\":\""}

id:54
event:delta
data:{"op":"append","value":"1","path":"/message/tool_calls/0/func/arguments"}

id:55
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"\\":"}

id:56
event:delta
data:{"path":"/message/tool_calls/0/func/arguments","op":"append","value":"1"}

id:57
event:delta
data:{"op":"append","value":"\"}","path":"/message/tool_calls/0/func/arguments"}

id:58
event:delta
data:{"path":"/message/tool_calls/0/streaming","op":"replace","value":"false"}

id:59
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:60
event:message
data:{"update_time":1752116571780,"status":"in_progress","id":"013286a9-7828-43b5-bb78-197412cd0f28","author":{"role":"tool","name":"canvas_open_new_canvas"},"content":{"content_parts":[{"text":""}],"reasoning_content":"","content_type":"text","parts":null},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"efa647a2-4ff3-4bd3-b460-8d49e6bb29b4","tool_call_id":"call_g0k39dqqe17h8p84rstd31t8","message_type":"answer"},"create_time":1752116571780}

id:61
event:delta
data:{"op":"append","value":"{\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"已成功创建 multi_image 类型的canvas，该类型的canvas中只可以插入图片\\\\n### 注意\\\\n1. 你可以使用图片生成工具生成多张图片，并插入到该canvas中\\\\n2. 一组图片一般来说会具备某种一致性，例如相同的画风、尺寸、角色、艺术符号等，请根据用户需求思考如何将这种一致性体现在提示词和图片比例参数中\\\\n3. 同一个canvas中一般会需要插入多个内容，请以<|FunctionCallBegin|>[工具1，工具2，工具3...]<|FunctionCallEnd|> 的格式一次性输出所有的工具调用指令，以节省调用次数和时间\\\\n4. 在完成所有图片的插入后，请使用 \\\\\\\"canvas_close_canvas\\\\\\\" 工具关闭canvas并保存内容\\\"}]}","path":"/message/content/content_parts/0/text"}

id:62
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:63
event:message
data:{"status":"in_progress","id":"179af7e9-95ec-436f-bc37-61931be53a56","author":{"name":"host","role":"assistant"},"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"message_type":"answer","is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"013286a9-7828-43b5-bb78-197412cd0f28","tool_call_id":""},"create_time":1752116574025,"update_time":1752116574025}

id:64
event:delta
data:{"value":"{\\\"id\\\":\\\"call_ty8bktn5jo3xfbzuqt39ux4i\\\",\\\"type\\\":\\\"server_function\\\",\\\"func\\\":{\\\"name\\\":\\\"creative_agent_mcp_gen_text2image_v3\\\",\\\"arguments\\\":\\\"\\\",\\\"extra\\\":{\\\"resource_cnt\\\":\\\"4\\\",\\\"resource_type\\\":\\\"image\\\"}},\\\"streaming\\\":true}","path":"/message/tool_calls/1","op":"add"}

id:65
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"{\""}

id:66
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"canvas"}

id:67
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"_content"}

id:68
event:delta
data:{"op":"append","value":"_id","path":"/message/tool_calls/1/func/arguments"}

id:69
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\":"}

id:70
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"1"}

id:71
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":",\""}

id:72
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"prompt"}

id:73
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\":\""}

id:74
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"可爱"}

id:75
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"卡通"}

id:76
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"风格"}

id:77
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"小猫"}

id:78
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"，"}

id:79
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"穿着"}

id:80
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"蓝色"}

id:81
event:delta
data:{"op":"append","value":"蝴蝶结","path":"/message/tool_calls/1/func/arguments"}

id:82
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"，"}

id:83
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"坐在"}

id:84
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"粉色"}

id:85
event:delta
data:{"op":"append","value":"云朵","path":"/message/tool_calls/1/func/arguments"}

id:86
event:delta
data:{"value":"上","path":"/message/tool_calls/1/func/arguments","op":"append"}

id:87
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"，"}

id:88
event:delta
data:{"value":"周围","path":"/message/tool_calls/1/func/arguments","op":"append"}

id:89
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"漂浮"}

id:90
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"着"}

id:91
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"彩色"}

id:92
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"气泡"}

id:93
event:delta
data:{"op":"append","value":"，","path":"/message/tool_calls/1/func/arguments"}

id:94
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"明亮"}

id:95
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"温暖"}

id:96
event:delta
data:{"value":"的","path":"/message/tool_calls/1/func/arguments","op":"append"}

id:97
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"色调"}

id:98
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"，"}

id:99
event:delta
data:{"value":"圆润","path":"/message/tool_calls/1/func/arguments","op":"append"}

id:100
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"的"}

id:101
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"线条"}

id:102
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"，"}

id:103
event:delta
data:{"op":"append","value":"儿童","path":"/message/tool_calls/1/func/arguments"}

id:104
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"插画"}

id:105
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"风格"}

id:106
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\",\""}

id:107
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"ratio"}

id:108
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\":\""}

id:109
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"1"}

id:110
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\":"}

id:111
event:delta
data:{"value":"1","path":"/message/tool_calls/1/func/arguments","op":"append"}

id:112
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\",\""}

id:113
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"version"}

id:114
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"\":\""}

id:115
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"通用"}

id:116
event:delta
data:{"path":"/message/tool_calls/1/func/arguments","op":"append","value":"版"}

id:117
event:delta
data:{"op":"append","value":"\"}","path":"/message/tool_calls/1/func/arguments"}

id:118
event:delta
data:{"path":"/message/tool_calls/2","op":"add","value":"{\\\"id\\\":\\\"call_35aakhj11m0qinufjvmxuowi\\\",\\\"type\\\":\\\"server_function\\\",\\\"func\\\":{\\\"name\\\":\\\"creative_agent_mcp_gen_text2image_v3\\\",\\\"arguments\\\":\\\"\\\",\\\"extra\\\":{\\\"resource_type\\\":\\\"image\\\",\\\"resource_cnt\\\":\\\"4\\\"}},\\\"streaming\\\":true}"}

id:119
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"{\""}

id:120
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"canvas"}

id:121
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"_content"}

id:122
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"_id"}

id:123
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\":"}

id:124
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"2"}

id:125
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":",\""}

id:126
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"prompt"}

id:127
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\":\""}

id:128
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"写实"}

id:129
event:delta
data:{"value":"摄影","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:130
event:delta
data:{"op":"append","value":"风格","path":"/message/tool_calls/2/func/arguments"}

id:131
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"小猫"}

id:132
event:delta
data:{"value":"，","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:133
event:delta
data:{"value":"橘","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:134
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"白"}

id:135
event:delta
data:{"value":"相间","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:136
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"的"}

id:137
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"毛色"}

id:138
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"，"}

id:139
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"绿色"}

id:140
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"眼睛"}

id:141
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"，"}

id:142
event:delta
data:{"value":"趴在","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:143
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"木质"}

id:144
event:delta
data:{"op":"append","value":"书架","path":"/message/tool_calls/2/func/arguments"}

id:145
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"上"}

id:146
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"，"}

id:147
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"阳光"}

id:148
event:delta
data:{"op":"append","value":"透过","path":"/message/tool_calls/2/func/arguments"}

id:149
event:delta
data:{"value":"窗户","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:150
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"形成"}

id:151
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"光斑"}

id:152
event:delta
data:{"value":"，","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:153
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"高清"}

id:154
event:delta
data:{"value":"细节","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:155
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"，"}

id:156
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"自然"}

id:157
event:delta
data:{"op":"append","value":"光线","path":"/message/tool_calls/2/func/arguments"}

id:158
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"，"}

id:159
event:delta
data:{"value":"浅","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:160
event:delta
data:{"op":"append","value":"景深","path":"/message/tool_calls/2/func/arguments"}

id:161
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\",\""}

id:162
event:delta
data:{"value":"ratio","path":"/message/tool_calls/2/func/arguments","op":"append"}

id:163
event:delta
data:{"op":"append","value":"\":\"","path":"/message/tool_calls/2/func/arguments"}

id:164
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"1"}

id:165
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\":"}

id:166
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"1"}

id:167
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\",\""}

id:168
event:delta
data:{"op":"append","value":"version","path":"/message/tool_calls/2/func/arguments"}

id:169
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\":\""}

id:170
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"艺术"}

id:171
event:system
data:{"content":"heartbeat","type":"heartbeat","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"}

id:172
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"版"}

id:173
event:delta
data:{"path":"/message/tool_calls/2/func/arguments","op":"append","value":"\"}"}

id:174
event:delta
data:{"path":"/message/tool_calls/3","op":"add","value":"{\\\"id\\\":\\\"call_s6nejs95eo6u203wagyggdua\\\",\\\"type\\\":\\\"server_function\\\",\\\"func\\\":{\\\"name\\\":\\\"creative_agent_mcp_gen_text2image_v3\\\",\\\"arguments\\\":\\\"\\\",\\\"extra\\\":{\\\"resource_type\\\":\\\"image\\\",\\\"resource_cnt\\\":\\\"4\\\"}},\\\"streaming\\\":true}"}

id:175
event:delta
data:{"value":"{\"","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:176
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"canvas"}

id:177
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"_content"}

id:178
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"_id"}

id:179
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\":"}

id:180
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"3"}

id:181
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":",\""}

id:182
event:delta
data:{"value":"prompt","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:183
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\":\""}

id:184
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"水彩"}

id:185
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"艺术"}

id:186
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"风格"}

id:187
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"小猫"}

id:188
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"，"}

id:189
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"灰白"}

id:190
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"毛色"}

id:191
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"，"}

id:192
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"蜷缩"}

id:193
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"在"}

id:194
event:delta
data:{"value":"毛线","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:195
event:delta
data:{"value":"球","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:196
event:delta
data:{"value":"旁","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:197
event:delta
data:{"value":"，","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:198
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"背景"}

id:199
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"有"}

id:200
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"淡"}

id:201
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"紫色"}

id:202
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"和"}

id:203
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"蓝色"}

id:204
event:delta
data:{"value":"的","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:205
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"渐变"}

id:206
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"，"}

id:207
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"湿润"}

id:208
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"的"}

id:209
event:delta
data:{"value":"笔触","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:210
event:delta
data:{"value":"效果","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:211
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"，"}

id:212
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"柔和"}

id:213
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"的"}

id:214
event:delta
data:{"value":"色彩","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:215
event:delta
data:{"value":"过渡","path":"/message/tool_calls/3/func/arguments","op":"append"}

id:216
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"，"}

id:217
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"艺术"}

id:218
event:delta
data:{"op":"append","value":"插画","path":"/message/tool_calls/3/func/arguments"}

id:219
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\",\""}

id:220
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"ratio"}

id:221
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\":\""}

id:222
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"1"}

id:223
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\":"}

id:224
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"1"}

id:225
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\",\""}

id:226
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"version"}

id:227
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"\":\""}

id:228
event:delta
data:{"op":"append","value":"通用","path":"/message/tool_calls/3/func/arguments"}

id:229
event:delta
data:{"path":"/message/tool_calls/3/func/arguments","op":"append","value":"版"}

id:230
event:delta
data:{"op":"append","value":"\"}","path":"/message/tool_calls/3/func/arguments"}

id:231
event:delta
data:{"path":"/message/tool_calls/3/streaming","op":"replace","value":"false"}

id:232
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:233
event:message
data:{"id":"e2024e23-b70f-4d88-9205-12fd5d9a8238","author":{"name":"creative_agent_mcp_gen_text2image_v3","role":"tool"},"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"message_type":"answer","is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"179af7e9-95ec-436f-bc37-61931be53a56","tool_call_id":"call_ty8bktn5jo3xfbzuqt39ux4i"},"create_time":1752116580490,"update_time":1752116580490,"status":"in_progress"}

id:234
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"{\\\"history_id\\\":\\\"20838883649026\\\",\\\"commerce_info\\\":{\\\"credit_count\\\":2,\\\"triplets\\\":[{\\\"resource_type\\\":\\\"aigc\\\",\\\"resource_id\\\":\\\"generate_img\\\",\\\"benefit_type\\\":\\\"image_uhd\\\"}]},\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"bc77a9fa-a25b-47ba-a964-dc7ae3e8e5a9\\\",\\\"ratio\\\":\\\"1:1\\\"}"}

id:235
event:system
data:{"type":"heartbeat","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","content":"heartbeat"}

id:236
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"replace","value":"{\\\"history_id\\\":\\\"20838883649026\\\",\\\"commerce_info\\\":{\\\"credit_count\\\":2,\\\"triplets\\\":[{\\\"resource_type\\\":\\\"aigc\\\",\\\"resource_id\\\":\\\"generate_img\\\",\\\"benefit_type\\\":\\\"image_uhd\\\"}]},\\\"resource_count\\\":1,\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"bc77a9fa-a25b-47ba-a964-dc7ae3e8e5a9\\\",\\\"ratio\\\":\\\"1:1\\\"}"}

id:237
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:238
event:message
data:{"update_time":1752116595615,"status":"in_progress","id":"af4d900d-678d-4a9d-8847-1f27376532f5","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"e2024e23-b70f-4d88-9205-12fd5d9a8238","tool_call_id":"call_35aakhj11m0qinufjvmxuowi","message_type":"answer"},"create_time":1752116595615}

id:239
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"{\\\"history_id\\\":\\\"20838899009794\\\",\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"5bcf6161-8d47-4a5b-8de6-407870cd0e2e\\\",\\\"ratio\\\":\\\"1:1\\\"}"}

id:240
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"replace","value":"{\\\"history_id\\\":\\\"20838899009794\\\",\\\"resource_count\\\":1,\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"5bcf6161-8d47-4a5b-8de6-407870cd0e2e\\\",\\\"ratio\\\":\\\"1:1\\\"}"}

id:241
event:delta
data:{"value":"finished_successfully","path":"/message/status","op":"replace"}

id:242
event:message
data:{"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"parent_message_id":"af4d900d-678d-4a9d-8847-1f27376532f5","tool_call_id":"call_s6nejs95eo6u203wagyggdua","message_type":"answer","is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"},"create_time":1752116596276,"update_time":1752116596276,"status":"in_progress","id":"b64d30da-46d9-491d-adfc-255d8c7cccab","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"}}

id:243
event:delta
data:{"op":"append","value":"{\\\"history_id\\\":\\\"20838899009538\\\",\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"5cc03c9b-45d4-43e4-b69b-3c764901ea0c\\\",\\\"ratio\\\":\\\"1:1\\\"}","path":"/message/content/content_parts/0/text"}

id:244
event:system
data:{"type":"heartbeat","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","content":"heartbeat"}

id:245
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"replace","value":"{\\\"history_id\\\":\\\"20838899009538\\\",\\\"resource_count\\\":1,\\\"resource_type\\\":\\\"image\\\",\\\"submit_info\\\":{\\\"code\\\":0,\\\"msg\\\":\\\"\\\"},\\\"submit_id\\\":\\\"5cc03c9b-45d4-43e4-b69b-3c764901ea0c\\\",\\\"ratio\\\":\\\"1:1\\\"}"}

id:246
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:247
event:message
data:{"id":"3866f8f0-2e2a-4c5d-915d-e3186b84df42","author":{"role":"assistant","name":"host"},"content":{"reasoning_content":"","content_type":"text","parts":null,"content_parts":[{"text":""}]},"metadata":{"parent_message_id":"b64d30da-46d9-491d-adfc-255d8c7cccab","tool_call_id":"","message_type":"answer","is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"},"create_time":1752116601112,"update_time":1752116601112,"status":"in_progress"}

id:248
event:delta
data:{"op":"add","value":"{\\\"id\\\":\\\"call_1rp4lkyn4e1bxv2tsvb7sg22\\\",\\\"type\\\":\\\"server_function\\\",\\\"func\\\":{\\\"name\\\":\\\"canvas_close_canvas\\\",\\\"arguments\\\":\\\"\\\"},\\\"streaming\\\":true}","path":"/message/tool_calls/4"}

id:249
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"{\""}

id:250
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"no"}

id:251
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"Args"}

id:252
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"\":"}

id:253
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"\"\""}

id:254
event:delta
data:{"path":"/message/tool_calls/4/func/arguments","op":"append","value":"}"}

id:255
event:delta
data:{"path":"/message/tool_calls/4/streaming","op":"replace","value":"false"}

id:256
event:delta
data:{"value":"finished_successfully","path":"/message/status","op":"replace"}

id:257
event:message
data:{"author":{"role":"tool","name":"canvas_close_canvas"},"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"3866f8f0-2e2a-4c5d-915d-e3186b84df42","tool_call_id":"call_1rp4lkyn4e1bxv2tsvb7sg22","message_type":"answer"},"create_time":1752116601326,"update_time":1752116601326,"status":"in_progress","id":"f1030d6c-873e-4f51-90ee-35f18ecd1830"}

id:258
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"{\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"已关闭画布\\\"}]}"}

id:259
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:260
event:message
data:{"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"f1030d6c-873e-4f51-90ee-35f18ecd1830","tool_call_id":"","message_type":"answer"},"create_time":1752116602832,"update_time":1752116602832,"status":"in_progress","id":"98ffe0a3-e11e-40e6-b616-c09d3e987ba9","author":{"role":"assistant","name":"host"},"content":{"parts":null,"content_parts":[{"text":""}],"reasoning_content":"","content_type":"text"}}

id:261
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"您"}

id:262
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"的"}

id:263
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"3"}

id:264
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"张"}

id:265
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"不同"}

id:266
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风格"}

id:267
event:delta
data:{"op":"append","value":"小猫","path":"/message/content/content_parts/0/text"}

id:268
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"图片"}

id:269
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"已"}

id:270
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"生成"}

id:271
event:delta
data:{"op":"append","value":"完成","path":"/message/content/content_parts/0/text"}

id:272
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"，"}

id:273
event:delta
data:{"value":"包含","path":"/message/content/content_parts/0/text","op":"append"}

id:274
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"可爱"}

id:275
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"卡通"}

id:276
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风"}

id:277
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"、"}

id:278
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"写实"}

id:279
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"摄影"}

id:280
event:delta
data:{"value":"风和","path":"/message/content/content_parts/0/text","op":"append"}

id:281
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"水彩"}

id:282
event:delta
data:{"value":"艺术","path":"/message/content/content_parts/0/text","op":"append"}

id:283
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"风"}

id:284
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"，"}

id:285
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"每张"}

id:286
event:delta
data:{"op":"append","value":"都","path":"/message/content/content_parts/0/text"}

id:287
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"有"}

id:288
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"独特"}

id:289
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"的"}

id:290
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"场景"}

id:291
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"和"}

id:292
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"细节"}

id:293
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"表现"}

id:294
event:delta
data:{"op":"append","value":"。","path":"/message/content/content_parts/0/text"}

id:295
event:delta
data:{"op":"append","value":"您","path":"/message/content/content_parts/0/text"}

id:296
event:delta
data:{"value":"可以","path":"/message/content/content_parts/0/text","op":"append"}

id:297
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"查看"}

id:298
event:delta
data:{"value":"生成","path":"/message/content/content_parts/0/text","op":"append"}

id:299
event:delta
data:{"value":"结果","path":"/message/content/content_parts/0/text","op":"append"}

id:300
event:delta
data:{"value":"，","path":"/message/content/content_parts/0/text","op":"append"}

id:301
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"如有"}

id:302
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"需要"}

id:303
event:delta
data:{"op":"append","value":"调整","path":"/message/content/content_parts/0/text"}

id:304
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"的"}

id:305
event:delta
data:{"value":"地方","path":"/message/content/content_parts/0/text","op":"append"}

id:306
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"，请"}

id:307
event:delta
data:{"path":"/message/content/content_parts/0/text","op":"append","value":"随时"}

id:308
event:delta
data:{"value":"告诉我","path":"/message/content/content_parts/0/text","op":"append"}

id:309
event:delta
data:{"value":"。","path":"/message/content/content_parts/0/text","op":"append"}

id:310
event:delta
data:{"path":"/message/status","op":"replace","value":"finished_successfully"}

id:311
event:message
data:{"content":{"content_type":"text","parts":null,"content_parts":[{"text":""}],"reasoning_content":""},"status":"finished_successfully","id":"b58c0a6b-1a0d-4d2f-a2e3-a51d3c477444","author":{"role":"assistant","name":"host"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180","parent_message_id":"98ffe0a3-e11e-40e6-b616-c09d3e987ba9","message_type":"answer"},"create_time":1752116604195,"update_time":1752116604195,"end_turn":true}

id:312
event:system
data:{"content":"完成3张不同风格小猫图片生成","type":"summary","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"}

id:313
event:system
data:{"title":"生成3张不同风格小猫图","type":"title_generation","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"}

id:314
event:system
data:{"type":"stream_complete","conversation_id":"6bffe517-1c4a-4ad3-d0a0-d58412a75180"}

{"ret":"0","errmsg":"success","systime":"1752116567","logid":"20250710110247B52FAC62F2F7EDF1FEE3"}`;

  injectRes0(): void {
    TestChunkedServer.singleton.setTexts([this.res_0]);
  }
}
