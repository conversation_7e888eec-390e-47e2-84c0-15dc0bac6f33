import {
  NuroAssistantMessageStatus,
  NuroConversationManager,
  NuroConversationState,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk";
import { SimpleAssistantServer } from "./simple_assistant_endpoint";
import { TestCase, TestConversationUtils } from "../utils";

export class SimpleAssistantResponse extends TestCase {
  constructor() {
    super("simple_assistant_response");
  }

  run(): void {
    // Setup
    const server = new SimpleAssistantServer();
    server.injectRes0();
    // Run
    const conversationManager = new NuroConversationManager();
    conversationManager.connect(new SSETransport("/mock", undefined));
    conversationManager.sendUserMessage(
      new NuroUserMessage(NuroUtils.randomUUIDString(), "你好"),
    );
    const lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(
      conversationManager.conversation,
    );
    if (lastAssistantMessage === undefined) {
      this.reject("The last message should be assistant message");
      return;
    }
    if (lastAssistantMessage.text !== "你好！有什么可以帮您的吗？😊") {
      this.reject(
        "The last message content should be '你好！有什么可以帮您的吗？😊'",
      );
      return;
    }
    if (lastAssistantMessage.id !== "oqoj15qm9e_assistant") {
      this.reject("The last message id should be 'oqoj15qm9e_assistant'");
      return;
    }
    if (
      lastAssistantMessage.messageStatus !==
      NuroAssistantMessageStatus.finished_successfully
    ) {
      this.reject("The last message status should be finished_successfully");
      return;
    }
    this.resolve();
  }
}
