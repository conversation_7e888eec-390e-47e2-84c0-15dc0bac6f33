{
  "compilerOptions": {
    "target": "ES6",
    "module": "CommonJS",
    "jsx": "react",
    "noLib": true,
    "experimentalDecorators": true,
    "types": [],
    "strict": true,
    "sourceMap": true,
    "declaration": true,
    "outDir": "../js", // JS Output
    "paths": {
      "@byted/tsnfoundation": ["./node_modules/@byted/tsnfoundation"]
    }
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": [],
  "swift": {
    "out": "../swift",
    "package": "NuroSDKUnitTest",
    "macros": {},
    "exclude": []
  },
  "kotlin": {
    "out": "../kotlin/src/main/java/com/bytedance/nurosdk/unittest",
    "package": "com.bytedance.nurosdk_unittest",
    "macros": {},
    "exclude": []
  }
}
