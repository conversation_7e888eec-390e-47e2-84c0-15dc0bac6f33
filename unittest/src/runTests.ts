import { IS_JS, TSNConsole } from "@byted/tsnfoundation";
import { TestContext, TestDevice } from "./utils";
import { SimpleAssistantResponse } from "./simple_assistant_response/simple_assistant_response";
import { SimpleServerToolResponse } from "./simple_server_tool_response/simple_server_tool_response";
import { SimpleClientToolResponse } from "./simple_client_tool_response/simple_client_tool_response";
import { SimpleClientToolAutoResponse } from "./simple_client_tool_auto_response/simple_client_tool_auto_response";
import { SimpleTaskResponse } from "./simple_task_response/simple_task_response";
import { SimpleCanvasResponse } from "./simple_canvas_response/simple_canvas_response";
import { ConversationManagerResponse } from "./conversation_manager_response/conversation_manager_response";
import { SimpleJsonRepairResponse } from "./simple_jsonrespair_response/simple_jsonrepair_response";

export function runTests(finishCallback?: (testDevice: TestDevice) => void) {
  const testDevice = new TestDevice();

  testDevice.defineTest(new SimpleAssistantResponse());
  testDevice.defineTest(new SimpleServerToolResponse());
  testDevice.defineTest(new SimpleClientToolResponse());
  testDevice.defineTest(new SimpleClientToolAutoResponse());
  testDevice.defineTest(new SimpleTaskResponse());
  // testDevice.defineTest(new SimpleCanvasResponse());
  testDevice.defineTest(new ConversationManagerResponse());
  testDevice.defineTest(new SimpleJsonRepairResponse());

  testDevice.runNextTest(() => {
    const failedTests = TestContext.globalContext.getFailedTests();
    const succeededTests = TestContext.globalContext.getSucceededTests();
    if (failedTests.length > 0) {
      TSNConsole.log("Test failed:");
      failedTests.forEach((test) => {
        TSNConsole.log(
          `Test ${test.functionName} failed: ${test.errorMessage}`,
        );
      });
      if (IS_JS) {
        throw "Test failed";
      }
    } else {
      TSNConsole.log("All tests passed!");
    }
    finishCallback?.(testDevice);
  });
}

if (IS_JS) {
  const { installDevice } = require("./installDevice.js");
  installDevice();
  runTests();
}
