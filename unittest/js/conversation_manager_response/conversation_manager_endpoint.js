"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mockLocalServerContinueGenerateStr = exports.mockLocalConsumptionStr = exports.mockConversationJSONStr = void 0;
exports.mockConversationJSONStr = '{"conversation":{"title":"3张治愈系小猫图片生成","create_time":1752122497171,"update_time":1752122553286,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","mapping":{"2a741ef1-34a0-4869-b98d-5f0f287480f7":{"id":"2a741ef1-34a0-4869-b98d-5f0f287480f7","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"contents\\":[{\\"id\\":\\"image_5692492857346\\",\\"type\\":\\"resource\\",\\"resource\\":{\\"mimeType\\":\\"image/jpeg\\",\\"uri\\":\\"\\"}}],\\"llm_contents\\":\\"已成功生成1组（共1张）图片。\\\\n[{\\\\\\"resource_id\\\\\\":\\\\\\"image_5692492857346\\\\\\",\\\\\\"resource_type\\\\\\":\\\\\\"image\\\\\\",\\\\\\"text\\\\\\":\\\\\\"生成成功\\\\\\"}]\\",\\"history_id\\":\\"20837240468738\\",\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"796e114b-33b6-4b7b-9beb-8408584318a3\\",\\"resource_type\\":\\"image\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"9dc0a85d-2328-4999-aea2-6a6566d12a7b","tool_call_id":"call_zs35iisla0rcm1yzvgjluy4p","message_type":"answer"},"create_time":1752122526943,"update_time":1752122526943,"status":"finished_successfully"},"36cc9a55-e64f-456a-9fe4-e1a31cfb9359":{"id":"36cc9a55-e64f-456a-9fe4-e1a31cfb9359","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"我将为您生成3张不同场景的小猫图片，包括花园玩耍、窗台打盹和纸箱探险的温馨画面，保持一致的治愈系风格。\\n\\n"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"fe72ddcd-66f5-ee1f-ce02-22e1b907577f","tool_call_id":"","message_type":"answer"},"create_time":1752122499254,"update_time":1752122499254,"status":"finished_successfully","tool_calls":[{"id":"call_4munofymvl6jzsfg20fp1qgh","type":"server_function","func":{"name":"canvas_open_new_canvas","arguments":"{\\"canvas_type\\":\\"multi_image\\",\\"num_contents\\":3,\\"ratio\\":\\"1:1\\"}"},"streaming":false}]},"48b1bba5-813a-4ab9-ae9f-47eb39327720":{"id":"48b1bba5-813a-4ab9-ae9f-47eb39327720","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"2a741ef1-34a0-4869-b98d-5f0f287480f7","tool_call_id":"","message_type":"answer"},"create_time":1752122550123,"update_time":1752122550123,"status":"finished_successfully","tool_calls":[{"id":"call_dtouxt4oj8hosfdma999vjg5","type":"server_function","func":{"name":"canvas_close_canvas","arguments":"{\\"noArgs\\":\\"\\"}"},"streaming":false}]},"4c3c73d9-7374-4eae-99be-2c65f358c8bd":{"id":"4c3c73d9-7374-4eae-99be-2c65f358c8bd","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"contents\\":[{\\"id\\":\\"image_5692492857090\\",\\"type\\":\\"resource\\",\\"resource\\":{\\"mimeType\\":\\"image/jpeg\\",\\"uri\\":\\"\\"}}],\\"llm_contents\\":\\"已成功生成1组（共1张）图片。\\\\n[{\\\\\\"resource_id\\\\\\":\\\\\\"image_5692492857090\\\\\\",\\\\\\"resource_type\\\\\\":\\\\\\"image\\\\\\",\\\\\\"text\\\\\\":\\\\\\"生成成功\\\\\\"}]\\",\\"history_id\\":\\"20860324034306\\",\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"93bbbe93-056c-476b-ba85-5bc578b6e7d6\\",\\"resource_type\\":\\"image\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"ac7cf6f6-a518-499f-97c0-d9840c1ca1c3","tool_call_id":"call_2jsi9ze9igusgc34l67mfv1z","message_type":"answer"},"create_time":1752122508726,"update_time":1752122508726,"status":"finished_successfully"},"79eb11f1-8f6f-4cfb-85dc-5e5b42926231":{"id":"79eb11f1-8f6f-4cfb-85dc-5e5b42926231","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"ade3d579-b97f-42b0-ade8-894782f30578","message_type":"answer"},"create_time":1752122552519,"update_time":1752122552519,"status":"finished_successfully","end_turn":true},"9dc0a85d-2328-4999-aea2-6a6566d12a7b":{"id":"9dc0a85d-2328-4999-aea2-6a6566d12a7b","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"contents\\":[{\\"id\\":\\"image_5692492856834\\",\\"type\\":\\"resource\\",\\"resource\\":{\\"mimeType\\":\\"image/jpeg\\",\\"uri\\":\\"\\"}}],\\"llm_contents\\":\\"已成功生成1组（共1张）图片。\\\\n[{\\\\\\"resource_id\\\\\\":\\\\\\"image_5692492856834\\\\\\",\\\\\\"resource_type\\\\\\":\\\\\\"image\\\\\\",\\\\\\"text\\\\\\":\\\\\\"生成成功\\\\\\"}]\\",\\"history_id\\":\\"20860324034050\\",\\"commerce_info\\":{\\"credit_count\\":2,\\"triplets\\":[{\\"resource_type\\":\\"aigc\\",\\"resource_id\\":\\"generate_img\\",\\"benefit_type\\":\\"image_uhd\\"}]},\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"55dbf4d1-3586-400c-8d69-ce427e866843\\",\\"resource_type\\":\\"image\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"4c3c73d9-7374-4eae-99be-2c65f358c8bd","tool_call_id":"call_g1ueoobgfsu43h9a91k9dj5z","message_type":"answer"},"create_time":1752122526905,"update_time":1752122526905,"status":"finished_successfully"},"ac7cf6f6-a518-499f-97c0-d9840c1ca1c3":{"id":"ac7cf6f6-a518-499f-97c0-d9840c1ca1c3","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"ae3eb7e9-e835-436e-abdc-aeac988fd6e1","tool_call_id":"","message_type":"answer"},"create_time":1752122503007,"update_time":1752122503007,"status":"finished_successfully","tool_calls":[{"id":"call_2jsi9ze9igusgc34l67mfv1z","type":"server_function","func":{"name":"creative_agent_mcp_gen_text2image_v3","arguments":"{\\"canvas_content_id\\":1,\\"prompt\\":\\"治愈系风格小猫在花园玩耍，橘白相间的毛色，绿色眼睛，追逐蝴蝶，周围有粉色花朵和绿色草地，阳光明媚，温暖色调，柔和光影\\",\\"ratio\\":\\"1:1\\",\\"version\\":\\"通用版\\"}","extra":{"resource_cnt":"4","resource_type":"image"}},"streaming":true},{"id":"call_g1ueoobgfsu43h9a91k9dj5z","type":"server_function","func":{"name":"creative_agent_mcp_gen_text2image_v3","arguments":"{\\"canvas_content_id\\":2,\\"prompt\\":\\"治愈系风格小猫在窗台打盹，灰白毛色，蓝色眼睛，蜷缩在柔软的垫子上，窗外有蓝天白云，室内有温暖的阳光洒入，浅景深，温馨氛围\\",\\"ratio\\":\\"1:1\\",\\"version\\":\\"通用版\\"}","extra":{"resource_cnt":"4","resource_type":"image"}},"streaming":true},{"id":"call_zs35iisla0rcm1yzvgjluy4p","type":"server_function","func":{"name":"creative_agent_mcp_gen_text2image_v3","arguments":"{\\"canvas_content_id\\":3,\\"prompt\\":\\"治愈系风格小猫在纸箱探险，黑色毛色，黄色眼睛，从纸箱中探出头来，周围散落着彩色纸屑，室内柔和灯光，可爱动态，高细节\\",\\"ratio\\":\\"1:1\\",\\"version\\":\\"通用版\\"}","extra":{"resource_cnt":"4","resource_type":"image"}},"streaming":false}]},"ade3d579-b97f-42b0-ade8-894782f30578":{"id":"ade3d579-b97f-42b0-ade8-894782f30578","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"您的3张治愈系小猫场景图已生成完成，分别展现了花园玩耍、窗台打盹和纸箱探险的温馨画面。每张图片都保持了柔和的色调与细腻的细节，希望能为您带来温暖的视觉体验。如果需要调整场景或风格，请随时告诉我。"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"b09bd3b2-5e1a-4af7-8c26-410c26e8d55f","tool_call_id":"","message_type":"answer"},"create_time":1752122551654,"update_time":1752122551654,"status":"finished_successfully"},"ae3eb7e9-e835-436e-abdc-aeac988fd6e1":{"id":"ae3eb7e9-e835-436e-abdc-aeac988fd6e1","author":{"role":"tool","name":"canvas_open_new_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已成功创建 multi_image 类型的canvas，该类型的canvas中只可以插入图片\\\\n### 注意\\\\n1. 你可以使用图片生成工具生成多张图片，并插入到该canvas中\\\\n2. 一组图片一般来说会具备某种一致性，例如相同的画风、尺寸、角色、艺术符号等，请根据用户需求思考如何将这种一致性体现在提示词和图片比例参数中\\\\n3. 同一个canvas中一般会需要插入多个内容，请以<|FunctionCallBegin|>[工具1，工具2，工具3...]<|FunctionCallEnd|> 的格式一次性输出所有的工具调用指令，以节省调用次数和时间\\\\n4. 在完成所有图片的插入后，请使用 \\\\\\"canvas_close_canvas\\\\\\" 工具关闭canvas并保存内容\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"36cc9a55-e64f-456a-9fe4-e1a31cfb9359","tool_call_id":"call_4munofymvl6jzsfg20fp1qgh","message_type":"answer"},"create_time":1752122500828,"update_time":1752122500828,"status":"finished_successfully"},"b09bd3b2-5e1a-4af7-8c26-410c26e8d55f":{"id":"b09bd3b2-5e1a-4af7-8c26-410c26e8d55f","author":{"role":"tool","name":"canvas_close_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已关闭画布\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"48b1bba5-813a-4ab9-ae9f-47eb39327720","tool_call_id":"call_dtouxt4oj8hosfdma999vjg5","message_type":"answer"},"create_time":1752122550358,"update_time":1752122550358,"status":"finished_successfully"},"fe72ddcd-66f5-ee1f-ce02-22e1b907577f":{"id":"fe72ddcd-66f5-ee1f-ce02-22e1b907577f","author":{"role":"user"},"content":{"content_type":"","parts":[],"content_parts":[{"text":"生成3张小猫的图片"}]},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"78556148-a721-4d50-2188-5f047bbef9b3","parent_message_id":"","metrics_extra":"{\\"userMessageId\\":\\"fe72ddcd-66f5-ee1f-ce02-22e1b907577f\\",\\"prompt\\":\\"生成3张小猫的图片\\",\\"isAddImage\\":0,\\"enterFrom\\":\\"click\\"}"},"create_time":1752122497118,"status":"finished_successfully"}},"user_id":3835562355599799,"version":"v30","metadata":{},"history_id":20846315544834,"summary":"已生成3张治愈系小猫场景图","scene":"creation_agent_v30","list_id":97536023042}}';
// 有本地工具调用，没有继续生成
exports.mockLocalConsumptionStr = '{"conversation":{"title":"生成十个猫的视频","create_time":1752134494413,"update_time":1752134499944,"conversation_id":"420ce080-7411-e56a-4d3b-cb8aa992b5ca","mapping":{"292f3513-cadd-4628-9800-23188ad9cb79":{"id":"292f3513-cadd-4628-9800-23188ad9cb79","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"420ce080-7411-e56a-4d3b-cb8aa992b5ca","parent_message_id":"fcd6b754-9b1c-a1dc-cff9-42062b99ffa6","tool_call_id":"","message_type":"answer"},"create_time":1752134498142,"update_time":1752134498142,"status":"finished_successfully","tool_calls":[{"id":"call_0y5w5xpd7eu1ktgyylwqingp","type":"client_function","func":{"name":"points_consumption_reminder","arguments":"{\\"reason\\":\\"生成10个视频将消耗较多积分\\"}"},"streaming":false}]},"fcd6b754-9b1c-a1dc-cff9-42062b99ffa6":{"id":"fcd6b754-9b1c-a1dc-cff9-42062b99ffa6","author":{"role":"user"},"content":{"content_type":"","parts":[],"content_parts":[{"text":"生成十个猫的视频"}]},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"420ce080-7411-e56a-4d3b-cb8aa992b5ca","parent_message_id":"","metrics_extra":"{\\"userMessageId\\":\\"fcd6b754-9b1c-a1dc-cff9-42062b99ffa6\\",\\"prompt\\":\\"生成十个猫的视频\\",\\"isAddImage\\":0,\\"enterFrom\\":\\"click\\"}"},"create_time":1752134494242,"status":"finished_successfully","tools":[{"id":"consumption_reminder","name":"points_consumption_reminder","description":"\\n  该工具用于向用户发出提示，提醒用户后续的生视频链路可能会消耗较多积分。**发出提示一定要通过该工具进行，不可以通过自然语言进行。**\\n\\n  ## 使用条件（满足任意一个条件就应该调用该工具提醒用户）\\n  1. 当用户消息里蕴含了**生成大于等于2个视频片段**的要求时\\n  2. 当用户消息里蕴含了**生成超过20秒的视频**的要求是\\n  ## 注意\\n  1. 每轮对话最多向用户发出一次提示即可，不需要重复提示。禁止重复多次调用该工具。\\n  2. 调用该工具之后应该立刻停止，等待工具返回结果后再调用生成工具，而不应该将该工具和其他工具一次性连续调用\\n  3. 调用该工具的时机在用户提出需求之后、调用其他任何工具之前。在一轮对话中，一旦调用了其他任何工具，例如“handoff_to_planner_agent”或者“canvas_open_new_canvas”，则绝不可以再调用该工具\\n  ## 例子\\n  * 用户说要生成20张图片，此时不要向用户发出提醒\\n  * 用户说要生成2个视频片段，此时需要向用户发出提醒\\n  * 用户要生成一个30秒的视频片段，由于每次调用生视频工具最多生成10秒的视频，因此至少要调用3次生视频工具，此时需要向用户发出提醒\\n  ","parameters":"{\\"type\\":\\"object\\",\\"properties\\":{\\"reason\\":{\\"type\\":\\"string\\",\\"description\\":\\"用20字以内简述调用该工具的原因\\"}},\\"required\\":[\\"reason\\"],\\"additionalProperties\\":false,\\"$schema\\":\\"http://json-schema.org/draft-07/schema#\\"}","type":"client_function"}]}},"user_id":3835562355599799,"version":"v30","metadata":{},"history_id":20873134080770,"summary":"生成10个猫主题视频需消耗较多积分","scene":"creation_agent_v30","list_id":100300800002}}';
// 有本地工具调用，有继续生成
exports.mockLocalServerContinueGenerateStr = '{"conversation":{"title":"生成二狗视频积分提醒","create_time":1752137422455,"update_time":1752137485319,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","mapping":{"0078c3a0-edb2-4c4e-bf5f-de1ac8cc41ff":{"id":"0078c3a0-edb2-4c4e-bf5f-de1ac8cc41ff","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"3893a84d-ac3b-4380-a564-2e6041798006_toolcall_call_zy2z0znugjwmf59fv2b6a64r","tool_call_id":"","message_type":"answer"},"create_time":1752137443315,"update_time":1752137443315,"status":"finished_successfully","tool_calls":[{"id":"call_45wkissnblyj0qr24cno67c8","type":"server_function","func":{"name":"canvas_open_new_canvas","arguments":"{\\"canvas_type\\":\\"multi_image\\",\\"num_contents\\":2,\\"ratio\\":\\"1:1\\"}"},"streaming":false}]},"0986f711-306f-46a2-b464-2a7200b1f187":{"id":"0986f711-306f-46a2-b464-2a7200b1f187","author":{"role":"tool","name":"creative_agent_mcp_gen_image2video_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"llm_contents\\":\\"[{\\\\\\"resource_type\\\\\\":\\\\\\"video\\\\\\",\\\\\\"text\\\\\\":\\\\\\"视频任务已成功提交，请继续响应用户的后续需求。\\\\\\"}]\\",\\"history_id\\":\\"20838900363010\\",\\"commerce_info\\":{\\"credit_count\\":6,\\"triplets\\":[{\\"resource_type\\":\\"aigc\\",\\"resource_id\\":\\"generate_video\\",\\"benefit_type\\":\\"basic_video_operation_vgfm_v_three\\"}]},\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"7a8ca1bd-f761-4563-b86e-51c1c5b2efd7\\",\\"resource_type\\":\\"video\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"c5c20756-b52b-4e24-962b-a60bfdea990d","tool_call_id":"call_6pbv1x653o0ik8a1q1a1ucvu","message_type":"answer"},"create_time":1752137479413,"update_time":1752137479413,"status":"finished_successfully"},"266541eb-ed67-4a0a-8e90-8a0f22f523cf":{"id":"266541eb-ed67-4a0a-8e90-8a0f22f523cf","author":{"role":"tool","name":"canvas_open_new_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已成功创建 multi_video 类型的canvas，该类型的canvas中只可以插入视频。\\\\n### 注意\\\\n1. 你可以使用视频生成工具生成多个视频片段，并插入到该canvas中\\\\n2. 同一个电影的所有分镜一般来说应该具有相同的尺寸和画风，请务必在调用视频生成工具时注意将这种一致性体现在参数（提示词和比例参数）中\\\\n3. 同一个canvas中一般会需要插入多个内容，请以<|FunctionCallBegin|>[工具1，工具2，工具3...]<|FunctionCallEnd|> 的格式一次性输出所有的工具调用指令，以节省调用次数和时间\\\\n4. 在完成所有视频片段的插入后，请使用 \\\\\\"canvas_close_canvas\\\\\\" 工具关闭canvas并保存内容\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"d9ef6d52-08ae-49da-80fe-af9c25791c68","tool_call_id":"call_53u6l7t4jagfvrulccol4rdn","message_type":"answer"},"create_time":1752137472754,"update_time":1752137472754,"status":"finished_successfully"},"3534b36c-7e00-4b9e-a09b-dacde5e36921":{"id":"3534b36c-7e00-4b9e-a09b-dacde5e36921","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"266541eb-ed67-4a0a-8e90-8a0f22f523cf","tool_call_id":"","message_type":"answer"},"create_time":1752137475441,"update_time":1752137475441,"status":"finished_successfully","tool_calls":[{"id":"call_7v8zo3l4kny8evxtoq41k62w","type":"server_function","func":{"name":"creative_agent_mcp_gen_image2video_v3","arguments":" {\\"canvas_content_id\\": 1, \\"first_frame_resource_id\\": \\"image_5716485130754\\", \\"prompt\\": \\"拉布拉多快速奔跑，飞盘在口中晃动，耳朵向后扬起，草地风吹草动\\", \\"duration\\": 3}"},"streaming":true},{"id":"call_6pbv1x653o0ik8a1q1a1ucvu","type":"server_function","func":{"name":"creative_agent_mcp_gen_image2video_v3","arguments":" {\\"canvas_content_id\\": 2, \\"first_frame_resource_id\\": \\"image_5716485130498\\", \\"prompt\\": \\"比熊犬舔舐爪子，奶油缓慢滴落，生日帽轻微晃动，尾巴小幅度摇摆\\", \\"duration\\": 3}"},"streaming":false}]},"3893a84d-ac3b-4380-a564-2e6041798006":{"id":"3893a84d-ac3b-4380-a564-2e6041798006","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"7124a02b-5116-de79-43b9-e04d5063e762","tool_call_id":"","message_type":"answer"},"create_time":1752137425376,"update_time":1752137425376,"status":"finished_successfully","tool_calls":[{"id":"call_zy2z0znugjwmf59fv2b6a64r","type":"client_function","func":{"name":"points_consumption_reminder","arguments":"{\\"reason\\":\\"生成2个视频将消耗较多积分\\"}"},"streaming":false}]},"3893a84d-ac3b-4380-a564-2e6041798006_toolcall_call_zy2z0znugjwmf59fv2b6a64r":{"id":"3893a84d-ac3b-4380-a564-2e6041798006_toolcall_call_zy2z0znugjwmf59fv2b6a64r","author":{"role":"tool"},"content":{"content_type":"","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"继续生成\\"}]}"}]},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"3893a84d-ac3b-4380-a564-2e6041798006","tool_call_id":"call_zy2z0znugjwmf59fv2b6a64r"},"create_time":1752137440563,"tools":[{"id":"consumption_reminder","name":"points_consumption_reminder","description":"\\n  该工具用于向用户发出提示，提醒用户后续的生视频链路可能会消耗较多积分。**发出提示一定要通过该工具进行，不可以通过自然语言进行。**\\n\\n  ## 使用条件（满足任意一个条件就应该调用该工具提醒用户）\\n  1. 当用户消息里蕴含了**生成大于等于2个视频片段**的要求时\\n  2. 当用户消息里蕴含了**生成超过20秒的视频**的要求是\\n  ## 注意\\n  1. 每轮对话最多向用户发出一次提示即可，不需要重复提示。禁止重复多次调用该工具。\\n  2. 调用该工具之后应该立刻停止，等待工具返回结果后再调用生成工具，而不应该将该工具和其他工具一次性连续调用\\n  3. 调用该工具的时机在用户提出需求之后、调用其他任何工具之前。在一轮对话中，一旦调用了其他任何工具，例如“handoff_to_planner_agent”或者“canvas_open_new_canvas”，则绝不可以再调用该工具\\n  ## 例子\\n  * 用户说要生成20张图片，此时不要向用户发出提醒\\n  * 用户说要生成2个视频片段，此时需要向用户发出提醒\\n  * 用户要生成一个30秒的视频片段，由于每次调用生视频工具最多生成10秒的视频，因此至少要调用3次生视频工具，此时需要向用户发出提醒\\n  ","parameters":"{\\"type\\":\\"object\\",\\"properties\\":{\\"reason\\":{\\"type\\":\\"string\\",\\"description\\":\\"用20字以内简述调用该工具的原因\\"}},\\"required\\":[\\"reason\\"],\\"additionalProperties\\":false,\\"$schema\\":\\"http://json-schema.org/draft-07/schema#\\"}","type":"client_function"}]},"7124a02b-5116-de79-43b9-e04d5063e762":{"id":"7124a02b-5116-de79-43b9-e04d5063e762","author":{"role":"user"},"content":{"content_type":"","parts":[],"content_parts":[{"text":"生成两只狗的视频，积分提醒"}]},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"","metrics_extra":"{\\"userMessageId\\":\\"7124a02b-5116-de79-43b9-e04d5063e762\\",\\"prompt\\":\\"生成两只狗的视频，积分提醒\\",\\"isAddImage\\":0,\\"enterFrom\\":\\"click\\"}"},"create_time":1752137422393,"status":"finished_successfully","tools":[{"id":"consumption_reminder","name":"points_consumption_reminder","description":"\\n  该工具用于向用户发出提示，提醒用户后续的生视频链路可能会消耗较多积分。**发出提示一定要通过该工具进行，不可以通过自然语言进行。**\\n\\n  ## 使用条件（满足任意一个条件就应该调用该工具提醒用户）\\n  1. 当用户消息里蕴含了**生成大于等于2个视频片段**的要求时\\n  2. 当用户消息里蕴含了**生成超过20秒的视频**的要求是\\n  ## 注意\\n  1. 每轮对话最多向用户发出一次提示即可，不需要重复提示。禁止重复多次调用该工具。\\n  2. 调用该工具之后应该立刻停止，等待工具返回结果后再调用生成工具，而不应该将该工具和其他工具一次性连续调用\\n  3. 调用该工具的时机在用户提出需求之后、调用其他任何工具之前。在一轮对话中，一旦调用了其他任何工具，例如“handoff_to_planner_agent”或者“canvas_open_new_canvas”，则绝不可以再调用该工具\\n  ## 例子\\n  * 用户说要生成20张图片，此时不要向用户发出提醒\\n  * 用户说要生成2个视频片段，此时需要向用户发出提醒\\n  * 用户要生成一个30秒的视频片段，由于每次调用生视频工具最多生成10秒的视频，因此至少要调用3次生视频工具，此时需要向用户发出提醒\\n  ","parameters":"{\\"type\\":\\"object\\",\\"properties\\":{\\"reason\\":{\\"type\\":\\"string\\",\\"description\\":\\"用20字以内简述调用该工具的原因\\"}},\\"required\\":[\\"reason\\"],\\"additionalProperties\\":false,\\"$schema\\":\\"http://json-schema.org/draft-07/schema#\\"}","type":"client_function"}]},"7c4ea137-3d31-4548-a7b9-55ed21ed9629":{"id":"7c4ea137-3d31-4548-a7b9-55ed21ed9629","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"f811800d-5690-4f39-8149-dcb41d46da26","tool_call_id":"","message_type":"answer"},"create_time":1752137469419,"update_time":1752137469419,"status":"finished_successfully","tool_calls":[{"id":"call_e61pmwoseuhsug379hyex8wg","type":"server_function","func":{"name":"canvas_close_canvas","arguments":"{\\"noArgs\\":\\"\\"}"},"streaming":false}]},"82bc6199-a1ef-49b2-9b9a-90b952a704fd":{"id":"82bc6199-a1ef-49b2-9b9a-90b952a704fd","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"您的2个治愈系小狗视频已提交生成，包含拉布拉多叼飞盘奔跑（3秒）和比熊犬舔奶油（3秒）场景。生成完成后即可查看，需要调整随时告诉我！"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"fd733299-799d-4409-ac0c-6537d628602b","tool_call_id":"","message_type":"answer"},"create_time":1752137484141,"update_time":1752137484141,"status":"finished_successfully"},"83ac63cd-3926-42e8-b3f7-a1584e16159d":{"id":"83ac63cd-3926-42e8-b3f7-a1584e16159d","author":{"role":"tool","name":"canvas_close_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已关闭画布\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"7c4ea137-3d31-4548-a7b9-55ed21ed9629","tool_call_id":"call_e61pmwoseuhsug379hyex8wg","message_type":"answer"},"create_time":1752137469615,"update_time":1752137469615,"status":"finished_successfully"},"911b42ce-41e2-40d7-8fad-b504134b6646":{"id":"911b42ce-41e2-40d7-8fad-b504134b6646","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"a6bdfc01-9128-4482-8e39-f461470c745b","tool_call_id":"","message_type":"answer"},"create_time":1752137446521,"update_time":1752137446521,"status":"finished_successfully","tool_calls":[{"id":"call_644wmp8k4wuxl42xde1nbvbp","type":"server_function","func":{"name":"creative_agent_mcp_gen_text2image_v3","arguments":" {\\"canvas_content_id\\": 1, \\"prompt\\": \\"治愈系风格拉布拉多幼犬叼着黄色飞盘，棕色短毛，黑色鼻子，户外草坪背景，阳光明媚，动态奔跑姿势\\", \\"ratio\\": \\"1:1\\", \\"version\\": \\"通用版\\"}","extra":{"resource_cnt":"4","resource_type":"image"}},"streaming":true},{"id":"call_ugntldbu44v5q97sk4ruy7iq","type":"server_function","func":{"name":"creative_agent_mcp_gen_text2image_v3","arguments":" {\\"canvas_content_id\\": 2, \\"prompt\\": \\"治愈系风格比熊犬在蛋糕旁舔爪子，白色卷毛，黑色圆眼，生日帽歪戴，奶油滴落，室内暖光温馨氛围\\", \\"ratio\\": \\"1:1\\", \\"version\\": \\"通用版\\"}","extra":{"resource_cnt":"4","resource_type":"image"}},"streaming":false}]},"a6bdfc01-9128-4482-8e39-f461470c745b":{"id":"a6bdfc01-9128-4482-8e39-f461470c745b","author":{"role":"tool","name":"canvas_open_new_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已成功创建 multi_image 类型的canvas，该类型的canvas中只可以插入图片\\\\n### 注意\\\\n1. 你可以使用图片生成工具生成多张图片，并插入到该canvas中\\\\n2. 一组图片一般来说会具备某种一致性，例如相同的画风、尺寸、角色、艺术符号等，请根据用户需求思考如何将这种一致性体现在提示词和图片比例参数中\\\\n3. 同一个canvas中一般会需要插入多个内容，请以<|FunctionCallBegin|>[工具1，工具2，工具3...]<|FunctionCallEnd|> 的格式一次性输出所有的工具调用指令，以节省调用次数和时间\\\\n4. 在完成所有图片的插入后，请使用 \\\\\\"canvas_close_canvas\\\\\\" 工具关闭canvas并保存内容\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"0078c3a0-edb2-4c4e-bf5f-de1ac8cc41ff","tool_call_id":"call_45wkissnblyj0qr24cno67c8","message_type":"answer"},"create_time":1752137443857,"update_time":1752137443857,"status":"finished_successfully"},"b621a4ce-4a28-4fc3-a852-7f6d08c9f6f8":{"id":"b621a4ce-4a28-4fc3-a852-7f6d08c9f6f8","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"0986f711-306f-46a2-b464-2a7200b1f187","tool_call_id":"","message_type":"answer"},"create_time":1752137481859,"update_time":1752137481859,"status":"finished_successfully","tool_calls":[{"id":"call_b0n7n2fjtr6ox92ma0yo0ww2","type":"server_function","func":{"name":"canvas_close_canvas","arguments":"{\\"noArgs\\":\\"\\"}"},"streaming":false}]},"c5c20756-b52b-4e24-962b-a60bfdea990d":{"id":"c5c20756-b52b-4e24-962b-a60bfdea990d","author":{"role":"tool","name":"creative_agent_mcp_gen_image2video_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"llm_contents\\":\\"[{\\\\\\"resource_type\\\\\\":\\\\\\"video\\\\\\",\\\\\\"text\\\\\\":\\\\\\"视频任务已成功提交，请继续响应用户的后续需求。\\\\\\"}]\\",\\"history_id\\":\\"20837241425922\\",\\"commerce_info\\":{\\"credit_count\\":6,\\"triplets\\":[{\\"resource_type\\":\\"aigc\\",\\"resource_id\\":\\"generate_video\\",\\"benefit_type\\":\\"basic_video_operation_vgfm_v_three\\"}]},\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"48d65fa0-5626-48ae-807a-9c551ba58236\\",\\"resource_type\\":\\"video\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"3534b36c-7e00-4b9e-a09b-dacde5e36921","tool_call_id":"call_7v8zo3l4kny8evxtoq41k62w","message_type":"answer"},"create_time":1752137479337,"update_time":1752137479337,"status":"finished_successfully"},"d9ef6d52-08ae-49da-80fe-af9c25791c68":{"id":"d9ef6d52-08ae-49da-80fe-af9c25791c68","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"83ac63cd-3926-42e8-b3f7-a1584e16159d","tool_call_id":"","message_type":"answer"},"create_time":1752137472296,"update_time":1752137472296,"status":"finished_successfully","tool_calls":[{"id":"call_53u6l7t4jagfvrulccol4rdn","type":"server_function","func":{"name":"canvas_open_new_canvas","arguments":"{\\"canvas_type\\":\\"multi_video\\",\\"num_contents\\":2,\\"ratio\\":\\"1:1\\"}"},"streaming":false}]},"e11550e5-17ed-4421-87e9-06dc0ab2825f":{"id":"e11550e5-17ed-4421-87e9-06dc0ab2825f","author":{"role":"assistant","name":"host"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":""}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"82bc6199-a1ef-49b2-9b9a-90b952a704fd","message_type":"answer"},"create_time":1752137485290,"update_time":1752137485290,"status":"finished_successfully","end_turn":true},"e51d10c3-3193-4be2-994b-0c0d3f12a002":{"id":"e51d10c3-3193-4be2-994b-0c0d3f12a002","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"contents\\":[{\\"id\\":\\"image_5716485130754\\",\\"type\\":\\"resource\\",\\"resource\\":{\\"mimeType\\":\\"image/jpeg\\",\\"uri\\":\\"\\"}}],\\"llm_contents\\":\\"已成功生成1组（共1张）图片。\\\\n[{\\\\\\"resource_id\\\\\\":\\\\\\"image_5716485130754\\\\\\",\\\\\\"resource_type\\\\\\":\\\\\\"image\\\\\\",\\\\\\"text\\\\\\":\\\\\\"生成成功\\\\\\"}]\\",\\"history_id\\":\\"20837241423874\\",\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"f1bcce63-0abf-42b5-928f-71d17fff56ac\\",\\"result_code\\":0,\\"resource_type\\":\\"image\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"911b42ce-41e2-40d7-8fad-b504134b6646","tool_call_id":"call_644wmp8k4wuxl42xde1nbvbp","message_type":"answer"},"create_time":1752137451349,"update_time":1752137451349,"status":"finished_successfully"},"f811800d-5690-4f39-8149-dcb41d46da26":{"id":"f811800d-5690-4f39-8149-dcb41d46da26","author":{"role":"tool","name":"creative_agent_mcp_gen_text2image_v3"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"contents\\":[{\\"id\\":\\"image_5716485130498\\",\\"type\\":\\"resource\\",\\"resource\\":{\\"mimeType\\":\\"image/jpeg\\",\\"uri\\":\\"\\"}}],\\"llm_contents\\":\\"已成功生成1组（共1张）图片。\\\\n[{\\\\\\"resource_id\\\\\\":\\\\\\"image_5716485130498\\\\\\",\\\\\\"resource_type\\\\\\":\\\\\\"image\\\\\\",\\\\\\"text\\\\\\":\\\\\\"生成成功\\\\\\"}]\\",\\"history_id\\":\\"20837241423618\\",\\"commerce_info\\":{\\"credit_count\\":2,\\"triplets\\":[{\\"resource_type\\":\\"aigc\\",\\"resource_id\\":\\"generate_img\\",\\"benefit_type\\":\\"image_uhd\\"}]},\\"submit_info\\":{\\"code\\":0,\\"msg\\":\\"\\"},\\"submit_id\\":\\"2a220618-fd76-46a8-add0-a9939e7e6f1c\\",\\"result_code\\":0,\\"resource_type\\":\\"image\\",\\"end\\":true,\\"ratio\\":\\"1:1\\"}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"e51d10c3-3193-4be2-994b-0c0d3f12a002","tool_call_id":"call_ugntldbu44v5q97sk4ruy7iq","message_type":"answer"},"create_time":1752137467126,"update_time":1752137467126,"status":"finished_successfully"},"fd733299-799d-4409-ac0c-6537d628602b":{"id":"fd733299-799d-4409-ac0c-6537d628602b","author":{"role":"tool","name":"canvas_close_canvas"},"content":{"content_type":"text","parts":[],"content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"已关闭画布\\"}]}"}],"reasoning_content":""},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"cf3af5c9-ce37-ac43-0a30-b8361b28baf3","parent_message_id":"b621a4ce-4a28-4fc3-a852-7f6d08c9f6f8","tool_call_id":"call_b0n7n2fjtr6ox92ma0yo0ww2","message_type":"answer"},"create_time":1752137482080,"update_time":1752137482080,"status":"finished_successfully"}},"user_id":3835562355599799,"version":"v30","metadata":{},"history_id":20872273929986,"summary":"因未获取到具体生成的视频相关资源等详细产物信息，暂无法准确按要求总结，可补充更多生成结果相关内容再进一步总结。","scene":"creation_agent_v30","list_id":110131206914}}';
//# sourceMappingURL=conversation_manager_endpoint.js.map