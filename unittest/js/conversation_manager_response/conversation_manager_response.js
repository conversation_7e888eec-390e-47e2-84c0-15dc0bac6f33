"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationManagerResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const conversation_manager_endpoint_1 = require("./conversation_manager_endpoint");
const utils_1 = require("../utils");
const tsnfoundation_1 = require("@byted/tsnfoundation");
class ConversationManagerResponse extends utils_1.TestCase {
    constructor() {
        super("conversation_manager_response");
        this.initSettings();
    }
    initSettings() {
        nurosdk_1.NuroSetting.version = "3.0.0";
        nurosdk_1.NuroSetting.canvasSettings = {
            startNode: "canvas_open_new_canvas",
            endNode: "close_canvas",
            nodes: [
                "creative_agent_mcp_gen_text2image_v3",
                "creative_agent_mcp_gen_image2image_v3",
                "creative_agent_mcp_gen_image2video_v3",
                "creative_agent_mcp_gen_text2video_v3",
                "creative_agent_mcp_gen_start_end2video_v3",
            ],
        };
    }
    run() {
        // 普通画布消息
        this.testCanvasMessage();
        // 本地工具调用，没有继续生成
        this.testLocalMessage();
        // 本地工具调用，有继续生成
        this.testLocalContinueGenerateMessage();
        this.resolve();
    }
    testCanvasMessage() {
        var _a, _b, _c;
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.conversation.taskChecker = new nurosdk_1.NuroTaskChecker();
        conversationManager.decodeConversationFromJSONString(conversation_manager_endpoint_1.mockConversationJSONStr, false);
        if (conversationManager.conversation.conversationId !==
            "78556148-a721-4d50-2188-5f047bbef9b3") {
            this.reject("conversationId should be '78556148-a721-4d50-2188-5f047bbef9b3' but now is " +
                conversationManager.conversation.conversationId);
        }
        if (conversationManager.conversation.summary !== "已生成3张治愈系小猫场景图") {
            this.reject("已生成3张治愈系小猫场景图'");
        }
        if (conversationManager.conversation.messages.length !== 5) {
            this.reject("should have 5 messages");
            return;
        }
        const tasks = conversationManager.conversation.tasks;
        if (tasks.length !== 1) {
            this.reject("should have 1 task");
            return;
        }
        const task = tasks[0];
        if ((task === null || task === void 0 ? void 0 : task.taskStatus) !== nurosdk_1.NuroTaskStatus.finished) {
            this.reject("task status should be finished'");
        }
        if ((task === null || task === void 0 ? void 0 : task.artifactMessages.length) !== 4) {
            this.reject("task artifact messages should be 4'");
        }
        const firstMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[0];
        if (firstMessage === undefined) {
            this.reject("first messages should be not be empty! ");
        }
        if (!(firstMessage instanceof nurosdk_1.NuroAssistantMessage)) {
            this.reject("first messages should be assistantMessage! ");
        }
        if (firstMessage.text !==
            "我将为您生成3张不同场景的小猫图片，包括花园玩耍、窗台打盹和纸箱探险的温馨画面，保持一致的治愈系风格。") {
            this.reject("first messages text failed, should be 我将为您生成3张不同场景的小猫图片，包括花园玩耍、窗台打盹和纸箱探险的温馨画面，保持一致的治愈系风格。 but now is " +
                firstMessage.text);
        }
        const secondMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[1];
        if (secondMessage === undefined) {
            this.reject("canvas messages should be not be empty! ");
        }
        if (!(secondMessage instanceof nurosdk_1.NuroCanvasMessage)) {
            this.reject("first messages should be NuroCanvasMessage! ");
        }
        if (secondMessage.status !== nurosdk_1.NuroCanvasStatus.end) {
            this.reject("first messages status failed, should be end but now is " +
                secondMessage.status);
        }
        if (secondMessage.nodes === undefined) {
            this.reject("first messages nodes should not be empty! ");
        }
        if (((_a = secondMessage.nodes) === null || _a === void 0 ? void 0 : _a.length) !== 3) {
            this.reject("first messages nodes length should be 3 , but now is " +
                ((_b = secondMessage.nodes) === null || _b === void 0 ? void 0 : _b.length));
        }
        (_c = secondMessage.nodes) === null || _c === void 0 ? void 0 : _c.forEach((node, index) => {
            if ((node === null || node === void 0 ? void 0 : node.toolResult) === undefined) {
                this.reject("first messages nodes should have result ! but now have no result ! index is " +
                    index);
            }
        });
        tsnfoundation_1.TSNConsole.log("pass canvas message test!");
    }
    testLocalMessage() {
        tsnfoundation_1.TSNConsole.log("begin local tool message test!");
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.conversation.taskChecker = new nurosdk_1.NuroTaskChecker();
        conversationManager.decodeConversationFromJSONString(conversation_manager_endpoint_1.mockLocalConsumptionStr, false);
        if (conversationManager.conversation.conversationId !==
            "420ce080-7411-e56a-4d3b-cb8aa992b5ca") {
            this.reject("conversationId should be '420ce080-7411-e56a-4d3b-cb8aa992b5ca'");
        }
        if (conversationManager.conversation.summary !==
            "生成10个猫主题视频需消耗较多积分") {
            this.reject("生成10个猫主题视频需消耗较多积分'");
        }
        if (conversationManager.conversation.messages.length !== 2) {
            this.reject("should have 2 messages");
            return;
        }
        const tasks = conversationManager.conversation.tasks;
        if (tasks.length !== 1) {
            this.reject("should have 1 task");
            return;
        }
        const task = tasks[0];
        if ((task === null || task === void 0 ? void 0 : task.taskStatus) !== nurosdk_1.NuroTaskStatus.finished) {
            this.reject("task status should be finished'");
        }
        if ((task === null || task === void 0 ? void 0 : task.artifactMessages.length) !== 1) {
            this.reject("task artifact messages should be 1 ");
        }
        const firstMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[0];
        if (firstMessage === undefined) {
            this.reject("canvas messages should be not be empty! ");
        }
        if (!(firstMessage instanceof nurosdk_1.NuroToolCallMessage)) {
            this.reject("first messages should be NuroToolCallMessage! ");
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.messageStatus) !==
            nurosdk_1.NuroToolCallMessageStatus.skipped) {
            this.reject("first messages status failed, should be skipped but now is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.messageStatus));
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolType) !== "client_function") {
            this.reject("first messages toolType failed, should be client_function but now is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolType));
        }
        tsnfoundation_1.TSNConsole.log("pass local tool message test!");
    }
    testLocalContinueGenerateMessage() {
        var _a, _b, _c, _d, _e, _f;
        tsnfoundation_1.TSNConsole.log("begin local tool message continue generate test!");
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.conversation.taskChecker = new nurosdk_1.NuroTaskChecker();
        conversationManager.decodeConversationFromJSONString(conversation_manager_endpoint_1.mockLocalServerContinueGenerateStr, false);
        if (conversationManager.conversation.conversationId !==
            "cf3af5c9-ce37-ac43-0a30-b8361b28baf3") {
            this.reject("conversationId should be 'cf3af5c9-ce37-ac43-0a30-b8361b28baf3'");
        }
        if (conversationManager.conversation.summary !==
            "因未获取到具体生成的视频相关资源等详细产物信息，暂无法准确按要求总结，可补充更多生成结果相关内容再进一步总结。") {
            this.reject("因未获取到具体生成的视频相关资源等详细产物信息，暂无法准确按要求总结，可补充更多生成结果相关内容再进一步总结。'");
        }
        if (conversationManager.conversation.messages.length !== 6) {
            this.reject("should have 6 messages");
            return;
        }
        const tasks = conversationManager.conversation.tasks;
        if (tasks.length !== 1) {
            this.reject("should have 1 task");
            return;
        }
        const task = tasks[0];
        if ((task === null || task === void 0 ? void 0 : task.taskStatus) !== nurosdk_1.NuroTaskStatus.finished) {
            this.reject("task status should be finished'");
        }
        if ((task === null || task === void 0 ? void 0 : task.artifactMessages.length) !== 5) {
            this.reject("task artifact messages should be 5 ");
        }
        const firstMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[0];
        if (firstMessage === undefined) {
            this.reject("canvas messages should be not be empty! ");
        }
        if (!(firstMessage instanceof nurosdk_1.NuroToolCallMessage)) {
            this.reject("first messages should be NuroToolCallMessage! ");
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.messageStatus) !==
            nurosdk_1.NuroToolCallMessageStatus.finished_successfully) {
            this.reject("first messages status failed, should be skipped but now is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.messageStatus));
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolType) !== "client_function") {
            this.reject("first messages toolType failed, should be client_function but now is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolType));
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolResult) !==
            '{"content":[{"type":"text","text":"继续生成"}]}') {
            this.reject("first messages toolResult not matched ! now tool result is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolResult));
        }
        if ((firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolResult) !==
            '{"content":[{"type":"text","text":"继续生成"}]}') {
            this.reject("first messages toolResult not matched ! now tool result is " +
                (firstMessage === null || firstMessage === void 0 ? void 0 : firstMessage.toolResult));
        }
        const secondMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[1];
        if (secondMessage === undefined) {
            this.reject("second messages should be not be empty! ");
        }
        if (!(secondMessage instanceof nurosdk_1.NuroCanvasMessage)) {
            this.reject("second messages should be NuroCanvasMessage! ");
        }
        if ((secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.status) !== nurosdk_1.NuroCanvasStatus.end) {
            this.reject("second messages status not matched ! should be end , now status is " +
                (secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.status));
        }
        if (((_a = secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.nodes) === null || _a === void 0 ? void 0 : _a.length) !== 2) {
            this.reject("second messages nodes length not matched ! should be 2 , now length is " +
                ((_b = secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.nodes) === null || _b === void 0 ? void 0 : _b.length));
        }
        (_c = secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.nodes) === null || _c === void 0 ? void 0 : _c.forEach((node, index) => {
            if ((node === null || node === void 0 ? void 0 : node.toolResult) === "") {
                this.reject("second messages nodes toolResult should not be empty! index is " +
                    index);
            }
        });
        const thirdMessage = task === null || task === void 0 ? void 0 : task.artifactMessages[2];
        if (thirdMessage === undefined) {
            this.reject("second messages should be not be empty! ");
        }
        if (!(thirdMessage instanceof nurosdk_1.NuroCanvasMessage)) {
            this.reject("second messages should be NuroCanvasMessage! ");
        }
        if ((thirdMessage === null || thirdMessage === void 0 ? void 0 : thirdMessage.status) !== nurosdk_1.NuroCanvasStatus.end) {
            this.reject("second messages status not matched ! should be end , now status is " +
                (secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.status));
        }
        if (((_d = thirdMessage === null || thirdMessage === void 0 ? void 0 : thirdMessage.nodes) === null || _d === void 0 ? void 0 : _d.length) !== 2) {
            this.reject("second messages nodes length not matched ! should be 2 , now length is " +
                ((_e = secondMessage === null || secondMessage === void 0 ? void 0 : secondMessage.nodes) === null || _e === void 0 ? void 0 : _e.length));
        }
        (_f = thirdMessage === null || thirdMessage === void 0 ? void 0 : thirdMessage.nodes) === null || _f === void 0 ? void 0 : _f.forEach((node, index) => {
            if ((node === null || node === void 0 ? void 0 : node.toolResult) === "") {
                this.reject("second messages nodes toolResult should not be empty! index is " +
                    index);
            }
        });
        tsnfoundation_1.TSNConsole.log("pass local tool message test!");
    }
}
exports.ConversationManagerResponse = ConversationManagerResponse;
//# sourceMappingURL=conversation_manager_response.js.map