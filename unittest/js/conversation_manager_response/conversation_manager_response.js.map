{"version": 3, "file": "conversation_manager_response.js", "sourceRoot": "", "sources": ["../../src/conversation_manager_response/conversation_manager_response.ts"], "names": [], "mappings": ";;;AAAA,4CAYwB;AACxB,mFAIyC;AACzC,oCAAoC;AACpC,wDAAkD;AAElD,MAAa,2BAA4B,SAAQ,gBAAQ;IACvD;QACE,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACvC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED,YAAY;QACV,qBAAW,CAAC,OAAO,GAAG,OAAO,CAAC;QAC9B,qBAAW,CAAC,cAAc,GAAG;YAC3B,SAAS,EAAE,wBAAwB;YACnC,OAAO,EAAE,cAAc;YACvB,KAAK,EAAE;gBACL,sCAAsC;gBACtC,uCAAuC;gBACvC,uCAAuC;gBACvC,sCAAsC;gBACtC,2CAA2C;aAC5C;SACF,CAAC;IACJ,CAAC;IAED,GAAG;QACD,SAAS;QACT,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,gBAAgB;QAChB,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACxB,eAAe;QACf,IAAI,CAAC,gCAAgC,EAAE,CAAC;QAExC,IAAI,CAAC,OAAO,EAAE,CAAC;IACjB,CAAC;IAEO,iBAAiB;;QACvB,MAAM,mBAAmB,GAAG,IAAI,iCAAmB,EAAE,CAAC;QACtD,mBAAmB,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,yBAAe,EAAE,CAAC;QACrE,mBAAmB,CAAC,gCAAgC,CAClD,uDAAuB,EACvB,KAAK,CACN,CAAC;QACF,IACE,mBAAmB,CAAC,YAAY,CAAC,cAAc;YAC/C,sCAAsC,EACtC;YACA,IAAI,CAAC,MAAM,CACT,6EAA6E;gBAC3E,mBAAmB,CAAC,YAAY,CAAC,cAAc,CAClD,CAAC;SACH;QACD,IACE,mBAAmB,CAAC,YAAY,CAAC,OAAO,KAAK,eAAe,EAC5D;YACA,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;SAC/B;QACD,IAAI,mBAAmB,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;YACtC,OAAO;SACR;QAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC;QACrD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO;SACR;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,MAAK,wBAAc,CAAC,QAAQ,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC;SAChD;QAED,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,MAAM,MAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC;SACpD;QAED,MAAM,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,yCAAyC,CAAC,CAAC;SACxD;QACD,IAAI,CAAC,CAAC,YAAY,YAAY,8BAAoB,CAAC,EAAE;YACnD,IAAI,CAAC,MAAM,CAAC,6CAA6C,CAAC,CAAC;SAC5D;QACD,IACG,YAAqC,CAAC,IAAI;YAC3C,qDAAqD,EACrD;YACA,IAAI,CAAC,MAAM,CACT,uGAAuG;gBACpG,YAAqC,CAAC,IAAI,CAC9C,CAAC;SACH;QAED,MAAM,aAAa,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,CAAC,aAAa,YAAY,2BAAiB,CAAC,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC;SAC7D;QAED,IAAK,aAAmC,CAAC,MAAM,KAAK,0BAAgB,CAAC,GAAG,EAAE;YACxE,IAAI,CAAC,MAAM,CACT,yDAAyD;gBACtD,aAAmC,CAAC,MAAM,CAC9C,CAAC;SACH;QAED,IAAK,aAAmC,CAAC,KAAK,KAAK,SAAS,EAAE;YAC5D,IAAI,CAAC,MAAM,CAAC,4CAA4C,CAAC,CAAC;SAC3D;QAED,IAAI,CAAA,MAAC,aAAmC,CAAC,KAAK,0CAAE,MAAM,MAAK,CAAC,EAAE;YAC5D,IAAI,CAAC,MAAM,CACT,uDAAuD;iBACrD,MAAC,aAAmC,CAAC,KAAK,0CAAE,MAAM,CAAA,CACrD,CAAC;SACH;QAED,MAAC,aAAmC,CAAC,KAAK,0CAAE,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAClE,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,MAAK,SAAS,EAAE;gBAClC,IAAI,CAAC,MAAM,CACT,8EAA8E;oBAC5E,KAAK,CACR,CAAC;aACH;QACH,CAAC,CAAC,CAAC;QACH,0BAAU,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC9C,CAAC;IAEO,gBAAgB;QACtB,0BAAU,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QACjD,MAAM,mBAAmB,GAAG,IAAI,iCAAmB,EAAE,CAAC;QACtD,mBAAmB,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,yBAAe,EAAE,CAAC;QACrE,mBAAmB,CAAC,gCAAgC,CAClD,uDAAuB,EACvB,KAAK,CACN,CAAC;QAEF,IACE,mBAAmB,CAAC,YAAY,CAAC,cAAc;YAC/C,sCAAsC,EACtC;YACA,IAAI,CAAC,MAAM,CACT,iEAAiE,CAClE,CAAC;SACH;QACD,IACE,mBAAmB,CAAC,YAAY,CAAC,OAAO;YACxC,mBAAmB,EACnB;YACA,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;SACnC;QAED,IAAI,mBAAmB,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;YACtC,OAAO;SACR;QAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC;QACrD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO;SACR;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,MAAK,wBAAc,CAAC,QAAQ,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC;SAChD;QAED,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,MAAM,MAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC;SACpD;QAED,MAAM,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,CAAC,YAAY,YAAY,6BAAmB,CAAC,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC;SAC/D;QAED,IACE,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,aAAa;YACpD,mCAAyB,CAAC,OAAO,EACjC;YACA,IAAI,CAAC,MAAM,CACT,6DAA6D;iBAC1D,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,aAAa,CAAA,CACvD,CAAC;SACH;QAED,IAAI,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,QAAQ,MAAK,iBAAiB,EAAE;YACzE,IAAI,CAAC,MAAM,CACT,uEAAuE;iBACpE,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,QAAQ,CAAA,CAClD,CAAC;SACH;QACD,0BAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAClD,CAAC;IAEO,gCAAgC;;QACtC,0BAAU,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QACnE,MAAM,mBAAmB,GAAG,IAAI,iCAAmB,EAAE,CAAC;QACtD,mBAAmB,CAAC,YAAY,CAAC,WAAW,GAAG,IAAI,yBAAe,EAAE,CAAC;QACrE,mBAAmB,CAAC,gCAAgC,CAClD,kEAAkC,EAClC,KAAK,CACN,CAAC;QAEF,IACE,mBAAmB,CAAC,YAAY,CAAC,cAAc;YAC/C,sCAAsC,EACtC;YACA,IAAI,CAAC,MAAM,CACT,iEAAiE,CAClE,CAAC;SACH;QACD,IACE,mBAAmB,CAAC,YAAY,CAAC,OAAO;YACxC,yDAAyD,EACzD;YACA,IAAI,CAAC,MAAM,CACT,0DAA0D,CAC3D,CAAC;SACH;QAED,IAAI,mBAAmB,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1D,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,CAAC;YACtC,OAAO;SACR;QAED,MAAM,KAAK,GAAG,mBAAmB,CAAC,YAAY,CAAC,KAAK,CAAC;QACrD,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;YAClC,OAAO;SACR;QAED,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QACtB,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,MAAK,wBAAc,CAAC,QAAQ,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,iCAAiC,CAAC,CAAC;SAChD;QAED,IAAI,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,MAAM,MAAK,CAAC,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,qCAAqC,CAAC,CAAC;SACpD;QAED,MAAM,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,CAAC,YAAY,YAAY,6BAAmB,CAAC,EAAE;YAClD,IAAI,CAAC,MAAM,CAAC,gDAAgD,CAAC,CAAC;SAC/D;QAED,IACE,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,aAAa;YACpD,mCAAyB,CAAC,qBAAqB,EAC/C;YACA,IAAI,CAAC,MAAM,CACT,6DAA6D;iBAC1D,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,aAAa,CAAA,CACvD,CAAC;SACH;QAED,IAAI,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,QAAQ,MAAK,iBAAiB,EAAE;YACzE,IAAI,CAAC,MAAM,CACT,uEAAuE;iBACpE,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,QAAQ,CAAA,CAClD,CAAC;SACH;QAED,IACE,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,UAAU;YACjD,6CAA6C,EAC7C;YACA,IAAI,CAAC,MAAM,CACT,6DAA6D;iBAC1D,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,UAAU,CAAA,CACpD,CAAC;SACH;QAED,IACE,CAAC,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,UAAU;YACjD,6CAA6C,EAC7C;YACA,IAAI,CAAC,MAAM,CACT,6DAA6D;iBAC1D,YAAoC,aAApC,YAAY,uBAAZ,YAAY,CAA0B,UAAU,CAAA,CACpD,CAAC;SACH;QAED,MAAM,aAAa,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAChD,IAAI,aAAa,KAAK,SAAS,EAAE;YAC/B,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,CAAC,aAAa,YAAY,2BAAiB,CAAC,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,+CAA+C,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,MAAM,MAAK,0BAAgB,CAAC,GAAG,EAAE;YACzE,IAAI,CAAC,MAAM,CACT,qEAAqE;iBAClE,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,MAAM,CAAA,CAC/C,CAAC;SACH;QACD,IAAI,CAAA,MAAC,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,KAAK,0CAAE,MAAM,MAAK,CAAC,EAAE;YAC7D,IAAI,CAAC,MAAM,CACT,yEAAyE;iBACvE,MAAC,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,KAAK,0CAAE,MAAM,CAAA,CACtD,CAAC;SACH;QACD,MAAC,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,KAAK,0CAAE,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YACnE,IAAI,CAAC,IAAuB,aAAvB,IAAI,uBAAJ,IAAI,CAAqB,UAAU,MAAK,EAAE,EAAE;gBAC/C,IAAI,CAAC,MAAM,CACT,iEAAiE;oBAC/D,KAAK,CACR,CAAC;aACH;QACH,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,YAAY,KAAK,SAAS,EAAE;YAC9B,IAAI,CAAC,MAAM,CAAC,0CAA0C,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,CAAC,YAAY,YAAY,2BAAiB,CAAC,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,+CAA+C,CAAC,CAAC;SAC9D;QACD,IAAI,CAAC,YAAkC,aAAlC,YAAY,uBAAZ,YAAY,CAAwB,MAAM,MAAK,0BAAgB,CAAC,GAAG,EAAE;YACxE,IAAI,CAAC,MAAM,CACT,qEAAqE;iBAClE,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,MAAM,CAAA,CAC/C,CAAC;SACH;QACD,IAAI,CAAA,MAAC,YAAkC,aAAlC,YAAY,uBAAZ,YAAY,CAAwB,KAAK,0CAAE,MAAM,MAAK,CAAC,EAAE;YAC5D,IAAI,CAAC,MAAM,CACT,yEAAyE;iBACvE,MAAC,aAAmC,aAAnC,aAAa,uBAAb,aAAa,CAAwB,KAAK,0CAAE,MAAM,CAAA,CACtD,CAAC;SACH;QACD,MAAC,YAAkC,aAAlC,YAAY,uBAAZ,YAAY,CAAwB,KAAK,0CAAE,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAClE,IAAI,CAAC,IAAuB,aAAvB,IAAI,uBAAJ,IAAI,CAAqB,UAAU,MAAK,EAAE,EAAE;gBAC/C,IAAI,CAAC,MAAM,CACT,iEAAiE;oBAC/D,KAAK,CACR,CAAC;aACH;QACH,CAAC,CAAC,CAAC;QAEH,0BAAU,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAClD,CAAC;CACF;AA3VD,kEA2VC"}