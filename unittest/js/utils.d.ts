import { EventStreamConfig, NuroAssistantMessage, NuroConversation, NuroMCPManager, NuroTask, NuroToolCallMessage, NuroCanvasMessage } from "@byted/nurosdk";
export declare class FailedTestInfo {
    functionName: string;
    errorMessage: string;
    constructor(functionName: string, errorMessage: string);
}
export declare class SuccessTestInfo {
    functionName: string;
    constructor(functionName: string);
}
export declare class TestContext {
    static globalContext: TestContext;
    private failedTests;
    private succeededTests;
    reportFailure(functionName: string, errorMessage: string): void;
    reportSuccess(functionName: string): void;
    getFailedTests(): FailedTestInfo[];
    getSucceededTests(): SuccessTestInfo[];
}
export declare class TestCase {
    name: string;
    resolve: () => void;
    reject: (errorMessage: string) => void;
    constructor(name: string);
    run(): void;
    delay(seconds: Double, cb: () => void): void;
}
export declare class TestDevice {
    static testMCPManager: Optional<NuroMCPManager>;
    private definedTests;
    defineTest(item: TestCase): void;
    runNextTest(onFinish: () => void): void;
}
export declare class TestChunkedServer {
    static singleton: TestChunkedServer;
    chunksParts: Array<Array<string>>;
    _pausedConfig: Optional<EventStreamConfig>;
    installNuroSDK(): void;
    setText(text: string): void;
    setTexts(texts: string[]): void;
    start(config: EventStreamConfig, fromResume?: boolean): void;
    resume(): void;
}
export declare class TestConversationUtils {
    static findLastAssistantMessage(conversation: Optional<NuroConversation>): Optional<NuroAssistantMessage>;
    static findLastToolCallMessage(conversation: Optional<NuroConversation>): Optional<NuroToolCallMessage>;
    static findLastTask(conversation: Optional<NuroConversation>): Optional<NuroTask>;
    static findLastCanvasMessage(conversation: Optional<NuroConversation>): Optional<NuroCanvasMessage>;
}
