"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleCanvasResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const utils_1 = require("../utils");
const simple_canvas_endpoint_1 = require("./simple_canvas_endpoint");
class SimpleCanvasResponse extends utils_1.TestCase {
    constructor() {
        super("simple_client_tool_auto_response");
    }
    run() {
        // Setup
        const server = new simple_canvas_endpoint_1.SimpleCanvasEndPoint();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.mcpManager = utils_1.TestDevice.testMCPManager;
        conversationManager.enableMCPTools();
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "我当前位置是哪里"));
        this.delay(1.0, () => {
            const lastTask = utils_1.TestConversationUtils.findLastTask(conversationManager.conversation);
            if (lastTask === undefined) {
                this.reject("Last task is undefined");
                return;
            }
            if (lastTask.artifactMessages.length === 0) {
                this.reject("Last task artifact messages is empty");
                return;
            }
            if (lastTask.artifactMessages.length !== 5) {
                this.reject("Last task artifact messages number wrong, should be 5 but current number is " +
                    lastTask.artifactMessages.length);
                return;
            }
            if (lastTask.taskStatus !== nurosdk_1.NuroTaskStatus.finished) {
                this.reject("Last task artifact messages task status wrong, should be finished but current task status is " +
                    lastTask.taskStatus);
                return;
            }
            this.resolve();
        });
    }
}
exports.SimpleCanvasResponse = SimpleCanvasResponse;
//# sourceMappingURL=simple_canvas_response.js.map