"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleTaskResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const simple_task_endpoint_1 = require("./simple_task_endpoint");
const utils_1 = require("../utils");
class SimpleTaskResponse extends utils_1.TestCase {
    constructor() {
        super("simple_task_response");
    }
    run() {
        // Setup
        const server = new simple_task_endpoint_1.SimpleTaskServer();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.conversation.taskChecker = new nurosdk_1.NuroTaskChecker();
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "广州今天天气"));
        const lastTask = utils_1.TestConversationUtils.findLastTask(conversationManager.conversation);
        if (lastTask === undefined) {
            this.reject("Last task is undefined");
            return;
        }
        const promptMessage = lastTask.promptMessages[0];
        if (promptMessage === undefined) {
            this.reject("The prompt message is undefined, the task should have prompt message");
            return;
        }
        if (promptMessage instanceof nurosdk_1.NuroUserMessage === false) {
            this.reject("The prompt message is not a NuroUserMessage, the task should have prompt message");
            return;
        }
        const toolMessage = lastTask.middlewareMessages[0];
        if (nurosdk_1.NuroSetting.version !== "3.0.0") {
            if (toolMessage === undefined) {
                this.reject("The middlewareMessages tool message is undefined, the task should have tool message");
                return;
            }
        }
        utils_1.TestChunkedServer.singleton.resume();
        const lastTask2 = utils_1.TestConversationUtils.findLastTask(conversationManager.conversation);
        if (lastTask2 === undefined) {
            this.reject("Last task is undefined");
            return;
        }
        const lastIndex = lastTask2.artifactMessages.length - 1;
        const artifactMessage = lastTask2.artifactMessages[lastIndex < 0 ? 0 : lastIndex];
        if (artifactMessage === undefined) {
            this.reject("The artifactMessages tool message is undefined, the task should have artifact message");
            return;
        }
        if (artifactMessage instanceof nurosdk_1.NuroAssistantMessage === false) {
            this.reject("The artifactMessages tool message is not a NuroAssistantMessageStatus, the task should have artifact message");
            return;
        }
        this.resolve();
    }
}
exports.SimpleTaskResponse = SimpleTaskResponse;
//# sourceMappingURL=simple_task_response.js.map