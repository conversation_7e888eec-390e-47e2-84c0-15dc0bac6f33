"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleServerToolEndpoint = void 0;
const utils_1 = require("../utils");
class SimpleServerToolEndpoint {
    constructor() {
        this.res_0 = `id:gl1pi90nhil
event:message
data: {"author":{"role":"assistant"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4","parent_message_id":""},"status":"in_progress","id":"uitjx3et279","create_time":0}

id:0zwe7ke1nd9
event:delta
data: {"op":"add","path":"/message/tool_calls/0","value":"{\\"id\\":\\"call_87rsuhvtqjxg1rea6j7p449c\\",\\"type\\":\\"server_function\\",\\"streaming\\":true,\\"func\\":{\\"name\\":\\"weather_mock_get_weather_7e03a363\\",\\"arguments\\":\\"\\"}}"}

id:b8g98uy1ui
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"{\\""}

id:cj06nsnmlc
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"city"}

id:1pqenpi40qd
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\":\\""}

id:70zdvczi1om
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"广州"}

id:0f08dup1hrue
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\",\\""}

id:6u01zncsb5m
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"date"}

id:0iozkbr7ey2e
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\":\\""}

id:09925vk2da95
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"今天"}

id:afwrj971z5
event:delta
data: {"op":"append","path":"/message/tool_calls/0/func/arguments","value":"\\"}"}

id:udktmod6l19
event:delta
data: {"op":"replace","path":"/message/tool_calls/0","value":"{\\"id\\":\\"call_87rsuhvtqjxg1rea6j7p449c\\",\\"type\\":\\"server_function\\",\\"streaming\\":false,\\"func\\":{\\"name\\":\\"weather_mock_get_weather_7e03a363\\",\\"arguments\\":\\"{\\\\\\"city\\\\\\":\\\\\\"广州\\\\\\",\\\\\\"date\\\\\\":\\\\\\"今天\\\\\\"}\\"}}"}

id:hxwyc6wvh7
event:message
data: {"author":{"role":"tool"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4","parent_message_id":"","tool_call_id":"call_87rsuhvtqjxg1rea6j7p449c"},"status":"finished_successfully","id":"tool_result_call_87rsuhvtqjxg1rea6j7p449c","content":{"content_type":"text","content_parts":[{"text":"{\\"content\\":[{\\"type\\":\\"text\\",\\"text\\":\\"The 广州 weather is snowy in 今天.\\"}]}"}]},"create_time":0}

id:ek9i7i5awid
event:message
data: {"author":{"role":"assistant"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4","parent_message_id":""},"status":"in_progress","id":"q0lvj2m2n8","create_time":0}

id:jkb4rx9sfu
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"广州"}

id:soed48oex18
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"今天的"}

id:1i4ls0p9i4l
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"天气"}

id:09elwd8v5q8d
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"是"}

id:p1fdzj3db8i
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"下"}

id:6o66dyvcdch
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"雪"}

id:ewnn8mva48v
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"。"}

id:zmfzq8sty6
event:delta
data: {"op":"replace","path":"/message/status","value":"finished_successfully"}

id:5hesasdlzwa
event:system
data: {"type":"stream_complete","conversation_id":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4"}

{"ret":"0","logid":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4"}

`;
    }
    injectRes0() {
        utils_1.TestChunkedServer.singleton.setText(this.res_0);
    }
}
exports.SimpleServerToolEndpoint = SimpleServerToolEndpoint;
//# sourceMappingURL=simple_server_tool_endpoint.js.map