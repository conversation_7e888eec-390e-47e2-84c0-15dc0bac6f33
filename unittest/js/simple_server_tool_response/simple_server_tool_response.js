"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleServerToolResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const simple_server_tool_endpoint_1 = require("./simple_server_tool_endpoint");
const utils_1 = require("../utils");
const tsnfoundation_1 = require("@byted/tsnfoundation");
class SimpleServerToolWeatherArgs extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.city = undefined;
        this.date = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("city")
], SimpleServerToolWeatherArgs.prototype, "city", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("date")
], SimpleServerToolWeatherArgs.prototype, "date", void 0);
class SimpleServerToolResponse extends utils_1.TestCase {
    constructor() {
        super("simple_server_tool_response");
    }
    run() {
        // Setup
        const server = new simple_server_tool_endpoint_1.SimpleServerToolEndpoint();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "广州今天天气"));
        const lastAssistantMessage = utils_1.TestConversationUtils.findLastAssistantMessage(conversationManager.conversation);
        if (lastAssistantMessage === undefined) {
            this.reject("The last message should be assistant message");
            return;
        }
        if (lastAssistantMessage.text !== "广州今天的天气是下雪。") {
            this.reject("The last message content should be '广州今天的天气是下雪。'");
            return;
        }
        const lastToolCallMessage = utils_1.TestConversationUtils.findLastToolCallMessage(conversationManager.conversation);
        if (lastToolCallMessage === undefined) {
            this.reject("The last message should be tool call message");
            return;
        }
        const toolArgs = lastToolCallMessage.toolArgs;
        if (toolArgs !== undefined) {
            const toolArgsObject = new SimpleServerToolWeatherArgs({
                JSONString: toolArgs,
            });
            if (toolArgsObject.city !== "广州") {
                this.reject("The tool args city should be '广州'");
                return;
            }
            if (toolArgsObject.date !== "今天") {
                this.reject("The tool args date should be '今天'");
                return;
            }
        }
        else {
            this.reject("The tool args should not be undefined");
            return;
        }
        const toolResult = lastToolCallMessage.decodeToolCallResultToMCPFormat();
        const contentFirst = toolResult.content[0];
        if (contentFirst instanceof nurosdk_1.MCPToolCallTextContent) {
            if (contentFirst.text !== "The 广州 weather is snowy in 今天.") {
                this.reject("The tool result content should be 'The 广州 weather is snowy in 今天.'");
                return;
            }
        }
        else {
            this.reject("The tool result content should be MCPToolCallTextContent");
            return;
        }
        this.resolve();
    }
}
exports.SimpleServerToolResponse = SimpleServerToolResponse;
//# sourceMappingURL=simple_server_tool_response.js.map