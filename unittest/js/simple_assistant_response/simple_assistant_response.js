"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleAssistantResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const simple_assistant_endpoint_1 = require("./simple_assistant_endpoint");
const utils_1 = require("../utils");
class SimpleAssistantResponse extends utils_1.TestCase {
    constructor() {
        super("simple_assistant_response");
    }
    run() {
        // Setup
        const server = new simple_assistant_endpoint_1.SimpleAssistantServer();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "你好"));
        const lastAssistantMessage = utils_1.TestConversationUtils.findLastAssistantMessage(conversationManager.conversation);
        if (lastAssistantMessage === undefined) {
            this.reject("The last message should be assistant message");
            return;
        }
        if (lastAssistantMessage.text !== "你好！有什么可以帮您的吗？😊") {
            this.reject("The last message content should be '你好！有什么可以帮您的吗？😊'");
            return;
        }
        if (lastAssistantMessage.id !== "oqoj15qm9e_assistant") {
            this.reject("The last message id should be 'oqoj15qm9e_assistant'");
            return;
        }
        if (lastAssistantMessage.messageStatus !==
            nurosdk_1.NuroAssistantMessageStatus.finished_successfully) {
            this.reject("The last message status should be finished_successfully");
            return;
        }
        this.resolve();
    }
}
exports.SimpleAssistantResponse = SimpleAssistantResponse;
//# sourceMappingURL=simple_assistant_response.js.map