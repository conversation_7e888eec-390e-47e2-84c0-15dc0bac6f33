"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleAssistantServer = void 0;
const utils_1 = require("../utils");
class SimpleAssistantServer {
    constructor() {
        this.res_0 = `id:045xwc2qsztg
event:message
data: {"author":{"role":"assistant"},"metadata":{"is_visually_hidden_from_conversation":false,"conversation_id":"6e3731b1-191c-373c-35b3-5cd97ec6710a","parent_message_id":""},"status":"in_progress","id":"oqoj15qm9e","create_time":0}

id:yynka2a9nqe
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"你好"}

id:p07cb7a3ww
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"！"}

id:r9572mux9vk
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"有什么"}

id:a9jo7yev3x
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"可以"}

id:00qgdq458kw9h
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"帮"}

id:3uefdayybxn
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"您的"}

id:uehkzwkjloh
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"吗"}

id:q62djf72z7
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"？"}

id:93dyz9eg4ui
event:delta
data: {"op":"append","path":"/message/content/content_parts/0/text","value":"😊"}

id:9gzf65s6ezd
event:delta
data: {"op":"replace","path":"/message/status","value":"finished_successfully"}

id:i3vkrpp4o4h
event:system
data: {"type":"stream_complete","conversation_id":"6e3731b1-191c-373c-35b3-5cd97ec6710a"}

{"ret":"0","logid":"899aae7c-5fb4-6e71-55e9-9e935e65f2e4"}

`;
    }
    injectRes0() {
        utils_1.TestChunkedServer.singleton.setText(this.res_0);
    }
}
exports.SimpleAssistantServer = SimpleAssistantServer;
//# sourceMappingURL=simple_assistant_endpoint.js.map