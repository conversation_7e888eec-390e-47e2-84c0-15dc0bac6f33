"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleClientToolAutoResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const utils_1 = require("../utils");
const simple_client_tool_auto_endpoint_1 = require("./simple_client_tool_auto_endpoint");
class SimpleClientToolAutoResponse extends utils_1.TestCase {
    constructor() {
        super("simple_client_tool_auto_response");
    }
    run() {
        // Setup
        const server = new simple_client_tool_auto_endpoint_1.SimpleClientToolAutoEndpoint();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.mcpManager = utils_1.TestDevice.testMCPManager;
        conversationManager.enableMCPTools();
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "我当前位置是哪里"));
        this.delay(1.0, () => {
            const lastToolCallMessage = utils_1.TestConversationUtils.findLastToolCallMessage(conversationManager.conversation);
            if (lastToolCallMessage === undefined) {
                this.reject("The last message should be tool call message");
                return;
            }
            if (lastToolCallMessage.messageStatus !==
                nurosdk_1.NuroToolCallMessageStatus.finished_successfully) {
                this.reject("The last message status should be finished successfully");
                return;
            }
            if (lastToolCallMessage.decodeToolCallResultAsPlainText() !== "佛山市") {
                this.reject("The last message tool result should be '佛山市'");
                return;
            }
            this.resolve();
        });
    }
}
exports.SimpleClientToolAutoResponse = SimpleClientToolAutoResponse;
//# sourceMappingURL=simple_client_tool_auto_response.js.map