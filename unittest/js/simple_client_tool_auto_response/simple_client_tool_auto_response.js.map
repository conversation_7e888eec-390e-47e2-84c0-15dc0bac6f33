{"version": 3, "file": "simple_client_tool_auto_response.js", "sourceRoot": "", "sources": ["../../src/simple_client_tool_auto_response/simple_client_tool_auto_response.ts"], "names": [], "mappings": ";;;AAAA,4CAWwB;AACxB,oCAAuE;AACvE,yFAAkF;AAElF,MAAa,4BAA6B,SAAQ,gBAAQ;IACxD;QACE,KAAK,CAAC,kCAAkC,CAAC,CAAC;IAC5C,CAAC;IAED,GAAG;QACD,QAAQ;QACR,MAAM,MAAM,GAAG,IAAI,+DAA4B,EAAE,CAAC;QAClD,MAAM,CAAC,UAAU,EAAE,CAAC;QACpB,MAAM;QACN,MAAM,mBAAmB,GAAG,IAAI,iCAAuB,EAAE,CAAC;QAC1D,mBAAmB,CAAC,OAAO,CAAC,IAAI,sBAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,CAAC;QAElE,mBAAmB,CAAC,UAAU,GAAG,kBAAU,CAAC,cAAc,CAAC;QAC3D,mBAAmB,CAAC,cAAc,EAAE,CAAC;QACrC,mBAAmB,CAAC,eAAe,CACjC,IAAI,yBAAe,CAAC,mBAAS,CAAC,gBAAgB,EAAE,EAAE,UAAU,CAAC,CAC9D,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,EAAE;YACnB,MAAM,mBAAmB,GAAG,6BAAqB,CAAC,uBAAuB,CACvE,mBAAmB,CAAC,YAAY,CACjC,CAAC;YACF,IAAI,mBAAmB,KAAK,SAAS,EAAE;gBACrC,IAAI,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,OAAO;aACR;YAED,IACE,mBAAmB,CAAC,aAAa;gBACjC,mCAAyB,CAAC,qBAAqB,EAC/C;gBACA,IAAI,CAAC,MAAM,CAAC,yDAAyD,CAAC,CAAC;gBACvE,OAAO;aACR;YACD,IAAI,mBAAmB,CAAC,+BAA+B,EAAE,KAAK,KAAK,EAAE;gBACnE,IAAI,CAAC,MAAM,CAAC,8CAA8C,CAAC,CAAC;gBAC5D,OAAO;aACR;YAED,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA3CD,oEA2CC"}