"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TestConversationUtils = exports.TestChunkedServer = exports.TestDevice = exports.TestCase = exports.TestContext = exports.SuccessTestInfo = exports.FailedTestInfo = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const nurosdk_1 = require("@byted/nurosdk");
class FailedTestInfo {
    constructor(functionName, errorMessage) {
        this.functionName = functionName;
        this.errorMessage = errorMessage;
    }
}
exports.FailedTestInfo = FailedTestInfo;
class SuccessTestInfo {
    constructor(functionName) {
        this.functionName = functionName;
    }
}
exports.SuccessTestInfo = SuccessTestInfo;
class TestContext {
    constructor() {
        this.failedTests = [];
        this.succeededTests = [];
    }
    reportFailure(functionName, errorMessage) {
        this.failedTests.push(new FailedTestInfo(functionName, errorMessage));
    }
    reportSuccess(functionName) {
        this.succeededTests.push(new SuccessTestInfo(functionName));
    }
    getFailedTests() {
        return this.failedTests;
    }
    getSucceededTests() {
        return this.succeededTests;
    }
}
exports.TestContext = TestContext;
TestContext.globalContext = new TestContext();
class TestCase {
    constructor(name) {
        this.resolve = () => { };
        this.reject = (_) => { };
        this.name = name;
    }
    run() { }
    delay(seconds, cb) {
        new tsnfoundation_1.TSNTimer(seconds, cb);
    }
}
exports.TestCase = TestCase;
class TestDevice {
    constructor() {
        this.definedTests = [];
    }
    defineTest(item) {
        this.definedTests.push(item);
    }
    runNextTest(onFinish) {
        const currentTest = this.definedTests.shift();
        if (currentTest !== undefined) {
            currentTest.resolve = () => {
                TestContext.globalContext.reportSuccess(currentTest.name);
                this.runNextTest(onFinish);
            };
            currentTest.reject = (errorMessage) => {
                TestContext.globalContext.reportFailure(currentTest.name, errorMessage);
                this.runNextTest(onFinish);
            };
            currentTest.run();
        }
        else {
            onFinish();
        }
    }
}
exports.TestDevice = TestDevice;
TestDevice.testMCPManager = undefined;
class TestChunkedServer {
    constructor() {
        this.chunksParts = [];
        this._pausedConfig = undefined;
    }
    installNuroSDK() {
        nurosdk_1.NuroSetting.version = "3.0.0";
        nurosdk_1.NuroSetting.canvasSettings = {
            startNode: "canvas_open_new_canvas",
            endNode: "close_canvas",
            nodes: [
                "creative_agent_mcp_gen_text2image_v3",
                "creative_agent_mcp_gen_image2image_v3",
                "creative_agent_mcp_gen_image2video_v3",
                "creative_agent_mcp_gen_text2video_v3",
                "creative_agent_mcp_gen_start_end2video_v3",
            ],
        };
        nurosdk_1.EventStreamAdapter.fetch = (config) => {
            this.start(config);
            return "";
        };
    }
    setText(text) {
        this.installNuroSDK();
        const chunks = [];
        let remainingText = text;
        while (remainingText.length > 0) {
            const chunkSize = tsnfoundation_1.TSNNumberConverter.toInt(Math.floor(Math.random() * tsnfoundation_1.TSNNumberConverter.toDouble(remainingText.length)) + 1);
            chunks.push(remainingText.substring(0, chunkSize));
            remainingText = remainingText.substring(chunkSize, remainingText.length);
        }
        this.chunksParts.push(chunks);
    }
    setTexts(texts) {
        for (let index = 0; index < texts.length; index++) {
            const element = texts[index];
            this.setText(element);
        }
    }
    start(config, fromResume = false) {
        const currentChunks = this.chunksParts.shift();
        if (currentChunks === undefined) {
            return;
        }
        if (config.onStart !== undefined && fromResume !== true) {
            config.onStart();
        }
        for (let index = 0; index < currentChunks.length; index++) {
            const element = currentChunks[index];
            if (config.onChunk !== undefined) {
                config.onChunk(element);
            }
        }
        if (currentChunks.join("").indexOf("stream_complete") < 0) {
            // 中断回复，等候 resume
            this._pausedConfig = config;
            return;
        }
        if (config.onFinish !== undefined) {
            config.onFinish();
        }
    }
    resume() {
        if (this._pausedConfig !== undefined) {
            this.start(this._pausedConfig, true);
            this._pausedConfig = undefined;
        }
    }
}
exports.TestChunkedServer = TestChunkedServer;
TestChunkedServer.singleton = new TestChunkedServer();
class TestConversationUtils {
    static findLastAssistantMessage(conversation) {
        const messages = conversation === null || conversation === void 0 ? void 0 : conversation.messages;
        if (messages === undefined) {
            return undefined;
        }
        let lastAssistantMessage = undefined;
        for (let index = messages.length - 1; index >= 0; index = index - 1) {
            const element = messages[index];
            if (element instanceof nurosdk_1.NuroAssistantMessage) {
                lastAssistantMessage = element;
            }
        }
        return lastAssistantMessage;
    }
    static findLastToolCallMessage(conversation) {
        const messages = conversation === null || conversation === void 0 ? void 0 : conversation.messages;
        if (messages === undefined) {
            return undefined;
        }
        let lastToolCallMessage = undefined;
        for (let index = messages.length - 1; index >= 0; index = index - 1) {
            const element = messages[index];
            if (element instanceof nurosdk_1.NuroToolCallMessage) {
                lastToolCallMessage = element;
            }
        }
        return lastToolCallMessage;
    }
    static findLastTask(conversation) {
        const tasks = conversation === null || conversation === void 0 ? void 0 : conversation.tasks;
        if (tasks === undefined) {
            return undefined;
        }
        let lastTask = undefined;
        for (let index = tasks.length - 1; index >= 0; index = index - 1) {
            const element = tasks[index];
            lastTask = element;
        }
        return lastTask;
    }
    static findLastCanvasMessage(conversation) {
        const tasks = conversation === null || conversation === void 0 ? void 0 : conversation.tasks;
        if (tasks === undefined) {
            return undefined;
        }
        // Nuro 3.0只有一个turn
        let lastTask = tasks[0];
        let resultMessage = undefined;
        lastTask === null || lastTask === void 0 ? void 0 : lastTask.artifactMessages.forEach((artifactMessage) => {
            if (artifactMessage instanceof nurosdk_1.NuroCanvasMessage) {
                resultMessage = artifactMessage;
            }
        });
        return resultMessage;
    }
}
exports.TestConversationUtils = TestConversationUtils;
//# sourceMappingURL=utils.js.map