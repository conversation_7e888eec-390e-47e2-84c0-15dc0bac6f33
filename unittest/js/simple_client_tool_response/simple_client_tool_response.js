"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SimpleClientToolResponse = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const utils_1 = require("../utils");
const tsnfoundation_1 = require("@byted/tsnfoundation");
const simple_client_tool_endpoint_1 = require("./simple_client_tool_endpoint");
class SimpleClientToolStruct extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.reason = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("reason")
], SimpleClientToolStruct.prototype, "reason", void 0);
class SimpleClientToolResponse extends utils_1.TestCase {
    constructor() {
        super("simple_client_tool_response");
    }
    run() {
        // Setup
        const server = new simple_client_tool_endpoint_1.SimpleClientToolEndpoint();
        server.injectRes0();
        // Run
        const conversationManager = new nurosdk_1.NuroConversationManager();
        conversationManager.connect(new nurosdk_1.SSETransport("/mock", undefined));
        conversationManager.mcpManager = utils_1.TestDevice.testMCPManager;
        conversationManager.enableMCPTools();
        conversationManager.sendUserMessage(new nurosdk_1.NuroUserMessage(nurosdk_1.NuroUtils.randomUUIDString(), "我当前位置是哪里"));
        this.delay(1.0, () => {
            var _a;
            const lastToolCallMessage = utils_1.TestConversationUtils.findLastToolCallMessage(conversationManager.conversation);
            if (lastToolCallMessage === undefined) {
                this.reject("The last message should be tool call message");
                return;
            }
            const toolArgsObject = new SimpleClientToolStruct({
                JSONString: (_a = lastToolCallMessage.toolArgs) !== null && _a !== void 0 ? _a : "{}",
            });
            if (toolArgsObject.reason !== "确定用户当前所在位置") {
                this.reject("The last message reason should be '确定用户当前所在位置'");
                return;
            }
            const tcMCPFormat = new nurosdk_1.MCPToolCallResult();
            tcMCPFormat.content = [nurosdk_1.MCPToolCallTextContent.create("广州市")];
            lastToolCallMessage.sendToolCallResultFromMCPFormat(tcMCPFormat);
            const lastToolCallMessage2 = utils_1.TestConversationUtils.findLastToolCallMessage(conversationManager.conversation);
            if (lastToolCallMessage2 === undefined) {
                this.reject("The last message should be tool call message");
                return;
            }
            if (lastToolCallMessage2.messageStatus !==
                nurosdk_1.NuroToolCallMessageStatus.finished_successfully) {
                this.reject("The last message status should be finished successfully");
                return;
            }
            if (lastToolCallMessage2.decodeToolCallResultAsPlainText() !== "广州市") {
                this.reject("The last message tool result should be '广州市'");
                return;
            }
            this.resolve();
        });
    }
}
exports.SimpleClientToolResponse = SimpleClientToolResponse;
//# sourceMappingURL=simple_client_tool_response.js.map