"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.installDevice = void 0;
const nurosdk_1 = require("@byted/nurosdk");
const tsnfoundation_1 = require("@byted/tsnfoundation");
const { McpServer } = require("@modelcontextprotocol/sdk/server/mcp.js");
const { z } = require("zod");
const utils_1 = require("./utils");
function installDevice() {
    nurosdk_1.NuroSetting.needDisplayServerFunctionMessage = true;
    const mcpManager = new nurosdk_1.NuroMCPManager();
    if (tsnfoundation_1.IS_JS) {
        const localServer = new McpServer({ name: "local", version: "1.0.0" });
        localServer.tool("get_location", "获取用户当前位置", { reason: z.string().describe("为什么需要获取地理位置，理由是什么。") }, (args) => __awaiter(this, void 0, void 0, function* () {
            if (args.reason === "不需要用户确认") {
                return { content: [{ type: "text", text: "佛山市" }] };
            }
            return { content: [] }; // 留空是因为我们需要通过自定义工具界面，请求用户授权获取用户的地理位置信息。
        }));
        mcpManager.registerServer({
            name: "local",
            adapter: new nurosdk_1.NuroMCPClientAdapterImpl("local", localServer),
        });
    }
    utils_1.TestDevice.testMCPManager = mcpManager;
}
exports.installDevice = installDevice;
if (tsnfoundation_1.IS_JS) {
    installDevice();
}
//# sourceMappingURL=installDevice.js.js.map