"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runTests = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const utils_1 = require("./utils");
const simple_assistant_response_1 = require("./simple_assistant_response/simple_assistant_response");
const simple_server_tool_response_1 = require("./simple_server_tool_response/simple_server_tool_response");
const simple_client_tool_response_1 = require("./simple_client_tool_response/simple_client_tool_response");
const simple_client_tool_auto_response_1 = require("./simple_client_tool_auto_response/simple_client_tool_auto_response");
const simple_task_response_1 = require("./simple_task_response/simple_task_response");
const conversation_manager_response_1 = require("./conversation_manager_response/conversation_manager_response");
const simple_jsonrepair_response_1 = require("./simple_jsonrespair_response/simple_jsonrepair_response");
function runTests(finishCallback) {
    const testDevice = new utils_1.TestDevice();
    testDevice.defineTest(new simple_assistant_response_1.SimpleAssistantResponse());
    testDevice.defineTest(new simple_server_tool_response_1.SimpleServerToolResponse());
    testDevice.defineTest(new simple_client_tool_response_1.SimpleClientToolResponse());
    testDevice.defineTest(new simple_client_tool_auto_response_1.SimpleClientToolAutoResponse());
    testDevice.defineTest(new simple_task_response_1.SimpleTaskResponse());
    // testDevice.defineTest(new SimpleCanvasResponse());
    testDevice.defineTest(new conversation_manager_response_1.ConversationManagerResponse());
    testDevice.defineTest(new simple_jsonrepair_response_1.SimpleJsonRepairResponse());
    testDevice.runNextTest(() => {
        const failedTests = utils_1.TestContext.globalContext.getFailedTests();
        const succeededTests = utils_1.TestContext.globalContext.getSucceededTests();
        if (failedTests.length > 0) {
            tsnfoundation_1.TSNConsole.log("Test failed:");
            failedTests.forEach((test) => {
                tsnfoundation_1.TSNConsole.log(`Test ${test.functionName} failed: ${test.errorMessage}`);
            });
            if (tsnfoundation_1.IS_JS) {
                throw "Test failed";
            }
        }
        else {
            tsnfoundation_1.TSNConsole.log("All tests passed!");
        }
        finishCallback === null || finishCallback === void 0 ? void 0 : finishCallback(testDevice);
    });
}
exports.runTests = runTests;
if (tsnfoundation_1.IS_JS) {
    const { installDevice } = require("./installDevice.js");
    installDevice();
    runTests();
}
//# sourceMappingURL=runTests.js.map