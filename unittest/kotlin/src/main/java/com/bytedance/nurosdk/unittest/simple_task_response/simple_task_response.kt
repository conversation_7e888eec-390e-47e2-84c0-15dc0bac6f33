//1a514c4e377accc9ad6eb015390cc028
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleTaskResponse(): TestCase("simple_task_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleTaskServer()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.conversation.taskChecker = NuroTaskChecker()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"))
        val lastTask = TestConversationUtils.findLastTask(conversationManager.conversation)
        if (lastTask == null) {
            this.reject("Last task is undefined")
                return
        }
        val promptMessage = lastTask.promptMessages.tsn_safeGet(0)
        if (promptMessage == null) {
            this.reject("The prompt message is undefined, the task should have prompt message")
                return
        }
        if (promptMessage is NuroUserMessage == false) {
            this.reject("The prompt message is not a NuroUserMessage, the task should have prompt message")
                return
        }
        val toolMessage = lastTask.middlewareMessages.tsn_safeGet(0)
        if (NuroSetting.version != "3.0.0") {
            if (toolMessage == null) {
                    this.reject("The middlewareMessages tool message is undefined, the task should have tool message")
                        return
                }
        }
        TestChunkedServer.singleton.resume()
        val lastTask2 = TestConversationUtils.findLastTask(conversationManager.conversation)
        if (lastTask2 == null) {
            this.reject("Last task is undefined")
                return
        }
        val lastIndex = lastTask2.artifactMessages.length - 1
        val artifactMessage = lastTask2.artifactMessages.tsn_safeGet(if(lastIndex < 0) 0 else lastIndex)
        if (artifactMessage == null) {
            this.reject("The artifactMessages tool message is undefined, the task should have artifact message")
                return
        }
        if (artifactMessage is NuroAssistantMessage == false) {
            this.reject("The artifactMessages tool message is not a NuroAssistantMessageStatus, the task should have artifact message")
                return
        }
        this.resolve()
    }

}