//fe81e919d6210b0c4a17cea72793868d
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleCanvasResponse(): TestCase("simple_client_tool_auto_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleCanvasEndPoint()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        this.delay(1.0, f0@{  ->
              val lastTask = TestConversationUtils.findLastTask(conversationManager.conversation)
                if (lastTask == null) {
                    this.reject("Last task is undefined")
                        return@f0
                }
                if (lastTask.artifactMessages.length == 0) {
                    this.reject("Last task artifact messages is empty")
                        return@f0
                }
                if (lastTask.artifactMessages.length != 5) {
                    this.reject("Last task artifact messages number wrong, should be 5 but current number is " + lastTask.artifactMessages.length)
                        return@f0
                }
                if (lastTask.taskStatus != NuroTaskStatus.finished) {
                    this.reject("Last task artifact messages task status wrong, should be finished but current task status is " + lastTask.taskStatus)
                        return@f0
                }
                this.resolve()
        })
    }

}