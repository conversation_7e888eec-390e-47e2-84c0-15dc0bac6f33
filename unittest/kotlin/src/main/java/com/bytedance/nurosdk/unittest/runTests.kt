//526a346569f8c86174c578f1ec5d9646
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

fun runTests(finishCallback: (TestDevice) -> Unit): Unit {
    val testDevice = TestDevice()
    testDevice.defineTest(SimpleAssistantResponse())
    testDevice.defineTest(SimpleServerToolResponse())
    testDevice.defineTest(SimpleClientToolResponse())
    testDevice.defineTest(SimpleClientToolAutoResponse())
    testDevice.defineTest(SimpleTaskResponse())
    testDevice.defineTest(ConversationManagerResponse())
    testDevice.defineTest(SimpleJsonRepairResponse())
    testDevice.runNextTest(f0@{  ->
          val failedTests = TestContext.globalContext.getFailedTests()
            val succeededTests = TestContext.globalContext.getSucceededTests()
            if (failedTests.length > 0) {
                TSNConsole.log("Test failed:")
                    failedTests.forEach(f1@{ test ->
                          TSNConsole.log("Test ${test.functionName} failed: ${test.errorMessage}")
                    })
            }
            else {
                TSNConsole.log("All tests passed!")
            }
            finishCallback(testDevice)
    })
}