//3a65d4acb04e9b3d7aa7fbea03e515d7
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class FailedTestInfo(functionName: String, errorMessage: String) {
    
    lateinit var functionName: String

    lateinit var errorMessage: String

    init {
        this.functionName = functionName
        this.errorMessage = errorMessage
    }

}

class SuccessTestInfo(functionName: String) {
    
    lateinit var functionName: String

    init {
        this.functionName = functionName
    }

}

class TestContext {
    companion object {
        var globalContext = TestContext()
    }
    private var failedTests: MutableList<FailedTestInfo> = mutableListOf()

    private var succeededTests: MutableList<SuccessTestInfo> = mutableListOf()

    fun reportFailure(functionName: String, errorMessage: String): Unit {
        this.failedTests.push(FailedTestInfo(functionName, errorMessage))
    }

    fun reportSuccess(functionName: String): Unit {
        this.succeededTests.push(SuccessTestInfo(functionName))
    }

    fun getFailedTests(): MutableList<FailedTestInfo> {
        return this.failedTests
    }

    fun getSucceededTests(): MutableList<SuccessTestInfo> {
        return this.succeededTests
    }

}

open class TestCase(name: String) {
    
    lateinit open var name: String

    open var resolve: () -> Unit = f0@{  ->
          
    }

    open var reject: (String) -> Unit = f0@{ _ ->
          
    }

    init {
        this.name = name
    }

    open fun run(): Unit {
        
    }

    open fun delay(seconds: Double, cb: () -> Unit): Unit {
        TSNTimer(seconds, cb)
    }

}

class TestDevice {
    companion object {
        var testMCPManager: NuroMCPManager? = null
    }
    private var definedTests: MutableList<TestCase> = mutableListOf()

    fun defineTest(item: TestCase) {
        this.definedTests.push(item)
    }

    fun runNextTest(onFinish: () -> Unit) {
        val currentTest = this.definedTests.shift()
        if (currentTest != null) {
            currentTest.resolve = f0@{  ->
                      TestContext.globalContext.reportSuccess(currentTest.name)
                        this.runNextTest(onFinish)
                }
                currentTest.reject = f0@{ errorMessage ->
                      TestContext.globalContext.reportFailure(currentTest.name, errorMessage)
                        this.runNextTest(onFinish)
                }
                currentTest.run()
        }
        else {
            onFinish()
        }
    }

}

class TestChunkedServer {
    companion object {
        var singleton = TestChunkedServer()
    }
    var chunksParts: MutableList<MutableList<String>> = mutableListOf()

    var _pausedConfig: EventStreamConfig? = null

    fun installNuroSDK(): Unit {
        NuroSetting.version = "3.0.0"
        NuroSetting.canvasSettings = mutableMapOf(
            "startNode" to "canvas_open_new_canvas", 
            "endNode" to "close_canvas", 
            "nodes" to mutableListOf(
            "creative_agent_mcp_gen_text2image_v3", 
            "creative_agent_mcp_gen_image2image_v3", 
            "creative_agent_mcp_gen_image2video_v3", 
            "creative_agent_mcp_gen_text2video_v3", 
            "creative_agent_mcp_gen_start_end2video_v3"
        )
        )
        EventStreamAdapter.fetch = f0@{ config: EventStreamConfig ->
              this.start(config)
                return@f0 ""
        }
    }

    fun setText(text: String): Unit {
        this.installNuroSDK()
        val chunks: MutableList<String> = mutableListOf()
        var remainingText = text
        while (remainingText.length > 0) {
            val chunkSize = TSNNumberConverter.toInt(Math.floor(Math.random() * TSNNumberConverter.toDouble(remainingText.length)) + 1)
                chunks.push(remainingText.substring(0, chunkSize))
                remainingText = remainingText.substring(chunkSize, remainingText.length)
        }
        this.chunksParts.push(chunks)
    }

    fun setTexts(texts: MutableList<String>): Unit {
        for (index in 0 until texts.length) {
              val element = texts[index]
                this.setText(element)
        }
    }

    fun start(config: EventStreamConfig, fromResume: Boolean = false): Unit {
        val currentChunks = this.chunksParts.shift()
        if (currentChunks == null) {
            return
        }
        run{
              
        val _config_onStart = config?.onStart
        if (_config_onStart != null && fromResume != true) {
            _config_onStart()
        }
              }
        for (index in 0 until currentChunks.length) {
              val element = currentChunks[index]
                val _config_onChunk = config?.onChunk
                if (_config_onChunk != null) {
                    _config_onChunk(element)
                }
        }
        if (currentChunks.join("").indexOf("stream_complete") < 0) {
            this._pausedConfig = config
                return
        }
        run{
              
        val _config_onFinish = config?.onFinish
        if (_config_onFinish != null) {
            _config_onFinish()
        }
              }
    }

    fun resume(): Unit {
        val _this__pausedConfig = this?._pausedConfig
        if (_this__pausedConfig != null) {
            this.start(_this__pausedConfig, true)
                this._pausedConfig = null
        }
    }

}

object TestConversationUtils {
    fun findLastAssistantMessage(conversation: NuroConversation?): NuroAssistantMessage? {
        val messages = conversation?.messages
        if (messages == null) {
            return null
        }
        var lastAssistantMessage: NuroAssistantMessage? = null
        run {
          var index = messages.length - 1
          var __first__ = true
          while (index >= 0) {
            if (!__first__) {
              index = index - 1
            }
            __first__ = false
            if (index >= 0) {
                  val element = messages[index]
                if (element is NuroAssistantMessage) {
                    lastAssistantMessage = element
                }
            }
            else {
              break
            }
          }
        }
        return lastAssistantMessage
    }

    fun findLastToolCallMessage(conversation: NuroConversation?): NuroToolCallMessage? {
        val messages = conversation?.messages
        if (messages == null) {
            return null
        }
        var lastToolCallMessage: NuroToolCallMessage? = null
        run {
          var index = messages.length - 1
          var __first__ = true
          while (index >= 0) {
            if (!__first__) {
              index = index - 1
            }
            __first__ = false
            if (index >= 0) {
                  val element = messages[index]
                if (element is NuroToolCallMessage) {
                    lastToolCallMessage = element
                }
            }
            else {
              break
            }
          }
        }
        return lastToolCallMessage
    }

    fun findLastTask(conversation: NuroConversation?): NuroTask? {
        val tasks = conversation?.tasks
        if (tasks == null) {
            return null
        }
        var lastTask: NuroTask? = null
        run {
          var index = tasks.length - 1
          var __first__ = true
          while (index >= 0) {
            if (!__first__) {
              index = index - 1
            }
            __first__ = false
            if (index >= 0) {
                  val element = tasks[index]
                lastTask = element
            }
            else {
              break
            }
          }
        }
        return lastTask
    }

    fun findLastCanvasMessage(conversation: NuroConversation?): NuroCanvasMessage? {
        val tasks = conversation?.tasks
        if (tasks == null) {
            return null
        }
        // Nuro 3.0只有一个turn
            var lastTask = tasks.tsn_safeGet(0)
        var resultMessage: NuroCanvasMessage? = null
        lastTask?.artifactMessages?.forEach(f0@{ artifactMessage ->
              if (artifactMessage is NuroCanvasMessage) {
                    resultMessage = artifactMessage
                }
        })
        return resultMessage
    }
}