//c9805675baf43f14b2ad67e990d4fcb0
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleServerToolEndpoint {
    
    var res_0 = "id:gl1pi90nhil\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"899aae7c-5fb4-6e71-55e9-9e935e65f2e4\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"uitjx3et279\",\"create_time\":0}\n\nid:0zwe7ke1nd9\nevent:delta\ndata: {\"op\":\"add\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_87rsuhvtqjxg1rea6j7p449c\\\",\\\"type\\\":\\\"server_function\\\",\\\"streaming\\\":true,\\\"func\\\":{\\\"name\\\":\\\"weather_mock_get_weather_7e03a363\\\",\\\"arguments\\\":\\\"\\\"}}\"}\n\nid:b8g98uy1ui\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"{\\\"\"}\n\nid:cj06nsnmlc\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"city\"}\n\nid:1pqenpi40qd\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\":\\\"\"}\n\nid:70zdvczi1om\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"广州\"}\n\nid:0f08dup1hrue\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\",\\\"\"}\n\nid:6u01zncsb5m\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"date\"}\n\nid:0iozkbr7ey2e\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\":\\\"\"}\n\nid:09925vk2da95\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"今天\"}\n\nid:afwrj971z5\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\"}\"}\n\nid:udktmod6l19\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_87rsuhvtqjxg1rea6j7p449c\\\",\\\"type\\\":\\\"server_function\\\",\\\"streaming\\\":false,\\\"func\\\":{\\\"name\\\":\\\"weather_mock_get_weather_7e03a363\\\",\\\"arguments\\\":\\\"{\\\\\\\"city\\\\\\\":\\\\\\\"广州\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"今天\\\\\\\"}\\\"}}\"}\n\nid:hxwyc6wvh7\nevent:message\ndata: {\"author\":{\"role\":\"tool\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"899aae7c-5fb4-6e71-55e9-9e935e65f2e4\",\"parent_message_id\":\"\",\"tool_call_id\":\"call_87rsuhvtqjxg1rea6j7p449c\"},\"status\":\"finished_successfully\",\"id\":\"tool_result_call_87rsuhvtqjxg1rea6j7p449c\",\"content\":{\"content_type\":\"text\",\"content_parts\":[{\"text\":\"{\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"The 广州 weather is snowy in 今天.\\\"}]}\"}]},\"create_time\":0}\n\nid:ek9i7i5awid\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"899aae7c-5fb4-6e71-55e9-9e935e65f2e4\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"q0lvj2m2n8\",\"create_time\":0}\n\nid:jkb4rx9sfu\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"广州\"}\n\nid:soed48oex18\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"今天的\"}\n\nid:1i4ls0p9i4l\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"天气\"}\n\nid:09elwd8v5q8d\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"是\"}\n\nid:p1fdzj3db8i\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"下\"}\n\nid:6o66dyvcdch\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"雪\"}\n\nid:ewnn8mva48v\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"。\"}\n\nid:zmfzq8sty6\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/status\",\"value\":\"finished_successfully\"}\n\nid:5hesasdlzwa\nevent:system\ndata: {\"type\":\"stream_complete\",\"conversation_id\":\"899aae7c-5fb4-6e71-55e9-9e935e65f2e4\"}\n\n{\"ret\":\"0\",\"logid\":\"899aae7c-5fb4-6e71-55e9-9e935e65f2e4\"}\n\n"

    fun injectRes0(): Unit {
        TestChunkedServer.singleton.setText(this.res_0)
    }

}