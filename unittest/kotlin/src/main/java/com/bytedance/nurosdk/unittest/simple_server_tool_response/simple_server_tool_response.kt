//98243e48faf84dd544c4edefadc18d5d
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleServerToolWeatherArgs: TSNSerializable {
    
    var city: String? = null

    var date: String? = null
    constructor() : super() { doInit(false) }
    constructor(JSONString: String) : super(JSONString) { doInit(true) }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) { doInit(true) }
    
    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.city = TSNJSONObject.getString(map, "city") ?: null
        this.date = TSNJSONObject.getString(map, "date") ?: null
    }
    
    override fun unmapping() {
        super.unmapping()
        this.encodeData["city"] = this.city
        this.encodeData["date"] = this.date
    }


}

class SimpleServerToolResponse(): TestCase("simple_server_tool_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleServerToolEndpoint()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "广州今天天气"))
        val lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(conversationManager.conversation)
        if (lastAssistantMessage == null) {
            this.reject("The last message should be assistant message")
                return
        }
        if (lastAssistantMessage.text != "广州今天的天气是下雪。") {
            this.reject("The last message content should be '广州今天的天气是下雪。'")
                return
        }
        val lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
        if (lastToolCallMessage == null) {
            this.reject("The last message should be tool call message")
                return
        }
        val toolArgs = lastToolCallMessage.toolArgs
        if (toolArgs != null) {
            val toolArgsObject = SimpleServerToolWeatherArgs(JSONString = toolArgs)
                if (toolArgsObject.city != "广州") {
                    this.reject("The tool args city should be '广州'")
                        return
                }
                if (toolArgsObject.date != "今天") {
                    this.reject("The tool args date should be '今天'")
                        return
                }
        }
        else {
            this.reject("The tool args should not be undefined")
                return
        }
        val toolResult = lastToolCallMessage.decodeToolCallResultToMCPFormat()
        val contentFirst = toolResult.content.tsn_safeGet(0)
        if (contentFirst is MCPToolCallTextContent) {
            if (contentFirst.text != "The 广州 weather is snowy in 今天.") {
                    this.reject("The tool result content should be 'The 广州 weather is snowy in 今天.'")
                        return
                }
        }
        else {
            this.reject("The tool result content should be MCPToolCallTextContent")
                return
        }
        this.resolve()
    }

}