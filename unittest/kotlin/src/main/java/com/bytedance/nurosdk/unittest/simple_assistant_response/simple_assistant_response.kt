//3389fbaf5e76ee13276b59ffacd270b4
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleAssistantResponse(): TestCase("simple_assistant_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleAssistantServer()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "你好"))
        val lastAssistantMessage = TestConversationUtils.findLastAssistantMessage(conversationManager.conversation)
        if (lastAssistantMessage == null) {
            this.reject("The last message should be assistant message")
                return
        }
        if (lastAssistantMessage.text != "你好！有什么可以帮您的吗？😊") {
            this.reject("The last message content should be '你好！有什么可以帮您的吗？😊'")
                return
        }
        if (lastAssistantMessage.id != "oqoj15qm9e_assistant") {
            this.reject("The last message id should be 'oqoj15qm9e_assistant'")
                return
        }
        if (lastAssistantMessage.messageStatus != NuroAssistantMessageStatus.finished_successfully) {
            this.reject("The last message status should be finished_successfully")
                return
        }
        this.resolve()
    }

}