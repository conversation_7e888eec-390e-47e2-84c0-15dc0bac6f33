//3307a33e4bbe8d5c2d82a420505c2391
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleClientToolStruct: TSNSerializable {
    
    var reason: String? = null
    constructor() : super() { doInit(false) }
    constructor(JSONString: String) : super(JSONString) { doInit(true) }
    constructor(JSONObject: TSNJSONObject) : super(JSONObject) { doInit(true) }
    
    override fun mapping(map: TSNJSONMap) {
        super.mapping(map)
        this.reason = TSNJSONObject.getString(map, "reason") ?: null
    }
    
    override fun unmapping() {
        super.unmapping()
        this.encodeData["reason"] = this.reason
    }


}

class SimpleClientToolResponse(): TestCase("simple_client_tool_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleClientToolEndpoint()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        this.delay(1.0, f0@{  ->
              val lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
                if (lastToolCallMessage == null) {
                    this.reject("The last message should be tool call message")
                        return@f0
                }
                val toolArgsObject = SimpleClientToolStruct(JSONString = lastToolCallMessage.toolArgs ?: "{}")
                if (toolArgsObject.reason != "确定用户当前所在位置") {
                    this.reject("The last message reason should be '确定用户当前所在位置'")
                        return@f0
                }
                val tcMCPFormat = MCPToolCallResult()
                tcMCPFormat.content = mutableListOf(MCPToolCallTextContent.create("广州市"))
                lastToolCallMessage.sendToolCallResultFromMCPFormat(tcMCPFormat)
                val lastToolCallMessage2 = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
                if (lastToolCallMessage2 == null) {
                    this.reject("The last message should be tool call message")
                        return@f0
                }
                if (lastToolCallMessage2.messageStatus != NuroToolCallMessageStatus.finished_successfully) {
                    this.reject("The last message status should be finished successfully")
                        return@f0
                }
                if (lastToolCallMessage2.decodeToolCallResultAsPlainText() != "广州市") {
                    this.reject("The last message tool result should be '广州市'")
                        return@f0
                }
                this.resolve()
        })
    }

}