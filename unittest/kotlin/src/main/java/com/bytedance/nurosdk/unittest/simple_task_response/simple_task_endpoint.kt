//faf4641be97133c87ffc954c82e01d83
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleTaskServer {
    
    var res_0 = "id:6bv80kpv9dh\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"7i44woc6vf\",\"create_time\":0}\n\nid:7kyo8euj5ai\nevent:delta\ndata: {\"op\":\"add\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_hig0u4dzh6e04ruge88um09u\\\",\\\"type\\\":\\\"server_function\\\",\\\"streaming\\\":true,\\\"func\\\":{\\\"name\\\":\\\"weather_mock_get_weather_7e03a363\\\",\\\"arguments\\\":\\\"\\\"}}\"}\n\nid:4nr496c27q3\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"{\\\"\"}\n\nid:2g6n3tm37vq\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"city\"}\n\nid:bwmy136gjwo\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\":\\\"\"}\n\nid:f13d62fq0oa\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"广州\"}\n\nid:zrhkwp8x3i\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\",\\\"\"}\n\nid:1zyj3sqoy71\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"date\"}\n\nid:gprkiuenttr\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\":\\\"\"}\n\nid:gkjezljbwdc\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"202\"}\n\nid:ot5ghgurun9\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"3\"}\n\nid:i1zkk6kjpkg\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"-\"}\n\nid:0fzu7yiofm78\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"11\"}\n\nid:wzx39dr5ws\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"-\"}\n\nid:qonhx0jv9an\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"01\"}\n\nid:yydp5bszz99\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\"}\"}\n\nid:xiiij2mmi4e\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_hig0u4dzh6e04ruge88um09u\\\",\\\"type\\\":\\\"server_function\\\",\\\"streaming\\\":false,\\\"func\\\":{\\\"name\\\":\\\"weather_mock_get_weather_7e03a363\\\",\\\"arguments\\\":\\\"{\\\\\\\"city\\\\\\\":\\\\\\\"广州\\\\\\\",\\\\\\\"date\\\\\\\":\\\\\\\"2023-11-01\\\\\\\"}\\\"}}\"}\n\n"

    var res_1 = "id:iu6pn0nq3zn\nevent:message\ndata: {\"author\":{\"role\":\"tool\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849\",\"parent_message_id\":\"\",\"tool_call_id\":\"call_hig0u4dzh6e04ruge88um09u\"},\"status\":\"finished_successfully\",\"id\":\"tool_result_call_hig0u4dzh6e04ruge88um09u\",\"content\":{\"content_type\":\"text\",\"content_parts\":[{\"text\":\"{\\\"content\\\":[{\\\"type\\\":\\\"text\\\",\\\"text\\\":\\\"The 广州 weather is sunny in 2023-11-01.\\\"}]}\"}]},\"create_time\":0}\n\nid:4kekhybiyx5\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"i257rknhdqd\",\"create_time\":0}\n\nid:r4l96op31ob\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"广州\"}\n\nid:nc0eblotl2\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"今天的\"}\n\nid:u0q59kz9wwp\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"天气\"}\n\nid:xq795ns5wd\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"是\"}\n\nid:o85kdcw2bv\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"晴天\"}\n\nid:k92kvbkj23j\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"。\"}\n\nid:74bzp8imb6k\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/status\",\"value\":\"finished_successfully\"}\n\nid:rldmj2i15xm\nevent:system\ndata: {\"type\":\"stream_complete\",\"conversation_id\":\"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849\"}\n\n{\"ret\":\"0\",\"logid\":\"7d2ebb97-5e80-79d7-d54f-fd2f08aa4849\"}\n\n"

    fun injectRes0(): Unit {
        TestChunkedServer.singleton.setTexts(mutableListOf(this.res_0, this.res_1))
    }

}