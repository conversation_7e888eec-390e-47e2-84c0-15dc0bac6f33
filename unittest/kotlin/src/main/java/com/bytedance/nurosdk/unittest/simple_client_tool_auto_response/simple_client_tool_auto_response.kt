//835ee5ec8e6a577618f9ac4009744719
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleClientToolAutoResponse(): TestCase("simple_client_tool_auto_response") {
    
    init {
    
    }

    override fun run(): Unit {
        // Setup
            val server = SimpleClientToolAutoEndpoint()
        server.injectRes0()
        // Run
            val conversationManager = NuroConversationManager()
        conversationManager.connect(SSETransport("/mock", null))
        conversationManager.mcpManager = TestDevice.testMCPManager
        conversationManager.enableMCPTools()
        conversationManager.sendUserMessage(NuroUserMessage(NuroUtils.randomUUIDString(), "我当前位置是哪里"))
        this.delay(1.0, f0@{  ->
              val lastToolCallMessage = TestConversationUtils.findLastToolCallMessage(conversationManager.conversation)
                if (lastToolCallMessage == null) {
                    this.reject("The last message should be tool call message")
                        return@f0
                }
                if (lastToolCallMessage.messageStatus != NuroToolCallMessageStatus.finished_successfully) {
                    this.reject("The last message status should be finished successfully")
                        return@f0
                }
                if (lastToolCallMessage.decodeToolCallResultAsPlainText() != "佛山市") {
                    this.reject("The last message tool result should be '佛山市'")
                        return@f0
                }
                this.resolve()
        })
    }

}