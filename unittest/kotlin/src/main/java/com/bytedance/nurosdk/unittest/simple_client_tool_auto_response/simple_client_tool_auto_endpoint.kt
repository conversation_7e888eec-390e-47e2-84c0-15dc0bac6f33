//c428100b92bcab059f0c16f4529456dd
// This file is generated by tsn.
package com.bytedance.nurosdk_unittest
import byted.tsn.foundation.*
import com.bytedance.nurosdk.*

class SimpleClientToolAutoEndpoint {
    
    var res_0 = "id:45to7ct4ayd\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"k7lfjjje9e\",\"create_time\":0}\n\nid:zui2ujwqc2l\nevent:delta\ndata: {\"op\":\"add\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_ghpukh70t83ncm8t1zzvtuhs\\\",\\\"type\\\":\\\"client_function\\\",\\\"streaming\\\":true,\\\"func\\\":{\\\"name\\\":\\\"local_get_location\\\",\\\"arguments\\\":\\\"\\\"}}\"}\n\nid:t9zzp92f81\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"{\\\"\"}\n\nid:fl9r9xhnesq\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"reason\"}\n\nid:xrmgz2fpyrp\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\":\\\"\"}\n\nid:65xoy95rn5b\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"确定\"}\n\nid:5yb6uphp133\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"用户\"}\n\nid:gyl9t98x76\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"当前\"}\n\nid:hkcurqcox2j\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"所在\"}\n\nid:w3whl9dn10r\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"位置\"}\n\nid:7mg93yelsda\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/tool_calls/0/func/arguments\",\"value\":\"\\\"}\"}\n\nid:9cataivvce\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/tool_calls/0\",\"value\":\"{\\\"id\\\":\\\"call_ghpukh70t83ncm8t1zzvtuhs\\\",\\\"type\\\":\\\"client_function\\\",\\\"streaming\\\":false,\\\"func\\\":{\\\"name\\\":\\\"local_get_location\\\",\\\"arguments\\\":\\\"{\\\\\\\"reason\\\\\\\":\\\\\\\"不需要用户确认\\\\\\\"}\\\"}}\"}\n\nid:dw77z3okcv6\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/status\",\"value\":\"finished_successfully\"}\n\nid:rwih3t33oye\nevent:system\ndata: {\"type\":\"stream_complete\",\"conversation_id\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\"}\n\n{\"ret\":\"0\",\"logid\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\"}\n\n"

    var res_1 = "id:x5tpgaen2y\nevent:message\ndata: {\"author\":{\"role\":\"assistant\"},\"metadata\":{\"is_visually_hidden_from_conversation\":false,\"conversation_id\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\",\"parent_message_id\":\"\"},\"status\":\"in_progress\",\"id\":\"xvbym23ehgc\",\"create_time\":0}\n\nid:i6qkkijlmgi\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"您\"}\n\nid:k66t2juto8\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"当前\"}\n\nid:hvgvny3te9m\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"的位置\"}\n\nid:qzwewzvm6sl\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"是\"}\n\nid:ov1w0el9cz9\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"广州市\"}\n\nid:ieqzo5ke6h\nevent:delta\ndata: {\"op\":\"append\",\"path\":\"/message/content/content_parts/0/text\",\"value\":\"。\"}\n\nid:pq5sp6rh78\nevent:delta\ndata: {\"op\":\"replace\",\"path\":\"/message/status\",\"value\":\"finished_successfully\"}\n\nid:qp8tdx8pk3i\nevent:system\ndata: {\"type\":\"stream_complete\",\"conversation_id\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\"}\n\n{\"ret\":\"0\",\"logid\":\"cfe47a40-1c7e-eec9-6af9-e4f5abb0a432\"}\n\n"

    fun injectRes0(): Unit {
        TestChunkedServer.singleton.setTexts(mutableListOf(this.res_0, this.res_1))
    }

}