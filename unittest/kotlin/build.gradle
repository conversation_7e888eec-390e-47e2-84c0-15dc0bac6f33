plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

apply from: "https://tosv.byted.org/obj/ttclient-android/publish.gradle"

android {
    namespace "com.bytedance.nurosdk_unittest"
    compileSdkVersion 34
    compileSdk = 34

    defaultConfig {
        minSdkVersion 21
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        consumerProguardFiles 'consumer-rules.pro'
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.13.1'
    implementation(project(":nurosdk"))
    implementation("byted.tsn:foundation:0.1.5")
}