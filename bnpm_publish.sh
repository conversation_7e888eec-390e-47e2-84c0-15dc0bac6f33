#!/bin/bash

set -e

is_semver() {
    local version="$1"
    local semver_regex='^(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)\.(0|[1-9][0-9]*)(-[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?(\+[0-9A-Za-z-]+(\.[0-9A-Za-z-]+)*)?$'

    if echo "$version" | grep -Eq "$semver_regex"; then
        return 0
    else
        return 1
    fi
}


if ! is_semver "$version"; then
  echo "非法的版本号，正确格式为 x.x.x 或 x.x.x-beta.x"
  exit 1
fi

cd src
npm install
npm run build
cd ../
cd js
npm install
cd ../

# 执行单测
cd unittest
cd src
npm install
npm run build
cd ../
cd js
npm install
node runTests.js
cd ../
cd ../

# 发布 bnpm
cd js
npm version "$version" --no-git-tag-version

echo -e "registry = 'https://bnpm.byted.org/'\n//bnpm.byted.org/:_authToken=$CUSTOM_NPM_TOKEN" > .npmrc

# 发布bnpm
npm publish