export declare class EventStreamConfig {
    endpoint: string;
    method: string;
    headers?: Record<string, string>;
    data?: string;
    onStart: Optional<() => void>;
    onChunk: Optional<(text: string) => void>;
    onError: Optional<(code: Int, message?: string) => void>;
    onFinish: Optional<() => void>;
    onCancel: Optional<(code: Int, message?: string) => void>;
}
export declare class EventStreamAdapter {
    static fetch: Optional<(config: EventStreamConfig) => string>;
    static cancel: Optional<(cancelToken: string) => void>;
    static reconnectEndpoint: Optional<string>;
    static interruptEndpoint: Optional<string>;
    static payloadEndpoint: Optional<string>;
}
export declare class SSEImpl {
    static fetch(config: EventStreamConfig): Optional<string>;
    static cancel(cancelToken: string): void;
}
