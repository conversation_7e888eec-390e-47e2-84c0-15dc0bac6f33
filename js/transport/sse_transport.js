"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSETransport = void 0;
const conversation_1 = require("../nuro/conversation");
const message_1 = require("../nuro/message");
const transport_1 = require("./transport");
const chat_request_1 = require("../idl/chat_request");
const sse_impl_1 = require("./sse_impl");
const chat_message_1 = require("../idl/chat_message");
const sse_message_1 = require("../idl/sse_message");
const message_processor_1 = require("../nuro/message_processor");
const logger_1 = require("./logger");
const tsnfoundation_1 = require("@byted/tsnfoundation");
const system_data_1 = require("../idl/system_data");
const mocker_1 = require("../nuro/mocker");
let SSETransport = class SSETransport extends transport_1.CommonTransport {
    constructor(endpoint, headers) {
        super();
        this.headers = {};
        this.currentChunked = "";
        this.messageDataType = ""; // reasoning, text, tool.
        this.endpoint = endpoint;
        if (headers !== undefined) {
            this.headers = headers;
        }
    }
    setConversationManager(conversationManager) {
        this._conversationManager = conversationManager;
    }
    sendMessage(message, tools) {
        this.sendMessages([message], tools);
    }
    resumeMessage() {
        var _a, _b, _c, _d, _e, _f;
        let headers = {
            "Content-Type": "application/json",
        };
        for (let key in this.headers) {
            headers[key] = (_a = this.headers[key]) !== null && _a !== void 0 ? _a : "";
        }
        let resumeData = new message_1.ResumeData();
        resumeData.conversationId =
            (_c = (_b = this._conversationManager) === null || _b === void 0 ? void 0 : _b.conversation.conversationId) !== null && _c !== void 0 ? _c : "";
        let length = (_e = (_d = this._conversationManager) === null || _d === void 0 ? void 0 : _d.conversation.messages.length) !== null && _e !== void 0 ? _e : 0;
        if (length > 0) {
            let message = (_f = this._conversationManager) === null || _f === void 0 ? void 0 : _f.conversation.messages[length - 1];
            let rawId = "";
            if (message !== undefined) {
                rawId = message.getResumeMsgId();
            }
            resumeData.messageId = rawId;
        }
        this.currentChunked = "";
        this.sendSSERequest(sse_impl_1.EventStreamAdapter.reconnectEndpoint, resumeData.toJSONString(), headers, () => {
            // onChunkStart
        }, () => {
            var _a;
            // onSendMessageFailed
            message_processor_1.MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager);
            (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
        }, () => {
            var _a;
            message_processor_1.MessageProcessor.markInProgressMessagesAsCancel(this._conversationManager);
            (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
        });
    }
    sendMessages(messages, tools) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        if (messages.length === 0) {
            (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
            return;
        }
        (_b = this._conversationManager) === null || _b === void 0 ? void 0 : _b.conversation.messages.forEach((message) => {
            if (message.isFinalStatus() === false) {
                if (message instanceof message_1.NuroToolCallMessage) {
                    message.setMsgStatus(message_1.NuroToolCallMessageStatus.skipped);
                }
            }
        });
        for (let index = 0; index < messages.length; index++) {
            const msg = messages[index];
            if (msg instanceof message_1.NuroUserMessage) {
                msg.setMsgStatus(message_1.NuroUserMessageStatus.sending);
            }
        }
        const chatRequest = new chat_request_1.ChatRequest();
        chatRequest.conversationId =
            (_c = this._conversationManager) === null || _c === void 0 ? void 0 : _c.conversation.conversationId;
        chatRequest.parentMessageId = (() => {
            var _a;
            const messages = (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.messages;
            if (messages !== undefined && messages.length > 0) {
                for (let index = messages.length - 1; index >= 0; index = index - 1) {
                    const msg = messages[index];
                    if (msg !== undefined) {
                        if (msg instanceof message_1.NuroAssistantMessage ||
                            msg instanceof message_1.NuroToolCallMessage) {
                            return msg._rawId;
                        }
                    }
                }
            }
            return undefined;
        })();
        if (chatRequest.parentMessageId == undefined) {
            chatRequest.systemPrompt =
                (_d = this._conversationManager) === null || _d === void 0 ? void 0 : _d.conversation.systemPrompt;
        }
        chatRequest.messages = [];
        var previousMessageId = undefined;
        for (let index = 0; index < messages.length; index++) {
            const message = messages[index];
            const chatMessage = new chat_message_1.ChatMessage();
            chatMessage.id = message.id;
            const chatAuthor = new chat_message_1.ChatAuthor();
            chatAuthor.role =
                message instanceof message_1.NuroToolCallMessage ? "tool" : "user";
            chatMessage.author = chatAuthor;
            const content = new chat_message_1.ChatContent();
            const metadata = new chat_message_1.ChatMessageMetadata();
            if (message instanceof message_1.NuroUserMessage) {
                let contentParts = [];
                if (message.text !== undefined && message.text.length > 0) {
                    let contentPart = new chat_message_1.ChatContentPart();
                    contentPart.text = message.text;
                    contentParts.push(contentPart);
                }
                if (message.files !== undefined && message.files.length > 0) {
                    message.files.forEach((it) => {
                        var _a, _b;
                        let contentPart = new chat_message_1.ChatContentPart();
                        let file = new chat_message_1.ChatMessageFileURI();
                        file.file_type =
                            it.type === message_1.NuroFileType.image
                                ? chat_message_1.ChatMessageFileType.IMAGE
                                : chat_message_1.ChatMessageFileType.VIDEO;
                        file.url = it.url;
                        file.uri = it.uri;
                        file.extra = it.extra;
                        file.file_description = it.file_description;
                        let nuroImageMetadata = (_b = (_a = it.metadata) === null || _a === void 0 ? void 0 : _a.image_metadata) !== null && _b !== void 0 ? _b : undefined;
                        if (file.file_type === chat_message_1.ChatMessageFileType.IMAGE &&
                            nuroImageMetadata !== undefined) {
                            let imageMetadata = new chat_message_1.ChatMessageImageMetadata();
                            imageMetadata.image_width = nuroImageMetadata.width;
                            imageMetadata.image_height = nuroImageMetadata.height;
                            imageMetadata.image_prompt = nuroImageMetadata.prompt;
                            imageMetadata.image_format = nuroImageMetadata.format;
                            file.image_metadata = imageMetadata;
                        }
                        contentPart.file = file;
                        contentParts.push(contentPart);
                    });
                }
                metadata.metricsExtra = message.metadata.metricsExtra;
                if (message.referenceInfo !== undefined &&
                    message.referenceInfo.length > 0) {
                    message.referenceInfo.forEach((it) => {
                        var _a, _b;
                        let contentPart = new chat_message_1.ChatContentPart();
                        if (it.text !== undefined && it.text.length > 0) {
                            contentPart.text = it.text;
                        }
                        if (it.file !== undefined) {
                            let file = new chat_message_1.ChatMessageFileURI();
                            file.file_type =
                                it.file.type === message_1.NuroFileType.image
                                    ? chat_message_1.ChatMessageFileType.IMAGE
                                    : chat_message_1.ChatMessageFileType.VIDEO;
                            file.url = it.file.url;
                            file.uri = it.file.uri;
                            file.extra = it.file.extra;
                            file.file_description = it.file.file_description;
                            let nuroImageMetadata = (_b = (_a = it.file.metadata) === null || _a === void 0 ? void 0 : _a.image_metadata) !== null && _b !== void 0 ? _b : undefined;
                            if (file.file_type === chat_message_1.ChatMessageFileType.IMAGE &&
                                nuroImageMetadata !== undefined) {
                                let imageMetadata = new chat_message_1.ChatMessageImageMetadata();
                                imageMetadata.image_width = nuroImageMetadata.width;
                                imageMetadata.image_height = nuroImageMetadata.height;
                                imageMetadata.image_prompt = nuroImageMetadata.prompt;
                                imageMetadata.image_format = nuroImageMetadata.format;
                                file.image_metadata = imageMetadata;
                            }
                            contentPart.file = file;
                        }
                        contentPart.is_referenced = true;
                        contentParts.push(contentPart);
                    });
                }
                content.content_parts = contentParts;
            }
            else if (message instanceof message_1.NuroToolCallMessage) {
                let tollmsg = message;
                let contentPart = new chat_message_1.ChatContentPart();
                contentPart.text = (_e = tollmsg.toolResult) !== null && _e !== void 0 ? _e : "";
                content.content_parts = [contentPart];
                metadata.tool_call_id = tollmsg.toolCallId;
            }
            chatMessage.content = content;
            chatMessage.tools = tools.map((it) => {
                const toolItem = new chat_message_1.ChatTool();
                toolItem.id = it.name;
                toolItem.type = chat_message_1.ChatToolType.client_function;
                toolItem.name = it.serverName + "_" + it.name;
                toolItem.description = it.description;
                toolItem.parameters = it.inputSchema;
                return toolItem;
            });
            metadata.conversation_id = (_f = chatRequest.conversationId) !== null && _f !== void 0 ? _f : "";
            metadata.parent_message_id =
                (_g = previousMessageId !== null && previousMessageId !== void 0 ? previousMessageId : chatRequest.parentMessageId) !== null && _g !== void 0 ? _g : "";
            chatMessage.metadata = metadata;
            chatMessage.create_time = message.createTime;
            (_h = chatRequest.messages) === null || _h === void 0 ? void 0 : _h.push(chatMessage);
            previousMessageId = message.id;
        }
        let headers = {
            "Content-Type": "application/json",
        };
        for (let key in this.headers) {
            headers[key] = (_j = this.headers[key]) !== null && _j !== void 0 ? _j : "";
        }
        this.currentChunked = "";
        this.sendSSERequest(this.endpoint, chatRequest.toJSONString(), headers, () => {
            // onChunkStart
        }, () => {
            var _a;
            // onSendMessageFailed
            message_processor_1.MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager);
            (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
        }, () => {
            var _a;
            message_processor_1.MessageProcessor.markInProgressMessagesAsCancel(this._conversationManager);
            (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
        });
    }
    sendSSERequest(endpoint, chatRequest, headers, onChunkStart, onMessageFailed, onConversationCancel) {
        if (mocker_1.NuroMockManager.isMocking()) {
            return;
        }
        if (endpoint === undefined) {
            return;
        }
        const esConfig = new sse_impl_1.EventStreamConfig();
        esConfig.endpoint = endpoint;
        esConfig.method = "POST";
        esConfig.headers = headers;
        esConfig.data = chatRequest !== null && chatRequest !== void 0 ? chatRequest : "";
        let chunkedStarted = false;
        esConfig.onChunk = (text) => {
            logger_1.NuroLogger.debug("SSETransport", () => `received chunk, ${text}`);
            if (chunkedStarted === false) {
                chunkedStarted = true;
                onChunkStart();
            }
            this.currentChunked = this.currentChunked + text;
            this.flushChuck(false);
        };
        esConfig.onFinish = () => {
            logger_1.NuroLogger.debug("SSETransport", () => `received finish`);
            this.flushChuck(true);
        };
        esConfig.onError = (code, message) => {
            logger_1.NuroLogger.debug("SSETransport", () => `received error, code = ${code.toString()}, message = ${message !== null && message !== void 0 ? message : ""}`);
            this.flushChuck(true);
            onMessageFailed();
        };
        esConfig.onCancel = (code, message) => {
            logger_1.NuroLogger.debug("SSETransport", () => `received cancel`);
            this.flushChuck(true);
            onConversationCancel();
        };
        logger_1.NuroLogger.debug("SSETransport", () => `send sse request, ${chatRequest}`);
        this.token = sse_impl_1.SSEImpl.fetch(esConfig);
    }
    flushChuck(ended) {
        let originalParts = this.currentChunked.split("\n\n");
        let partsLength = originalParts.length;
        let currentIndex = -1;
        originalParts.forEach((part) => {
            var _a;
            currentIndex = currentIndex + 1;
            if (currentIndex === partsLength - 1) {
                if (ended === false) {
                    // last part
                    this.currentChunked = part;
                    return;
                }
            }
            if (part.indexOf("logid") > 0 &&
                part.indexOf("ret") > 0 &&
                part.indexOf("{") === 0) {
                let res = new sse_message_1.SSEFinalMessage({
                    JSONString: part,
                });
                if (res.ret !== undefined) {
                    if (res.ret === "0") {
                        message_processor_1.MessageProcessor.markLastUserMessageAsFinished(this._conversationManager);
                    }
                    else {
                        message_processor_1.MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager);
                    }
                    (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
                    return;
                }
            }
            let id = "";
            let event = "";
            let data = "";
            part.split("\n").forEach((line) => {
                if (line.indexOf("id:") === 0) {
                    id = line.substr(3).trim();
                }
                else if (line.indexOf("event:") === 0) {
                    event = line.substr(6).trim();
                }
                else if (line.indexOf("data:") === 0) {
                    data = line.substr(5).trim();
                }
                if (id !== "" && event !== "" && data !== "") {
                    this.onSSEEvent(id, event, data);
                }
            });
        });
        if (ended === true) {
            this.currentChunked = "";
        }
    }
    onSSEEvent(id, event, data) {
        var _a, _b, _c;
        if (event === "message") {
            logger_1.NuroLogger.debug("SSETransport", () => `received message, ${data}`);
            const _chatMessage = new chat_message_1.ChatMessage({ JSONString: data });
            const coversationId = (_a = _chatMessage.metadata) === null || _a === void 0 ? void 0 : _a.conversation_id;
            this.currentChatMessage = _chatMessage;
            if (this._conversationManager !== undefined &&
                coversationId !== undefined) {
                this._conversationManager.conversation.conversationId = coversationId;
            }
            const contentParts = (_b = _chatMessage.content) === null || _b === void 0 ? void 0 : _b.content_parts;
            if (contentParts !== undefined && contentParts.length > 0) {
                message_processor_1.MessageProcessor.convertChatMessageToNuroMessage(this._conversationManager, _chatMessage, message_processor_1.ConvertType.new_message).forEach((it) => {
                    var _a;
                    message_processor_1.MessageProcessor.markLastUserMessageAsFinished(this._conversationManager);
                    (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(it);
                });
            }
        }
        else if (event === "delta") {
            logger_1.NuroLogger.debug("SSETransport", () => `received delta, ${data}`);
            let delta = new sse_message_1.SSEDeltaMessage({ JSONString: data });
            let deltaPath = delta.path;
            if (deltaPath !== undefined &&
                deltaPath.indexOf("/message/content/content_parts/") >= 0) {
                this.messageDataType = "assistant";
            }
            else if (deltaPath !== undefined &&
                deltaPath.indexOf("/message/tool_calls/") >= 0) {
                // 输出 tool 代表 reasoning 结束
                message_processor_1.MessageProcessor.markMessagesAsFinished(this._conversationManager);
                this.messageDataType = "tool_call";
            }
            if (this.currentChatMessage !== undefined) {
                this.currentChatMessage.applyPatch(delta, undefined);
                message_processor_1.MessageProcessor.convertChatMessageToNuroMessage(this._conversationManager, this.currentChatMessage, message_processor_1.ConvertType.new_message).forEach((it) => {
                    var _a;
                    message_processor_1.MessageProcessor.markLastUserMessageAsFinished(this._conversationManager);
                    (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(it);
                });
            }
        }
        else if (event === "system" && data.indexOf("stream_error") > 0) {
            logger_1.NuroLogger.error("SSETransport", () => `received system stream_error, ${data}`);
            message_processor_1.MessageProcessor.markInProgressMessagesAsFailed(this._conversationManager);
            // failed, so change to ready to send message.
        }
        else if (event === "system") {
            let sysData = new system_data_1.SystemData({ JSONString: data });
            logger_1.NuroLogger.debug("SSETransport", () => `received system data, ${data}`);
            if (sysData.type === system_data_1.SystemDataType.stream_complete) {
                message_processor_1.MessageProcessor.markMessagesAsFinished(this._conversationManager);
            }
            if (sysData.type === system_data_1.SystemDataType.summary &&
                this._conversationManager !== undefined &&
                this._conversationManager.conversation !== undefined) {
                this._conversationManager.conversation.summary = sysData.content;
            }
            let listeners = (_c = this._conversationManager) === null || _c === void 0 ? void 0 : _c.conversation.systemDataListeners;
            if (listeners !== undefined) {
                tsnfoundation_1.TSNMapUtils.forEach(listeners, (key, listener) => {
                    listener(sysData);
                });
            }
        }
    }
};
SSETransport = __decorate([
    tsnfoundation_1.TSNOpenClass
], SSETransport);
exports.SSETransport = SSETransport;
//# sourceMappingURL=sse_transport.js.map