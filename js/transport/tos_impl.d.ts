import { NuroFile, NuroLocalFile } from "../nurosdk";
export declare class TOSFileUploadConfig {
    nuroFile: NuroFile;
    localFile: NuroLocalFile;
    onFinish: Optional<() => void>;
    onError: Optional<(code: Int, message?: string) => void>;
    onCancel: Optional<(code: Int, message?: string) => void>;
    constructor(nuroFile: NuroFile, localFile: NuroLocalFile);
}
export declare class TOSFileUploadAdapter {
    static upload: Optional<(config: TOSFileUploadConfig) => string>;
    static cancel: Optional<(cancelToken: string) => void>;
}
export declare class TOSFileUploadImpl {
    static upload(config: TOSFileUploadConfig): Optional<string>;
    static cancel(cancelToken: string): void;
}
