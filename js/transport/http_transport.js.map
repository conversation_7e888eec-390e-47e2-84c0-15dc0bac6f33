{"version": 3, "file": "http_transport.js", "sourceRoot": "", "sources": ["../../src/transport/http_transport.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,qCAAsC;AACtC,yCAAwD;AACxD,wDAAgE;AAEhE,MAAa,aAAa;IAA1B;QACU,mBAAc,GAAW,EAAE,CAAC;IAsDtC,CAAC;IApDC,WAAW,CACT,QAA0B,EAC1B,OAAyB,EAAE,EAC3B,UAAkC,EAAE,EACpC,eAAyC,EACzC,YAAqD;QAErD,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO;SACR;QAED,MAAM,QAAQ,GAAG,IAAI,4BAAiB,EAAE,CAAC;QACzC,QAAQ,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7B,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QACzB,QAAQ,CAAC,OAAO,GAAG,OAAO,CAAC;QAC3B,QAAQ,CAAC,IAAI,GAAG,IAAI,aAAJ,IAAI,cAAJ,IAAI,GAAI,EAAE,CAAC;QAC3B,QAAQ,CAAC,OAAO,GAAG,CAAC,IAAY,EAAE,EAAE;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;QACnD,CAAC,CAAC;QACF,QAAQ,CAAC,QAAQ,GAAG,GAAG,EAAE;YACvB,IAAI,MAAM,GAAG,IAAI,QAAQ,CAAC,EAAE,UAAU,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;YAC/D,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,EAAE;gBACrB,mBAAU,CAAC,KAAK,CACd,eAAe,EACf,GAAG,EAAE,CACH,gCAAgC,MAAM,CAAC,GAAG,gBAAgB,QAAQ,YAAY,IAAI,EAAE,CACvF,CAAC;gBACF,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;aACtC;iBAAM;gBACL,mBAAU,CAAC,KAAK,CACd,eAAe,EACf,GAAG,EAAE,CACH,gCAAgC,MAAM,CAAC,GAAG,cAAc,MAAM,CAAC,MAAM,gBAAgB,QAAQ,YAAY,IAAI,EAAE,CAClH,CAAC;gBACF,YAAY,CAAC,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;aACzC;QACH,CAAC,CAAC;QACF,QAAQ,CAAC,OAAO,GAAG,CAAC,IAAS,EAAE,OAAgB,EAAE,EAAE;YACjD,mBAAU,CAAC,KAAK,CACd,eAAe,EACf,GAAG,EAAE,CACH,gCAAgC,IAAI,eAAe,OAAO,gBAAgB,QAAQ,YAAY,IAAI,EAAE,CACvG,CAAC;YACF,YAAY,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,OAAO,CAAC,CAAC;QACzC,CAAC,CAAC;QACF,mBAAU,CAAC,KAAK,CACd,eAAe,EACf,GAAG,EAAE,CACH,2BAA2B,QAAQ,YAAY,IAAI,cAAc,QAAQ,CAAC,MAAM,EAAE,CACrF,CAAC;QACF,kBAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC1B,CAAC;CACF;AAvDD,sCAuDC;AAED,MAAa,QAAS,SAAQ,+BAAe;IAA7C;;QACkB,QAAG,GAAW,GAAG,CAAC;QACf,WAAM,GAAY,SAAS,CAAC;QAC3B,YAAO,GAAY,SAAS,CAAC;QAC/B,UAAK,GAAY,SAAS,CAAC;IAC/C,CAAC;CAAA;AAJiB;IAAf,IAAA,uBAAO,EAAC,KAAK,CAAC;qCAAmB;AACf;IAAlB,IAAA,uBAAO,EAAC,QAAQ,CAAC;wCAA6B;AAC3B;IAAnB,IAAA,uBAAO,EAAC,SAAS,CAAC;yCAA8B;AAC/B;IAAjB,IAAA,uBAAO,EAAC,OAAO,CAAC;uCAA4B;AAJ/C,4BAKC"}