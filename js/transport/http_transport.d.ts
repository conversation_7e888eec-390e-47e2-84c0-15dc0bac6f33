import { TSNSerializable } from "@byted/tsnfoundation";
export declare class HttpTransport {
    private currentChunked;
    sendRequest(endpoint: Optional<string>, data: Optional<string>, headers: Record<string, string> | undefined, successCallback: (result: string) => void, failCallback: (code: string, reason?: string) => void): void;
}
export declare class HttpData extends TSNSerializable {
    ret: string;
    errmsg?: string;
    systime?: string;
    logid?: string;
}
