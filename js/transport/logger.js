"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroLogger = exports.NuroLoggerAdapter = exports.NuroLogLevel = void 0;
// Define log levels
var NuroLogLevel;
(function (NuroLogLevel) {
    NuroLogLevel[NuroLogLevel["DEBUG"] = 0] = "DEBUG";
    NuroLogLevel[NuroLogLevel["INFO"] = 1] = "INFO";
    NuroLogLevel[NuroLogLevel["ERROR"] = 2] = "ERROR";
    NuroLogLevel[NuroLogLevel["NONE"] = 3] = "NONE";
})(NuroLogLevel = exports.NuroLogLevel || (exports.NuroLogLevel = {}));
class NuroLoggerAdapter {
}
exports.NuroLoggerAdapter = NuroLoggerAdapter;
class NuroLogger {
    static setLogLevel(level) {
        NuroLogger.currentLevel = level;
    }
    static debug(tag, msg) {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.DEBUG.valueOf()) {
            const logger = NuroLoggerAdapter.debug;
            if (logger !== undefined) {
                logger(tag, `[DEBUG] ${msg()}`);
            }
        }
    }
    static info(tag, msg) {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.INFO.valueOf()) {
            const logger = NuroLoggerAdapter.info;
            if (logger !== undefined) {
                logger(tag, `[INFO] ${msg()}`);
            }
        }
    }
    static error(tag, msg) {
        if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.ERROR.valueOf()) {
            const logger = NuroLoggerAdapter.error;
            if (logger !== undefined) {
                logger(tag, `[ERROR] ${msg()}`);
            }
        }
    }
}
exports.NuroLogger = NuroLogger;
NuroLogger.currentLevel = NuroLogLevel.ERROR; // Default log level
//# sourceMappingURL=logger.js.map