"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpData = exports.HttpTransport = void 0;
const logger_1 = require("./logger");
const sse_impl_1 = require("./sse_impl");
const tsnfoundation_1 = require("@byted/tsnfoundation");
class HttpTransport {
    constructor() {
        this.currentChunked = "";
    }
    sendRequest(endpoint, data = "", headers = {}, successCallback, failCallback) {
        if (endpoint === undefined) {
            return;
        }
        const esConfig = new sse_impl_1.EventStreamConfig();
        esConfig.endpoint = endpoint;
        esConfig.method = "POST";
        esConfig.headers = headers;
        esConfig.data = data !== null && data !== void 0 ? data : "";
        esConfig.onChunk = (text) => {
            this.currentChunked = this.currentChunked + text;
        };
        esConfig.onFinish = () => {
            let result = new HttpData({ JSONString: this.currentChunked });
            if (result.ret == "0") {
                logger_1.NuroLogger.debug("HttpTransport", () => `sendRequest: onFinish: ret = ${result.ret}, endpoint = ${endpoint}, data = ${data}`);
                successCallback(this.currentChunked);
            }
            else {
                logger_1.NuroLogger.error("HttpTransport", () => `sendRequest: onFinish: ret = ${result.ret}, errmsg = ${result.errmsg}, endpoint = ${endpoint}, data = ${data}`);
                failCallback(result.ret, result.errmsg);
            }
        };
        esConfig.onError = (code, message) => {
            logger_1.NuroLogger.error("HttpTransport", () => `sendRequest: onError: code = ${code}, message = ${message}, endpoint = ${endpoint}, data = ${data}`);
            failCallback(code.toString(), message);
        };
        logger_1.NuroLogger.debug("HttpTransport", () => `sendRequest: endpoint = ${endpoint}, data = ${data}, method = ${esConfig.method}`);
        sse_impl_1.SSEImpl.fetch(esConfig);
    }
}
exports.HttpTransport = HttpTransport;
class HttpData extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.ret = "0";
        this.errmsg = undefined;
        this.systime = undefined;
        this.logid = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("ret")
], HttpData.prototype, "ret", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("errmsg")
], HttpData.prototype, "errmsg", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("systime")
], HttpData.prototype, "systime", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("logid")
], HttpData.prototype, "logid", void 0);
exports.HttpData = HttpData;
//# sourceMappingURL=http_transport.js.map