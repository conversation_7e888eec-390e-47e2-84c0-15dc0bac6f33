"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSEImpl = exports.EventStreamAdapter = exports.EventStreamConfig = void 0;
class EventStreamConfig {
    constructor() {
        this.endpoint = "";
        this.method = "POST";
    }
}
exports.EventStreamConfig = EventStreamConfig;
class EventStreamAdapter {
}
exports.EventStreamAdapter = EventStreamAdapter;
class SSEImpl {
    static fetch(config) {
        const fetcher = EventStreamAdapter.fetch;
        if (fetcher !== undefined) {
            return fetcher(config);
        }
        return undefined;
    }
    static cancel(cancelToken) {
        const canceler = EventStreamAdapter.cancel;
        if (canceler !== undefined) {
            canceler(cancelToken);
        }
    }
}
exports.SSEImpl = SSEImpl;
//# sourceMappingURL=sse_impl.js.map