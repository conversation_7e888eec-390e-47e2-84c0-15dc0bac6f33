import { NuroMCPToolItem } from "../nurosdk";
import type { NuroConversationManager } from "../nuro/conversation_manager";
import type { NuroMessage } from "../nuro/message";
export declare abstract class CommonTransport {
    abstract setConversationManager(conversationManager: NuroConversationManager): void;
    abstract sendMessage(message: NuroMessage, tools: NuroMCPToolItem[]): void;
    abstract sendMessages(messages: NuroMessage[], tools: NuroMCPToolItem[]): void;
    token: Optional<string>;
    abstract resumeMessage(): void;
}
