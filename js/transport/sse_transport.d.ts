import { NuroMessage } from "../nuro/message";
import type { NuroConversationManager } from "../nuro/conversation_manager";
import { CommonTransport } from "./transport";
import { NuroMCPToolItem } from "../nuro/mcp";
export declare class SSETransport extends CommonTransport {
    private _conversationManager?;
    private endpoint;
    private headers;
    private currentChunked;
    private currentChatMessage;
    private messageDataType;
    constructor(endpoint: string, headers?: Record<string, string>);
    setConversationManager(conversationManager: NuroConversationManager): void;
    sendMessage(message: NuroMessage, tools: NuroMCPToolItem[]): void;
    resumeMessage(): void;
    sendMessages(messages: NuroMessage[], tools: NuroMCPToolItem[]): void;
    sendSSERequest(endpoint: Optional<string>, chatRequest: Optional<string>, headers: Record<string, string>, onChunkStart: () => void, onMessageFailed: () => void, onConversationCancel: () => void): void;
    flushChuck(ended: boolean): void;
    onSSEEvent(id: string, event: string, data: string): void;
}
