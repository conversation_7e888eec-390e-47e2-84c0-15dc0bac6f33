"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOSFileUploadImpl = exports.TOSFileUploadAdapter = exports.TOSFileUploadConfig = void 0;
class TOSFileUploadConfig {
    constructor(nuroFile, localFile) {
        this.nuroFile = nuroFile;
        this.localFile = localFile;
    }
}
exports.TOSFileUploadConfig = TOSFileUploadConfig;
class TOSFileUploadAdapter {
}
exports.TOSFileUploadAdapter = TOSFileUploadAdapter;
class TOSFileUploadImpl {
    static upload(config) {
        const uploader = TOSFileUploadAdapter.upload;
        if (uploader !== undefined) {
            return uploader(config);
        }
        return undefined;
    }
    static cancel(cancelToken) {
        const canceler = TOSFileUploadAdapter.cancel;
        if (canceler !== undefined) {
            canceler(cancelToken);
        }
    }
}
exports.TOSFileUploadImpl = TOSFileUploadImpl;
//# sourceMappingURL=tos_impl.js.map