export declare enum NuroLogLevel {
    DEBUG = 0,
    INFO = 1,
    ERROR = 2,
    NONE = 3
}
export declare class NuroLoggerAdapter {
    static debug: Optional<(tag: string, msg: string) => void>;
    static info: Optional<(tag: string, msg: string) => void>;
    static error: Optional<(tag: string, msg: string) => void>;
}
export declare class NuroLogger {
    private static currentLevel;
    static setLogLevel(level: NuroLogLevel): void;
    static debug(tag: string, msg: () => string): void;
    static info(tag: string, msg: () => string): void;
    static error(tag: string, msg: () => string): void;
}
