export declare class NuroSetting {
    /**
     * 是否需要展示 Server 端的 Function 调用消息
     * 如果为 true，则会在 Conversation.messages 中带有对应的 ToolCallMessage。
     */
    static needDisplayServerFunctionMessage: boolean;
    /**
     * 请求版本
     */
    static version: string;
    /**
     *
     */
    static canvasSettings: CanvasSettings;
    /**
     * agent之间的屏蔽toolCall
     */
    static shieldToolCall: string[];
}
export interface CanvasSettings {
    startNode: string;
    endNode: string;
    nodes: string[];
}
