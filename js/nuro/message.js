"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroCanvasMessage = exports.NuroCanvasStatus = exports.NuroCanvasNode = exports.getNodeFromNuroToolCallMsg = exports.CANVAS_ADD_TO_END = exports.CANVAS_DEFAULT = exports.NuroToolCallMessage = exports.NuroAssistantMessage = exports.NuroReasoningMessage = exports.NuroUserMessage = exports.RefContent = exports.ReferenceRole = exports.NuroFile = exports.NuroFileMetadata = exports.NuroImageMetadata = exports.NuroLocalFile = exports.NuroFileType = exports.NuroMessage = exports.NuroTaskMessageType = exports.ResumeData = exports.InterruptData = exports.PayLoadData = exports.NuroMessageType = exports.NuroToolCallMessageStatus = exports.NuroReasoningMessageStatus = exports.NuroAssistantMessageStatus = exports.NuroUserMessageStatus = exports.ChatMessageError = void 0;
const http_transport_1 = require("../transport/http_transport");
const tsnfoundation_1 = require("@byted/tsnfoundation");
const chat_message_1 = require("../idl/chat_message");
const mcp_tool_result_1 = require("../mcp/mcp_tool_result");
const sse_impl_1 = require("../transport/sse_impl");
const mocker_1 = require("./mocker");
var ChatMessageError;
(function (ChatMessageError) {
    /**
     * 推流前异常错误
     * 推流失败，比如第一条事件就推送失败了
     */
    ChatMessageError["interrupt_status"] = "interrupt_status";
    /**
     * 推流前异常错误
     * pe策略失败
     */
    ChatMessageError["pe_policy_failed_status"] = "pe_policy_failed_status";
    /**
     * 推流前异常错误
     * 获取流失败
     */
    ChatMessageError["chat_stream_failed_status"] = "chat_stream_failed_status";
    /**
     * 安全审核拦截
     * 输入文本审核拦截
     */
    ChatMessageError["input_text_block_status"] = "input_text_block_status";
    /**
     * 安全审核拦截
     * 输出文本审核拦截
     */
    ChatMessageError["output_text_block_status"] = "output_text_block_status";
    /**
     * 推流异常状态
     * 推送思考内容截止
     */
    ChatMessageError["send_reasoning_content_stop_status"] = "send_reasoning_content_stop_status";
    /**
     * 推流异常状态
     * 推送内容截止
     */
    ChatMessageError["send_content_stop_status"] = "send_content_stop_status";
    /**
     * sdk返回值错误
     * sse请求错误
     */
    ChatMessageError["send_failed"] = "send_failed";
    /**
     * sdk返回值错误
     * 上传文件失败
     */
    ChatMessageError["upload_failed"] = "upload_failed";
})(ChatMessageError = exports.ChatMessageError || (exports.ChatMessageError = {}));
var NuroUserMessageStatus;
(function (NuroUserMessageStatus) {
    /**
     * 刚创建状态
     */
    NuroUserMessageStatus["none"] = "none";
    /**
     * 正在上传图片/视频
     */
    NuroUserMessageStatus["uploading_files"] = "uploading_files";
    /**
     * 正在发送
     */
    NuroUserMessageStatus["sending"] = "sending";
    /**
     * 收到返回的 AssistantMessage
     * 收到一点点立即变状态，不会等AssistantMessage完整收到再改变改状态
     */
    NuroUserMessageStatus["finished_successfully"] = "finished_successfully";
    /**
     * 失败
     */
    NuroUserMessageStatus["failed"] = "failed";
    /**
     * 取消
     */
    NuroUserMessageStatus["cancelled"] = "cancelled";
})(NuroUserMessageStatus = exports.NuroUserMessageStatus || (exports.NuroUserMessageStatus = {}));
var NuroAssistantMessageStatus;
(function (NuroAssistantMessageStatus) {
    /**
     * 刚创建状态
     */
    NuroAssistantMessageStatus["none"] = "none";
    /**
     * 正在回传数据流
     */
    NuroAssistantMessageStatus["streaming"] = "streaming";
    /**
     * 数据流返回完成
     */
    NuroAssistantMessageStatus["finished_successfully"] = "finished_successfully";
    /**
     * 失败
     */
    NuroAssistantMessageStatus["failed"] = "failed";
    /**
     * 取消
     */
    NuroAssistantMessageStatus["cancelled"] = "cancelled";
})(NuroAssistantMessageStatus = exports.NuroAssistantMessageStatus || (exports.NuroAssistantMessageStatus = {}));
var NuroReasoningMessageStatus;
(function (NuroReasoningMessageStatus) {
    /**
     * 刚创建状态
     */
    NuroReasoningMessageStatus["none"] = "none";
    /**
     * 正在回传数据流
     */
    NuroReasoningMessageStatus["streaming"] = "streaming";
    /**
     * 数据流返回完成
     */
    NuroReasoningMessageStatus["finished_successfully"] = "finished_successfully";
    /**
     * 失败
     */
    NuroReasoningMessageStatus["failed"] = "failed";
    /**
     * 取消
     */
    NuroReasoningMessageStatus["cancelled"] = "cancelled";
})(NuroReasoningMessageStatus = exports.NuroReasoningMessageStatus || (exports.NuroReasoningMessageStatus = {}));
var NuroToolCallMessageStatus;
(function (NuroToolCallMessageStatus) {
    /**
     * 刚创建状态
     */
    NuroToolCallMessageStatus["none"] = "none";
    /**
     * 工具参数正在流式传输中，上层业务不应允许用户操作本工具。
     */
    NuroToolCallMessageStatus["streaming"] = "streaming";
    /**
     * 流式传输失败，可能是 LLM 或 Host Agent 或网络异常原因。
     */
    NuroToolCallMessageStatus["streaming_failed"] = "streaming_failed";
    /**
     * 流式传输取消，可能是用户主动取消
     */
    NuroToolCallMessageStatus["streaming_cancelled"] = "streaming_cancelled";
    /**
     * 正在调用本地或远端方法
     * 此处执行过程中，toolResult 有可能被流式更新。
     */
    NuroToolCallMessageStatus["invoking"] = "invoking";
    /**
     * 本地方法无法直接返回结果
     * 等 sendToolResultMessage 方法调用返回结果
     */
    NuroToolCallMessageStatus["wait_user_response"] = "wait_user_response";
    /**
     * 用户发送其他消息，不再通过sendToolResultMessage 回调结果
     */
    NuroToolCallMessageStatus["skipped"] = "skipped";
    /**
     * 方法成功回调结果
     */
    NuroToolCallMessageStatus["finished_successfully"] = "finished_successfully";
})(NuroToolCallMessageStatus = exports.NuroToolCallMessageStatus || (exports.NuroToolCallMessageStatus = {}));
var NuroMessageType;
(function (NuroMessageType) {
    NuroMessageType["user"] = "user";
    NuroMessageType["assistant"] = "assistant";
    NuroMessageType["reasoning"] = "reasoning";
    NuroMessageType["toolcall"] = "tool_call";
    NuroMessageType["canvas"] = "canvas";
})(NuroMessageType = exports.NuroMessageType || (exports.NuroMessageType = {}));
/**
 *  PayLoadData
 */
class PayLoadData extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.conversationId = "";
        this.messageId = "";
        this.payload = ""; //传submit_id
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], PayLoadData.prototype, "conversationId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("message_id")
], PayLoadData.prototype, "messageId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("payload")
], PayLoadData.prototype, "payload", void 0);
exports.PayLoadData = PayLoadData;
/**
 *  InterruptData
 */
class InterruptData extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.conversationId = "";
        this.messageId = "";
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], InterruptData.prototype, "conversationId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("message_id")
], InterruptData.prototype, "messageId", void 0);
exports.InterruptData = InterruptData;
/**
 *  ResumeData
 */
class ResumeData extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.conversationId = "";
        this.messageId = "";
        this.from_first_message = true;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], ResumeData.prototype, "conversationId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("message_id")
], ResumeData.prototype, "messageId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("from_first_message")
], ResumeData.prototype, "from_first_message", void 0);
exports.ResumeData = ResumeData;
var NuroTaskMessageType;
(function (NuroTaskMessageType) {
    NuroTaskMessageType["promptMessage"] = "promptMessage";
    NuroTaskMessageType["middlewareMessage"] = "middlewareMessage";
    NuroTaskMessageType["artifactMessage"] = "artifactMessage";
    NuroTaskMessageType["shieldMessage"] = "shieldMessage";
})(NuroTaskMessageType = exports.NuroTaskMessageType || (exports.NuroTaskMessageType = {}));
class NuroMessage {
    constructor(id, type) {
        this.updated = 0;
        this.createTime = 0;
        this.metadata = new chat_message_1.ChatMessageMetadata();
        /**
         * 原始的消息 id，用于标识服务端消息的唯一性，请勿在前端使用！！！
         */
        this._rawId = "";
        /**
         * 消息索引值，用于排序
         */
        this._messageIndex = 0;
        this.id = id;
        this.type = type;
        this.updated = 0;
    }
    setMessagePayload(payload, successCallback, failCallback) {
        var _a, _b, _c;
        if (sse_impl_1.EventStreamAdapter.payloadEndpoint === undefined) {
            return;
        }
        this.metadata.payload = payload;
        let payLoadData = new PayLoadData();
        payLoadData.messageId = this._rawId;
        payLoadData.conversationId =
            (_b = (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.conversationId) !== null && _b !== void 0 ? _b : "";
        payLoadData.payload = payload;
        new http_transport_1.HttpTransport().sendRequest(sse_impl_1.EventStreamAdapter.payloadEndpoint, (_c = payLoadData.toJSONString()) !== null && _c !== void 0 ? _c : "", {}, successCallback, (code, reason) => {
            failCallback(code, reason);
        });
    }
    baseCopy(message) {
        message.updated = this.updated;
        message.createTime = this.createTime;
        message.errorMsg = this.errorMsg;
        message.metadata = this.metadata;
        message._rawId = this._rawId;
        message._conversationManager = this._conversationManager;
        message.taskMessageType = this.taskMessageType;
        message._task = this._task;
        message.endTurn = this.endTurn;
        return message;
    }
    copy() {
        const message = new NuroMessage(this.id, this.type);
        this.baseCopy(message);
        return message;
    }
    isDisplay() {
        var _a, _b;
        if (this.taskMessageType === NuroTaskMessageType.middlewareMessage) {
            let show = (_b = (_a = this._task) === null || _a === void 0 ? void 0 : _a.isDisplayingMiddlewareMessage(this)) !== null && _b !== void 0 ? _b : true;
            return show;
        }
        else {
            return true;
        }
    }
    isFinalStatus() {
        return true;
    }
    isCancelledStatus() {
        return true;
    }
    isFailedStatus() {
        return true;
    }
    isEqualToObject(message) {
        if (message instanceof NuroMessage) {
            return (this.id === message.id &&
                this.type === message.type &&
                this.createTime === message.createTime &&
                this.errorMsg === message.errorMsg &&
                this.metadata.isEqualToObject(message.metadata) === true &&
                this.taskMessageType === message.taskMessageType &&
                this.endTurn === message.endTurn);
        }
        return false;
    }
    /**
     * 获取这条消息 对应的 这轮回话里面的 相关消息（包含 NuroUserMessage）
     */
    getMessageGroup() {
        var _a, _b, _c, _d;
        let msgs = (_b = (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.conversation.messages) !== null && _b !== void 0 ? _b : [];
        let targetIndex = -1;
        for (let i = 0; i < msgs.length; i = i + 1) {
            let id = (_d = (_c = msgs[i]) === null || _c === void 0 ? void 0 : _c.id) !== null && _d !== void 0 ? _d : "";
            if (id === this.id) {
                targetIndex = i;
                break;
            }
        }
        if (targetIndex === -1)
            return [];
        // 向前查找：包含第一个遇到的 type='a'
        let startIndex = 0;
        for (let i = targetIndex; i >= 0; i = i - 1) {
            if (msgs[i] instanceof NuroUserMessage) {
                startIndex = i;
                break;
            }
            // 若到头部仍未找到 a，从索引 0 开始
            if (i === 0)
                startIndex = 0;
        }
        // 向后查找：不包含第一个遇到的 type='a'
        let endIndex = msgs.length;
        for (let i = targetIndex + 1; i < msgs.length; i = i + 1) {
            if (msgs[i] instanceof NuroUserMessage) {
                endIndex = i;
                break;
            }
        }
        return msgs.slice(startIndex, endIndex);
    }
    needResume() {
        if (this === undefined) {
            return false;
        }
        if (this instanceof NuroUserMessage) {
            return false;
        }
        return !this.isFinalStatus();
    }
    getResumeMsgId() {
        return this._rawId;
    }
}
exports.NuroMessage = NuroMessage;
var NuroFileType;
(function (NuroFileType) {
    NuroFileType["image"] = "image";
    NuroFileType["video"] = "video";
})(NuroFileType = exports.NuroFileType || (exports.NuroFileType = {}));
class NuroLocalFile {
    constructor(localPath, localFileObject) {
        this.localPath = localPath;
        this.localFileObject = localFileObject;
    }
    isEqualToObject(other) {
        if (other instanceof NuroLocalFile) {
            return this.localPath === other.localPath;
        }
        return false;
    }
}
exports.NuroLocalFile = NuroLocalFile;
class NuroImageMetadata {
    isEqualToObject(other) {
        if (other instanceof NuroImageMetadata) {
            return (this.width === other.width &&
                this.height === other.height &&
                this.format === other.format &&
                this.prompt === other.prompt);
        }
        return false;
    }
}
exports.NuroImageMetadata = NuroImageMetadata;
class NuroFileMetadata {
    constructor(imageMetadata) {
        this.image_metadata = imageMetadata;
    }
    isEqualToObject(other) {
        var _a;
        if (other instanceof NuroFileMetadata) {
            if (this.image_metadata === undefined &&
                other.image_metadata === undefined) {
                return true;
            }
            else if (this.image_metadata !== undefined &&
                other.image_metadata !== undefined) {
                return (((_a = this.image_metadata) === null || _a === void 0 ? void 0 : _a.isEqualToObject(other.image_metadata)) === true);
            }
            else {
                return false;
            }
        }
        return false;
    }
}
exports.NuroFileMetadata = NuroFileMetadata;
class NuroFile {
    constructor(type, url, mimeType, localFile = undefined, file_description) {
        this.type = type;
        this.url = url;
        this.mimeType = mimeType;
        this.localFile = localFile;
        this.file_description = file_description;
    }
    isEqualToObject(other) {
        if (other instanceof NuroFile) {
            let localFileEqual = false;
            if (this.localFile !== undefined && other.localFile !== undefined) {
                localFileEqual =
                    this.localFile.isEqualToObject(other.localFile) === true;
            }
            else if (this.localFile === undefined &&
                other.localFile === undefined) {
                localFileEqual = true;
            }
            let localMetadataEqual = false;
            if (this.metadata !== undefined && other.metadata !== undefined) {
                localMetadataEqual =
                    this.metadata.isEqualToObject(other.metadata) === true;
            }
            else if (this.metadata === undefined && other.metadata === undefined) {
                localMetadataEqual = true;
            }
            return (this.type === other.type &&
                this.url === other.url &&
                this.uri === other.uri &&
                this.file_description === other.file_description &&
                this.mimeType === other.mimeType &&
                localMetadataEqual &&
                localFileEqual);
        }
        return false;
    }
}
exports.NuroFile = NuroFile;
var ReferenceRole;
(function (ReferenceRole) {
    ReferenceRole[ReferenceRole["User"] = 0] = "User";
    ReferenceRole[ReferenceRole["Assistant"] = 1] = "Assistant";
})(ReferenceRole = exports.ReferenceRole || (exports.ReferenceRole = {}));
class RefContent {
    constructor(text, file) {
        this.text = text;
        this.file = file;
    }
}
exports.RefContent = RefContent;
class NuroUserMessage extends NuroMessage {
    constructor(id, text, files = undefined, messageStatus = NuroUserMessageStatus.none, referenceInfo = undefined) {
        super(id, NuroMessageType.user);
        this.text = text;
        this.files = files;
        this.createTime = tsnfoundation_1.TSNNumberConverter.toInt64(Date.now());
        this.messageStatus = messageStatus;
        this.referenceInfo = referenceInfo;
    }
    copy() {
        const message = new NuroUserMessage(this.id, this.text, this.files, this.messageStatus, this.referenceInfo);
        this.baseCopy(message);
        return message;
    }
    isEqualToObject(other) {
        var _a, _b;
        if (other instanceof NuroUserMessage) {
            let filesEqual = true;
            if (this.files !== undefined &&
                other.files !== undefined &&
                this.files.length === other.files.length) {
                for (let i = 0; i < this.files.length; i++) {
                    if (this.files[i].isEqualToObject(other.files[i]) !== true) {
                        filesEqual = false;
                        break;
                    }
                }
            }
            else if (((_a = this.files) === null || _a === void 0 ? void 0 : _a.length) !== ((_b = other.files) === null || _b === void 0 ? void 0 : _b.length)) {
                filesEqual = false;
            }
            return (super.isEqualToObject(other) === true &&
                this.text === other.text &&
                this.messageStatus === other.messageStatus &&
                filesEqual);
        }
        return false;
    }
    setMsgStatus(status) {
        var _a;
        let newmsg = this.copy();
        if (newmsg instanceof NuroUserMessage) {
            newmsg.messageStatus = status;
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(newmsg);
    }
    isFinalStatus() {
        return (this.messageStatus === NuroUserMessageStatus.finished_successfully ||
            this.messageStatus === NuroUserMessageStatus.failed ||
            this.messageStatus === NuroUserMessageStatus.cancelled);
    }
    isCancelledStatus() {
        return this.messageStatus === NuroUserMessageStatus.cancelled;
    }
    isFailedStatus() {
        return this.messageStatus === NuroUserMessageStatus.failed;
    }
}
exports.NuroUserMessage = NuroUserMessage;
class NuroReasoningMessage extends NuroMessage {
    constructor(id, text, messageStatus = NuroReasoningMessageStatus.none) {
        super(id, NuroMessageType.reasoning);
        this.text = text;
        this.messageStatus = messageStatus;
    }
    copy() {
        const message = new NuroReasoningMessage(this.id, this.text, this.messageStatus);
        this.baseCopy(message);
        return message;
    }
    isEqualToObject(other) {
        if (other instanceof NuroReasoningMessage) {
            return (super.isEqualToObject(other) === true &&
                this.text === other.text &&
                this.messageStatus === other.messageStatus);
        }
        return false;
    }
    isFinalStatus() {
        return (this.messageStatus === NuroReasoningMessageStatus.finished_successfully ||
            this.messageStatus === NuroReasoningMessageStatus.failed ||
            this.messageStatus === NuroReasoningMessageStatus.cancelled);
    }
    isCancelledStatus() {
        return this.messageStatus === NuroReasoningMessageStatus.cancelled;
    }
    isFailedStatus() {
        return this.messageStatus === NuroReasoningMessageStatus.failed;
    }
    setMsgStatus(status) {
        var _a;
        let newmsg = this.copy();
        if (newmsg instanceof NuroReasoningMessage) {
            newmsg.messageStatus = status;
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(newmsg);
    }
    setStatus(status) {
        if (status === undefined) {
            return;
        }
        switch (status) {
            case chat_message_1.ChatMessageStatus.finished_successfully: {
                this.messageStatus = NuroReasoningMessageStatus.finished_successfully;
                break;
            }
            case chat_message_1.ChatMessageStatus.in_progress: {
                this.messageStatus = NuroReasoningMessageStatus.streaming;
                break;
            }
            default: {
                this.messageStatus = NuroReasoningMessageStatus.failed;
                this.errorMsg = status.valueOf();
                break;
            }
        }
    }
}
exports.NuroReasoningMessage = NuroReasoningMessage;
class NuroAssistantMessage extends NuroMessage {
    constructor(id, name, text, files = undefined, messageStatus = NuroAssistantMessageStatus.none) {
        super(id, NuroMessageType.assistant);
        this.relateToolCalls = [];
        this.text = text;
        this.files = files;
        this.name = name;
        this.messageStatus = messageStatus;
    }
    copy() {
        const message = new NuroAssistantMessage(this.id, this.name, this.text, this.files, this.messageStatus);
        message.relateToolCalls = this.relateToolCalls;
        this.baseCopy(message);
        return message;
    }
    isEqualToObject(other) {
        var _a, _b;
        if (other instanceof NuroAssistantMessage) {
            let filesEqual = true;
            if (this.files !== undefined &&
                other.files !== undefined &&
                this.files.length === other.files.length) {
                for (let i = 0; i < this.files.length; i++) {
                    if (this.files[i].isEqualToObject(other.files[i]) !== true) {
                        filesEqual = false;
                        break;
                    }
                }
            }
            else if (((_a = this.files) === null || _a === void 0 ? void 0 : _a.length) !== ((_b = other.files) === null || _b === void 0 ? void 0 : _b.length)) {
                filesEqual = false;
            }
            return (super.isEqualToObject(other) === true &&
                this.name === other.name &&
                this.text === other.text &&
                this.messageStatus === other.messageStatus &&
                filesEqual);
        }
        return false;
    }
    setMsgStatus(status) {
        var _a;
        let newmsg = this.copy();
        if (newmsg instanceof NuroAssistantMessage) {
            newmsg.messageStatus = status;
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(newmsg);
    }
    isFinalStatus() {
        return (this.messageStatus === NuroAssistantMessageStatus.finished_successfully ||
            this.messageStatus === NuroAssistantMessageStatus.failed ||
            this.messageStatus === NuroAssistantMessageStatus.cancelled);
    }
    isCancelledStatus() {
        return this.messageStatus === NuroAssistantMessageStatus.cancelled;
    }
    isFailedStatus() {
        return this.messageStatus === NuroAssistantMessageStatus.failed;
    }
    setStatus(status) {
        if (status === undefined) {
            return;
        }
        switch (status) {
            case chat_message_1.ChatMessageStatus.finished_successfully: {
                this.messageStatus = NuroAssistantMessageStatus.finished_successfully;
                break;
            }
            case chat_message_1.ChatMessageStatus.in_progress: {
                this.messageStatus = NuroAssistantMessageStatus.streaming;
                break;
            }
            default: {
                this.messageStatus = NuroAssistantMessageStatus.failed;
                this.errorMsg = status.valueOf();
                break;
            }
        }
    }
    // 标识是否是对话结束
    isFinalTurn() {
        var _a;
        if (this.endTurn === true || ((_a = this.metadata) === null || _a === void 0 ? void 0 : _a.end_turn) === true) {
            return true;
        }
        return false;
    }
}
exports.NuroAssistantMessage = NuroAssistantMessage;
class NuroToolCallMessage extends NuroMessage {
    constructor(id, toolCallId, toolType, toolName, toolArgs, toolExtra, toolResult, messageStatus = NuroToolCallMessageStatus.none) {
        super(id, NuroMessageType.toolcall);
        this.toolCallId = toolCallId;
        this.toolType = toolType;
        this.toolName = toolName;
        this.toolArgs = toolArgs;
        this.toolExtra = toolExtra;
        this.toolResult = toolResult;
        this.messageStatus = messageStatus;
        if (toolResult !== undefined && toolResult.length > 0) {
            const toolCallResult = new mcp_tool_result_1.MCPToolCallResult({ JSONString: toolResult });
            if (toolCallResult.content.length > 0 &&
                this.messageStatus !== NuroToolCallMessageStatus.invoking) {
                this.messageStatus = NuroToolCallMessageStatus.finished_successfully;
            }
        }
    }
    sendToolCallResult(result, userMessage = undefined) {
        var _a;
        this.toolResult = result;
        if (this.toolResult !== undefined &&
            this.toolResult !== "" &&
            this._conversationManager !== undefined) {
            this.setMsgStatus(NuroToolCallMessageStatus.finished_successfully);
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.sendToolResultMessage(this, userMessage);
    }
    sendToolCallResultFromMCPFormat(callResult, userMessage = undefined) {
        var _a;
        if (mocker_1.NuroMockManager.isMocking()) {
            return;
        }
        this.sendToolCallResult((_a = callResult.toJSONString()) !== null && _a !== void 0 ? _a : "", userMessage);
    }
    decodeToolCallResultAsPlainText() {
        if (this.toolResult !== undefined) {
            if (this.toolResult.trim().indexOf("{") === 0) {
                const formattedResult = this.decodeToolCallResultToMCPFormat();
                let content = "";
                for (let index = 0; index < formattedResult.content.length; index++) {
                    const element = formattedResult.content[index];
                    if (element instanceof mcp_tool_result_1.MCPToolCallTextContent) {
                        content += element.text;
                    }
                }
                return content;
            }
            else {
                return this.toolResult;
            }
        }
        else {
            return undefined;
        }
    }
    decodeToolCallResultToMCPFormat() {
        var _a;
        return new mcp_tool_result_1.MCPToolCallResult({ JSONString: (_a = this.toolResult) !== null && _a !== void 0 ? _a : "{}" });
    }
    setMsgStatus(status) {
        var _a;
        let newmsg = this.copy();
        if (newmsg instanceof NuroToolCallMessage) {
            newmsg.messageStatus = status;
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(newmsg);
    }
    isFinalStatus() {
        return (this.messageStatus === NuroToolCallMessageStatus.skipped ||
            this.messageStatus === NuroToolCallMessageStatus.finished_successfully ||
            this.messageStatus === NuroToolCallMessageStatus.streaming_failed ||
            this.messageStatus === NuroToolCallMessageStatus.streaming_cancelled);
    }
    isCancelledStatus() {
        return this.messageStatus === NuroToolCallMessageStatus.streaming_cancelled;
    }
    isFailedStatus() {
        return this.messageStatus === NuroToolCallMessageStatus.streaming_failed;
    }
    copy() {
        const message = new NuroToolCallMessage(this.id, this.toolCallId, this.toolType, this.toolName, this.toolArgs, this.toolExtra, this.toolResult, this.messageStatus);
        this.baseCopy(message);
        return message;
    }
    isEqualToObject(other) {
        if (other instanceof NuroToolCallMessage) {
            return (super.isEqualToObject(other) === true &&
                this.toolCallId === other.toolCallId &&
                this.toolType === other.toolType &&
                this.toolName === other.toolName &&
                this.toolArgs === other.toolArgs &&
                this.toolExtra === other.toolExtra &&
                this.toolResult === other.toolResult &&
                this.messageStatus === other.messageStatus &&
                this.endTurn === other.endTurn);
        }
        return false;
    }
    isClientToolSkipped() {
        if (this.toolType === chat_message_1.ChatToolCallType.client_function) {
            return this.messageStatus === NuroToolCallMessageStatus.skipped;
        }
        return false;
    }
}
exports.NuroToolCallMessage = NuroToolCallMessage;
exports.CANVAS_DEFAULT = -10000;
exports.CANVAS_ADD_TO_END = -10001;
function getNodeFromNuroToolCallMsg(id, nodeIndex, toolCall) {
    var _a, _b;
    const node = new NuroCanvasNode(id, nodeIndex, toolCall.toolCallId, toolCall.toolType, toolCall.toolName, toolCall.toolArgs, toolCall.toolExtra, toolCall.toolResult, toolCall.messageStatus);
    node.nodeIndex = nodeIndex;
    node._rawId = (_a = toolCall._rawId) !== null && _a !== void 0 ? _a : "";
    node._conversationManager = toolCall._conversationManager;
    node.createTime = (_b = toolCall.createTime) !== null && _b !== void 0 ? _b : 0;
    node._messageIndex = toolCall._messageIndex;
    node.messageStatus = toolCall.messageStatus;
    node.endTurn = toolCall.endTurn;
    return node;
}
exports.getNodeFromNuroToolCallMsg = getNodeFromNuroToolCallMsg;
// NuroCanvasNode，代表中间插入的节点信息
class NuroCanvasNode extends NuroToolCallMessage {
    constructor(id, nodeIndex, toolCallId, toolType, toolName, toolArgs, toolExtra, toolResult, messageStatus = NuroToolCallMessageStatus.none) {
        super(id, toolCallId, toolType, toolName, toolArgs, toolExtra, toolResult, messageStatus);
        this.nodeIndex = exports.CANVAS_DEFAULT; // 标识插入节点的相对位置
        this.nodeIndex = nodeIndex;
    }
    copy() {
        const message = new NuroCanvasNode(this.id, this.nodeIndex, this.toolCallId, this.toolType, this.toolName, this.toolArgs, this.toolExtra, this.toolResult, this.messageStatus);
        this.baseCopy(message);
        return message;
    }
    isCancelledStatus() {
        // 生成节点没有取消状态
        return super.isCancelledStatus();
    }
    isFailedStatus() {
        // 生成节点没有fail状态
        return super.isFailedStatus();
    }
    isFinalStatus() {
        return super.isFinalStatus();
    }
    getResumeMsgId() {
        return this._rawId;
    }
    isEqualToObject(other) {
        if (other instanceof NuroCanvasNode) {
            return (super.isEqualToObject(other) === true &&
                this.nodeIndex === other.nodeIndex);
        }
        return false;
    }
}
exports.NuroCanvasNode = NuroCanvasNode;
var NuroCanvasStatus;
(function (NuroCanvasStatus) {
    NuroCanvasStatus[NuroCanvasStatus["none"] = 0] = "none";
    NuroCanvasStatus[NuroCanvasStatus["init"] = 1] = "init";
    NuroCanvasStatus[NuroCanvasStatus["streaming"] = 2] = "streaming";
    NuroCanvasStatus[NuroCanvasStatus["cancel"] = 3] = "cancel";
    NuroCanvasStatus[NuroCanvasStatus["end"] = 4] = "end";
})(NuroCanvasStatus = exports.NuroCanvasStatus || (exports.NuroCanvasStatus = {}));
class NuroCanvasMessage extends NuroMessage {
    constructor(id) {
        super(id, NuroMessageType.canvas);
        this.status = NuroCanvasStatus.none;
        // 标识对应的图片
        this.nodes = [];
    }
    updateStartNode(node) {
        this.startNode = node;
    }
    updateCanvasStatus(status) {
        this.status = status;
        this.setMsgStatus(this.status);
    }
    updateEndNode(node) {
        this.endNode = node;
    }
    /*
     ** 增加一个finish接口，因为收到end的时候，有可能还在streaming
     */
    finish() {
        this.status = NuroCanvasStatus.end;
        this.setMsgStatus(NuroCanvasStatus.end);
    }
    addOrReplaceNode(node) {
        var _a;
        // 如果之前的流关闭了，不能再写入,先简单做个判断
        if (this.nodes === undefined) {
            this.nodes = [];
        }
        if (node.nodeIndex > ((_a = this.nodes) === null || _a === void 0 ? void 0 : _a.length)) {
            this.nodes.push(node);
        }
        else {
            if (node.nodeIndex === exports.CANVAS_ADD_TO_END) {
                let length = this.nodes.length;
                this.nodes[length] = node;
            }
            else {
                let index = node.nodeIndex - 1;
                this.nodes[index] = node;
            }
        }
    }
    findNodeByToolCallId(toolCallId) {
        if (this.nodes === undefined) {
            return undefined;
        }
        let targetNode = undefined;
        this.nodes.forEach((it) => {
            if (it.toolCallId === toolCallId) {
                targetNode = it;
            }
        });
        return targetNode;
    }
    findEndNode(toolCallId) {
        if (this.endNode === undefined) {
            return undefined;
        }
        if (this.endNode.toolCallId === toolCallId) {
            return this.endNode;
        }
        return undefined;
    }
    findStartNode(toolCallId) {
        if (this.startNode === undefined) {
            return undefined;
        }
        if (this.startNode.toolCallId === toolCallId) {
            return this.startNode;
        }
        return undefined;
    }
    isCanvasOpen() {
        return (this.status !== NuroCanvasStatus.end &&
            this.status !== NuroCanvasStatus.cancel);
    }
    copy() {
        var _a, _b, _c;
        const message = new NuroCanvasMessage(this.id);
        message.status = this.status;
        message.nodes = (_a = this.nodes) === null || _a === void 0 ? void 0 : _a.map((it) => it.copy());
        message.startNode = (_b = this.startNode) === null || _b === void 0 ? void 0 : _b.copy();
        message.endNode = (_c = this.endNode) === null || _c === void 0 ? void 0 : _c.copy();
        this.baseCopy(message);
        return message;
    }
    isCancelledStatus() {
        // 没有cancel状态，生成过程中，交互需要禁止
        return this.status === NuroCanvasStatus.cancel;
    }
    isFailedStatus() {
        // 画布消息不维护失败状态，由业务自行判断。
        return false;
    }
    isFinalStatus() {
        return this.status === NuroCanvasStatus.end;
    }
    setMsgStatus(status) {
        var _a;
        this.status = status;
        let newmsg = this.copy();
        if (newmsg instanceof NuroCanvasMessage) {
            newmsg.status = status;
        }
        (_a = this._conversationManager) === null || _a === void 0 ? void 0 : _a.receivedMessage(newmsg);
    }
    getResumeMsgId() {
        let result = "";
        if (this.startNode !== undefined) {
            if (this.startNode.needResume()) {
                result = this.startNode._rawId;
            }
        }
        if (this.nodes !== undefined) {
            this.nodes.forEach((node) => {
                if (node !== undefined) {
                    if (node.needResume()) {
                        result = node._rawId;
                    }
                }
            });
        }
        if (this.endNode !== undefined) {
            if (this.endNode.needResume()) {
                result = this.endNode._rawId;
            }
        }
        // 如果都没有，取第一个startNode
        if (result === "") {
            if (this.startNode !== undefined) {
                result = this.startNode._rawId;
            }
        }
        return result;
    }
    isEqualToObject(message) {
        var _a, _b, _c;
        if (message instanceof NuroCanvasMessage) {
            let result = super.isEqualToObject(message) === true &&
                this.status === message.status &&
                ((_a = this.startNode) === null || _a === void 0 ? void 0 : _a.isEqualToObject(message.startNode)) === true &&
                ((_b = this.endNode) === null || _b === void 0 ? void 0 : _b.isEqualToObject(message.endNode)) === true;
            if (result === false) {
                return false;
            }
            if (this.nodes === undefined) {
                if (message.nodes !== undefined) {
                    return false;
                }
                else {
                    return true;
                }
            }
            else if (this.nodes.length === 0) {
                if (message.nodes === undefined) {
                    return false;
                }
                else if (message.nodes.length > 0) {
                    return false;
                }
                return true;
            }
            else {
                if (message.nodes === undefined) {
                    return false;
                }
                else if (message.nodes.length === 0) {
                    return false;
                }
                else if (this.nodes.length !== message.nodes.length) {
                    return false;
                }
                else {
                    for (let i = 0; i < this.nodes.length; i++) {
                        if (((_c = this.nodes[i]) === null || _c === void 0 ? void 0 : _c.isEqualToObject(message.nodes[i])) === false) {
                            return false;
                        }
                    }
                    return true;
                }
            }
        }
        return false;
    }
}
exports.NuroCanvasMessage = NuroCanvasMessage;
//# sourceMappingURL=message.js.map