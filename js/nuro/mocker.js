"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroMockManager = void 0;
class NuroMockManager {
    static setMocker(mocker) {
        NuroMockManager.mocker = mocker;
    }
    static checkMock(userMessage, manager) {
        var _a, _b;
        return (_b = (_a = NuroMockManager.mocker) === null || _a === void 0 ? void 0 : _a.checkMock(userMessage, manager)) !== null && _b !== void 0 ? _b : false;
    }
    static isMocking() {
        return NuroMockManager.mocker !== undefined;
    }
}
exports.NuroMockManager = NuroMockManager;
NuroMockManager.mocker = undefined;
//# sourceMappingURL=mocker.js.map