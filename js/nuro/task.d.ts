import { NuroMessage, NuroTaskMessageType, NuroToolCallMessage } from "./message";
export declare enum NuroMessageOp {
    add = "add",
    update = "update",
    delete = "delete"
}
export declare enum NuroTaskOp {
    add = "add",
    update = "update",
    delete = "delete"
}
export declare enum NuroTaskStatus {
    none = "none",
    running = "running",
    finished = "finished",
    failed = "failed",
    cancelled = "cancelled"
}
export declare class NuroMessageOut {
    message: NuroMessage;
    messageOp: NuroMessageOp;
    constructor(message: NuroMessage, op: NuroMessageOp);
}
export declare class NuroTaskOut {
    task: NuroTask;
    taskOp: NuroTaskOp;
    messages: NuroMessageOut[];
    constructor(task: NuroTask, taskOp: NuroTaskOp);
    updateMessage(messages: NuroMessageOut[]): void;
}
export declare class NuroTask {
    /**
     * 任务ID
     */
    taskId: string;
    /**
     * 任务状态
     */
    taskStatus: NuroTaskStatus;
    /**
     * 消息哈希
     * 用于判断消息是否发生了变化
     */
    messageHash: string;
    /**
     * 用户输入的问题
     * 可能包含多个 UserMessage
     */
    promptMessages: Array<NuroMessage>;
    /**
     * 中间消息
     * 可能包含多个 ReasoningMessage、AssistantMessage、ToolCallMessage
     * 这些消息表示了生成产物的过程
     */
    middlewareMessages: Array<NuroMessage>;
    /**
     * 产物消息
     * 可能包含多个 AssistantMessage、ToolCallMessage
     */
    artifactMessages: Array<NuroMessage>;
    /**
     *
     * 屏蔽掉的消息信息
     */
    shieldMessages: Array<NuroMessage>;
    constructor(taskId: string);
    recalculateMessageHash(): void;
    displayingMiddlewareMessages(): Array<NuroMessage>;
    isDisplayingMiddlewareMessage(msg: NuroMessage): boolean;
    addMessage(msg: NuroMessage, taskMsgType: NuroTaskMessageType): void;
    containMessage(msg: NuroMessage): boolean;
    needResume(): boolean;
}
export declare class NuroTaskChecker {
    isPromptMessage(message: NuroMessage): boolean;
    isArtifactMessage(message: NuroMessage): boolean;
    isShieldMessage(message: NuroMessage): boolean;
}
export declare class NuroCanvasChecker {
    isNuroCanvasMessage(message: NuroToolCallMessage): boolean;
}
