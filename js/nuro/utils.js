"use strict";
/// <reference path="utils.uni.d.ts" />
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroUtils = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class NuroUtils {
    static randomUUIDString() {
        if (tsnfoundation_1.IS_SWIFT) {
            return new UUID().uuidString;
        }
        else if (tsnfoundation_1.IS_KT) {
            return UUID.randomUUID().toString();
        }
        else if (tsnfoundation_1.IS_JS) {
            return "xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
                var r = (Math.random() * 16) | 0, v = c == "x" ? r : (r & 0x3) | 0x8;
                return v.toString(16);
            });
        }
        else {
            return "";
        }
    }
    static isJSONValid(json) {
        if (tsnfoundation_1.IS_JS) {
            try {
                JSON.parse(json);
                return true;
            }
            catch (e) {
                return false;
            }
        }
        return JSONVerifier.isValid(json);
    }
    static stringEndsWith(str, suffix) {
        if (str.length < suffix.length) {
            return false;
        }
        return str.substr(str.length - suffix.length) === suffix;
    }
    // for Canvas做的临时逻辑
    static completeQuotes(str) {
        const len = str.length;
        if (len === 0) {
            return "";
        }
        let output = [];
        let inString = false;
        for (let i = 0; i < len; i++) {
            const char = str.substring(i, i + 1);
            if (char === '"' && (i === 0 || str.substring(i - 1, i) !== "\\")) {
                if (inString) {
                    // Potentially the end of a string.
                    // Peek ahead to see if the next non-whitespace character is a valid JSON delimiter.
                    let nextNonWhitespaceChar = undefined;
                    for (let j = i + 1; j < len; j++) {
                        const nextChar = str.substring(j, j + 1);
                        if (nextChar !== " " &&
                            nextChar !== "\n" &&
                            nextChar !== "\r" &&
                            nextChar !== "\t") {
                            nextNonWhitespaceChar = nextChar;
                            break;
                        }
                    }
                    if (nextNonWhitespaceChar !== undefined &&
                        nextNonWhitespaceChar !== "," &&
                        nextNonWhitespaceChar !== "}" &&
                        nextNonWhitespaceChar !== "]" &&
                        nextNonWhitespaceChar !== ":") {
                        // This is likely an unescaped quote within a string.
                        output.push("\\", char);
                    }
                    else {
                        // This appears to be a valid end of a string value.
                        output.push(char);
                        inString = false;
                    }
                }
                else {
                    // Start of a new string.
                    output.push(char);
                    inString = true;
                }
            }
            else {
                output.push(char);
            }
        }
        // If inString is true here, the string was unclosed.
        if (inString) {
            output.push('"');
        }
        const result = output.join("");
        return result;
    }
    static jsonrepair(str) {
        var _a;
        if (this.isJSONValid(str) === true) {
            return str;
        }
        // Heuristic to remove a single trailing comma (Chinese or ASCII)
        // if it's the very last character of the trimmed string.
        let tempStr = str.trim();
        if (tempStr.length > 0 && NuroUtils.stringEndsWith(tempStr, ",") === true) {
            // Remove the comma and any trailing whitespace that might have been before it or became exposed.
            tempStr = tempStr.substring(0, tempStr.length - 1);
        }
        const parseStr = tempStr;
        const len = parseStr.length;
        if (len === 0) {
            return "";
        }
        let output = [];
        const stack = []; // Stack will store '{', '[', or '"'
        let inString = false;
        for (let i = 0; i < len; i++) {
            const char = parseStr.substring(i, i + 1);
            output.push(char);
            if (char === '"' && (i === 0 || parseStr.substring(i - 1, i) !== "\\")) {
                // Non-escaped quote
                if (inString) {
                    if (stack.length > 0 && stack[stack.length - 1] === '"') {
                        stack.pop();
                    }
                    inString = false;
                }
                else {
                    stack.push('"');
                    inString = true;
                }
            }
            else if (!inString) {
                if (char === "{") {
                    stack.push("{");
                }
                else if (char === "[") {
                    stack.push("[");
                }
                else if (char === "}") {
                    if (stack.length > 0 && stack[stack.length - 1] === "{") {
                        stack.pop();
                    }
                }
                else if (char === "]") {
                    if (stack.length > 0 && stack[stack.length - 1] === "[") {
                        stack.pop();
                    }
                }
            }
        }
        while (stack.length > 0) {
            const openSymbol = stack.pop();
            if (openSymbol === '"') {
                output.push('"');
            }
            else if (openSymbol === "{") {
                const currentOutputStr = output.join("");
                if (this.stringEndsWith(currentOutputStr, ":") === true) {
                    output.push('""'); // Add empty string as value
                }
                output.push("}");
            }
            else if (openSymbol === "[") {
                output.push("]");
            }
        }
        let repairedString = output.join("");
        if (this.isJSONValid(repairedString) === true) {
            return repairedString;
        }
        else {
            // 处理一下转义
            let quoteRepairedString = (_a = this.completeQuotes(repairedString)) !== null && _a !== void 0 ? _a : "";
            if (this.isJSONValid(quoteRepairedString) === true) {
                return quoteRepairedString;
            }
        }
        return undefined;
    }
}
exports.NuroUtils = NuroUtils;
//# sourceMappingURL=utils.js.map