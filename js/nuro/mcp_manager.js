"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroMCPManager = exports.NuroMCPToolItem = exports.NuroMCPServerConfig = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class NuroMCPServerConfig {
    constructor(name, adapter) {
        this.name = name;
        this.adapter = adapter;
    }
}
exports.NuroMCPServerConfig = NuroMCPServerConfig;
class NuroMCPToolItem {
    constructor(name, description, inputSchema) {
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
    }
}
exports.NuroMCPToolItem = NuroMCPToolItem;
class NuroMCPManager {
    constructor() {
        this.servers = {};
        this.toolsServerMapping = {};
    }
    registerServer(config) {
        this.servers[config.name] = config;
    }
    getAllTools(callback) {
        const allTools = [];
        let taskCount = tsnfoundation_1.TSNMapUtils.size(this.servers);
        tsnfoundation_1.TSNMapUtils.forEach(this.servers, (serverName, server) => {
            server.adapter.listTools((tools) => {
                tools.forEach((tool) => {
                    allTools.push(tool);
                    this.toolsServerMapping[serverName + "." + tool.name] = serverName;
                });
                taskCount--;
                if (taskCount === 0) {
                    callback(allTools);
                }
            });
        });
    }
    callTool(functionNameWithPrefix, functionCallArguments, functionCallId, callback) {
        return __awaiter(this, void 0, void 0, function* () {
            const serverName = this.toolsServerMapping[functionNameWithPrefix];
            if (serverName === undefined) {
                return;
            }
            const server = this.servers[serverName];
            if (server === undefined) {
                return;
            }
            server.adapter.callTool(functionCallId, functionNameWithPrefix, functionCallArguments, (result) => {
                callback(result);
            });
        });
    }
}
exports.NuroMCPManager = NuroMCPManager;
//# sourceMappingURL=mcp_manager.js.map