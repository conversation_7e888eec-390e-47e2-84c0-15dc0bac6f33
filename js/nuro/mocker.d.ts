import type { NuroUserMessage } from "./message";
import type { NuroConversationManager } from "./conversation_manager";
export interface INuroMocker {
    checkMock(userMessage: NuroUserMessage, manager: NuroConversationManager): boolean;
}
export declare class NuroMockManager {
    private static mocker;
    static setMocker(mocker: Optional<INuroMocker>): void;
    static checkMock(userMessage: NuroUserMessage, manager: NuroConversationManager): boolean;
    static isMocking(): boolean;
}
