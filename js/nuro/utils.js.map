{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/nuro/utils.ts"], "names": [], "mappings": ";AAAA,uCAAuC;;;AAEvC,wDAA0E;AAE1E,MAAa,SAAS;IACpB,MAAM,CAAC,gBAAgB;QACrB,IAAI,wBAAQ,EAAE;YACZ,OAAO,IAAI,IAAI,EAAE,CAAC,UAAU,CAAC;SAC9B;aAAM,IAAI,qBAAK,EAAE;YAChB,OAAO,IAAI,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,CAAC;SACrC;aAAM,IAAI,qBAAK,EAAE;YAChB,OAAO,sCAAsC,CAAC,OAAO,CACnD,OAAO,EACP,UAAU,CAAC;gBACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAC9B,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;gBACrC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;YACxB,CAAC,CACF,CAAC;SACH;aAAM;YACL,OAAO,EAAE,CAAC;SACX;IACH,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAY;QAC7B,IAAI,qBAAK,EAAE;YACT,IAAI;gBACF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACjB,OAAO,IAAI,CAAC;aACb;YAAC,OAAO,CAAC,EAAE;gBACV,OAAO,KAAK,CAAC;aACd;SACF;QACD,OAAO,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,cAAc,CAAC,GAAW,EAAE,MAAc;QAC/C,IAAI,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE;YAC9B,OAAO,KAAK,CAAC;SACd;QACD,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,MAAM,CAAC;IAC3D,CAAC;IAED,mBAAmB;IACnB,MAAM,CAAC,cAAc,CAAC,GAAW;QAC/B,MAAM,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC;QAEvB,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,OAAO,EAAE,CAAC;SACX;QAED,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAErC,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;gBACjE,IAAI,QAAQ,EAAE;oBACZ,mCAAmC;oBACnC,oFAAoF;oBACpF,IAAI,qBAAqB,GAAqB,SAAS,CAAC;oBACxD,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;wBAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;wBACzC,IACE,QAAQ,KAAK,GAAG;4BAChB,QAAQ,KAAK,IAAI;4BACjB,QAAQ,KAAK,IAAI;4BACjB,QAAQ,KAAK,IAAI,EACjB;4BACA,qBAAqB,GAAG,QAAQ,CAAC;4BACjC,MAAM;yBACP;qBACF;oBAED,IACE,qBAAqB,KAAK,SAAS;wBACnC,qBAAqB,KAAK,GAAG;wBAC7B,qBAAqB,KAAK,GAAG;wBAC7B,qBAAqB,KAAK,GAAG;wBAC7B,qBAAqB,KAAK,GAAG,EAC7B;wBACA,qDAAqD;wBACrD,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;qBACzB;yBAAM;wBACL,oDAAoD;wBACpD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;wBAClB,QAAQ,GAAG,KAAK,CAAC;qBAClB;iBACF;qBAAM;oBACL,yBAAyB;oBACzB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBAClB,QAAQ,GAAG,IAAI,CAAC;iBACjB;aACF;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACnB;SACF;QAED,qDAAqD;QACrD,IAAI,QAAQ,EAAE;YACZ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;QAED,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC/B,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,GAAW;;QAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;YAClC,OAAO,GAAG,CAAC;SACZ;QACD,iEAAiE;QACjE,yDAAyD;QACzD,IAAI,OAAO,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;QACzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;YACzE,iGAAiG;YACjG,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;SACpD;QACD,MAAM,QAAQ,GAAG,OAAO,CAAC;QACzB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;QAE5B,IAAI,GAAG,KAAK,CAAC,EAAE;YACb,OAAO,EAAE,CAAC;SACX;QAED,IAAI,MAAM,GAAa,EAAE,CAAC;QAC1B,MAAM,KAAK,GAAa,EAAE,CAAC,CAAC,oCAAoC;QAChE,IAAI,QAAQ,GAAG,KAAK,CAAC;QAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;YAC5B,MAAM,IAAI,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,EAAE;gBACtE,oBAAoB;gBACpB,IAAI,QAAQ,EAAE;oBACZ,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;wBACvD,KAAK,CAAC,GAAG,EAAE,CAAC;qBACb;oBACD,QAAQ,GAAG,KAAK,CAAC;iBAClB;qBAAM;oBACL,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBAChB,QAAQ,GAAG,IAAI,CAAC;iBACjB;aACF;iBAAM,IAAI,CAAC,QAAQ,EAAE;gBACpB,IAAI,IAAI,KAAK,GAAG,EAAE;oBAChB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjB;qBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;oBACvB,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBACjB;qBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;oBACvB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;wBACvD,KAAK,CAAC,GAAG,EAAE,CAAC;qBACb;iBACF;qBAAM,IAAI,IAAI,KAAK,GAAG,EAAE;oBACvB,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;wBACvD,KAAK,CAAC,GAAG,EAAE,CAAC;qBACb;iBACF;aACF;SACF;QAED,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;YACvB,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;YAC/B,IAAI,UAAU,KAAK,GAAG,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,UAAU,KAAK,GAAG,EAAE;gBAC7B,MAAM,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACzC,IAAI,IAAI,CAAC,cAAc,CAAC,gBAAgB,EAAE,GAAG,CAAC,KAAK,IAAI,EAAE;oBACvD,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,4BAA4B;iBAChD;gBACD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,UAAU,KAAK,GAAG,EAAE;gBAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;SACF;QAED,IAAI,cAAc,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,KAAK,IAAI,EAAE;YAC7C,OAAO,cAAc,CAAC;SACvB;aAAM;YACL,SAAS;YACT,IAAI,mBAAmB,GAAG,MAAA,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC;YACpE,IAAI,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,KAAK,IAAI,EAAE;gBAClD,OAAO,mBAAmB,CAAC;aAC5B;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAzLD,8BAyLC"}