import { CommonTransport } from "../transport/transport";
import { NuroConversation } from "./conversation";
import { NuroMCPManager } from "./mcp";
import { NuroMessage, NuroToolCallMessage, NuroUserMessage } from "./message";
import { NuroMessageOp } from "./task";
export declare class NuroConversationManager {
    readonly conversation: NuroConversation;
    private activeTransport?;
    /**
     * Client Side MCP
     */
    mcpManager?: NuroMCPManager;
    private toolCalled;
    constructor(conversationId?: string);
    /**
     * 注册需要链接的后端通道
     * @param transport
     */
    connect(transport: CommonTransport): void;
    /**
     * 使能本地工具调用
     */
    enableMCPTools(): void;
    /**
     * 解析会话历史数据
     * @param str 会话历史数据 jsonstr
     */
    decodeConversationFromJSONString(str: string, needResume?: boolean): void;
    /**
     * 会话开始时需要提前填入的信息
     * @param value
     */
    setSystemPrompt(value: string): void;
    resumeMessageIfNeed(): void;
    sendUserMessage(message: Optional<NuroUserMessage>): void;
    private _doSendUserMessage;
    private noUpload;
    private uploadFiles;
    /**
     * 本地工具方法 结果回调
     * @param message 回调的工具
     * @param userMessage 额外需要带的用户消息
     */
    sendToolResultMessage(message: NuroToolCallMessage, userMessage?: Optional<NuroUserMessage>): void;
    sendToolResultMessages(messages: NuroToolCallMessage[]): void;
    /**
     * 重试操作
     * @param message 需要被重试的消息
     *
     * @param message 是否需要删除重试消息
     */
    regenerateMessage(message: NuroMessage, needDelete: boolean): void;
    /**
     * 中断 当前会话请求
     */
    interruptResponse(): void;
    receivedMessage(_message: NuroMessage, fromSSE?: boolean): void;
    notifyConversationUpdate(message: NuroMessage, op: NuroMessageOp): void;
    onToolCall(toolCallMessages: NuroToolCallMessage[]): void;
}
