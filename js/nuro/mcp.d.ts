export interface NuroMCPClientAdapter {
    listTools(callback: (tools: NuroMCPToolItem[]) => void): void;
    callTool(toolCallId: string, toolName: string, toolArgs: Optional<string>, callback: (result: string) => void): void;
}
export declare class NuroMCPServerConfig {
    name: string;
    adapter: NuroMCPClientAdapter;
    constructor(name: string, adapter: NuroMCPClientAdapter);
}
export declare class NuroMCPToolItem {
    /**
     * 服务名称
     */
    serverName: string;
    /**
     * 工具名称
     */
    name: string;
    /**
     * 工具描述
     */
    description: string;
    /**
     * JSON Schema
     */
    inputSchema: string;
    constructor(serverName: string, name: string, description: string, inputSchema: string);
}
export declare class NuroMCPManager {
    private servers;
    private toolsServerMapping;
    registerServer(config: NuroMCPServerConfig): void;
    getAllTools(callback: (tools: NuroMCPToolItem[]) => void): void;
    callTool(functionNameWithPrefix: string, functionCallArguments: Optional<string>, functionCallId: string, callback: (result: string) => void): void;
}
