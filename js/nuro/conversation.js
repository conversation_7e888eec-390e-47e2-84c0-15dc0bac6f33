"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroConversation = exports.NuroConversationState = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const message_1 = require("./message");
const utils_1 = require("./utils");
const nurosdk_1 = require("../nurosdk");
const task_1 = require("./task");
const setting_1 = require("./setting");
var NuroConversationState;
(function (NuroConversationState) {
    /**
     * The conversation is preparing to send the first message.
     * UI should show a loading indicator, should not allow user to send a message.
     */
    NuroConversationState["preparing"] = "preparing";
    /**
     * The conversation is streaming the response.
     * UI should show the response with streaming, should not allow user to send new message.
     * UI can display a pause button allow user to stop streaming.
     */
    NuroConversationState["streamingResponse"] = "streamingResponse";
    /**
     * The conversation is ready to send the first message.
     * UI should allow the user to send the first message.
     */
    NuroConversationState["readyToSendMessage"] = "readyToSendMessage";
})(NuroConversationState = exports.NuroConversationState || (exports.NuroConversationState = {}));
class NuroConversation {
    constructor(conversationId) {
        this.conversationState = NuroConversationState.preparing;
        this.messages = [];
        this.tasks = [];
        this.taskChecker = undefined;
        this.summary = undefined;
        this.stateUpdateListeners = {};
        this.messageUpdateListeners = {};
        this.taskUpdateListeners = {};
        this.systemDataListeners = {};
        this.conversationId = conversationId;
    }
    addStateUpdateListener(listener) {
        const uuid = utils_1.NuroUtils.randomUUIDString();
        this.stateUpdateListeners[uuid] = listener;
        return uuid;
    }
    removeStateUpdateListener(token) {
        this.stateUpdateListeners[token] = undefined;
    }
    removeAllStateUpdateListeners() {
        this.stateUpdateListeners = {};
    }
    addSystemDataListener(listener) {
        const uuid = utils_1.NuroUtils.randomUUIDString();
        this.systemDataListeners[uuid] = listener;
        return uuid;
    }
    removeSystemDataListener(token) {
        this.systemDataListeners[token] = undefined;
    }
    removeSystemDataListeners() {
        this.systemDataListeners = {};
    }
    addMessageUpdateListener(listener) {
        const uuid = utils_1.NuroUtils.randomUUIDString();
        this.messageUpdateListeners[uuid] = listener;
        return uuid;
    }
    removeMessageUpdateListener(token) {
        this.messageUpdateListeners[token] = undefined;
    }
    removeAllMessageUpdateListeners() {
        this.messageUpdateListeners = {};
    }
    addTaskUpdateListener(listener) {
        const uuid = utils_1.NuroUtils.randomUUIDString();
        this.taskUpdateListeners[uuid] = listener;
        return uuid;
    }
    removeTaskUpdateListener(token) {
        this.taskUpdateListeners[token] = undefined;
    }
    removeAllTaskUpdateListeners() {
        this.taskUpdateListeners = {};
    }
    needResume() {
        var _a;
        if (nurosdk_1.EventStreamAdapter.reconnectEndpoint === undefined) {
            return false;
        }
        if (setting_1.NuroSetting.version === "3.0.0") {
            // 3.0.0按任务来分
            let length = this.tasks.length;
            if (length <= 0) {
                return false;
            }
            let lastTask = this.tasks[length - 1];
            return (lastTask === null || lastTask === void 0 ? void 0 : lastTask.taskStatus) === task_1.NuroTaskStatus.running;
        }
        else {
            let length = this.messages.length;
            if (length <= 0) {
                return false;
            }
            let lastMessage = this.messages[length - 1];
            return (_a = lastMessage === null || lastMessage === void 0 ? void 0 : lastMessage.needResume()) !== null && _a !== void 0 ? _a : false;
        }
    }
    updateState(state) {
        nurosdk_1.NuroLogger.info("NuroConversation", () => `updateConversationState: state = ${state}, conversationId = ${this.conversationId}`);
        this.conversationState = state;
        tsnfoundation_1.TSNMapUtils.forEach(this.stateUpdateListeners, (key, listener) => {
            listener(state);
        });
    }
    updateMessage(message, op) {
        nurosdk_1.NuroLogger.info("NuroConversation", () => `updateMessage: conversationId = ${this.conversationId}, msgType = ${message.type}, id = ${message.id}, op = ${op}`);
        //  this.updateStateWhenMsgUpdate();
        tsnfoundation_1.TSNMapUtils.forEach(this.messageUpdateListeners, (key, listener) => {
            listener(message, op);
        });
        let messageOut = new nurosdk_1.NuroMessageOut(message, op);
        this.processTask([messageOut], true);
    }
    processTask(msgOut, needUpdate = true) {
        const taskChecker = this.taskChecker;
        if (taskChecker !== undefined) {
            const newTasks = [];
            let currentTask;
            this.messages.forEach((it, index) => {
                if (taskChecker.isArtifactMessage(it)) {
                    if (currentTask === undefined) {
                        const newTask = new task_1.NuroTask(it.id);
                        currentTask = newTask;
                        newTask.taskStatus = task_1.NuroTaskStatus.running;
                        newTasks.push(newTask);
                    }
                    if (taskChecker.isShieldMessage(it)) {
                        currentTask === null || currentTask === void 0 ? void 0 : currentTask.addMessage(it, message_1.NuroTaskMessageType.shieldMessage);
                    }
                    else {
                        currentTask === null || currentTask === void 0 ? void 0 : currentTask.addMessage(it, message_1.NuroTaskMessageType.artifactMessage);
                    }
                }
                else {
                    if (currentTask !== undefined &&
                        currentTask.artifactMessages.length > 0) {
                        currentTask = undefined; // reset currentTask because it has artifactMessages, and received a new message.
                    }
                    if (currentTask === undefined) {
                        const newTask = new task_1.NuroTask(it.id);
                        currentTask = newTask;
                        newTask.taskStatus = task_1.NuroTaskStatus.running;
                        newTasks.push(newTask);
                    }
                    if (currentTask !== undefined) {
                        if (taskChecker.isPromptMessage(it) === true) {
                            if (currentTask.middlewareMessages.length <= 0) {
                                currentTask.addMessage(it, message_1.NuroTaskMessageType.promptMessage);
                            }
                            else {
                                const newTask = new task_1.NuroTask(it.id);
                                newTask.taskStatus = task_1.NuroTaskStatus.running;
                                newTask.addMessage(it, message_1.NuroTaskMessageType.promptMessage);
                                newTasks.push(newTask);
                                currentTask = newTask;
                            }
                        }
                        else {
                            currentTask.addMessage(it, message_1.NuroTaskMessageType.middlewareMessage);
                        }
                    }
                }
                if (currentTask !== undefined) {
                    if (it.isCancelledStatus()) {
                        currentTask.taskStatus = task_1.NuroTaskStatus.cancelled;
                        currentTask = undefined;
                    }
                    else if (it.isFailedStatus()) {
                        currentTask.taskStatus = task_1.NuroTaskStatus.failed;
                        currentTask = undefined;
                    }
                    else {
                        if (index === this.messages.length - 1) {
                            if (it instanceof message_1.NuroAssistantMessage) {
                                if (it.isFinalTurn()) {
                                    currentTask.taskStatus = task_1.NuroTaskStatus.finished;
                                }
                            }
                            else if (it instanceof message_1.NuroToolCallMessage) {
                                if (it === null || it === void 0 ? void 0 : it.isClientToolSkipped()) {
                                    currentTask.taskStatus = task_1.NuroTaskStatus.finished;
                                }
                            }
                        }
                    }
                }
            });
            newTasks.forEach((task) => task.recalculateMessageHash());
            const newTasksLength = newTasks.length;
            let changeTasks = [];
            for (let i = 0; i < newTasksLength; i++) {
                if (i < this.tasks.length) {
                    if (newTasks[i].messageHash !== this.tasks[i].messageHash) {
                        this.tasks[i] = newTasks[i];
                        if (needUpdate) {
                            let taskOut = new nurosdk_1.NuroTaskOut(this.tasks[i], nurosdk_1.NuroTaskOp.update);
                            changeTasks.push(taskOut);
                        }
                    }
                }
                else {
                    this.tasks.push(newTasks[i]);
                    if (needUpdate) {
                        let taskOut = new nurosdk_1.NuroTaskOut(this.tasks[this.tasks.length - 1], nurosdk_1.NuroTaskOp.add);
                        changeTasks.push(taskOut);
                    }
                }
            }
            if (this.tasks.length > newTasksLength) {
                const tasksToRemoveCount = this.tasks.length - newTasksLength;
                const removedTasks = this.tasks.splice(newTasksLength, tasksToRemoveCount);
                removedTasks.forEach((removedTask) => {
                    if (needUpdate) {
                        let taskOut = new nurosdk_1.NuroTaskOut(removedTask, nurosdk_1.NuroTaskOp.delete);
                        changeTasks.push(taskOut);
                    }
                });
            }
            changeTasks.forEach((changeTask) => {
                changeTask.updateMessage(msgOut);
            });
            tsnfoundation_1.TSNMapUtils.forEach(this.taskUpdateListeners, (key, listener) => {
                listener(changeTasks);
            });
        }
    }
    findToolCallMessageByToolCallId(toolCallId) {
        let result = undefined;
        this.messages.forEach((it) => {
            if (it instanceof message_1.NuroToolCallMessage) {
                if (it.toolCallId === toolCallId) {
                    result = it;
                }
            }
        });
        return result;
    }
    findLastOpenNuroCanvasMessage(messageList) {
        if (messageList === undefined) {
            tsnfoundation_1.TSNConsole.log(" message list is empty! ");
            return undefined;
        }
        let result = undefined;
        let index = messageList.length - 1;
        while (index >= 0) {
            if (messageList[index] instanceof message_1.NuroCanvasMessage) {
                let message = messageList[index];
                if (message.isCanvasOpen()) {
                    result = message;
                    break;
                }
                break;
            }
            index--;
        }
        return result;
    }
    findLOpenNuroCanvasMessage(messageList) {
        if (messageList === undefined) {
            tsnfoundation_1.TSNConsole.log(" message list is empty! ");
            return undefined;
        }
        let result = undefined;
        let index = messageList.length - 1;
        while (index >= 0) {
            if (messageList[index] instanceof message_1.NuroCanvasMessage) {
                let message = messageList[index];
                if (message.isCanvasOpen()) {
                    if (result === undefined) {
                        result = [];
                    }
                    result.push(message);
                }
            }
            index--;
        }
        return result;
    }
    findOpenNuroCanvasMessageById(canvasMsgId, messageList) {
        if (messageList === undefined) {
            tsnfoundation_1.TSNConsole.log(" message list is empty! ");
            return undefined;
        }
        let result = undefined;
        let index = this.messages.length - 1;
        while (index >= 0) {
            if (this.messages[index] instanceof message_1.NuroCanvasMessage) {
                let message = this.messages[index];
                if (message.id === canvasMsgId) {
                    result = message;
                    break;
                }
            }
            index--;
        }
        return result;
    }
    findLastUserMessage() {
        let length = this.messages.length;
        for (let i = length - 1; i >= 0; i = i - 1) {
            let msg = this.messages[i];
            if (msg instanceof message_1.NuroUserMessage) {
                return msg;
            }
        }
        return undefined;
    }
}
exports.NuroConversation = NuroConversation;
//# sourceMappingURL=conversation.js.map