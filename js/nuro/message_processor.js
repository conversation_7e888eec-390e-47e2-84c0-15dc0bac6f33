"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MessageProcessor = exports.ConvertType = void 0;
const message_1 = require("../nuro/message");
const chat_message_1 = require("../idl/chat_message");
const setting_1 = require("./setting");
const mcp_tool_result_1 = require("../mcp/mcp_tool_result");
const utils_1 = require("./utils");
const tsnfoundation_1 = require("@byted/tsnfoundation");
var ConvertType;
(function (ConvertType) {
    ConvertType["new_message"] = "new_message";
    ConvertType["history"] = "history";
})(ConvertType = exports.ConvertType || (exports.ConvertType = {}));
class MessageProcessor {
    static convertChatMessageToNuroMessage(conversationManager, chatMessage, type) {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y, _z;
        const is_visually_hidden_from_conversation = (_a = chatMessage.metadata) === null || _a === void 0 ? void 0 : _a.is_visually_hidden_from_conversation;
        if (is_visually_hidden_from_conversation !== undefined &&
            is_visually_hidden_from_conversation === true) {
            // Server 端要求该消息不展示在会话中，所以不返回任何消息。
            return [];
        }
        const role = (_b = chatMessage.author) === null || _b === void 0 ? void 0 : _b.role;
        const name = (_c = chatMessage.author) === null || _c === void 0 ? void 0 : _c.name;
        let msgs = [];
        if (role !== undefined) {
            if (role === "user") {
                let text = "";
                let files = [];
                let referenceInfo = [];
                (_e = (_d = chatMessage.content) === null || _d === void 0 ? void 0 : _d.content_parts) === null || _e === void 0 ? void 0 : _e.forEach((it) => {
                    if (it.text !== undefined && it.text.length > 0) {
                        if (it.is_referenced === true) {
                            referenceInfo.push({
                                text: it.text,
                            });
                        }
                        else {
                            text = text + it.text.trim();
                        }
                    }
                    if (it.file !== undefined) {
                        let file = new message_1.NuroFile(message_1.NuroFileType.image, it.file.url, undefined);
                        file.uri = it.file.uri;
                        if (it.file.image_metadata !== undefined) {
                            let fileImageMetadata = new message_1.NuroImageMetadata();
                            fileImageMetadata.width = it.file.image_metadata.image_width;
                            fileImageMetadata.height = it.file.image_metadata.image_height;
                            fileImageMetadata.prompt = it.file.image_metadata.image_prompt;
                            fileImageMetadata.format = it.file.image_metadata.image_format;
                            let fileMetadata = new message_1.NuroFileMetadata(fileImageMetadata);
                            file.metadata = fileMetadata;
                        }
                        if (it.is_referenced === true) {
                            referenceInfo.push({ file: file });
                        }
                        else {
                            files.push(file);
                        }
                    }
                });
                let msg = new message_1.NuroUserMessage((_f = chatMessage.id) !== null && _f !== void 0 ? _f : "", text, files.length > 0 ? files : undefined, undefined, referenceInfo.length > 0 ? referenceInfo : undefined);
                msg._conversationManager = conversationManager;
                msg.createTime = (_g = chatMessage.create_time) !== null && _g !== void 0 ? _g : 0;
                msg._rawId = (_h = chatMessage.id) !== null && _h !== void 0 ? _h : "";
                msg._messageIndex = msgs.length;
                if (chatMessage.status === chat_message_1.ChatMessageStatus.in_progress) {
                    msg.messageStatus = message_1.NuroUserMessageStatus.sending;
                }
                else {
                    msg.messageStatus = message_1.NuroUserMessageStatus.finished_successfully;
                }
                if (chatMessage.metadata !== undefined) {
                    msg.metadata = chatMessage.metadata;
                }
                msgs.push(msg);
            }
            else if (role === "assistant") {
                let reasoning = "";
                let text = "";
                let files = [];
                (_k = (_j = chatMessage.content) === null || _j === void 0 ? void 0 : _j.content_parts) === null || _k === void 0 ? void 0 : _k.forEach((it) => {
                    if (it.reasoning_content !== undefined &&
                        it.reasoning_content.length > 0) {
                        reasoning = reasoning + it.reasoning_content;
                    }
                    if (it.text !== undefined && it.text.length > 0) {
                        text = text + it.text.trim();
                    }
                    if (it.file !== undefined) {
                        let file = new message_1.NuroFile(message_1.NuroFileType.image, it.file.url, undefined);
                        file.uri = it.file.uri;
                        if (it.file.image_metadata !== undefined) {
                            let fileImageMetadata = new message_1.NuroImageMetadata();
                            fileImageMetadata.width = it.file.image_metadata.image_width;
                            fileImageMetadata.height = it.file.image_metadata.image_height;
                            fileImageMetadata.prompt = it.file.image_metadata.image_prompt;
                            fileImageMetadata.format = it.file.image_metadata.image_format;
                            let fileMetadata = new message_1.NuroFileMetadata(fileImageMetadata);
                            file.metadata = fileMetadata;
                        }
                        files.push(file);
                    }
                });
                if (reasoning.length > 0) {
                    let reasoningMsg = new message_1.NuroReasoningMessage(((_l = chatMessage.id) !== null && _l !== void 0 ? _l : "") + "_reasoning", reasoning);
                    reasoningMsg._conversationManager = conversationManager;
                    reasoningMsg.createTime = (_m = chatMessage.create_time) !== null && _m !== void 0 ? _m : 0;
                    reasoningMsg._rawId = (_o = chatMessage.id) !== null && _o !== void 0 ? _o : "";
                    reasoningMsg._messageIndex = msgs.length;
                    if (text.length > 0 || files.length > 0) {
                        reasoningMsg.messageStatus =
                            message_1.NuroReasoningMessageStatus.finished_successfully;
                    }
                    else {
                        reasoningMsg.setStatus(chatMessage.status);
                    }
                    if (chatMessage.metadata !== undefined) {
                        reasoningMsg.metadata = new chat_message_1.ChatMessageMetadata({
                            JSONString: (_p = chatMessage.metadata.toJSONString()) !== null && _p !== void 0 ? _p : "{}",
                        });
                    }
                    msgs.push(reasoningMsg);
                }
                let assistantMsg;
                let isLastTurnMsg = false;
                if (chatMessage.end_turn === true) {
                    isLastTurnMsg = true;
                    let canvasMsgList = (_q = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLOpenNuroCanvasMessage(msgs)) !== null && _q !== void 0 ? _q : conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLOpenNuroCanvasMessage(conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.messages);
                    if (canvasMsgList !== undefined) {
                        canvasMsgList.forEach((it) => {
                            it.setMsgStatus(message_1.NuroCanvasStatus.end);
                            msgs.push(it);
                        });
                    }
                }
                if (text.length > 0 || files.length > 0 || isLastTurnMsg === true) {
                    let msg = new message_1.NuroAssistantMessage(((_r = chatMessage.id) !== null && _r !== void 0 ? _r : "") + "_assistant", name, text, files.length > 0 ? files : undefined);
                    msg._conversationManager = conversationManager;
                    msg.createTime = (_s = chatMessage.create_time) !== null && _s !== void 0 ? _s : 0;
                    msg._rawId = (_t = chatMessage.id) !== null && _t !== void 0 ? _t : "";
                    msg._messageIndex = msgs.length;
                    msg.setStatus(chatMessage.status);
                    if (chatMessage.metadata !== undefined) {
                        msg.metadata = new chat_message_1.ChatMessageMetadata({
                            JSONString: (_u = chatMessage.metadata.toJSONString()) !== null && _u !== void 0 ? _u : "{}",
                        });
                    }
                    if (isLastTurnMsg) {
                        msg.endTurn = true;
                    }
                    assistantMsg = msg;
                    msgs.push(msg);
                }
                (_v = chatMessage.tool_calls) === null || _v === void 0 ? void 0 : _v.forEach((it) => {
                    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y;
                    let itFunc = it._func;
                    if (itFunc === undefined) {
                        return;
                    }
                    if (it.type === chat_message_1.ChatToolCallType.server_function &&
                        setting_1.NuroSetting.needDisplayServerFunctionMessage === false) {
                        return;
                    }
                    let jsonrepairSrc = (_a = itFunc.arguments) !== null && _a !== void 0 ? _a : "";
                    let jsonrepairResult = utils_1.NuroUtils.jsonrepair(jsonrepairSrc);
                    if (jsonrepairResult === undefined) {
                        jsonrepairResult = (_b = itFunc._lastRepairedArguments) !== null && _b !== void 0 ? _b : jsonrepairSrc;
                    }
                    else if (jsonrepairResult === "") {
                        jsonrepairResult = (_c = itFunc._lastRepairedArguments) !== null && _c !== void 0 ? _c : jsonrepairSrc;
                    }
                    else {
                        itFunc._lastRepairedArguments = jsonrepairResult;
                    }
                    let toolName = (_d = itFunc.name) !== null && _d !== void 0 ? _d : "";
                    // 先统一转成toolCallMessage
                    let toolCall = new message_1.NuroToolCallMessage(((_e = chatMessage.id) !== null && _e !== void 0 ? _e : "") + "_toolcall_" + ((_f = it.id) !== null && _f !== void 0 ? _f : ""), (_g = it.id) !== null && _g !== void 0 ? _g : "", (_j = (_h = it.type) === null || _h === void 0 ? void 0 : _h.valueOf()) !== null && _j !== void 0 ? _j : chat_message_1.ChatToolCallType.client_function.valueOf(), toolName, jsonrepairResult, itFunc.extra, "");
                    toolCall._conversationManager = conversationManager;
                    toolCall.createTime = (_k = chatMessage.create_time) !== null && _k !== void 0 ? _k : 0;
                    toolCall._rawId = (_l = chatMessage.id) !== null && _l !== void 0 ? _l : "";
                    toolCall._messageIndex = msgs.length;
                    if (type === ConvertType.new_message) {
                        toolCall.messageStatus = message_1.NuroToolCallMessageStatus.invoking;
                        if (it.streaming === true) {
                            if (chatMessage.status ===
                                chat_message_1.ChatMessageStatus.send_content_stop_status) {
                                toolCall.messageStatus =
                                    message_1.NuroToolCallMessageStatus.streaming_failed;
                            }
                            else {
                                toolCall.messageStatus = message_1.NuroToolCallMessageStatus.streaming;
                            }
                        }
                    }
                    else {
                        toolCall.messageStatus = message_1.NuroToolCallMessageStatus.skipped;
                    }
                    // 判断画布
                    if (toolName.indexOf(setting_1.NuroSetting.canvasSettings.startNode) >= 0) {
                        // 如果收到一个打开画布的指令，就新建一个画布消息 等到streaming=false的时候，才能新建,否则流失输出会收到多条消息
                        let canvasMsgId = ((_m = chatMessage.id) !== null && _m !== void 0 ? _m : "") + "_canvas";
                        let existCanvasMsg = (_p = (_o = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation) === null || _o === void 0 ? void 0 : _o.findOpenNuroCanvasMessageById(canvasMsgId, msgs)) !== null && _p !== void 0 ? _p : (_q = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation) === null || _q === void 0 ? void 0 : _q.findOpenNuroCanvasMessageById(canvasMsgId, conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.messages);
                        let msg = undefined;
                        if (existCanvasMsg === undefined) {
                            msg = new message_1.NuroCanvasMessage(canvasMsgId);
                            msg._conversationManager = conversationManager;
                            msg.createTime = (_r = chatMessage.create_time) !== null && _r !== void 0 ? _r : 0;
                            msg._rawId = (_s = chatMessage.id) !== null && _s !== void 0 ? _s : "";
                            msg._messageIndex = msgs.length;
                        }
                        else {
                            msg = existCanvasMsg.copy();
                        }
                        if (chatMessage.metadata !== undefined) {
                            msg.metadata = new chat_message_1.ChatMessageMetadata({
                                JSONString: (_t = chatMessage.metadata.toJSONString()) !== null && _t !== void 0 ? _t : "{}",
                            });
                        }
                        if (type === ConvertType.history) {
                            msg.status = message_1.NuroCanvasStatus.streaming;
                        }
                        else {
                            msg.updateCanvasStatus(message_1.NuroCanvasStatus.streaming);
                        }
                        msg.updateStartNode(toolCall);
                        msgs.push(msg);
                    }
                    else if (toolName.indexOf(setting_1.NuroSetting.canvasSettings.endNode) >= 0) {
                        // 如果收到一个关闭画布的指令，找到最近的NuroCanvasMessage,设置它的status为end
                        let msg = (_u = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLastOpenNuroCanvasMessage(msgs)) !== null && _u !== void 0 ? _u : conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLastOpenNuroCanvasMessage(conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.messages);
                        if (msg !== undefined) {
                            let update_msg = msg.copy();
                            msg.endNode = toolCall;
                            update_msg.updateEndNode(toolCall);
                            msgs.push(update_msg);
                        }
                    }
                    else if (setting_1.NuroSetting.canvasSettings.nodes.some((node) => node.indexOf(toolName) >= 0)) {
                        // 如果收到一个插入内容的指令，找到最近的NuroCanvasMessage,执行插入Node操作
                        let msg = (_v = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLastOpenNuroCanvasMessage(msgs)) !== null && _v !== void 0 ? _v : conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLastOpenNuroCanvasMessage(conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.messages);
                        if (msg !== undefined) {
                            let update_msg = msg.copy();
                            let nodeIndex = message_1.CANVAS_DEFAULT;
                            if (itFunc.arguments !== undefined) {
                                if (utils_1.NuroUtils.isJSONValid(itFunc.arguments)) {
                                    nodeIndex =
                                        (_w = JSON.parse(itFunc.arguments).canvas_content_id) !== null && _w !== void 0 ? _w : message_1.CANVAS_DEFAULT;
                                }
                                else {
                                    // 有可能被转义影响到了，处理一下, 临时修复逻辑，单独起一个分支处理，减少影响, 后面随着canvas的升级干掉
                                    let repairResult = (_x = utils_1.NuroUtils.completeQuotes(itFunc.arguments)) !== null && _x !== void 0 ? _x : "";
                                    if (utils_1.NuroUtils.isJSONValid(repairResult)) {
                                        nodeIndex =
                                            (_y = JSON.parse(itFunc.arguments).canvas_content_id) !== null && _y !== void 0 ? _y : message_1.CANVAS_DEFAULT;
                                    }
                                }
                            }
                            if (nodeIndex !== message_1.CANVAS_DEFAULT) {
                                const targetNode = (0, message_1.getNodeFromNuroToolCallMsg)(toolCall.id, nodeIndex, toolCall);
                                update_msg.addOrReplaceNode(targetNode);
                                msgs.push(update_msg);
                            }
                            else {
                                // 历史消息中，可能用户主动interrupt了，导致toolArgs不能解析出完整数据，但是还是需要update这个tool,并且把msg 关掉
                                // 此时这个节点不需要展示
                                if (type === ConvertType.history) {
                                    const node = (0, message_1.getNodeFromNuroToolCallMsg)(toolCall.id, message_1.CANVAS_ADD_TO_END, toolCall);
                                    update_msg.addOrReplaceNode(node);
                                    msgs.push(update_msg);
                                }
                            }
                        }
                        else {
                            assistantMsg === null || assistantMsg === void 0 ? void 0 : assistantMsg.relateToolCalls.push(toolCall);
                            msgs.push(toolCall);
                        }
                    }
                    else {
                        // 没有可用画布，直接作为toolCall
                        assistantMsg === null || assistantMsg === void 0 ? void 0 : assistantMsg.relateToolCalls.push(toolCall);
                        msgs.push(toolCall);
                    }
                });
            }
            else if (role === "tool") {
                let content_parts = (_x = (_w = chatMessage.content) === null || _w === void 0 ? void 0 : _w.content_parts) !== null && _x !== void 0 ? _x : [];
                let toolCallId = (_y = chatMessage.metadata) === null || _y === void 0 ? void 0 : _y.tool_call_id;
                let canvasMessageList = (_z = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLOpenNuroCanvasMessage(msgs)) !== null && _z !== void 0 ? _z : conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findLOpenNuroCanvasMessage(conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.messages);
                let len = content_parts.length;
                if (len > 0) {
                    if (toolCallId !== undefined) {
                        let part = content_parts[0];
                        // 确认一下是否为画布工具，如果是画布的工具，需要到画布的消息中去更新
                        if (part !== undefined) {
                            let toolResult = part.text;
                            // 找到对应的工具调用消息，更新它的结果
                            let updateCanvasMsg = undefined;
                            if (canvasMessageList !== undefined) {
                                canvasMessageList.forEach((lastCanvasMsg) => {
                                    let endNode = lastCanvasMsg.findEndNode(toolCallId !== null && toolCallId !== void 0 ? toolCallId : "");
                                    let startNode = lastCanvasMsg.findStartNode(toolCallId !== null && toolCallId !== void 0 ? toolCallId : "");
                                    if (endNode !== undefined) {
                                        updateCanvasMsg = lastCanvasMsg.copy();
                                        if (toolResult !== undefined) {
                                            endNode.toolResult = toolResult;
                                        }
                                        updateCanvasMsg.updateEndNode(endNode.copy());
                                        if (chatMessage.status ===
                                            chat_message_1.ChatMessageStatus.finished_successfully) {
                                            if (type === ConvertType.history) {
                                                updateCanvasMsg.status = message_1.NuroCanvasStatus.end;
                                            }
                                            else {
                                                updateCanvasMsg.finish();
                                            }
                                        }
                                        msgs.push(updateCanvasMsg);
                                    }
                                    else if (startNode !== undefined) {
                                        updateCanvasMsg = lastCanvasMsg.copy();
                                        if (toolResult !== undefined) {
                                            startNode.toolResult = toolResult;
                                        }
                                        updateCanvasMsg.updateStartNode(startNode.copy());
                                        msgs.push(updateCanvasMsg);
                                    }
                                    else {
                                        let node = lastCanvasMsg.findNodeByToolCallId(toolCallId !== null && toolCallId !== void 0 ? toolCallId : "");
                                        if (node !== undefined) {
                                            updateCanvasMsg =
                                                lastCanvasMsg.copy();
                                            let targetNode = node.copy();
                                            if (toolResult !== undefined &&
                                                toolResult.trim().indexOf("{") !== 0) {
                                                // 不是一个合法的 JSON，兼容处理成 mcp text
                                                let r = new mcp_tool_result_1.MCPToolCallResult();
                                                r.content = [
                                                    mcp_tool_result_1.MCPToolCallTextContent.create(toolResult !== null && toolResult !== void 0 ? toolResult : ""),
                                                ];
                                                targetNode.toolResult = r.toJSONString();
                                            }
                                            else {
                                                const firstJson = this.extractFirstJsonObject(toolResult !== null && toolResult !== void 0 ? toolResult : "");
                                                targetNode.toolResult = firstJson !== null && firstJson !== void 0 ? firstJson : "";
                                            }
                                            if (chatMessage.status === chat_message_1.ChatMessageStatus.in_progress) {
                                                targetNode.messageStatus =
                                                    message_1.NuroToolCallMessageStatus.invoking;
                                            }
                                            else {
                                                targetNode.messageStatus =
                                                    message_1.NuroToolCallMessageStatus.finished_successfully;
                                            }
                                            updateCanvasMsg.addOrReplaceNode(targetNode);
                                            msgs.push(updateCanvasMsg);
                                        }
                                        else {
                                            tsnfoundation_1.TSNConsole.log("[NuroCanvasMsgToolCall] cannot find node with toolCallId " +
                                                toolCallId);
                                        }
                                    }
                                });
                            }
                            // 如果画布没找到，再到消息中找
                            if (updateCanvasMsg === undefined) {
                                let toolMsg = conversationManager === null || conversationManager === void 0 ? void 0 : conversationManager.conversation.findToolCallMessageByToolCallId(toolCallId);
                                if (toolMsg !== undefined &&
                                    toolMsg instanceof message_1.NuroToolCallMessage) {
                                    const toolMsgCopy = toolMsg.copy();
                                    if (toolResult !== undefined &&
                                        toolResult.trim().indexOf("{") !== 0) {
                                        // 不是一个合法的 JSON，兼容处理成 mcp text
                                        let r = new mcp_tool_result_1.MCPToolCallResult();
                                        r.content = [mcp_tool_result_1.MCPToolCallTextContent.create(toolResult)];
                                        toolMsgCopy.toolResult = r.toJSONString();
                                    }
                                    else {
                                        toolMsgCopy.toolResult = toolResult;
                                    }
                                    if (chatMessage.status === chat_message_1.ChatMessageStatus.in_progress) {
                                        toolMsgCopy.messageStatus =
                                            message_1.NuroToolCallMessageStatus.invoking;
                                    }
                                    else {
                                        toolMsgCopy.messageStatus =
                                            message_1.NuroToolCallMessageStatus.finished_successfully;
                                    }
                                    msgs.push(toolMsgCopy);
                                }
                            }
                        }
                    }
                }
            }
        }
        return msgs;
    }
    static markMessagesAsFinished(conversationManager) {
        if (conversationManager === undefined) {
            return;
        }
        conversationManager.conversation.messages.forEach((it) => {
            if (it.isFinalStatus() === false) {
                if (it instanceof message_1.NuroReasoningMessage) {
                    it.setMsgStatus(message_1.NuroReasoningMessageStatus.finished_successfully);
                }
                if (it instanceof message_1.NuroAssistantMessage) {
                    it.setMsgStatus(message_1.NuroAssistantMessageStatus.finished_successfully);
                }
            }
        });
    }
    static markLastUserMessageAsFinished(conversationManager) {
        if (conversationManager === undefined) {
            return;
        }
        conversationManager.conversation.messages.forEach((it) => {
            if (it instanceof message_1.NuroUserMessage) {
                if (it.isFinalStatus() === false) {
                    it.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
                }
            }
        });
    }
    static markInProgressMessagesAsFailed(conversationManager) {
        if (conversationManager === undefined) {
            return;
        }
        let errorMsg = message_1.ChatMessageError.send_failed.valueOf();
        conversationManager.conversation.messages.forEach((it) => {
            if (it.isFinalStatus() === false) {
                it.errorMsg = errorMsg;
                if (it instanceof message_1.NuroUserMessage) {
                    it.setMsgStatus(message_1.NuroUserMessageStatus.failed);
                }
                if (it instanceof message_1.NuroReasoningMessage) {
                    it.setMsgStatus(message_1.NuroReasoningMessageStatus.failed);
                }
                if (it instanceof message_1.NuroAssistantMessage) {
                    it.setMsgStatus(message_1.NuroAssistantMessageStatus.failed);
                }
                if (it instanceof message_1.NuroToolCallMessage) {
                    if (it.messageStatus === message_1.NuroToolCallMessageStatus.streaming) {
                        it.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming_failed);
                    }
                    else {
                        it.setMsgStatus(message_1.NuroToolCallMessageStatus.skipped);
                    }
                }
            }
        });
    }
    static markInProgressMessagesAsCancel(conversationManager) {
        if (conversationManager === undefined) {
            return;
        }
        conversationManager.conversation.messages.forEach((it) => {
            if (it.isFinalStatus() === false) {
                if (it instanceof message_1.NuroUserMessage) {
                    it.setMsgStatus(message_1.NuroUserMessageStatus.cancelled);
                }
                if (it instanceof message_1.NuroReasoningMessage) {
                    it.setMsgStatus(message_1.NuroReasoningMessageStatus.cancelled);
                }
                if (it instanceof message_1.NuroAssistantMessage) {
                    it.setMsgStatus(message_1.NuroAssistantMessageStatus.cancelled);
                }
                if (it instanceof message_1.NuroToolCallMessage) {
                    if (it.messageStatus === message_1.NuroToolCallMessageStatus.streaming ||
                        it.messageStatus ===
                            message_1.NuroToolCallMessageStatus.streaming_cancelled ||
                        it.messageStatus === message_1.NuroToolCallMessageStatus.wait_user_response) {
                        it.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming_cancelled);
                    }
                    else {
                        it.setMsgStatus(message_1.NuroToolCallMessageStatus.skipped);
                    }
                }
                if (it instanceof message_1.NuroCanvasMessage) {
                    it.setMsgStatus(message_1.NuroCanvasStatus.cancel);
                }
            }
        });
    }
    static isUserMessage(message) {
        return message instanceof message_1.NuroUserMessage;
    }
    static isToolCallMessage(message) {
        return message instanceof message_1.NuroToolCallMessage;
    }
    static extractFirstJsonObject(jsonString) {
        let balance = 0;
        let startIndex = -1;
        for (let i = 0; i < jsonString.length; i++) {
            // 由于 jsonString 是 String 类型，不能直接用数字索引，需要先转换为字符串字面量
            if (jsonString.charAt(i) === "{") {
                if (balance === 0) {
                    startIndex = i;
                }
                balance++;
            }
            else if (jsonString.charAt(i) === "}") {
                balance--;
                if (balance === 0 && startIndex !== -1) {
                    return jsonString.substring(startIndex, i + 1);
                }
            }
        }
        return ""; // 没有找到完整的 JSON 对象
    }
}
exports.MessageProcessor = MessageProcessor;
//# sourceMappingURL=message_processor.js.map