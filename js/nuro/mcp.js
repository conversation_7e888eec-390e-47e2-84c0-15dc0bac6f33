"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroMCPManager = exports.NuroMCPToolItem = exports.NuroMCPServerConfig = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class NuroMCPServerConfig {
    constructor(name, adapter) {
        this.name = name;
        this.adapter = adapter;
    }
}
exports.NuroMCPServerConfig = NuroMCPServerConfig;
class NuroMCPToolItem {
    constructor(serverName, name, description, inputSchema) {
        this.serverName = serverName;
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
    }
}
exports.NuroMCPToolItem = NuroMCPToolItem;
class NuroMCPManager {
    constructor() {
        this.servers = {};
        this.toolsServerMapping = {};
    }
    registerServer(config) {
        this.servers[config.name] = config;
    }
    getAllTools(callback) {
        const allTools = [];
        let taskCount = tsnfoundation_1.TSNMapUtils.size(this.servers);
        if (taskCount === 0) {
            callback(allTools);
            return;
        }
        tsnfoundation_1.TSNMapUtils.forEach(this.servers, (serverName, server) => {
            server.adapter.listTools((tools) => {
                tools.forEach((tool) => {
                    allTools.push(tool);
                    this.toolsServerMapping[serverName + "_" + tool.name] = serverName;
                });
                taskCount = taskCount - 1;
                if (taskCount === 0) {
                    tsnfoundation_1.TSNConsole.log(allTools);
                    callback(allTools);
                }
            });
        });
    }
    callTool(functionNameWithPrefix, functionCallArguments, functionCallId, callback) {
        const serverName = this.toolsServerMapping[functionNameWithPrefix];
        if (serverName === undefined) {
            return;
        }
        const server = this.servers[serverName];
        if (server === undefined) {
            return;
        }
        server.adapter.callTool(functionCallId, functionNameWithPrefix.substr(serverName.length + 1), functionCallArguments, (result) => {
            callback(result);
        });
    }
}
exports.NuroMCPManager = NuroMCPManager;
//# sourceMappingURL=mcp.js.map