{"version": 3, "file": "mocker.js", "sourceRoot": "", "sources": ["../../src/nuro/mocker.ts"], "names": [], "mappings": ";;;AAUA,MAAa,eAAe;IAG1B,MAAM,CAAC,SAAS,CAAC,MAA6B;QAC5C,eAAe,CAAC,MAAM,GAAG,MAAM,CAAC;IAClC,CAAC;IAED,MAAM,CAAC,SAAS,CACd,WAA4B,EAC5B,OAAgC;;QAEhC,OAAO,MAAA,MAAA,eAAe,CAAC,MAAM,0CAAE,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,mCAAI,KAAK,CAAC;IAC1E,CAAC;IAED,MAAM,CAAC,SAAS;QACd,OAAO,eAAe,CAAC,MAAM,KAAK,SAAS,CAAC;IAC9C,CAAC;;AAhBH,0CAiBC;AAhBgB,sBAAM,GAA0B,SAAS,CAAC"}