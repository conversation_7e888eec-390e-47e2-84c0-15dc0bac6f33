"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroCanvasChecker = exports.NuroTaskChecker = exports.NuroTask = exports.NuroTaskOut = exports.NuroMessageOut = exports.NuroTaskStatus = exports.NuroTaskOp = exports.NuroMessageOp = void 0;
const message_1 = require("./message");
const chat_message_1 = require("../idl/chat_message");
const tsnfoundation_1 = require("@byted/tsnfoundation");
const setting_1 = require("./setting");
var NuroMessageOp;
(function (NuroMessageOp) {
    NuroMessageOp["add"] = "add";
    NuroMessageOp["update"] = "update";
    NuroMessageOp["delete"] = "delete";
})(NuroMessageOp = exports.NuroMessageOp || (exports.NuroMessageOp = {}));
var NuroTaskOp;
(function (NuroTaskOp) {
    NuroTaskOp["add"] = "add";
    NuroTaskOp["update"] = "update";
    NuroTaskOp["delete"] = "delete";
})(NuroTaskOp = exports.NuroTaskOp || (exports.NuroTaskOp = {}));
var NuroTaskStatus;
(function (NuroTaskStatus) {
    NuroTaskStatus["none"] = "none";
    NuroTaskStatus["running"] = "running";
    NuroTaskStatus["finished"] = "finished";
    NuroTaskStatus["failed"] = "failed";
    NuroTaskStatus["cancelled"] = "cancelled";
})(NuroTaskStatus = exports.NuroTaskStatus || (exports.NuroTaskStatus = {}));
class NuroMessageOut {
    constructor(message, op) {
        this.message = message;
        this.messageOp = op;
    }
}
exports.NuroMessageOut = NuroMessageOut;
class NuroTaskOut {
    constructor(task, taskOp) {
        this.messages = [];
        this.task = task;
        this.taskOp = taskOp;
    }
    updateMessage(messages) {
        messages.forEach((msg) => {
            if (this.task.containMessage(msg.message)) {
                this.messages.push(msg);
            }
            else {
                this.messages.push(new NuroMessageOut(msg.message, NuroMessageOp.delete));
            }
        });
    }
}
exports.NuroTaskOut = NuroTaskOut;
class NuroTask {
    constructor(taskId) {
        /**
         * 任务状态
         */
        this.taskStatus = NuroTaskStatus.none;
        /**
         * 消息哈希
         * 用于判断消息是否发生了变化
         */
        this.messageHash = "";
        /**
         * 用户输入的问题
         * 可能包含多个 UserMessage
         */
        this.promptMessages = [];
        /**
         * 中间消息
         * 可能包含多个 ReasoningMessage、AssistantMessage、ToolCallMessage
         * 这些消息表示了生成产物的过程
         */
        this.middlewareMessages = [];
        /**
         * 产物消息
         * 可能包含多个 AssistantMessage、ToolCallMessage
         */
        this.artifactMessages = [];
        /**
         *
         * 屏蔽掉的消息信息
         */
        this.shieldMessages = [];
        this.taskId = taskId;
    }
    recalculateMessageHash() {
        let messageHashPart = this.promptMessages
            .concat(this.middlewareMessages)
            .concat(this.shieldMessages)
            .concat(this.artifactMessages);
        const messageHash = messageHashPart
            .map((it) => it.id + "_" + it.updated.toString(10))
            .join(",");
        this.messageHash = messageHash;
    }
    displayingMiddlewareMessages() {
        let addFirstAssistant = false;
        return this.middlewareMessages.filter((msg) => {
            if (msg instanceof message_1.NuroAssistantMessage) {
                if (addFirstAssistant === false) {
                    addFirstAssistant = true;
                    return true;
                }
            }
            else if (msg instanceof message_1.NuroToolCallMessage) {
                if (msg.toolType === chat_message_1.ChatToolCallType.client_function.valueOf()) {
                    return true;
                }
            }
            return false;
        });
    }
    isDisplayingMiddlewareMessage(msg) {
        let showMsgs = this.displayingMiddlewareMessages();
        return showMsgs.some((it) => {
            it.id === msg.id;
        });
    }
    addMessage(msg, taskMsgType) {
        msg._task = this;
        msg.taskMessageType = taskMsgType;
        if (taskMsgType === message_1.NuroTaskMessageType.promptMessage) {
            this.promptMessages.push(msg);
        }
        else if (taskMsgType === message_1.NuroTaskMessageType.middlewareMessage) {
            this.middlewareMessages.push(msg);
        }
        else if (taskMsgType === message_1.NuroTaskMessageType.artifactMessage) {
            this.artifactMessages.push(msg);
        }
        else if (taskMsgType === message_1.NuroTaskMessageType.shieldMessage) {
            this.shieldMessages.push(msg);
        }
    }
    containMessage(msg) {
        var _a, _b, _c, _d;
        for (let i = 0; i < this.promptMessages.length; i++) {
            if (((_a = this.promptMessages[i]) === null || _a === void 0 ? void 0 : _a.id) === msg.id) {
                return true;
            }
        }
        for (let i = 0; i < this.middlewareMessages.length; i++) {
            if (((_b = this.middlewareMessages[i]) === null || _b === void 0 ? void 0 : _b.id) === msg.id) {
                return true;
            }
        }
        for (let i = 0; i < this.artifactMessages.length; i++) {
            if (((_c = this.artifactMessages[i]) === null || _c === void 0 ? void 0 : _c.id) === msg.id) {
                return true;
            }
        }
        for (let i = 0; i < this.shieldMessages.length; i++) {
            if (((_d = this.shieldMessages[i]) === null || _d === void 0 ? void 0 : _d.id) === msg.id) {
                return true;
            }
        }
        return false;
    }
    needResume() {
        return this.taskStatus === NuroTaskStatus.running;
    }
}
exports.NuroTask = NuroTask;
let NuroTaskChecker = class NuroTaskChecker {
    isPromptMessage(message) {
        return message instanceof message_1.NuroUserMessage;
    }
    isArtifactMessage(message) {
        return !(message instanceof message_1.NuroUserMessage);
    }
    isShieldMessage(message) {
        if (message instanceof message_1.NuroToolCallMessage) {
            return (setting_1.NuroSetting.shieldToolCall.indexOf(message.toolName) >= 0);
        }
        return false;
    }
};
NuroTaskChecker = __decorate([
    tsnfoundation_1.TSNOpenClass
], NuroTaskChecker);
exports.NuroTaskChecker = NuroTaskChecker;
class NuroCanvasChecker {
    isNuroCanvasMessage(message) {
        return false;
    }
}
exports.NuroCanvasChecker = NuroCanvasChecker;
//# sourceMappingURL=task.js.map