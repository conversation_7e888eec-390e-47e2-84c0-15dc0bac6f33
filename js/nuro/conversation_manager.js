"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroConversationManager = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const conversation_1 = require("./conversation");
const message_1 = require("./message");
const tos_impl_1 = require("../transport/tos_impl");
const history_message_1 = require("../idl/history_message");
const message_processor_1 = require("./message_processor");
const sse_impl_1 = require("../transport/sse_impl");
const utils_1 = require("./utils");
const mcp_tool_result_1 = require("../mcp/mcp_tool_result");
const chat_message_1 = require("../idl/chat_message");
const http_transport_1 = require("../transport/http_transport");
const mocker_1 = require("./mocker");
const task_1 = require("./task");
class NuroConversationManager {
    constructor(conversationId = utils_1.NuroUtils.randomUUIDString()) {
        this.toolCalled = {};
        this.conversation = new conversation_1.NuroConversation(conversationId);
    }
    /**
     * 注册需要链接的后端通道
     * @param transport
     */
    connect(transport) {
        this.activeTransport = transport;
        transport.setConversationManager(this);
        this.conversation.conversationState =
            conversation_1.NuroConversationState.readyToSendMessage;
    }
    /**
     * 使能本地工具调用
     */
    enableMCPTools() {
        let _self = this;
        this.conversation.addStateUpdateListener((state) => {
            if (_self === undefined) {
                return;
            }
            if (state === conversation_1.NuroConversationState.readyToSendMessage) {
                let msgs = [];
                this.conversation.messages.forEach((msg) => {
                    if (msg instanceof message_1.NuroToolCallMessage) {
                        if (msg.messageStatus === message_1.NuroToolCallMessageStatus.invoking)
                            if (msg.toolType === chat_message_1.ChatToolCallType.client_function.valueOf()) {
                                if (msg.toolResult === "") {
                                    msgs.push(msg);
                                }
                                else if (msg.toolResult === undefined) {
                                    msgs.push(msg);
                                }
                            }
                    }
                });
                if (msgs.length > 0) {
                    _self.onToolCall(msgs);
                }
            }
        });
    }
    /**
     * 解析会话历史数据
     * @param str 会话历史数据 jsonstr
     */
    decodeConversationFromJSONString(str, needResume = false) {
        var _a, _b, _c, _d;
        let history = new history_message_1.HistoryMessages({ JSONString: str });
        this.conversation.conversationId =
            (_b = (_a = history.conversation) === null || _a === void 0 ? void 0 : _a.conversation_id) !== null && _b !== void 0 ? _b : "";
        let messages = history.getChatMessages();
        this.conversation.summary = (_d = (_c = history.conversation) === null || _c === void 0 ? void 0 : _c.summary) !== null && _d !== void 0 ? _d : "";
        // Sort messages by createTime
        messages.sort((a, b) => {
            var _a, _b;
            const timeA = tsnfoundation_1.TSNNumberConverter.toDouble((_a = a.create_time) !== null && _a !== void 0 ? _a : 0);
            const timeB = tsnfoundation_1.TSNNumberConverter.toDouble((_b = b.create_time) !== null && _b !== void 0 ? _b : 0);
            if (timeA < timeB) {
                return -1;
            }
            else if (timeA > timeB) {
                return 1;
            }
            else {
                return 0;
            }
        });
        let nuroMessagesR = {};
        messages.forEach((message) => {
            var _a, _b;
            let msgs = message_processor_1.MessageProcessor.convertChatMessageToNuroMessage(this, message, message_processor_1.ConvertType.history);
            msgs.forEach((message) => {
                nuroMessagesR[message.id] = message;
            });
            let nuroMessages = tsnfoundation_1.TSNMapUtils.values(nuroMessagesR);
            let length = nuroMessages.length;
            if (length > 1) {
                let parentid = (_b = (_a = nuroMessages[0]) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : "";
                for (let index = 1; index < length; index = index + 1) {
                    let msg = nuroMessages[index];
                    if (msg !== undefined) {
                        msg.metadata.parent_message_id = parentid;
                        parentid = msg.id;
                    }
                }
            }
            // this.conversation.messages = nuroMessages;
            // this.conversation.processTask([], false);
            nuroMessages.forEach((msg) => {
                this.receivedMessage(msg, false);
            });
        });
        // 恢复历史数据
        if (needResume) {
            this.resumeMessageIfNeed();
        }
    }
    /**
     * 会话开始时需要提前填入的信息
     * @param value
     */
    setSystemPrompt(value) {
        this.conversation.systemPrompt = value;
    }
    resumeMessageIfNeed() {
        var _a;
        if (this.conversation.needResume() === false) {
            return;
        }
        this.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.resumeMessage();
    }
    sendUserMessage(message) {
        if (message === undefined) {
            return;
        }
        if (mocker_1.NuroMockManager.checkMock(message, this)) {
            return;
        }
        if (message._conversationManager === undefined) {
            message._conversationManager = this;
        }
        this.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        message.setMsgStatus(message_1.NuroUserMessageStatus.none);
        if (message.files !== undefined && message.files.length > 0) {
            let _files = [];
            for (let index = 0; index < message.files.length; index++) {
                const file = message.files[index];
                _files.push(file);
            }
            message.setMsgStatus(message_1.NuroUserMessageStatus.uploading_files);
            this.uploadFiles(_files, () => {
                this._doSendUserMessage(message);
            }, () => {
                message.errorMsg = message_1.ChatMessageError.upload_failed.valueOf();
                message.setMsgStatus(message_1.NuroUserMessageStatus.failed);
                this.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
            });
        }
        else {
            this._doSendUserMessage(message);
        }
    }
    _doSendUserMessage(message) {
        var _a;
        if (this.mcpManager !== undefined) {
            this.mcpManager.getAllTools((tools) => {
                var _a;
                (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessage(message, tools);
            });
        }
        else {
            (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessage(message, []);
        }
    }
    noUpload(file) {
        return file.url !== undefined && file.uri !== undefined;
    }
    uploadFiles(files, successCallback, failCallback) {
        let uploadQueueFiles = files;
        let file = uploadQueueFiles[0];
        if (file !== undefined) {
            let noUp = this.noUpload(file);
            if (noUp === true) {
                uploadQueueFiles.shift();
                this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
            }
            else {
                if (file.localFile !== undefined) {
                    let config = new tos_impl_1.TOSFileUploadConfig(file, file.localFile);
                    config.onFinish = () => {
                        uploadQueueFiles.shift();
                        this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
                    };
                    config.onError = (code, message) => {
                        failCallback();
                    };
                    tos_impl_1.TOSFileUploadImpl.upload(config);
                }
                else {
                    uploadQueueFiles.shift();
                    this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
                }
            }
        }
        else {
            successCallback();
        }
    }
    /**
     * 本地工具方法 结果回调
     * @param message 回调的工具
     * @param userMessage 额外需要带的用户消息
     */
    sendToolResultMessage(message, userMessage = undefined) {
        var _a;
        this.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        let msgs = [];
        msgs.push(message);
        if (userMessage !== undefined) {
            msgs.push(userMessage);
        }
        if (this.mcpManager !== undefined) {
            this.mcpManager.getAllTools((tools) => {
                var _a;
                (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessages(msgs, tools);
            });
        }
        else {
            (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessages(msgs, []);
        }
    }
    sendToolResultMessages(messages) {
        var _a, _b;
        messages.forEach((message) => {
            this.receivedMessage(message);
        });
        this.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        if (this.mcpManager !== undefined) {
            this.mcpManager.getAllTools((tools) => {
                var _a, _b;
                if (tsnfoundation_1.IS_KT) {
                    (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessages(messages, tools);
                }
                else {
                    (_b = this.activeTransport) === null || _b === void 0 ? void 0 : _b.sendMessages(messages, tools);
                }
            });
        }
        else {
            if (tsnfoundation_1.IS_KT) {
                (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.sendMessages(messages, []);
            }
            else {
                (_b = this.activeTransport) === null || _b === void 0 ? void 0 : _b.sendMessages(messages, []);
            }
        }
    }
    /**
     * 重试操作
     * @param message 需要被重试的消息
     *
     * @param message 是否需要删除重试消息
     */
    regenerateMessage(message, needDelete) {
        let hasTool = false;
        let index = -1;
        let userMessage = undefined;
        let indexuser = -1;
        for (let i = this.conversation.messages.length - 1; i > -1; i = i - 1) {
            let msg = this.conversation.messages[i];
            if (msg === undefined) {
                continue;
            }
            if (msg.id === message.id && msg.type === message.type) {
                index = i;
            }
            if (index > -1) {
                if (msg instanceof message_1.NuroToolCallMessage) {
                    hasTool = true;
                }
                else if (msg instanceof message_1.NuroUserMessage) {
                    userMessage = msg;
                    indexuser = i;
                    break;
                }
            }
        }
        if (index !== -1) {
            if (userMessage !== undefined) {
                if (needDelete === true) {
                    this.conversation.messages.splice(index, 1);
                    this.notifyConversationUpdate(message, task_1.NuroMessageOp.delete);
                }
                if (needDelete === true && index !== indexuser) {
                    this.conversation.messages.splice(indexuser, 1);
                    this.notifyConversationUpdate(userMessage, task_1.NuroMessageOp.delete);
                }
                let newMessage;
                if (hasTool === true && needDelete === false) {
                    newMessage = new message_1.NuroUserMessage(utils_1.NuroUtils.randomUUIDString(), "再次生成");
                }
                else {
                    newMessage = userMessage.copy();
                    if (newMessage !== undefined) {
                        newMessage.id = utils_1.NuroUtils.randomUUIDString();
                    }
                }
                this.sendUserMessage(newMessage);
            }
        }
    }
    /**
     * 中断 当前会话请求
     */
    interruptResponse() {
        var _a, _b, _c;
        let token = (_a = this.activeTransport) === null || _a === void 0 ? void 0 : _a.token;
        message_processor_1.MessageProcessor.markInProgressMessagesAsCancel(this);
        if (token !== undefined) {
            sse_impl_1.SSEImpl.cancel(token);
        }
        let interruptData = new message_1.InterruptData();
        interruptData.conversationId = (_b = this.conversation.conversationId) !== null && _b !== void 0 ? _b : "";
        let lastUserMessage = this.conversation.findLastUserMessage();
        interruptData.messageId = (_c = lastUserMessage === null || lastUserMessage === void 0 ? void 0 : lastUserMessage.id) !== null && _c !== void 0 ? _c : "";
        new http_transport_1.HttpTransport().sendRequest(sse_impl_1.EventStreamAdapter.interruptEndpoint, interruptData.toJSONString(), {}, (result) => { }, (code, reason) => { });
    }
    receivedMessage(_message, fromSSE = true) {
        var _a, _b, _c, _d;
        let message = _message.copy();
        message._conversationManager = this;
        let index = -1;
        for (let i = 0; i < this.conversation.messages.length; i++) {
            if (((_a = this.conversation.messages[i]) === null || _a === void 0 ? void 0 : _a.id) === message.id &&
                ((_b = this.conversation.messages[i]) === null || _b === void 0 ? void 0 : _b.type) === message.type) {
                index = i;
                break;
            }
        }
        if (index >= 0) {
            // message already received, current is a streaming message.
            let oldmsg = this.conversation.messages[index];
            if (oldmsg !== undefined) {
                if (oldmsg.isEqualToObject(message) === true) {
                    return;
                }
                if (oldmsg.isFinalStatus() === true && fromSSE === true) {
                    return;
                }
                message.updated = oldmsg.updated + 1;
            }
            this.conversation.messages[index] = message;
            this.notifyConversationUpdate(message, task_1.NuroMessageOp.update);
        }
        else {
            let length = this.conversation.messages.length;
            if (length > 0) {
                message.metadata.parent_message_id =
                    (_d = (_c = this.conversation.messages[length - 1]) === null || _c === void 0 ? void 0 : _c.id) !== null && _d !== void 0 ? _d : "";
            }
            this.conversation.messages.push(message);
            this.notifyConversationUpdate(message, task_1.NuroMessageOp.add);
        }
    }
    notifyConversationUpdate(message, op) {
        this.conversation.updateMessage(message, op);
    }
    onToolCall(toolCallMessages) {
        var _a;
        var _toolCallMessages = toolCallMessages;
        let toolCallCount = toolCallMessages.length;
        for (let index = 0; index < toolCallMessages.length; index++) {
            const toolCallMessage = toolCallMessages[index];
            if (this.toolCalled[toolCallMessage.id] === true) {
                toolCallCount = toolCallCount - 1;
                return;
            }
            this.toolCalled[toolCallMessage.id] = true;
            (_a = this.mcpManager) === null || _a === void 0 ? void 0 : _a.callTool(toolCallMessage.toolName, toolCallMessage.toolArgs, toolCallMessage.id, (result) => {
                const toolCallResult = new mcp_tool_result_1.MCPToolCallResult({ JSONString: result });
                if (toolCallResult.content.length <= 0) {
                    toolCallMessage._conversationManager = this;
                    toolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.wait_user_response);
                    // 无内容，代表需要等候用户操作，由 UI 再次发送 result。
                    // toolCallCount 也不作减 1 处理，因为 UI 会主动发送 result，不由此函数处理。
                }
                else {
                    let toolMsgCopy = toolCallMessage.copy();
                    toolMsgCopy.toolResult = result;
                    toolMsgCopy.messageStatus =
                        message_1.NuroToolCallMessageStatus.finished_successfully;
                    _toolCallMessages[index] = toolMsgCopy;
                    toolCallCount = toolCallCount - 1;
                    if (toolCallCount <= 0) {
                        this.sendToolResultMessages(_toolCallMessages);
                    }
                }
            });
        }
    }
}
exports.NuroConversationManager = NuroConversationManager;
//# sourceMappingURL=conversation_manager.js.map