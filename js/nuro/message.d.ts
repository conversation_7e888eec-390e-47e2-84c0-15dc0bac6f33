import type { NuroConversationManager } from "./conversation_manager";
import { TSNSerializable } from "@byted/tsnfoundation";
import { ChatMessageMetadata, ChatMessageStatus } from "../idl/chat_message";
import { MCPToolCallResult } from "../mcp/mcp_tool_result";
import type { NuroTask } from "./task";
export declare enum ChatMessageError {
    /**
     * 推流前异常错误
     * 推流失败，比如第一条事件就推送失败了
     */
    interrupt_status = "interrupt_status",
    /**
     * 推流前异常错误
     * pe策略失败
     */
    pe_policy_failed_status = "pe_policy_failed_status",
    /**
     * 推流前异常错误
     * 获取流失败
     */
    chat_stream_failed_status = "chat_stream_failed_status",
    /**
     * 安全审核拦截
     * 输入文本审核拦截
     */
    input_text_block_status = "input_text_block_status",
    /**
     * 安全审核拦截
     * 输出文本审核拦截
     */
    output_text_block_status = "output_text_block_status",
    /**
     * 推流异常状态
     * 推送思考内容截止
     */
    send_reasoning_content_stop_status = "send_reasoning_content_stop_status",
    /**
     * 推流异常状态
     * 推送内容截止
     */
    send_content_stop_status = "send_content_stop_status",
    /**
     * sdk返回值错误
     * sse请求错误
     */
    send_failed = "send_failed",
    /**
     * sdk返回值错误
     * 上传文件失败
     */
    upload_failed = "upload_failed"
}
export declare enum NuroUserMessageStatus {
    /**
     * 刚创建状态
     */
    none = "none",
    /**
     * 正在上传图片/视频
     */
    uploading_files = "uploading_files",
    /**
     * 正在发送
     */
    sending = "sending",
    /**
     * 收到返回的 AssistantMessage
     * 收到一点点立即变状态，不会等AssistantMessage完整收到再改变改状态
     */
    finished_successfully = "finished_successfully",
    /**
     * 失败
     */
    failed = "failed",
    /**
     * 取消
     */
    cancelled = "cancelled"
}
export declare enum NuroAssistantMessageStatus {
    /**
     * 刚创建状态
     */
    none = "none",
    /**
     * 正在回传数据流
     */
    streaming = "streaming",
    /**
     * 数据流返回完成
     */
    finished_successfully = "finished_successfully",
    /**
     * 失败
     */
    failed = "failed",
    /**
     * 取消
     */
    cancelled = "cancelled"
}
export declare enum NuroReasoningMessageStatus {
    /**
     * 刚创建状态
     */
    none = "none",
    /**
     * 正在回传数据流
     */
    streaming = "streaming",
    /**
     * 数据流返回完成
     */
    finished_successfully = "finished_successfully",
    /**
     * 失败
     */
    failed = "failed",
    /**
     * 取消
     */
    cancelled = "cancelled"
}
export declare enum NuroToolCallMessageStatus {
    /**
     * 刚创建状态
     */
    none = "none",
    /**
     * 工具参数正在流式传输中，上层业务不应允许用户操作本工具。
     */
    streaming = "streaming",
    /**
     * 流式传输失败，可能是 LLM 或 Host Agent 或网络异常原因。
     */
    streaming_failed = "streaming_failed",
    /**
     * 流式传输取消，可能是用户主动取消
     */
    streaming_cancelled = "streaming_cancelled",
    /**
     * 正在调用本地或远端方法
     * 此处执行过程中，toolResult 有可能被流式更新。
     */
    invoking = "invoking",
    /**
     * 本地方法无法直接返回结果
     * 等 sendToolResultMessage 方法调用返回结果
     */
    wait_user_response = "wait_user_response",
    /**
     * 用户发送其他消息，不再通过sendToolResultMessage 回调结果
     */
    skipped = "skipped",
    /**
     * 方法成功回调结果
     */
    finished_successfully = "finished_successfully"
}
export declare enum NuroMessageType {
    user = "user",
    assistant = "assistant",
    reasoning = "reasoning",
    toolcall = "tool_call",
    canvas = "canvas"
}
/**
 *  PayLoadData
 */
export declare class PayLoadData extends TSNSerializable {
    conversationId: string;
    messageId: string;
    payload: string;
}
/**
 *  InterruptData
 */
export declare class InterruptData extends TSNSerializable {
    conversationId: string;
    messageId: string;
}
/**
 *  ResumeData
 */
export declare class ResumeData extends TSNSerializable {
    conversationId: string;
    messageId: string;
    from_first_message?: boolean;
}
export declare enum NuroTaskMessageType {
    promptMessage = "promptMessage",
    middlewareMessage = "middlewareMessage",
    artifactMessage = "artifactMessage",
    shieldMessage = "shieldMessage"
}
export declare class NuroMessage {
    id: string;
    type: string;
    updated: Int;
    createTime: Int64;
    errorMsg?: string;
    metadata: ChatMessageMetadata;
    taskMessageType?: NuroTaskMessageType;
    endTurn?: boolean;
    /**
     * 原始的消息 id，用于标识服务端消息的唯一性，请勿在前端使用！！！
     */
    _rawId: string;
    /**
     * 会话管理器，用于发送消息，上层业务勿用。
     */
    _conversationManager: Weak<NuroConversationManager>;
    /**
     * 消息对应的task
     */
    _task: Weak<NuroTask>;
    /**
     * 消息索引值，用于排序
     */
    _messageIndex: Int;
    constructor(id: string, type: string);
    setMessagePayload(payload: string, successCallback: (result: string) => void, failCallback: (code: string, reason?: string) => void): void;
    baseCopy(message: NuroMessage): NuroMessage;
    copy(): NuroMessage;
    isDisplay(): boolean;
    isFinalStatus(): boolean;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    isEqualToObject(message: any): boolean;
    /**
     * 获取这条消息 对应的 这轮回话里面的 相关消息（包含 NuroUserMessage）
     */
    getMessageGroup(): NuroMessage[];
    needResume(): boolean;
    getResumeMsgId(): string;
}
export declare enum NuroFileType {
    image = "image",
    video = "video"
}
export declare class NuroLocalFile {
    localPath: string;
    localFileObject?: any;
    constructor(localPath: string, localFileObject?: any);
    isEqualToObject(other: any): boolean;
}
export declare class NuroImageMetadata {
    width?: Int;
    height?: Int;
    format?: string;
    prompt?: string;
    isEqualToObject(other: any): boolean;
}
export declare class NuroFileMetadata {
    image_metadata?: NuroImageMetadata;
    constructor(imageMetadata: NuroImageMetadata);
    isEqualToObject(other: any): boolean;
}
export declare class NuroFile {
    /**
     * image, video, etc.
     */
    type: NuroFileType;
    /**
     * url of the file.
     */
    url?: string;
    /**
     * uri of the file.
     */
    uri?: string;
    /**
     *
     */
    extra?: Record<string, any>;
    /**
     * 本地文件信息，当文件被用户选择后，并且未被上传时，会有该字段。
     * 该信息将透传至 TOS Uploader 方法，用于上传文件，文件上传后，url 字段会被更新。
     */
    localFile?: NuroLocalFile;
    /**
     * 增加imagemeta信息
     */
    mimeType?: string;
    /**
     * 文件描述, 业务需要添加的描述，可以用来描述相对关系
     */
    file_description?: string;
    /**
     * metadata, 用来存prompt等信息
     */
    metadata?: NuroFileMetadata;
    constructor(type: NuroFileType, url?: string, mimeType?: string, localFile?: Optional<NuroLocalFile>, file_description?: string);
    isEqualToObject(other: any): boolean;
}
export declare enum ReferenceRole {
    User = 0,
    Assistant = 1
}
export declare class RefContent {
    text?: string;
    file?: NuroFile;
    constructor(text?: string, file?: NuroFile);
}
export declare class NuroUserMessage extends NuroMessage {
    text?: string;
    files?: NuroFile[];
    messageStatus: NuroUserMessageStatus;
    referenceInfo?: RefContent[];
    constructor(id: string, text?: string, files?: Optional<NuroFile[]>, messageStatus?: NuroUserMessageStatus, referenceInfo?: Optional<RefContent[]>);
    copy(): NuroMessage;
    isEqualToObject(other: any): boolean;
    setMsgStatus(status: NuroUserMessageStatus): void;
    isFinalStatus(): boolean;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
}
export declare class NuroReasoningMessage extends NuroMessage {
    text: string;
    messageStatus: NuroReasoningMessageStatus;
    constructor(id: string, text: string, messageStatus?: NuroReasoningMessageStatus);
    copy(): NuroMessage;
    isEqualToObject(other: any): boolean;
    isFinalStatus(): boolean;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    setMsgStatus(status: NuroReasoningMessageStatus): void;
    setStatus(status?: ChatMessageStatus): void;
}
export declare class NuroAssistantMessage extends NuroMessage {
    name?: string;
    text?: string;
    files?: NuroFile[];
    messageStatus: NuroAssistantMessageStatus;
    relateToolCalls: Array<NuroToolCallMessage>;
    constructor(id: string, name?: string, text?: string, files?: Optional<NuroFile[]>, messageStatus?: NuroAssistantMessageStatus);
    copy(): NuroMessage;
    isEqualToObject(other: any): boolean;
    setMsgStatus(status: NuroAssistantMessageStatus): void;
    isFinalStatus(): boolean;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    setStatus(status?: ChatMessageStatus): void;
    isFinalTurn(): boolean;
}
export declare class NuroToolCallMessage extends NuroMessage {
    /**
     * functionType: server_function, client_function.
     */
    toolCallId: string;
    toolType: string;
    toolName: string;
    toolArgs?: string;
    toolResult?: string;
    toolExtra?: Record<string, string>;
    messageStatus: NuroToolCallMessageStatus;
    constructor(id: string, toolCallId: string, toolType: string, toolName: string, toolArgs?: string, toolExtra?: Record<string, string>, toolResult?: string, messageStatus?: NuroToolCallMessageStatus);
    sendToolCallResult(result: string, userMessage?: Optional<NuroUserMessage>): void;
    sendToolCallResultFromMCPFormat(callResult: MCPToolCallResult, userMessage?: Optional<NuroUserMessage>): void;
    decodeToolCallResultAsPlainText(): Optional<string>;
    decodeToolCallResultToMCPFormat(): MCPToolCallResult;
    setMsgStatus(status: NuroToolCallMessageStatus): void;
    isFinalStatus(): boolean;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    copy(): NuroToolCallMessage;
    isEqualToObject(other: any): boolean;
    isClientToolSkipped(): boolean;
}
export declare const CANVAS_DEFAULT: Int;
export declare const CANVAS_ADD_TO_END: Int;
export declare function getNodeFromNuroToolCallMsg(id: string, nodeIndex: Int, toolCall: NuroToolCallMessage): NuroCanvasNode;
export declare class NuroCanvasNode extends NuroToolCallMessage {
    nodeIndex: Int;
    constructor(id: string, nodeIndex: Int, toolCallId: string, toolType: string, toolName: string, toolArgs?: string, toolExtra?: Record<string, string>, toolResult?: string, messageStatus?: NuroToolCallMessageStatus);
    copy(): NuroCanvasNode;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    isFinalStatus(): boolean;
    getResumeMsgId(): string;
    isEqualToObject(other: any): boolean;
}
export declare enum NuroCanvasStatus {
    none = 0,
    init = 1,
    streaming = 2,
    cancel = 3,
    end = 4
}
export declare class NuroCanvasMessage extends NuroMessage {
    status: NuroCanvasStatus;
    startNode?: NuroToolCallMessage;
    endNode?: NuroToolCallMessage;
    nodes?: NuroCanvasNode[];
    constructor(id: string);
    updateStartNode(node: NuroToolCallMessage): void;
    updateCanvasStatus(status: NuroCanvasStatus): void;
    updateEndNode(node: NuroToolCallMessage): void;
    finish(): void;
    addOrReplaceNode(node: NuroCanvasNode): void;
    findNodeByToolCallId(toolCallId: string): Optional<NuroCanvasNode>;
    findEndNode(toolCallId: string): Optional<NuroToolCallMessage>;
    findStartNode(toolCallId: string): Optional<NuroToolCallMessage>;
    isCanvasOpen(): boolean;
    copy(): NuroCanvasMessage;
    isCancelledStatus(): boolean;
    isFailedStatus(): boolean;
    isFinalStatus(): boolean;
    setMsgStatus(status: NuroCanvasStatus): void;
    getResumeMsgId(): string;
    isEqualToObject(message: any): boolean;
}
