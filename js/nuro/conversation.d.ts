import { NuroMessage, NuroToolCallMessage, NuroCanvasMessage, NuroUserMessage } from "./message";
import { NuroMessageOp, NuroMessageOut, NuroTaskOut } from "../nurosdk";
import { SystemData } from "../idl/system_data";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>uro<PERSON>askChecker } from "./task";
export declare enum NuroConversationState {
    /**
     * The conversation is preparing to send the first message.
     * UI should show a loading indicator, should not allow user to send a message.
     */
    preparing = "preparing",
    /**
     * The conversation is streaming the response.
     * UI should show the response with streaming, should not allow user to send new message.
     * UI can display a pause button allow user to stop streaming.
     */
    streamingResponse = "streamingResponse",
    /**
     * The conversation is ready to send the first message.
     * UI should allow the user to send the first message.
     */
    readyToSendMessage = "readyToSendMessage"
}
export declare class NuroConversation {
    conversationId: string;
    systemPrompt?: string;
    conversationState: NuroConversationState;
    messages: Array<NuroMessage>;
    tasks: Array<NuroTask>;
    taskChecker?: NuroTaskChecker;
    summary?: string;
    constructor(conversationId: string);
    private stateUpdateListeners;
    private messageUpdateListeners;
    private taskUpdateListeners;
    systemDataListeners: Record<string, (systemData: SystemData) => void>;
    addStateUpdateListener(listener: (state: NuroConversationState) => void): string;
    removeStateUpdateListener(token: string): void;
    removeAllStateUpdateListeners(): void;
    addSystemDataListener(listener: (systemData: SystemData) => void): string;
    removeSystemDataListener(token: string): void;
    removeSystemDataListeners(): void;
    addMessageUpdateListener(listener: (message: NuroMessage, op: NuroMessageOp) => void): string;
    removeMessageUpdateListener(token: string): void;
    removeAllMessageUpdateListeners(): void;
    addTaskUpdateListener(listener: (tasks: NuroTaskOut[]) => void): string;
    removeTaskUpdateListener(token: string): void;
    removeAllTaskUpdateListeners(): void;
    needResume(): boolean;
    updateState(state: NuroConversationState): void;
    updateMessage(message: NuroMessage, op: NuroMessageOp): void;
    processTask(msgOut: NuroMessageOut[], needUpdate?: boolean): void;
    findToolCallMessageByToolCallId(toolCallId: string): Optional<NuroToolCallMessage>;
    findLastOpenNuroCanvasMessage(messageList: Optional<NuroMessage[]>): Optional<NuroCanvasMessage>;
    findLOpenNuroCanvasMessage(messageList: Optional<NuroMessage[]>): Optional<NuroCanvasMessage[]>;
    findOpenNuroCanvasMessageById(canvasMsgId: string, messageList: Optional<NuroMessage[]>): Optional<NuroCanvasMessage>;
    findLastUserMessage(): Optional<NuroUserMessage>;
}
