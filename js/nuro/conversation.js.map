{"version": 3, "file": "conversation.js", "sourceRoot": "", "sources": ["../../src/nuro/conversation.ts"], "names": [], "mappings": ";;;AAAA,wDAA+D;AAC/D,uCAQmB;AACnB,mCAAoC;AACpC,wCAQoB;AAGpB,iCAAmE;AACnE,uCAAwC;AAExC,IAAY,qBAmBX;AAnBD,WAAY,qBAAqB;IAC/B;;;OAGG;IACH,gDAAuB,CAAA;IAEvB;;;;OAIG;IACH,gEAAuC,CAAA;IAEvC;;;OAGG;IACH,kEAAyC,CAAA;AAC3C,CAAC,EAnBW,qBAAqB,GAArB,6BAAqB,KAArB,6BAAqB,QAmBhC;AAED,MAAa,gBAAgB;IAS3B,YAAY,cAAsB;QANlC,sBAAiB,GAA0B,qBAAqB,CAAC,SAAS,CAAC;QAC3E,aAAQ,GAAuB,EAAE,CAAC;QAClC,UAAK,GAAoB,EAAE,CAAC;QAC5B,gBAAW,GAAqB,SAAS,CAAC;QAC1C,YAAO,GAAY,SAAS,CAAC;QAKrB,yBAAoB,GAGxB,EAAE,CAAC;QACC,2BAAsB,GAG1B,EAAE,CAAC;QACC,wBAAmB,GACzB,EAAE,CAAC;QAEL,wBAAmB,GAAqD,EAAE,CAAC;QAbzE,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAcD,sBAAsB,CACpB,QAAgD;QAEhD,MAAM,IAAI,GAAG,iBAAS,CAAC,gBAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,yBAAyB,CAAC,KAAa;QACrC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IAC/C,CAAC;IAED,6BAA6B;QAC3B,IAAI,CAAC,oBAAoB,GAAG,EAAE,CAAC;IACjC,CAAC;IAED,qBAAqB,CAAC,QAA0C;QAC9D,MAAM,IAAI,GAAG,iBAAS,CAAC,gBAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wBAAwB,CAAC,KAAa;QACpC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IAC9C,CAAC;IAED,yBAAyB;QACvB,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAChC,CAAC;IAED,wBAAwB,CACtB,QAA2D;QAE3D,MAAM,IAAI,GAAG,iBAAS,CAAC,gBAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,2BAA2B,CAAC,KAAa;QACvC,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IACjD,CAAC;IAED,+BAA+B;QAC7B,IAAI,CAAC,sBAAsB,GAAG,EAAE,CAAC;IACnC,CAAC;IAED,qBAAqB,CAAC,QAAwC;QAC5D,MAAM,IAAI,GAAG,iBAAS,CAAC,gBAAgB,EAAE,CAAC;QAC1C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC;QAC1C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,wBAAwB,CAAC,KAAa;QACpC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC;IAC9C,CAAC;IAED,4BAA4B;QAC1B,IAAI,CAAC,mBAAmB,GAAG,EAAE,CAAC;IAChC,CAAC;IAED,UAAU;;QACR,IAAI,4BAAkB,CAAC,iBAAiB,KAAK,SAAS,EAAE;YACtD,OAAO,KAAK,CAAC;SACd;QAED,IAAI,qBAAW,CAAC,OAAO,KAAK,OAAO,EAAE;YACnC,aAAa;YACb,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAC/B,IAAI,MAAM,IAAI,CAAC,EAAE;gBACf,OAAO,KAAK,CAAC;aACd;YACD,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtC,OAAO,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,UAAU,MAAK,qBAAc,CAAC,OAAO,CAAC;SACxD;aAAM;YACL,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;YAElC,IAAI,MAAM,IAAI,CAAC,EAAE;gBACf,OAAO,KAAK,CAAC;aACd;YACD,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAE5C,OAAO,MAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,EAAE,mCAAI,KAAK,CAAC;SAC3C;IACH,CAAC;IAED,WAAW,CAAC,KAA4B;QACtC,oBAAU,CAAC,IAAI,CACb,kBAAkB,EAClB,GAAG,EAAE,CACH,oCAAoC,KAAK,sBAAsB,IAAI,CAAC,cAAc,EAAE,CACvF,CAAC;QACF,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAC;QAC/B,2BAAW,CAAC,OAAO,CAAC,IAAI,CAAC,oBAAoB,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YAC/D,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,aAAa,CAAC,OAAoB,EAAE,EAAiB;QACnD,oBAAU,CAAC,IAAI,CACb,kBAAkB,EAClB,GAAG,EAAE,CACH,mCAAmC,IAAI,CAAC,cAAc,eAAe,OAAO,CAAC,IAAI,UAAU,OAAO,CAAC,EAAE,UAAU,EAAE,EAAE,CACtH,CAAC;QACF,oCAAoC;QACpC,2BAAW,CAAC,OAAO,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;YACjE,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,IAAI,UAAU,GAAG,IAAI,wBAAc,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,WAAW,CAAC,MAAwB,EAAE,aAAsB,IAAI;QAC9D,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,MAAM,QAAQ,GAAoB,EAAE,CAAC;YACrC,IAAI,WAA+B,CAAC;YACpC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;gBAClC,IAAI,WAAW,CAAC,iBAAiB,CAAC,EAAE,CAAC,EAAE;oBACrC,IAAI,WAAW,KAAK,SAAS,EAAE;wBAC7B,MAAM,OAAO,GAAG,IAAI,eAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACpC,WAAW,GAAG,OAAO,CAAC;wBACtB,OAAO,CAAC,UAAU,GAAG,qBAAc,CAAC,OAAO,CAAC;wBAC5C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBACxB;oBACD,IAAI,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,EAAE;wBACnC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC,EAAE,EAAE,6BAAmB,CAAC,aAAa,CAAC,CAAC;qBAChE;yBAAM;wBACL,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC,EAAE,EAAE,6BAAmB,CAAC,eAAe,CAAC,CAAC;qBAClE;iBACF;qBAAM;oBACL,IACE,WAAW,KAAK,SAAS;wBACzB,WAAW,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EACvC;wBACA,WAAW,GAAG,SAAS,CAAC,CAAC,iFAAiF;qBAC3G;oBACD,IAAI,WAAW,KAAK,SAAS,EAAE;wBAC7B,MAAM,OAAO,GAAG,IAAI,eAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;wBACpC,WAAW,GAAG,OAAO,CAAC;wBACtB,OAAO,CAAC,UAAU,GAAG,qBAAc,CAAC,OAAO,CAAC;wBAC5C,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBACxB;oBACD,IAAI,WAAW,KAAK,SAAS,EAAE;wBAC7B,IAAI,WAAW,CAAC,eAAe,CAAC,EAAE,CAAC,KAAK,IAAI,EAAE;4BAC5C,IAAI,WAAW,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAC,EAAE;gCAC9C,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,6BAAmB,CAAC,aAAa,CAAC,CAAC;6BAC/D;iCAAM;gCACL,MAAM,OAAO,GAAG,IAAI,eAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;gCACpC,OAAO,CAAC,UAAU,GAAG,qBAAc,CAAC,OAAO,CAAC;gCAC5C,OAAO,CAAC,UAAU,CAAC,EAAE,EAAE,6BAAmB,CAAC,aAAa,CAAC,CAAC;gCAC1D,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gCACvB,WAAW,GAAG,OAAO,CAAC;6BACvB;yBACF;6BAAM;4BACL,WAAW,CAAC,UAAU,CAAC,EAAE,EAAE,6BAAmB,CAAC,iBAAiB,CAAC,CAAC;yBACnE;qBACF;iBACF;gBACD,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC7B,IAAI,EAAE,CAAC,iBAAiB,EAAE,EAAE;wBAC1B,WAAW,CAAC,UAAU,GAAG,qBAAc,CAAC,SAAS,CAAC;wBAClD,WAAW,GAAG,SAAS,CAAC;qBACzB;yBAAM,IAAI,EAAE,CAAC,cAAc,EAAE,EAAE;wBAC9B,WAAW,CAAC,UAAU,GAAG,qBAAc,CAAC,MAAM,CAAC;wBAC/C,WAAW,GAAG,SAAS,CAAC;qBACzB;yBAAM;wBACL,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;4BACtC,IAAI,EAAE,YAAY,8BAAoB,EAAE;gCACtC,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE;oCACpB,WAAW,CAAC,UAAU,GAAG,qBAAc,CAAC,QAAQ,CAAC;iCAClD;6BACF;iCAAM,IAAI,EAAE,YAAY,6BAAmB,EAAE;gCAC5C,IAAK,EAA0B,aAA1B,EAAE,uBAAF,EAAE,CAA0B,mBAAmB,EAAE,EAAE;oCACtD,WAAW,CAAC,UAAU,GAAG,qBAAc,CAAC,QAAQ,CAAC;iCAClD;6BACF;yBACF;qBACF;iBACF;YACH,CAAC,CAAC,CAAC;YAEH,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,sBAAsB,EAAE,CAAC,CAAC;YAC1D,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM,CAAC;YACvC,IAAI,WAAW,GAAuB,EAAE,CAAC;YAEzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;gBACvC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;oBACzB,IAAI,QAAQ,CAAC,CAAC,CAAE,CAAC,WAAW,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,CAAC,WAAW,EAAE;wBAC3D,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAE,CAAC;wBAC7B,IAAI,UAAU,EAAE;4BACd,IAAI,OAAO,GAAG,IAAI,qBAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAE,EAAE,oBAAU,CAAC,MAAM,CAAC,CAAC;4BACjE,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;yBAC3B;qBACF;iBACF;qBAAM;oBACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAE,CAAC,CAAC;oBAC9B,IAAI,UAAU,EAAE;wBACd,IAAI,OAAO,GAAG,IAAI,qBAAW,CAC3B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAE,EAClC,oBAAU,CAAC,GAAG,CACf,CAAC;wBACF,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC3B;iBACF;aACF;YAED,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,EAAE;gBACtC,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,cAAc,CAAC;gBAC9D,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CACpC,cAAc,EACd,kBAAkB,CACnB,CAAC;gBACF,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;oBACnC,IAAI,UAAU,EAAE;wBACd,IAAI,OAAO,GAAG,IAAI,qBAAW,CAAC,WAAW,EAAE,oBAAU,CAAC,MAAM,CAAC,CAAC;wBAC9D,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;qBAC3B;gBACH,CAAC,CAAC,CAAC;aACJ;YACD,WAAW,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,EAAE;gBACjC,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;YACnC,CAAC,CAAC,CAAC;YAEH,2BAAW,CAAC,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,EAAE,QAAQ,EAAE,EAAE;gBAC9D,QAAQ,CAAC,WAAW,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;SACJ;IACH,CAAC;IAED,+BAA+B,CAC7B,UAAkB;QAElB,IAAI,MAAM,GAAkC,SAAS,CAAC;QACtD,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAC3B,IAAI,EAAE,YAAY,6BAAmB,EAAE;gBACrC,IAAI,EAAE,CAAC,UAAU,KAAK,UAAU,EAAE;oBAChC,MAAM,GAAG,EAAE,CAAC;iBACb;aACF;QACH,CAAC,CAAC,CAAC;QACH,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,6BAA6B,CAC3B,WAAoC;QAEpC,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,0BAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAC3C,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,MAAM,GAAgC,SAAS,CAAC;QACpD,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,2BAAiB,EAAE;gBACnD,IAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAsB,CAAC;gBACtD,IAAI,OAAO,CAAC,YAAY,EAAE,EAAE;oBAC1B,MAAM,GAAG,OAAO,CAAC;oBACjB,MAAM;iBACP;gBACD,MAAM;aACP;YACD,KAAK,EAAE,CAAC;SACT;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,0BAA0B,CACxB,WAAoC;QAEpC,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,0BAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAC3C,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,MAAM,GAAkC,SAAS,CAAC;QACtD,IAAI,KAAK,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;QACnC,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,2BAAiB,EAAE;gBACnD,IAAI,OAAO,GAAG,WAAW,CAAC,KAAK,CAAsB,CAAC;gBACtD,IAAI,OAAO,CAAC,YAAY,EAAE,EAAE;oBAC1B,IAAI,MAAM,KAAK,SAAS,EAAE;wBACxB,MAAM,GAAG,EAAE,CAAC;qBACb;oBACD,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACtB;aACF;YACD,KAAK,EAAE,CAAC;SACT;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,6BAA6B,CAC3B,WAAmB,EACnB,WAAoC;QAEpC,IAAI,WAAW,KAAK,SAAS,EAAE;YAC7B,0BAAU,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAC3C,OAAO,SAAS,CAAC;SAClB;QACD,IAAI,MAAM,GAAgC,SAAS,CAAC;QACpD,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC;QAErC,OAAO,KAAK,IAAI,CAAC,EAAE;YACjB,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAY,2BAAiB,EAAE;gBACrD,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAsB,CAAC;gBACxD,IAAI,OAAO,CAAC,EAAE,KAAK,WAAW,EAAE;oBAC9B,MAAM,GAAG,OAAO,CAAC;oBACjB,MAAM;iBACP;aACF;YACD,KAAK,EAAE,CAAC;SACT;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,mBAAmB;QACjB,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YAC1C,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,GAAG,YAAY,yBAAe,EAAE;gBAClC,OAAO,GAAG,CAAC;aACZ;SACF;QACD,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AA/VD,4CA+VC"}