"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroSetting = void 0;
class NuroSetting {
}
exports.NuroSetting = NuroSetting;
/**
 * 是否需要展示 Server 端的 Function 调用消息
 * 如果为 true，则会在 Conversation.messages 中带有对应的 ToolCallMessage。
 */
NuroSetting.needDisplayServerFunctionMessage = true;
/**
 * 请求版本
 */
NuroSetting.version = "3.0.0";
/**
 *
 */
NuroSetting.canvasSettings = {
    startNode: "",
    endNode: "",
    nodes: [], // 默认值
};
/**
 * agent之间的屏蔽toolCall
 */
NuroSetting.shieldToolCall = ["handoff_to_planner", "handoff_to_host"];
//# sourceMappingURL=setting.js.map