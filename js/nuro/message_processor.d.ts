import { NuroMessage } from "../nuro/message";
import { ChatMessage } from "../idl/chat_message";
import type { NuroConversationManager } from "./conversation_manager";
export declare enum ConvertType {
    new_message = "new_message",
    history = "history"
}
export declare class MessageProcessor {
    static convertChatMessageToNuroMessage(conversationManager: Optional<NuroConversationManager>, chatMessage: ChatMessage, type: ConvertType): NuroMessage[];
    static markMessagesAsFinished(conversationManager: Optional<NuroConversationManager>): void;
    static markLastUserMessageAsFinished(conversationManager: Optional<NuroConversationManager>): void;
    static markInProgressMessagesAsFailed(conversationManager: Optional<NuroConversationManager>): void;
    static markInProgressMessagesAsCancel(conversationManager: Optional<NuroConversationManager>): void;
    static isUserMessage(message: NuroMessage): boolean;
    static isToolCallMessage(message: NuroMessage): boolean;
    static extractFirstJsonObject(jsonString: string): string;
}
