{"version": 3, "file": "mcp.js", "sourceRoot": "", "sources": ["../../src/nuro/mcp.ts"], "names": [], "mappings": ";;;AAAA,wDAI8B;AAY9B,MAAa,mBAAmB;IAI9B,YAAY,IAAY,EAAE,OAA6B;QACrD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AARD,kDAQC;AAED,MAAa,eAAe;IAkB1B,YACE,UAAkB,EAClB,IAAY,EACZ,WAAmB,EACnB,WAAmB;QAEnB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;CACF;AA7BD,0CA6BC;AAED,MAAa,cAAc;IAA3B;QACU,YAAO,GAAwC,EAAE,CAAC;QAClD,uBAAkB,GAA2B,EAAE,CAAC;IAmD1D,CAAC;IAjDC,cAAc,CAAC,MAA2B;QACxC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;IACrC,CAAC;IAED,WAAW,CAAC,QAA4C;QACtD,MAAM,QAAQ,GAAsB,EAAE,CAAC;QACvC,IAAI,SAAS,GAAG,2BAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/C,IAAI,SAAS,KAAK,CAAC,EAAE;YACnB,QAAQ,CAAC,QAAQ,CAAC,CAAC;YACnB,OAAO;SACR;QACD,2BAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,UAAU,EAAE,MAAM,EAAE,EAAE;YACvD,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,EAAE;gBACjC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;oBACrB,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,CAAC,kBAAkB,CAAC,UAAU,GAAG,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC;gBACrE,CAAC,CAAC,CAAC;gBACH,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;gBAC1B,IAAI,SAAS,KAAK,CAAC,EAAE;oBACnB,0BAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBACzB,QAAQ,CAAC,QAAQ,CAAC,CAAC;iBACpB;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,QAAQ,CACN,sBAA8B,EAC9B,qBAAuC,EACvC,cAAsB,EACtB,QAAkC;QAElC,MAAM,UAAU,GAAG,IAAI,CAAC,kBAAkB,CAAC,sBAAsB,CAAC,CAAC;QACnE,IAAI,UAAU,KAAK,SAAS,EAAE;YAC5B,OAAO;SACR;QACD,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACxC,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,OAAO;SACR;QACD,MAAM,CAAC,OAAO,CAAC,QAAQ,CACrB,cAAc,EACd,sBAAsB,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,EACpD,qBAAqB,EACrB,CAAC,MAAM,EAAE,EAAE;YACT,QAAQ,CAAC,MAAM,CAAC,CAAC;QACnB,CAAC,CACF,CAAC;IACJ,CAAC;CACF;AArDD,wCAqDC"}