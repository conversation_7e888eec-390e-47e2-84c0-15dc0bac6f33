"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatRequest = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const chat_message_1 = require("./chat_message");
const setting_1 = require("../nuro/setting");
class ChatRequest extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        /**
         * 填当前 Conversation 唯一的 UUID，如果是新的 Conversation 则为空
         */
        this.conversationId = undefined;
        /**
         * 填上一个消息的 UUID
         */
        this.parentMessageId = undefined;
        /**
         * 消息列表
         */
        this.messages = undefined;
        /**
         * system_prompt,eg:{\"mode\":\"潮玩二创\"}
         */
        this.systemPrompt = undefined;
        /**
         * 版本
         */
        this.version = setting_1.NuroSetting.version;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], ChatRequest.prototype, "conversationId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("parent_message_id")
], ChatRequest.prototype, "parentMessageId", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("messages", () => chat_message_1.ChatMessage)
], ChatRequest.prototype, "messages", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("system_prompt")
], ChatRequest.prototype, "systemPrompt", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("version")
], ChatRequest.prototype, "version", void 0);
exports.ChatRequest = ChatRequest;
//# sourceMappingURL=chat_request.js.map