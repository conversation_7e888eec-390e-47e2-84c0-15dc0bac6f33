import { TSNSerializable } from "@byted/tsnfoundation";
import { SSEDeltaMessage } from "./sse_message";
export declare class ChatPatchable extends TSNSerializable {
    applyPatch(delta: SSEDeltaMessage, pathComponents?: string[]): void;
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
    applyToArrayAny<T>(delta: SSEDeltaMessage, pathComponents: string[], newValueBuilder: (value?: string) => T, originValue?: T[]): T[];
    applyToArrayString(delta: SSEDeltaMessage, pathComponents: string[], originValue?: string[]): string[];
    applyToString(delta: SSEDeltaMessage, originValue?: string): string;
}
export declare enum ChatMessageStatus {
    /**
     * 消息状态, finished_successfully
     */
    finished_successfully = "finished_successfully",
    /**
     * 消息状态,  in_progress
     */
    in_progress = "in_progress",
    /**
     * 推流前异常错误
     * 推流失败，比如第一条事件就推送失败了
     */
    interrupt_status = "interrupt_status",
    /**
     * 推流前异常错误
     * pe策略失败
     */
    pe_policy_failed_status = "pe_policy_failed_status",
    /**
     * 推流前异常错误
     * 获取流失败
     */
    chat_stream_failed_status = "chat_stream_failed_status",
    /**
     * 安全审核拦截
     * 输入文本审核拦截
     */
    input_text_block_status = "input_text_block_status",
    /**
     * 安全审核拦截
     * 输出文本审核拦截
     */
    output_text_block_status = "output_text_block_status",
    /**
     * 推流异常状态
     * 推送思考内容截止
     */
    send_reasoning_content_stop_status = "send_reasoning_content_stop_status",
    /**
     * 推流异常状态
     * 推送内容截止
     */
    send_content_stop_status = "send_content_stop_status"
}
export declare enum ChatContentType {
    text = "text",
    image_url = "image_url"
}
export declare enum ChatMessageFileType {
    /**
     * txt
     */
    TXT = 1,
    /**
     * pdf
     */
    PDF = 2,
    /**
     * doc
     */
    DOC = 3,
    /**
     * docx
     */
    DOCX = 4,
    /**
     * image
     */
    IMAGE = 5,
    /**
     * video
     */
    VIDEO = 6,
    /**
     * audio
     */
    AUDIO = 7
}
export declare class ChatMessageImageMetadata extends ChatPatchable {
    image_width?: Int;
    image_height?: Int;
    image_format?: string;
    image_prompt?: string;
}
export declare class ChatMessageFileURI extends ChatPatchable {
    uri?: string;
    file_type?: ChatMessageFileType;
    file_name?: string;
    url?: string;
    image_metadata?: ChatMessageImageMetadata;
    extra?: Record<string, any>;
    file_description?: string;
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
}
export declare class ChatContentPart extends ChatPatchable {
    text?: string;
    file?: ChatMessageFileURI;
    is_referenced?: boolean;
    reasoning_content?: string;
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
}
export declare class ChatContent extends ChatPatchable {
    /**
     * 内容类型：text、image_url
     */
    content_type?: ChatContentType;
    /**
     * 消息内容
     */
    content_parts?: ChatContentPart[];
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
}
export declare enum ChatToolCallType {
    client_function = "client_function",
    server_function = "server_function"
}
export declare class ChatToolCallFunc extends ChatPatchable {
    /**
     * 函数名称
     */
    name?: string;
    /**
     * 函数调用参数
     */
    arguments?: string;
    /**
     * 内部使用，上一次成功修复的 JSON 参数，流式传输使用。
     */
    _lastRepairedArguments?: string;
    /**
     * 其他附带的内容
     */
    extra?: Record<string, string>;
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
}
/**
 * 工具调用定义
 */
export declare class ChatToolCall extends ChatPatchable {
    /**
     * 工具调用ID
     */
    id?: string;
    /**
     * 类型：client_function、server_function
     */
    type?: ChatToolCallType;
    /**
     * 是否正在流式返回工具参数
     */
    streaming?: boolean;
    /**
     * func
     */
    _func?: ChatToolCallFunc;
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
}
export declare enum ChatToolType {
    client_function = "client_function",
    server_function = "server_function"
}
export declare class ChatTool extends ChatPatchable {
    /**
     * 工具ID
     */
    id?: string;
    /**
     * 工具类型
     */
    type?: ChatToolType;
    /**
     * 工具名称
     */
    name?: string;
    /**
     * 工具描述
     */
    description?: string;
    /**
     * 工具参数（JSONSchema）
     */
    parameters?: string;
}
export declare class ChatAuthor extends ChatPatchable {
    /**
     * 角色
     */
    role?: string;
    /**
     * 名称
     */
    name?: string;
}
export declare class FinishDetails extends ChatPatchable {
    /**
     * 类型,eg: stop, interrupted
     */
    type: string;
    isEqualToObject(obj: any): boolean;
}
export declare class ChatMessageMetadata extends ChatPatchable {
    /**
     * 是否在会话中隐藏，有些消息不需要展示
     */
    is_visually_hidden_from_conversation: boolean;
    /**
     * 对话ID
     */
    conversation_id: string;
    /**
     * 父消息ID
     */
    parent_message_id: string;
    /**
     * 工具调用ID
     */
    tool_call_id?: string;
    /**
     * 完成详情(FinishDetails结构见公共结构)
     */
    finish_details?: FinishDetails;
    /**
     * 用户GUI操作
     */
    action?: string;
    /**
     * 回复类型：follow_up、answer
     * 1. follow_up: 表示推荐问
     * 2. answer表示：模型正常回答
     */
    message_type?: string;
    /**
     * 模型想起
     */
    model_detail?: string;
    /**
     * payload
     */
    payload?: string;
    /**
     * 是否对话已经完结，不是的话需要 续连
     */
    end_turn?: boolean;
    /**
     * 埋点透传字段
     */
    metricsExtra?: string;
    isEqualToObject(obj: any): boolean;
}
export declare class ChatMessage extends ChatPatchable {
    /**
     * 消息作者
     */
    author?: ChatAuthor;
    /**
     * 消息元数据
     */
    metadata?: ChatMessageMetadata;
    /**
     * 消息状态, finished_successfully, in_progress
     */
    status?: ChatMessageStatus;
    /**
     * 消息ID, UUID
     */
    id?: string;
    /**
     * 消息内容, 展示给用户查看
     */
    content?: ChatContent;
    /**
     * 更新时间，unix毫秒时间戳
     */
    update_time?: Int;
    /**
     * 创建时间, unix毫秒时间戳
     */
    create_time?: Int64;
    /**
     * 是否结束本轮对话，一轮对话可能由多个message构成
     */
    end_turn?: boolean;
    /**
     * 下发到端上需要执行的操作，端上被视为Agent的工具，可以被进行工具调用
     */
    tool_calls?: ChatToolCall[];
    tools?: ChatTool[];
    applyPatchPath(name: string, delta: SSEDeltaMessage, pathComponents: string[]): void;
    static convertValueToChatMessageStatus(value: string): ChatMessageStatus;
    static convertValueToChatMessageFileType(value: Int): ChatMessageFileType;
}
