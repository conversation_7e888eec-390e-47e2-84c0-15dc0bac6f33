import { TSNSerializable } from "@byted/tsnfoundation";
import { ChatMessage } from "./chat_message";
export declare class ChatRequest extends TSNSerializable {
    /**
     * 填当前 Conversation 唯一的 UUID，如果是新的 Conversation 则为空
     */
    conversationId?: string;
    /**
     * 填上一个消息的 UUID
     */
    parentMessageId?: string;
    /**
     * 消息列表
     */
    messages?: ChatMessage[];
    /**
     * system_prompt,eg:{\"mode\":\"潮玩二创\"}
     */
    systemPrompt?: string;
    /**
     * 版本
     */
    version?: string;
}
