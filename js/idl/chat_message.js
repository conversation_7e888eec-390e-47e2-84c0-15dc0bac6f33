"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ChatMessage = exports.ChatMessageMetadata = exports.FinishDetails = exports.ChatAuthor = exports.ChatTool = exports.ChatToolType = exports.ChatToolCall = exports.ChatToolCallFunc = exports.ChatToolCallType = exports.ChatContent = exports.ChatContentPart = exports.ChatMessageFileURI = exports.ChatMessageImageMetadata = exports.ChatMessageFileType = exports.ChatContentType = exports.ChatMessageStatus = exports.ChatPatchable = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class ChatPatchable extends tsnfoundation_1.TSNSerializable {
    applyPatch(delta, pathComponents) {
        let _pathComponents = pathComponents !== null && pathComponents !== void 0 ? pathComponents : (() => {
            const deltaPath = delta.path;
            if (deltaPath === undefined) {
                let v = [];
                return v;
            }
            let p = deltaPath.split("/");
            if (p[0] === "") {
                p.shift();
            }
            if (p[0] === "message") {
                p.shift();
            }
            return p;
        })();
        const currentPath = _pathComponents.shift();
        if (currentPath !== undefined) {
            this.applyPatchPath(currentPath, delta, _pathComponents);
        }
    }
    applyPatchPath(name, delta, pathComponents) { }
    applyToArrayAny(delta, pathComponents, newValueBuilder, originValue) {
        let _pathComponents = pathComponents;
        const indexPath = _pathComponents.shift();
        if (indexPath === undefined) {
            return [];
        }
        const index = parseInt(indexPath);
        let parts = originValue !== null && originValue !== void 0 ? originValue : [];
        if (parts[index] === undefined) {
            for (let j = parts.length; j < index + 1; j++) {
                if (parts[j] === undefined) {
                    parts.push(newValueBuilder(undefined));
                }
            }
        }
        if ((delta.op === "replace" || delta.op === "add") &&
            _pathComponents.length === 0) {
            parts[index] = newValueBuilder(delta.value);
        }
        const newValue = parts[index];
        if (newValue instanceof ChatPatchable) {
            newValue.applyPatch(delta, _pathComponents);
        }
        return parts;
    }
    applyToArrayString(delta, pathComponents, originValue) {
        var _a, _b;
        let _pathComponents = pathComponents;
        const indexPath = _pathComponents.shift();
        if (indexPath === undefined) {
            return [];
        }
        const index = parseInt(indexPath);
        let parts = originValue !== null && originValue !== void 0 ? originValue : [];
        if (parts[index] === undefined) {
            for (let j = parts.length; j < index + 1; j++) {
                if (parts[j] === undefined) {
                    parts.push("");
                }
            }
        }
        if ((delta.op === "replace" || delta.op === "add") &&
            _pathComponents.length === 0) {
            if (delta.value !== undefined) {
                parts[index] = delta.value;
            }
        }
        else if (delta.op === "append") {
            parts[index] = ((_a = parts[index]) !== null && _a !== void 0 ? _a : "") + ((_b = delta.value) !== null && _b !== void 0 ? _b : "");
        }
        return parts;
    }
    applyToString(delta, originValue) {
        var _a;
        if (delta.op === "replace") {
            if (delta.value !== undefined) {
                return delta.value;
            }
        }
        else if (delta.op === "append") {
            return (originValue !== null && originValue !== void 0 ? originValue : "") + ((_a = delta.value) !== null && _a !== void 0 ? _a : "");
        }
        return originValue !== null && originValue !== void 0 ? originValue : "";
    }
}
exports.ChatPatchable = ChatPatchable;
var ChatMessageStatus;
(function (ChatMessageStatus) {
    /**
     * 消息状态, finished_successfully
     */
    ChatMessageStatus["finished_successfully"] = "finished_successfully";
    /**
     * 消息状态,  in_progress
     */
    ChatMessageStatus["in_progress"] = "in_progress";
    /**
     * 推流前异常错误
     * 推流失败，比如第一条事件就推送失败了
     */
    ChatMessageStatus["interrupt_status"] = "interrupt_status";
    /**
     * 推流前异常错误
     * pe策略失败
     */
    ChatMessageStatus["pe_policy_failed_status"] = "pe_policy_failed_status";
    /**
     * 推流前异常错误
     * 获取流失败
     */
    ChatMessageStatus["chat_stream_failed_status"] = "chat_stream_failed_status";
    /**
     * 安全审核拦截
     * 输入文本审核拦截
     */
    ChatMessageStatus["input_text_block_status"] = "input_text_block_status";
    /**
     * 安全审核拦截
     * 输出文本审核拦截
     */
    ChatMessageStatus["output_text_block_status"] = "output_text_block_status";
    /**
     * 推流异常状态
     * 推送思考内容截止
     */
    ChatMessageStatus["send_reasoning_content_stop_status"] = "send_reasoning_content_stop_status";
    /**
     * 推流异常状态
     * 推送内容截止
     */
    ChatMessageStatus["send_content_stop_status"] = "send_content_stop_status";
})(ChatMessageStatus = exports.ChatMessageStatus || (exports.ChatMessageStatus = {}));
var ChatContentType;
(function (ChatContentType) {
    ChatContentType["text"] = "text";
    ChatContentType["image_url"] = "image_url";
})(ChatContentType = exports.ChatContentType || (exports.ChatContentType = {}));
var ChatMessageFileType;
(function (ChatMessageFileType) {
    /**
     * txt
     */
    ChatMessageFileType[ChatMessageFileType["TXT"] = 1] = "TXT";
    /**
     * pdf
     */
    ChatMessageFileType[ChatMessageFileType["PDF"] = 2] = "PDF";
    /**
     * doc
     */
    ChatMessageFileType[ChatMessageFileType["DOC"] = 3] = "DOC";
    /**
     * docx
     */
    ChatMessageFileType[ChatMessageFileType["DOCX"] = 4] = "DOCX";
    /**
     * image
     */
    ChatMessageFileType[ChatMessageFileType["IMAGE"] = 5] = "IMAGE";
    /**
     * video
     */
    ChatMessageFileType[ChatMessageFileType["VIDEO"] = 6] = "VIDEO";
    /**
     * audio
     */
    ChatMessageFileType[ChatMessageFileType["AUDIO"] = 7] = "AUDIO";
})(ChatMessageFileType = exports.ChatMessageFileType || (exports.ChatMessageFileType = {}));
class ChatMessageImageMetadata extends ChatPatchable {
    constructor() {
        super(...arguments);
        this.image_width = undefined;
        this.image_height = undefined;
        this.image_format = undefined;
        this.image_prompt = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("image_width")
], ChatMessageImageMetadata.prototype, "image_width", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("image_height")
], ChatMessageImageMetadata.prototype, "image_height", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("image_format")
], ChatMessageImageMetadata.prototype, "image_format", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("image_prompt")
], ChatMessageImageMetadata.prototype, "image_prompt", void 0);
exports.ChatMessageImageMetadata = ChatMessageImageMetadata;
class ChatMessageFileURI extends ChatPatchable {
    constructor() {
        super(...arguments);
        this.uri = undefined;
        this.file_type = undefined;
        this.file_name = undefined;
        this.url = undefined;
        this.image_metadata = undefined;
        this.extra = undefined;
        this.file_description = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        var _a;
        if (name === "uri") {
            this.uri = this.applyToString(delta, this.uri);
        }
        else if (name === "file_type") {
            if (delta.op === "replace") {
                if (delta.value !== undefined) {
                    this.file_type = ChatMessage.convertValueToChatMessageFileType(parseInt(delta.value));
                }
            }
        }
        else if (name === "file_name") {
            this.file_name = this.applyToString(delta, this.file_name);
        }
        else if (name === "url") {
            this.url = this.applyToString(delta, this.url);
        }
        else if (name === "image_metadata") {
            if (this.image_metadata === undefined) {
                this.image_metadata = new ChatMessageImageMetadata();
            }
            (_a = this.image_metadata) === null || _a === void 0 ? void 0 : _a.applyPatch(delta, pathComponents);
        }
        else if (name === "file_description") {
            this.file_description = this.applyToString(delta, this.file_description);
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("uri")
], ChatMessageFileURI.prototype, "uri", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("file_type")
], ChatMessageFileURI.prototype, "file_type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("file_name")
], ChatMessageFileURI.prototype, "file_name", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("url")
], ChatMessageFileURI.prototype, "url", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("image_metadata", () => ChatMessageImageMetadata)
], ChatMessageFileURI.prototype, "image_metadata", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("extra")
], ChatMessageFileURI.prototype, "extra", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("file_description")
], ChatMessageFileURI.prototype, "file_description", void 0);
exports.ChatMessageFileURI = ChatMessageFileURI;
class ChatContentPart extends ChatPatchable {
    constructor() {
        super(...arguments);
        this.text = undefined;
        this.file = undefined;
        this.is_referenced = undefined;
        this.reasoning_content = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        var _a, _b;
        if (name === "text") {
            this.text = this.applyToString(delta, this.text);
        }
        else if (name === "reasoning_content") {
            this.reasoning_content = this.applyToString(delta, this.reasoning_content);
        }
        else if (name === "file") {
            if (this.file === undefined) {
                this.file = new ChatMessageFileURI({ JSONString: (_a = delta.value) !== null && _a !== void 0 ? _a : "{}" });
            }
            (_b = this.file) === null || _b === void 0 ? void 0 : _b.applyPatch(delta, pathComponents);
        }
        else if (name === "is_referenced") {
            if (delta.value === "true") {
                this.is_referenced = true;
            }
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("text")
], ChatContentPart.prototype, "text", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("file", () => ChatMessageFileURI)
], ChatContentPart.prototype, "file", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("is_referenced")
], ChatContentPart.prototype, "is_referenced", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("reasoning_content")
], ChatContentPart.prototype, "reasoning_content", void 0);
exports.ChatContentPart = ChatContentPart;
class ChatContent extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 内容类型：text、image_url
         */
        this.content_type = undefined;
        /**
         * 消息内容
         */
        this.content_parts = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        if (name === "content_parts") {
            this.content_parts = this.applyToArrayAny(delta, pathComponents, (value) => {
                if (value !== undefined && value.indexOf("{") === 0) {
                    return new ChatContentPart({ JSONString: value !== null && value !== void 0 ? value : "{}" });
                }
                else {
                    return new ChatContentPart();
                }
            }, this.content_parts);
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("content_type")
], ChatContent.prototype, "content_type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("content_parts", () => ChatContentPart)
], ChatContent.prototype, "content_parts", void 0);
exports.ChatContent = ChatContent;
var ChatToolCallType;
(function (ChatToolCallType) {
    ChatToolCallType["client_function"] = "client_function";
    ChatToolCallType["server_function"] = "server_function";
})(ChatToolCallType = exports.ChatToolCallType || (exports.ChatToolCallType = {}));
class ChatToolCallFunc extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 函数名称
         */
        this.name = undefined;
        /**
         * 函数调用参数
         */
        this.arguments = undefined;
        /**
         * 内部使用，上一次成功修复的 JSON 参数，流式传输使用。
         */
        this._lastRepairedArguments = "{}";
        /**
         * 其他附带的内容
         */
        this.extra = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        if (name === "arguments") {
            this.arguments = this.applyToString(delta, this.arguments);
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("name")
], ChatToolCallFunc.prototype, "name", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("arguments")
], ChatToolCallFunc.prototype, "arguments", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("extra")
], ChatToolCallFunc.prototype, "extra", void 0);
exports.ChatToolCallFunc = ChatToolCallFunc;
/**
 * 工具调用定义
 */
class ChatToolCall extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 工具调用ID
         */
        this.id = undefined;
        /**
         * 类型：client_function、server_function
         */
        this.type = undefined;
        /**
         * 是否正在流式返回工具参数
         */
        this.streaming = undefined;
        /**
         * func
         */
        this._func = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        var _a;
        if (name === "func") {
            if (this._func === undefined) {
                this._func = new ChatToolCallFunc();
            }
            (_a = this._func) === null || _a === void 0 ? void 0 : _a.applyPatch(delta, pathComponents);
        }
        else if (name === "streaming") {
            if (delta.op === "replace") {
                if (delta.value !== undefined) {
                    this.streaming = delta.value === "true";
                }
            }
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("id")
], ChatToolCall.prototype, "id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], ChatToolCall.prototype, "type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("streaming")
], ChatToolCall.prototype, "streaming", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("func", () => ChatToolCallFunc)
], ChatToolCall.prototype, "_func", void 0);
exports.ChatToolCall = ChatToolCall;
var ChatToolType;
(function (ChatToolType) {
    ChatToolType["client_function"] = "client_function";
    ChatToolType["server_function"] = "server_function";
})(ChatToolType = exports.ChatToolType || (exports.ChatToolType = {}));
class ChatTool extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 工具ID
         */
        this.id = undefined;
        /**
         * 工具类型
         */
        this.type = undefined;
        /**
         * 工具名称
         */
        this.name = undefined;
        /**
         * 工具描述
         */
        this.description = undefined;
        /**
         * 工具参数（JSONSchema）
         */
        this.parameters = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("id")
], ChatTool.prototype, "id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], ChatTool.prototype, "type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("name")
], ChatTool.prototype, "name", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("description")
], ChatTool.prototype, "description", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("parameters")
], ChatTool.prototype, "parameters", void 0);
exports.ChatTool = ChatTool;
class ChatAuthor extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 角色
         */
        this.role = undefined;
        /**
         * 名称
         */
        this.name = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("role")
], ChatAuthor.prototype, "role", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("name")
], ChatAuthor.prototype, "name", void 0);
exports.ChatAuthor = ChatAuthor;
class FinishDetails extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 类型,eg: stop, interrupted
         */
        this.type = "";
    }
    isEqualToObject(obj) {
        if (obj instanceof FinishDetails) {
            return obj.type === this.type;
        }
        return false;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], FinishDetails.prototype, "type", void 0);
exports.FinishDetails = FinishDetails;
class ChatMessageMetadata extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 是否在会话中隐藏，有些消息不需要展示
         */
        this.is_visually_hidden_from_conversation = false;
        /**
         * 对话ID
         */
        this.conversation_id = "";
        /**
         * 父消息ID
         */
        this.parent_message_id = "";
        /**
         * 工具调用ID
         */
        this.tool_call_id = undefined;
        /**
         * 完成详情(FinishDetails结构见公共结构)
         */
        this.finish_details = undefined;
        /**
         * 用户GUI操作
         */
        this.action = undefined;
        /**
         * 回复类型：follow_up、answer
         * 1. follow_up: 表示推荐问
         * 2. answer表示：模型正常回答
         */
        this.message_type = undefined;
        /**
         * 模型想起
         */
        this.model_detail = undefined;
        /**
         * payload
         */
        this.payload = undefined;
        /**
         * 是否对话已经完结，不是的话需要 续连
         */
        this.end_turn = undefined;
        /**
         * 埋点透传字段
         */
        this.metricsExtra = undefined;
    }
    isEqualToObject(obj) {
        if (obj instanceof ChatMessageMetadata) {
            let finish_details_is_equal = true;
            if (obj.finish_details !== undefined &&
                this.finish_details !== undefined) {
                finish_details_is_equal = obj.finish_details.isEqualToObject(this.finish_details);
            }
            else if (obj.finish_details !== undefined &&
                this.finish_details === undefined) {
                finish_details_is_equal = false;
            }
            else if (obj.finish_details === undefined &&
                this.finish_details !== undefined) {
                finish_details_is_equal = false;
            }
            return (obj.conversation_id === this.conversation_id &&
                obj.parent_message_id === this.parent_message_id &&
                obj.tool_call_id === this.tool_call_id &&
                obj.is_visually_hidden_from_conversation ===
                    this.is_visually_hidden_from_conversation &&
                obj.action === this.action &&
                obj.message_type === this.message_type &&
                obj.model_detail === this.model_detail &&
                obj.payload === this.payload &&
                obj.end_turn === this.end_turn &&
                finish_details_is_equal === true);
        }
        return false;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("is_visually_hidden_from_conversation")
], ChatMessageMetadata.prototype, "is_visually_hidden_from_conversation", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], ChatMessageMetadata.prototype, "conversation_id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("parent_message_id")
], ChatMessageMetadata.prototype, "parent_message_id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("tool_call_id")
], ChatMessageMetadata.prototype, "tool_call_id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("finish_details", () => FinishDetails)
], ChatMessageMetadata.prototype, "finish_details", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("action")
], ChatMessageMetadata.prototype, "action", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("message_type")
], ChatMessageMetadata.prototype, "message_type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("model_detail")
], ChatMessageMetadata.prototype, "model_detail", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("payload")
], ChatMessageMetadata.prototype, "payload", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("end_turn")
], ChatMessageMetadata.prototype, "end_turn", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("metrics_extra")
], ChatMessageMetadata.prototype, "metricsExtra", void 0);
exports.ChatMessageMetadata = ChatMessageMetadata;
class ChatMessage extends ChatPatchable {
    constructor() {
        super(...arguments);
        /**
         * 消息作者
         */
        this.author = undefined;
        /**
         * 消息元数据
         */
        this.metadata = undefined;
        /**
         * 消息状态, finished_successfully, in_progress
         */
        this.status = undefined;
        /**
         * 消息ID, UUID
         */
        this.id = undefined;
        /**
         * 消息内容, 展示给用户查看
         */
        this.content = undefined;
        /**
         * 更新时间，unix毫秒时间戳
         */
        this.update_time = undefined;
        /**
         * 创建时间, unix毫秒时间戳
         */
        this.create_time = 0;
        /**
         * 是否结束本轮对话，一轮对话可能由多个message构成
         */
        this.end_turn = undefined;
        /**
         * 下发到端上需要执行的操作，端上被视为Agent的工具，可以被进行工具调用
         */
        this.tool_calls = undefined;
        this.tools = undefined;
    }
    applyPatchPath(name, delta, pathComponents) {
        var _a;
        if (name === "content") {
            if (this.content === undefined) {
                this.content = new ChatContent();
            }
            (_a = this.content) === null || _a === void 0 ? void 0 : _a.applyPatch(delta, pathComponents);
        }
        else if (name === "status") {
            if (delta.op === "replace") {
                if (delta.value !== undefined) {
                    this.status = ChatMessage.convertValueToChatMessageStatus(delta.value);
                }
            }
        }
        else if (name === "tool_calls") {
            this.tool_calls = this.applyToArrayAny(delta, pathComponents, (value) => {
                if (value !== undefined && value.indexOf("{") === 0) {
                    return new ChatToolCall({ JSONString: value });
                }
                return new ChatToolCall();
            }, this.tool_calls);
        }
        else if (name == "end_turn") {
            if (delta.op === "replace") {
                if (delta.value === "true") {
                    this.end_turn = true;
                }
                else {
                    this.end_turn = false;
                }
            }
        }
    }
    static convertValueToChatMessageStatus(value) {
        switch (value) {
            case "finished_successfully":
                return ChatMessageStatus.finished_successfully;
            case "in_progress":
                return ChatMessageStatus.in_progress;
            case "interrupt_status":
                return ChatMessageStatus.interrupt_status;
            case "pe_policy_failed_status":
                return ChatMessageStatus.pe_policy_failed_status;
            case "chat_stream_failed_status":
                return ChatMessageStatus.chat_stream_failed_status;
            case "input_text_block_status":
                return ChatMessageStatus.input_text_block_status;
            case "output_text_block_status":
                return ChatMessageStatus.output_text_block_status;
            case "send_reasoning_content_stop_status":
                return ChatMessageStatus.send_reasoning_content_stop_status;
            case "send_content_stop_status":
                return ChatMessageStatus.send_content_stop_status;
            default:
                return ChatMessageStatus.finished_successfully;
        }
    }
    static convertValueToChatMessageFileType(value) {
        switch (value) {
            case 1:
                return ChatMessageFileType.TXT;
            case 2:
                return ChatMessageFileType.PDF;
            case 3:
                return ChatMessageFileType.DOC;
            case 4:
                return ChatMessageFileType.DOCX;
            case 5:
                return ChatMessageFileType.IMAGE;
            case 6:
                return ChatMessageFileType.VIDEO;
            case 7:
                return ChatMessageFileType.AUDIO;
            default:
                return ChatMessageFileType.IMAGE;
        }
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("author", () => ChatAuthor)
], ChatMessage.prototype, "author", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("metadata", () => ChatMessageMetadata)
], ChatMessage.prototype, "metadata", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("status")
], ChatMessage.prototype, "status", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("id")
], ChatMessage.prototype, "id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("content", () => ChatContent)
], ChatMessage.prototype, "content", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("update_time")
], ChatMessage.prototype, "update_time", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("create_time", undefined, "Int64")
], ChatMessage.prototype, "create_time", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("end_turn")
], ChatMessage.prototype, "end_turn", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("tool_calls", () => ChatToolCall)
], ChatMessage.prototype, "tool_calls", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("tools", () => ChatTool)
], ChatMessage.prototype, "tools", void 0);
exports.ChatMessage = ChatMessage;
//# sourceMappingURL=chat_message.js.map