"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SystemData = exports.SystemDataType = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
var SystemDataType;
(function (SystemDataType) {
    SystemDataType["title_generation"] = "title_generation";
    SystemDataType["stream_complete"] = "stream_complete";
    SystemDataType["heartbeat"] = "heartbeat";
    SystemDataType["summary"] = "summary";
})(SystemDataType = exports.SystemDataType || (exports.SystemDataType = {}));
class SystemData extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.type = undefined;
        this.conversation_id = undefined;
        this.title = undefined;
        this.content = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], SystemData.prototype, "type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], SystemData.prototype, "conversation_id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("title")
], SystemData.prototype, "title", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("content")
], SystemData.prototype, "content", void 0);
exports.SystemData = SystemData;
//# sourceMappingURL=system_data.js.map