"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SSEFinalMessage = exports.SSEDeltaMessage = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class SSEDeltaMessage extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.op = undefined;
        this.path = undefined;
        this.value = undefined;
        this.original_value = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("op")
], SSEDeltaMessage.prototype, "op", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("path")
], SSEDeltaMessage.prototype, "path", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("value")
], SSEDeltaMessage.prototype, "value", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("original_value")
], SSEDeltaMessage.prototype, "original_value", void 0);
exports.SSEDeltaMessage = SSEDeltaMessage;
class SSEFinalMessage extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.ret = undefined;
        this.errmsg = undefined;
        this.systime = undefined;
        this.logid = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("ret")
], SSEFinalMessage.prototype, "ret", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("errmsg")
], SSEFinalMessage.prototype, "errmsg", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("systime")
], SSEFinalMessage.prototype, "systime", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("logid")
], SSEFinalMessage.prototype, "logid", void 0);
exports.SSEFinalMessage = SSEFinalMessage;
//# sourceMappingURL=sse_message.js.map