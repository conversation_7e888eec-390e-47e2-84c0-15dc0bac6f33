"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HistoryMessages = exports.HistoryConversation = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
const chat_message_1 = require("./chat_message");
class HistoryConversation extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.title = undefined;
        this.create_time = 0;
        this.update_time = 0;
        this.conversation_id = "";
        this.conversationMapping = undefined;
        this.summary = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("title")
], HistoryConversation.prototype, "title", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("create_time")
], HistoryConversation.prototype, "create_time", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("update_time")
], HistoryConversation.prototype, "update_time", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation_id")
], HistoryConversation.prototype, "conversation_id", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("mapping", () => chat_message_1.ChatMessage, "Record")
], HistoryConversation.prototype, "conversationMapping", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("summary")
], HistoryConversation.prototype, "summary", void 0);
exports.HistoryConversation = HistoryConversation;
class HistoryMessages extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.conversation = undefined;
        this.total = undefined;
        this.hasMore = undefined;
    }
    getChatMessages() {
        var _a;
        let result = [];
        let _mapping = (_a = this.conversation) === null || _a === void 0 ? void 0 : _a.conversationMapping;
        if (_mapping !== undefined) {
            let toolCallRoleMessages = [];
            tsnfoundation_1.TSNMapUtils.forEach(_mapping, (id, message) => {
                var _a;
                let role = (_a = message.author) === null || _a === void 0 ? void 0 : _a.role;
                if (role !== undefined && role === "tool") {
                    toolCallRoleMessages.push(message);
                    return;
                }
                result.push(message);
            });
            toolCallRoleMessages.forEach((message) => {
                result.push(message);
            });
        }
        else {
            return [];
        }
        return result;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("conversation", () => HistoryConversation)
], HistoryMessages.prototype, "conversation", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("total")
], HistoryMessages.prototype, "total", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("has_more")
], HistoryMessages.prototype, "hasMore", void 0);
exports.HistoryMessages = HistoryMessages;
//# sourceMappingURL=history_message.js.map