import { TSNSerializable } from "@byted/tsnfoundation";
import { ChatMessage } from "./chat_message";
export declare class HistoryConversation extends TSNSerializable {
    title?: string;
    create_time: Int;
    update_time: Int;
    conversation_id: string;
    conversationMapping?: Record<string, ChatMessage>;
    summary?: string;
}
export declare class HistoryMessages extends TSNSerializable {
    conversation?: HistoryConversation;
    total?: Int;
    hasMore?: boolean;
    getChatMessages(): Array<ChatMessage>;
}
