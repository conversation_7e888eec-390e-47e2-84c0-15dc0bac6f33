"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockHelper = void 0;
const message_1 = require("../nuro/message");
const dispatchers_1 = require("./dispatchers");
const conversation_1 = require("../nuro/conversation");
const utils_1 = require("../nuro/utils");
const chat_message_1 = require("../idl/chat_message");
const setting_1 = require("../nuro/setting");
class MockHelper {
    static checkMock(userMessage, manager) {
        if (!setting_1.NuroSetting.isMocking) {
            return false;
        }
        if (userMessage.text === "mock credits insufficient" ||
            userMessage.text === "mock c") {
            this.mockInsufficientCredits(manager);
            return true;
        }
        if (userMessage.text === "mock credits confirm" ||
            userMessage.text === "mock cc") {
            this.mockCreditsConfirm(manager);
            return true;
        }
        if (userMessage.text === "mock gen image" ||
            userMessage.text === "mock i") {
            this.mockGenImage(manager);
            return true;
        }
        if (userMessage.text === "mock gen two images" ||
            userMessage.text === "mock ii") {
            this.mockGenTwoImages(manager);
            return true;
        }
        if (userMessage.text === "mock gen image and video" ||
            userMessage.text === "mock iv") {
            this.mockGenImageAndVideo(manager);
            return true;
        }
        return false;
    }
    static createUserMessage(manager, text) {
        const message = new message_1.NuroUserMessage(utils_1.NuroUtils.randomUUIDString(), text);
        message._conversationManager = manager;
        message.setMsgStatus(message_1.NuroUserMessageStatus.sending);
        return message;
    }
    static createToolCallMessage(manager, functionName) {
        const toolCall = new message_1.NuroToolCallMessage(utils_1.NuroUtils.randomUUIDString(), "", chat_message_1.ChatToolCallType.client_function.valueOf(), functionName, undefined, undefined, message_1.NuroToolCallMessageStatus.none);
        toolCall._conversationManager = manager;
        return toolCall;
    }
    static splitTextIntoChunks(text) {
        const chunks = [];
        let index = 0;
        while (index < text.length) {
            chunks.push(text.slice(index, Math.min(index + 20, text.length)));
            index += 20;
        }
        return chunks;
    }
    static updateText(currentChunk, chunks, message, callback) {
        var _a, _b, _c;
        if (currentChunk < chunks.length) {
            message.text = (_c = (_a = message.text) === null || _a === void 0 ? void 0 : _a.concat((_b = chunks[currentChunk]) !== null && _b !== void 0 ? _b : "")) !== null && _c !== void 0 ? _c : "";
            message.setMsgStatus(message_1.NuroAssistantMessageStatus.streaming);
            dispatchers_1.Dispatcher.postDelay(500, () => {
                this.updateText(currentChunk + 1, chunks, message, callback);
            });
        }
        else {
            dispatchers_1.Dispatcher.postDelay(500, () => {
                message.setMsgStatus(message_1.NuroAssistantMessageStatus.finished_successfully);
                callback(message);
            });
        }
    }
    static createAssistantMessage(manager, text, callback) {
        const chunks = this.splitTextIntoChunks(text);
        const message = new message_1.NuroAssistantMessage(utils_1.NuroUtils.randomUUIDString(), "");
        message._conversationManager = manager;
        message.setMsgStatus(message_1.NuroAssistantMessageStatus.streaming);
        this.updateText(0, chunks, message, callback);
    }
    static mockGenImage(manager) {
        manager.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        const userMessage = this.createUserMessage(manager, "帮我生成一张小猫图片");
        dispatchers_1.Dispatcher.postDelay(1000, () => {
            userMessage.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
            // 使用新方法创建助手消息
            this.createAssistantMessage(manager, "当然可以！接下来将根据需求梳理任务，并生成图片。", (message) => {
                const imageToolCallMessage = this.createToolCallMessage(manager, "generate_text2image");
                imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming);
                dispatchers_1.Dispatcher.postDelay(1000, () => {
                    imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                    this.createAssistantMessage(manager, "如果需要生成更精准的效果，可以提供具体品牌/色彩偏好，我会进一步细化关键词结构。", (message) => {
                        manager.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
                    });
                });
            });
        });
    }
    static mockGenTwoImages(manager) {
        manager.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        const userMessage = this.createUserMessage(manager, "帮我生成二组时装图片");
        dispatchers_1.Dispatcher.postDelay(1000, () => {
            userMessage.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
            // 使用新方法创建助手消息
            this.createAssistantMessage(manager, "当然可以！接下来将根据需求梳理任务，并生成图片。", (message) => {
                const imageToolCallMessage = this.createToolCallMessage(manager, "generate_text2image");
                imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming);
                dispatchers_1.Dispatcher.postDelay(1000, () => {
                    imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                    this.createAssistantMessage(manager, "接下来我将为你创作第二组图片", (message) => {
                        const imageToolCallMessage = this.createToolCallMessage(manager, "generate_text2image");
                        imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming);
                        dispatchers_1.Dispatcher.postDelay(1000, () => {
                            imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                            this.createAssistantMessage(manager, "如果需要生成更精准的效果，可以提供具体品牌/色彩偏好，我会进一步细化关键词结构。", (message) => {
                                manager.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
                            });
                        });
                    });
                });
            });
        });
    }
    static mockGenImageAndVideo(manager) {
        manager.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        const userMessage = this.createUserMessage(manager, "帮我生成一个跳舞的小狗视频");
        dispatchers_1.Dispatcher.postDelay(1000, () => {
            userMessage.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
            this.createAssistantMessage(manager, "当然可以！我将先生成一个跳舞的小狗图片，再基于该图片生成视频。", (message) => {
                const imageToolCallMessage = this.createToolCallMessage(manager, "generate_text2image");
                imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming);
                dispatchers_1.Dispatcher.postDelay(2000, () => {
                    imageToolCallMessage.setMessagePayload('{"submitId":"581595_d0a7574c-ca6c-447f-a78e-843be0244c3f"}', (result) => { }, (code, reason) => { });
                    dispatchers_1.Dispatcher.postDelay(1000, () => {
                        imageToolCallMessage.sendToolCallResult('{"history_id":"19676697893378","commerce_info":{"credit_count":3}}');
                        imageToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                        this.createAssistantMessage(manager, "已成功生成一组欢快跳舞的小狗图片。接下来，我将基于这套图片风格生成小狗视频", (message) => {
                            const videoToolCallMessage = this.createToolCallMessage(manager, "creative_agent_mcp_gen_image2Video");
                            videoToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.streaming);
                            dispatchers_1.Dispatcher.postDelay(2000, () => {
                                videoToolCallMessage.setMessagePayload('{"submitId":"581595_9e526da3-375d-4c4b-abf5-0c3a9a11c53b"}', (result) => { }, (code, reason) => { });
                                dispatchers_1.Dispatcher.postDelay(1000, () => {
                                    videoToolCallMessage.sendToolCallResult('{"history_id":"15866175550210","commerce_info":{"credit_count":5}}');
                                    videoToolCallMessage.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                                    this.createAssistantMessage(manager, "已成功生成一只欢快跳舞的小狗视频。如果需要生成更精准的效果，可以提供具体品牌/色彩偏好，我会进一步细化关键词结构。", (message) => {
                                        manager.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
                                    });
                                });
                            });
                        });
                    });
                });
            });
        });
    }
    static mockInsufficientCredits(manager) {
        manager.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        const userMessage = this.createUserMessage(manager, "帮我生成时尚穿搭图片");
        dispatchers_1.Dispatcher.postDelay(1000, () => {
            userMessage.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
            this.createAssistantMessage(manager, "当然可以！接下来将根据需求梳理任务，并生成图片。", (message) => {
                const creditToolCall = this.createToolCallMessage(manager, "insufficient_points");
                creditToolCall.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                manager.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
            });
        });
    }
    static mockCreditsConfirm(manager) {
        manager.conversation.updateState(conversation_1.NuroConversationState.streamingResponse);
        const userMessage = this.createUserMessage(manager, "帮我生成时尚穿搭图片");
        dispatchers_1.Dispatcher.postDelay(1000, () => {
            userMessage.setMsgStatus(message_1.NuroUserMessageStatus.finished_successfully);
            this.createAssistantMessage(manager, "当然可以！接下来将根据需求梳理任务，并生成图片。", (message) => {
                const creditToolCall = this.createToolCallMessage(manager, "points_consumption_reminder");
                creditToolCall.setMsgStatus(message_1.NuroToolCallMessageStatus.finished_successfully);
                manager.conversation.updateState(conversation_1.NuroConversationState.readyToSendMessage);
            });
        });
    }
}
exports.MockHelper = MockHelper;
//# sourceMappingURL=mock_helper.js.map