import type { NuroConversationManager } from "../nuro/conversation_manager";
import { NuroUserMessage } from "../nuro/message";
export declare class MockHelper {
    static checkMock(userMessage: NuroUserMessage, manager: NuroConversationManager): boolean;
    private static createUserMessage;
    private static createToolCallMessage;
    private static splitTextIntoChunks;
    private static updateText;
    private static createAssistantMessage;
    private static mockGenImage;
    private static mockGenTwoImages;
    private static mockGenImageAndVideo;
    private static mockInsufficientCredits;
    private static mockCreditsConfirm;
}
