"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Dispatcher = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class Dispatcher {
    static setSleepImpl(sleep) {
        this.sleepImpl = sleep;
    }
    static sleep(timeMs) {
        this.sleepImpl(timeMs);
    }
    static postDelay(timeMs, callback) {
        tsnfoundation_1.TSNDispatchQueue.global().run(() => {
            Dispatcher.sleep(timeMs);
            tsnfoundation_1.TSNDispatchQueue.main().run(() => {
                callback();
            });
        });
    }
}
exports.Dispatcher = Dispatcher;
Dispatcher.sleepImpl = (timeMs) => { };
//# sourceMappingURL=dispatchers.js.map