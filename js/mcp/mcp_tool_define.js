"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformToZodSchema = exports.MCPToolDefineArrayProperty = exports.MCPToolDefineBooleanProperty = exports.MCPToolDefineNumberProperty = exports.MCPToolDefineIntegerProperty = exports.MCPToolDefineStringProperty = exports.MCPToolDefineObjectProperty = exports.MCPToolDefineProperty = exports.MCPToolDefine = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class MCPToolDefine {
    constructor(name, description, inputSchema) {
        this.name = name;
        this.description = description;
        this.inputSchema = inputSchema;
    }
}
exports.MCPToolDefine = MCPToolDefine;
class MCPToolDefineProperty extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.type = "";
        this.title = undefined;
        this.description = undefined;
        this.deprecated = undefined;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], MCPToolDefineProperty.prototype, "type", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("title")
], MCPToolDefineProperty.prototype, "title", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("description")
], MCPToolDefineProperty.prototype, "description", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("deprecated")
], MCPToolDefineProperty.prototype, "deprecated", void 0);
exports.MCPToolDefineProperty = MCPToolDefineProperty;
class MCPToolDefineObjectProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this.properties = {};
        this.required = [];
    }
    afterInit() {
        super.afterInit();
        this.type = "object";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defProperty(name, value) {
        this.properties[name] = value;
        return this;
    }
    defRequired(required) {
        this.required = required;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("properties", () => MCPToolDefineProperty, "Record")
], MCPToolDefineObjectProperty.prototype, "properties", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("required")
], MCPToolDefineObjectProperty.prototype, "required", void 0);
exports.MCPToolDefineObjectProperty = MCPToolDefineObjectProperty;
class MCPToolDefineStringProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this._default = undefined;
        this.examples = undefined;
        this._enum = undefined;
        this._const = undefined;
        this.minLength = undefined;
        this.maxLength = undefined;
        this.pattern = undefined;
    }
    afterInit() {
        super.afterInit();
        this.type = "string";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defDefault(value) {
        this._default = value;
        return this;
    }
    defExamples(values) {
        this.examples = values;
        return this;
    }
    defEnum(values) {
        this._enum = values;
        return this;
    }
    defConst(value) {
        this._const = value;
        return this;
    }
    defMinLength(value) {
        this.minLength = value;
        return this;
    }
    defMaxLength(value) {
        this.maxLength = value;
        return this;
    }
    defPattern(value) {
        this.pattern = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("default")
], MCPToolDefineStringProperty.prototype, "_default", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("examples")
], MCPToolDefineStringProperty.prototype, "examples", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("enum")
], MCPToolDefineStringProperty.prototype, "_enum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("const")
], MCPToolDefineStringProperty.prototype, "_const", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("minLength")
], MCPToolDefineStringProperty.prototype, "minLength", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("maxLength")
], MCPToolDefineStringProperty.prototype, "maxLength", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("pattern")
], MCPToolDefineStringProperty.prototype, "pattern", void 0);
exports.MCPToolDefineStringProperty = MCPToolDefineStringProperty;
class MCPToolDefineIntegerProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this._default = undefined;
        this.examples = undefined;
        this._enum = undefined;
        this._const = undefined;
        this.minimum = undefined;
        this.maximum = undefined;
    }
    afterInit() {
        super.afterInit();
        this.type = "integer";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defDefault(value) {
        this._default = value;
        return this;
    }
    defExamples(values) {
        this.examples = values;
        return this;
    }
    defEnum(values) {
        this._enum = values;
        return this;
    }
    defConst(value) {
        this._const = value;
        return this;
    }
    defMinimum(value) {
        this.minimum = value;
        return this;
    }
    defMaximum(value) {
        this.maximum = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("default")
], MCPToolDefineIntegerProperty.prototype, "_default", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("examples")
], MCPToolDefineIntegerProperty.prototype, "examples", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("enum")
], MCPToolDefineIntegerProperty.prototype, "_enum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("const")
], MCPToolDefineIntegerProperty.prototype, "_const", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("minimum")
], MCPToolDefineIntegerProperty.prototype, "minimum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("maximum")
], MCPToolDefineIntegerProperty.prototype, "maximum", void 0);
exports.MCPToolDefineIntegerProperty = MCPToolDefineIntegerProperty;
class MCPToolDefineNumberProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this._default = undefined;
        this.examples = undefined;
        this._enum = undefined;
        this._const = undefined;
        this.minimum = undefined;
        this.maximum = undefined;
    }
    afterInit() {
        super.afterInit();
        this.type = "number";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defDefault(value) {
        this._default = value;
        return this;
    }
    defExamples(values) {
        this.examples = values;
        return this;
    }
    defEnum(values) {
        this._enum = values;
        return this;
    }
    defConst(value) {
        this._const = value;
        return this;
    }
    defMinimum(value) {
        this.minimum = value;
        return this;
    }
    defMaximum(value) {
        this.maximum = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("default")
], MCPToolDefineNumberProperty.prototype, "_default", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("examples")
], MCPToolDefineNumberProperty.prototype, "examples", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("enum")
], MCPToolDefineNumberProperty.prototype, "_enum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("const")
], MCPToolDefineNumberProperty.prototype, "_const", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("minimum")
], MCPToolDefineNumberProperty.prototype, "minimum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("maximum")
], MCPToolDefineNumberProperty.prototype, "maximum", void 0);
exports.MCPToolDefineNumberProperty = MCPToolDefineNumberProperty;
class MCPToolDefineBooleanProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this._default = undefined;
    }
    afterInit() {
        super.afterInit();
        this.type = "boolean";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defDefault(value) {
        this._default = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("default")
], MCPToolDefineBooleanProperty.prototype, "_default", void 0);
exports.MCPToolDefineBooleanProperty = MCPToolDefineBooleanProperty;
class MCPToolDefineArrayProperty extends MCPToolDefineProperty {
    constructor() {
        super(...arguments);
        this.items = undefined;
        this._enum = undefined;
        this.maxItems = undefined;
        this.minItems = undefined;
    }
    afterInit() {
        super.afterInit();
        this.type = "array";
    }
    defDescription(value) {
        this.description = value;
        return this;
    }
    defTitle(value) {
        this.title = value;
        return this;
    }
    defDeprecated(value) {
        this.deprecated = value;
        return this;
    }
    defItems(value) {
        this.items = value;
        return this;
    }
    defEnum(values) {
        this._enum = values;
        return this;
    }
    defMaxItems(value) {
        this.maxItems = value;
        return this;
    }
    defMinItems(value) {
        this.minItems = value;
        return this;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("items", () => MCPToolDefineProperty)
], MCPToolDefineArrayProperty.prototype, "items", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("enum")
], MCPToolDefineArrayProperty.prototype, "_enum", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("maxItems")
], MCPToolDefineArrayProperty.prototype, "maxItems", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("minItems")
], MCPToolDefineArrayProperty.prototype, "minItems", void 0);
exports.MCPToolDefineArrayProperty = MCPToolDefineArrayProperty;
function transformToZodSchema(Zod, value, isRoot = true) {
    if (value === undefined) {
        return {};
    }
    if (tsnfoundation_1.IS_JS) {
        let schema;
        switch (value.type) {
            case "object":
                const objProp = value;
                const shape = {};
                for (const key in objProp.properties) {
                    shape[key] = transformToZodSchema(Zod, objProp.properties[key], false);
                    // Mark as optional if not in required list
                    if (objProp.required.indexOf(key) < 0) {
                        shape[key] = shape[key].optional();
                    }
                }
                schema = isRoot ? shape : Zod.z.object(shape);
                break;
            case "string":
                const strProp = value;
                let strSchema = Zod.string();
                if (strProp.minLength !== undefined) {
                    strSchema = strSchema.min(strProp.minLength);
                }
                if (strProp.maxLength !== undefined) {
                    strSchema = strSchema.max(strProp.maxLength);
                }
                // if (strProp.pattern !== undefined) {
                //   strSchema = strSchema.regex(new window.RegExp(strProp.pattern));
                // }
                if (strProp._enum !== undefined) {
                    // Zod enums require at least one value
                    if (strProp._enum.length > 0) {
                        schema = Zod.enum(strProp._enum);
                    }
                    else {
                        // Handle empty enum case if necessary, maybe default to string?
                        schema = Zod.string();
                    }
                }
                else {
                    schema = strSchema;
                }
                if (strProp._const !== undefined) {
                    schema = Zod.literal(strProp._const);
                }
                if (strProp._default !== undefined) {
                    schema = schema.default(strProp._default);
                }
                break;
            case "integer":
                const intProp = value;
                let intSchema = Zod.number().int();
                if (intProp.minimum !== undefined) {
                    intSchema = intSchema.min(intProp.minimum);
                }
                if (intProp.maximum !== undefined) {
                    intSchema = intSchema.max(intProp.maximum);
                }
                if (intProp._enum !== undefined) {
                    // Zod doesn't have a direct number enum, use union of literals
                    if (intProp._enum.length > 0) {
                        const literals = intProp._enum.map((val) => Zod.literal(val));
                        schema = Zod.union(literals); // Need at least two for union
                    }
                    else {
                        schema = intSchema;
                    }
                }
                else {
                    schema = intSchema;
                }
                if (intProp._const !== undefined) {
                    schema = Zod.literal(intProp._const);
                }
                if (intProp._default !== undefined) {
                    schema = schema.default(intProp._default);
                }
                break;
            case "number":
                const numProp = value;
                let numSchema = Zod.number();
                if (numProp.minimum !== undefined) {
                    numSchema = numSchema.min(numProp.minimum);
                }
                if (numProp.maximum !== undefined) {
                    numSchema = numSchema.max(numProp.maximum);
                }
                if (numProp._enum !== undefined) {
                    // Zod doesn't have a direct number enum, use union of literals
                    if (numProp._enum.length > 0) {
                        const literals = numProp._enum.map((val) => Zod.literal(val));
                        schema = Zod.union(literals); // Need at least two for union
                    }
                    else {
                        schema = numSchema;
                    }
                }
                else {
                    schema = numSchema;
                }
                if (numProp._const !== undefined) {
                    schema = Zod.literal(numProp._const);
                }
                if (numProp._default !== undefined) {
                    schema = schema.default(numProp._default);
                }
                break;
            case "boolean":
                const boolProp = value;
                schema = Zod.boolean();
                if (boolProp._default !== undefined) {
                    schema = schema.default(boolProp._default);
                }
                break;
            case "array":
                const arrProp = value;
                let itemSchema = Zod.any(); // Default to any if items is not defined
                if (arrProp.items) {
                    itemSchema = transformToZodSchema(Zod, arrProp.items, false);
                }
                let arrSchema = Zod.array(itemSchema);
                if (arrProp.minItems !== undefined) {
                    arrSchema = arrSchema.min(arrProp.minItems);
                }
                if (arrProp.maxItems !== undefined) {
                    arrSchema = arrSchema.max(arrProp.maxItems);
                }
                // Note: Zod doesn't directly support array enums like JSON schema.
                // You might need custom validation logic if strict enum checking is needed for arrays.
                schema = arrSchema;
                break;
            default:
                // Fallback for unknown types or handle specific cases like 'null'
                schema = Zod.any();
                break;
        }
        // Apply common properties like description
        if (value.description) {
            schema = schema.describe(value.description);
        }
        return schema;
    }
    else {
        return {};
    }
}
exports.transformToZodSchema = transformToZodSchema;
//# sourceMappingURL=mcp_tool_define.js.map