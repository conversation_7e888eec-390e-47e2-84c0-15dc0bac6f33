import { TSNSerializable } from "@byted/tsnfoundation";
export declare class MCP<PERSON><PERSON>CallContent extends TSNSerializable {
    type: string;
}
export declare class MCPToolCallResource extends MCPToolCallContent {
    uri?: string;
    name?: string;
    text?: string;
    mimeType?: string;
}
export declare class MCPToolCallResourceContent extends MCPToolCallContent {
    resource?: MCPToolCallResource;
    afterInit(): void;
    static create(uri: string, name: string, text?: string, mimeType?: string): MCPToolCallResourceContent;
}
export declare class MCPToolCallResult extends TSNSerializable {
    content: MCPToolCallContent[];
    afterParse(): void;
}
export declare class MCPToolCallTextContent extends MCPToolCallContent {
    text: string;
    afterInit(): void;
    static create(text: string): MCPToolCallTextContent;
}
