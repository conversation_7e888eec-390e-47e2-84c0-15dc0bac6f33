"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPToolCallTextContent = exports.MCPToolCallResult = exports.MCPToolCallResourceContent = exports.MCPToolCallResource = exports.MCPToolCallContent = void 0;
const tsnfoundation_1 = require("@byted/tsnfoundation");
class MCPToolCallContent extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.type = "text";
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("type")
], MCPToolCallContent.prototype, "type", void 0);
exports.MCPToolCallContent = MCPToolCallContent;
class MCPToolCallResource extends MCPToolCallContent {
    constructor() {
        super(...arguments);
        this.uri = undefined;
        this.name = undefined;
        this.text = undefined;
        this.mimeType = undefined;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("uri")
], MCPToolCallResource.prototype, "uri", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("name")
], MCPToolCallResource.prototype, "name", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("text")
], MCPToolCallResource.prototype, "text", void 0);
__decorate([
    (0, tsnfoundation_1.TSNJSON)("mimeType")
], MCPToolCallResource.prototype, "mimeType", void 0);
exports.MCPToolCallResource = MCPToolCallResource;
class MCPToolCallResourceContent extends MCPToolCallContent {
    constructor() {
        super(...arguments);
        this.resource = new MCPToolCallResource();
    }
    afterInit() {
        super.afterInit();
        this.type = "resource";
    }
    static create(uri, name, text, mimeType) {
        const content = new MCPToolCallResourceContent();
        const resource = new MCPToolCallResource();
        resource.uri = uri;
        resource.name = name;
        resource.text = text;
        resource.mimeType = mimeType;
        content.resource = resource;
        return content;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("resource", () => MCPToolCallResource)
], MCPToolCallResourceContent.prototype, "resource", void 0);
exports.MCPToolCallResourceContent = MCPToolCallResourceContent;
class MCPToolCallResult extends tsnfoundation_1.TSNSerializable {
    constructor() {
        super(...arguments);
        this.content = [];
    }
    afterParse() {
        this.content = this.content.map((it) => {
            if (it.type === "text") {
                return new MCPToolCallTextContent({ JSONObject: it.rawData });
            }
            else if (it.type === "resource") {
                return new MCPToolCallResourceContent({ JSONObject: it.rawData });
            }
            else {
                return it;
            }
        });
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("content", () => MCPToolCallContent)
], MCPToolCallResult.prototype, "content", void 0);
exports.MCPToolCallResult = MCPToolCallResult;
class MCPToolCallTextContent extends MCPToolCallContent {
    constructor() {
        super(...arguments);
        this.text = "";
    }
    afterInit() {
        super.afterInit();
        this.type = "text";
    }
    static create(text) {
        const content = new MCPToolCallTextContent();
        content.text = text;
        return content;
    }
}
__decorate([
    (0, tsnfoundation_1.TSNJSON)("text")
], MCPToolCallTextContent.prototype, "text", void 0);
exports.MCPToolCallTextContent = MCPToolCallTextContent;
//# sourceMappingURL=mcp_tool_result.js.map