"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NuroMCPClientAdapterImpl = void 0;
// @ts-nocheck
const { McpServer } = require("@modelcontextprotocol/sdk/server/mcp.js");
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const McpClient = Client;
const { InMemoryTransport } = require("@modelcontextprotocol/sdk/inMemory.js");
const nurosdk_1 = require("../nurosdk");
class NuroMCPClientAdapterImpl {
    constructor(serverName, mcpServer) {
        this.serverName = serverName;
        this.mcpServer = mcpServer;
        const transports = InMemoryTransport.createLinkedPair();
        const clientTransport = transports[0];
        const serverTransport = transports[1];
        this.mcpServer.connect(serverTransport);
        const mcpClient = new McpClient({
            name: this.serverName,
            version: "1.0.0",
        });
        mcpClient.connect(clientTransport);
        this.mcpClient = mcpClient;
    }
    listTools(callback) {
        return __awaiter(this, void 0, void 0, function* () {
            const tools = (yield this.mcpClient.listTools()).tools;
            callback(tools.map((tool) => {
                var _a;
                return new nurosdk_1.NuroMCPToolItem(this.serverName, tool.name, (_a = tool.description) !== null && _a !== void 0 ? _a : "", JSON.stringify(tool.inputSchema), JSON.stringify(tool));
            }));
        });
    }
    callTool(toolCallId, toolName, toolArgs, callback) {
        this.mcpClient.callTool({
            name: toolName,
            arguments: (() => {
                try {
                    return JSON.parse(toolArgs);
                }
                catch (error) {
                    return {};
                }
            })(),
        }).then((result) => {
            callback(JSON.stringify(result));
        });
    }
}
exports.NuroMCPClientAdapterImpl = NuroMCPClientAdapterImpl;
//# sourceMappingURL=mcp_client_adapter_impl.js.js.map