import { NuroMCPClientAdapter, NuroMCPToolItem } from "../nurosdk";
export declare class NuroMCPClientAdapterImpl implements NuroMCPClientAdapter {
    readonly serverName: string;
    readonly mcpServer: any;
    private readonly mcpClient;
    constructor(serverName: string, mcpServer: any);
    listTools(callback: (tools: NuroMCPToolItem[]) => void): Promise<void>;
    callTool(toolCallId: string, toolName: string, toolArgs: string, callback: (result: string) => void): void;
}
