import { TSNSerializable } from "@byted/tsnfoundation";
export declare class MCPToolDefine {
    name: string;
    description: string;
    inputSchema?: MCPToolDefineObjectProperty;
    constructor(name: string, description: string, inputSchema?: MCPToolDefineObjectProperty);
}
export declare class MCPToolDefineProperty extends TSNSerializable {
    type: string;
    title?: string;
    description?: string;
    deprecated?: boolean;
    defTitle(value: string): MCPToolDefineProperty;
    defDescription(value: string): MCPToolDefineProperty;
    defDeprecated(value: boolean): MCPToolDefineProperty;
}
export declare class MCPToolDefineObjectProperty extends MCPToolDefineProperty {
    properties: Record<string, MCPToolDefineProperty>;
    required: string[];
    afterInit(): void;
    defDescription(value: string): MCPToolDefineObjectProperty;
    defTitle(value: string): MCPToolDefineObjectProperty;
    defDeprecated(value: boolean): MCPToolDefineObjectProperty;
    defProperty(name: string, value: MCPToolDefineProperty): MCPToolDefineObjectProperty;
    defRequired(required: string[]): MCPToolDefineObjectProperty;
}
export declare class MCPToolDefineStringProperty extends MCPToolDefineProperty {
    _default?: string;
    examples?: string[];
    _enum?: string[];
    _const?: string;
    minLength?: Int;
    maxLength?: Int;
    pattern?: string;
    afterInit(): void;
    defDescription(value: string): MCPToolDefineStringProperty;
    defTitle(value: string): MCPToolDefineStringProperty;
    defDeprecated(value: boolean): MCPToolDefineStringProperty;
    defDefault(value: string): MCPToolDefineStringProperty;
    defExamples(values: string[]): MCPToolDefineStringProperty;
    defEnum(values: string[]): MCPToolDefineStringProperty;
    defConst(value: string): MCPToolDefineStringProperty;
    defMinLength(value: Int): MCPToolDefineStringProperty;
    defMaxLength(value: Int): MCPToolDefineStringProperty;
    defPattern(value: string): MCPToolDefineStringProperty;
}
export declare class MCPToolDefineIntegerProperty extends MCPToolDefineProperty {
    _default?: Int;
    examples?: Int[];
    _enum?: Int[];
    _const?: Int;
    minimum?: Int;
    maximum?: Int;
    afterInit(): void;
    defDescription(value: string): MCPToolDefineIntegerProperty;
    defTitle(value: string): MCPToolDefineIntegerProperty;
    defDeprecated(value: boolean): MCPToolDefineIntegerProperty;
    defDefault(value: Int): MCPToolDefineIntegerProperty;
    defExamples(values: Int[]): MCPToolDefineIntegerProperty;
    defEnum(values: Int[]): MCPToolDefineIntegerProperty;
    defConst(value: Int): MCPToolDefineIntegerProperty;
    defMinimum(value: Int): MCPToolDefineIntegerProperty;
    defMaximum(value: Int): MCPToolDefineIntegerProperty;
}
export declare class MCPToolDefineNumberProperty extends MCPToolDefineProperty {
    _default?: Double;
    examples?: Double[];
    _enum?: Double[];
    _const?: Double;
    minimum?: Double;
    maximum?: Double;
    afterInit(): void;
    defDescription(value: string): MCPToolDefineNumberProperty;
    defTitle(value: string): MCPToolDefineNumberProperty;
    defDeprecated(value: boolean): MCPToolDefineNumberProperty;
    defDefault(value: Double): MCPToolDefineNumberProperty;
    defExamples(values: Double[]): MCPToolDefineNumberProperty;
    defEnum(values: Double[]): MCPToolDefineNumberProperty;
    defConst(value: Double): MCPToolDefineNumberProperty;
    defMinimum(value: Double): MCPToolDefineNumberProperty;
    defMaximum(value: Double): MCPToolDefineNumberProperty;
}
export declare class MCPToolDefineBooleanProperty extends MCPToolDefineProperty {
    _default?: boolean;
    afterInit(): void;
    defDescription(value: string): MCPToolDefineBooleanProperty;
    defTitle(value: string): MCPToolDefineBooleanProperty;
    defDeprecated(value: boolean): MCPToolDefineBooleanProperty;
    defDefault(value: boolean): MCPToolDefineBooleanProperty;
}
export declare class MCPToolDefineArrayProperty extends MCPToolDefineProperty {
    items?: MCPToolDefineProperty;
    _enum?: any[];
    maxItems?: Int;
    minItems?: Int;
    afterInit(): void;
    defDescription(value: string): MCPToolDefineArrayProperty;
    defTitle(value: string): MCPToolDefineArrayProperty;
    defDeprecated(value: boolean): MCPToolDefineArrayProperty;
    defItems(value: MCPToolDefineProperty): MCPToolDefineArrayProperty;
    defEnum(values: any[]): MCPToolDefineArrayProperty;
    defMaxItems(value: Int): MCPToolDefineArrayProperty;
    defMinItems(value: Int): MCPToolDefineArrayProperty;
}
export declare function transformToZodSchema(Zod: any, value?: MCPToolDefineProperty, isRoot?: boolean): Record<string, any>;
