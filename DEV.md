# NuroSDK

## 工程说明

这是一个多端工程，基于 TSN 构建，源码在 `src` 目录下。

- kotlin 目录是 android 代码。
- js 目录是 web 和 nodejs 代码。
- swift 目录是 ios 代码。

## 编译方法

确保你已安装 NodeJS 18+ 和 Java 环境。

```shell
cd src
npm install
npm run build
```

## 发布方法

直接在 Bits 平台上升级组件，然后依赖新的版本即可。

- ios https://bits.bytedance.net/app_center/detail/client_component/NuroSDK_50585?tab=overview
- android https://bits.bytedance.net/app_center/detail/client_component/com.bytedance_nurosdk_50589?tab=overview
- web https://bits.bytedance.net/app_center/detail/client_component/NuroSDKJS_50710?tab=version

## 测试方法

在 test 目录下，有四个子工程。

### agent_host

使用 langchain.js 开发的 host agent，在 BOE 环境中有部署对应的测试服务，你不需要本地运行该子工程。

BOE 环境服务地址 -> http://************:8888/sse

### agent_client

NodeJS 开发的测试客户端，用于连接到 Host Agent 测试消息流和工具调用是否正常。

你需要修改 `test/agent_client/src/test_plain_text.ts` 中的服务地址为上面的 BOE 环境服务地址，才能正常运行。

```shell
# 运行方法
cd test/agent_client
npm install
npm start
```

你可以修改 `test/agent_client/src/*.ts` 文件，尝试跑一些自己的用例。

### agent_client_android & agent_client_ios

同理，这两个子工程是 Android 和 iOS 的测试客户端，直接导入 Android Studio 和 Xcode 即可运行。

你需要修改工程代码中的 sse 服务地址为上面的 BOE 环境服务地址，才能正常运行。

目前三端工程均完整跑通了 MCP 工具调用流程。

## 类、方法说明

### IDL

在 `src/idl` 目录下，有三个 IDL 文件，它们是与 Host Agent 交互的原始 IDL， 文档 https://bytedance.larkoffice.com/wiki/MiDawLLWOizlzrkFyXwceyFcnqL

### Transport

在 `src/transport` 目录下，有三个文件，它们是与 Host Agent 交互的封装类，目前仅支持 SSE，已预留其它协议的支持。

### Nuro

在 `src/nuro` 目录下，这是面向上层业务的所有封装类。

#### NuroConversationManager

这是一个会话管理器，它负责管理所有的会话，包括创建、销毁、获取、更新等。

所有的消息请求，都应先创建一个 `NuroConversationManager` 实例开始。

```ts
export declare class NuroConversationManager {
    readonly conversation: NuroConversation;
    /**
     * Client Side MCP
     */
    mcpManager?: NuroMCPManager;
    /**
     * Client Side MCP Tool Call Interceptor
     * 如果返回 true 则代表本次 Tool Call 由上层业务处理，否则由 SDK 调用处理。
     */
    mcpToolCallInterceptor?: Optional<(toolCall: NuroToolCallMessage) => boolean>;
    constructor();
    connect(transport: CommonTransport): void;
    enableMCPTools(): void;
    sendUserMessage(message: NuroUserMessage): void;
}
```

#### NuroConversation

这是一个会话类，它负责持有消息实体，记录会话状态，它提供消息、状态更新的回调。

```ts
export declare class NuroConversation {
    addStateUpdateListener(listener: (state: NuroConverstaionState) => void): void;
    removeStateUpdateListener(listener: (state: NuroConverstaionState) => void): void;
    addMessageUpdateListener(listener: (message: NuroMessage) => void): void;
    removeMessageUpdateListener(listener: (message: NuroMessage) => void): void;
}
```

#### NuroMCP

这是一个 MCP 工具调用类，它负责管理 MCP 工具调用的状态，包括创建、销毁、获取、更新等。

核心是 `NuroMCPManager`，如果你需要注册客户端 MCP Tool，应创建一个 `NuroMCPManager` 实例，然后通过 `registerServer` 注册。

```ts
export declare class NuroMCPManager {
    registerServer(config: NuroMCPServerConfig): void;
}
```

#### NuroMessage

这是一个消息基类，有以下子类：

- NuroUserMessage
- NuroAssistantMessage
- NuroToolCallMessage

NuroConversation.messages 中持有当前会话的所有消息实体，上层业务可以通过这个数组获取消息，然后使用 UI 按需渲染。