import ObjectMapper
//93728d7c8a38627c714810587fae1f56
// This file is generated by tsn.
import TSNFoundation

public enum SystemDataType: String {
    case title_generation = "title_generation"
    case stream_complete = "stream_complete"
    case heartbeat = "heartbeat"
    case summary = "summary"
}

extension SystemDataType {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class SystemData: TSNSerializable {

    public var type: SystemDataType? = nil

    public var conversation_id: String? = nil

    public var title: String? = nil

    public var content: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        type <- map["type"]
        conversation_id <- map["conversation_id"]
        title <- map["title"]
        content <- map["content"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == SystemData.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
