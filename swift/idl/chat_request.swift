import ObjectMapper
//8195c101be221d20856823ce6e6373e4
// This file is generated by tsn.
import TSNFoundation

public class ChatRequest: TSNSerializable {

    /**
       * 填当前 Conversation 唯一的 UUID，如果是新的 Conversation 则为空
       */
    public var conversationId: String? = nil

    /**
       * 填上一个消息的 UUID
       */
    public var parentMessageId: String? = nil

    /**
       * 消息列表
       */
    public var messages: [ChatMessage]? = nil

    /**
       * system_prompt,eg:{\"mode\":\"潮玩二创\"}
       */
    public var systemPrompt: String? = nil

    /**
       * 版本
       */
    public var version: String? = NuroSetting.version

    override public func mapping(map: Map) {
        super.mapping(map: map)
        conversationId <- map["conversation_id"]
        parentMessageId <- map["parent_message_id"]
        messages <- map["messages"]
        systemPrompt <- map["system_prompt"]
        version <- map["version"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatRequest.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
