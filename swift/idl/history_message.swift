import ObjectMapper
//3519827431cafabd782450dc6e0785e9
// This file is generated by tsn.
import TSNFoundation

public class HistoryConversation: TSNSerializable {

    public var title: String? = nil

    public var create_time: Int = 0

    public var update_time: Int = 0

    public var conversation_id: String = ""

    public var conversationMapping: [String: ChatMessage]? = nil

    public var summary: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        title <- map["title"]
        create_time <- map["create_time"]
        update_time <- map["update_time"]
        conversation_id <- map["conversation_id"]
        conversationMapping <- map["mapping"]
        summary <- map["summary"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == HistoryConversation.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class HistoryMessages: TSNSerializable {

    public var conversation: HistoryConversation? = nil

    public var total: Int? = nil

    public var hasMore: Bool? = nil

    public func getChatMessages() -> [ChatMessage] {
        var result: [ChatMessage] = []
        var _mapping = self.conversation?.conversationMapping
        if let __mapping = _mapping {
            var toolCallRoleMessages: [ChatMessage] = []
            TSNMapUtils.forEach(
                __mapping,
                { (id, message) in
                    var role = message.author?.role
                    if let _role = role, _role == "tool" {
                        toolCallRoleMessages.push(message)
                        return
                    }
                    result.push(message)
                })
            toolCallRoleMessages.forEach({ (message) in
                result.push(message)
            })
        } else {
            return []
        }
        return result
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        conversation <- map["conversation"]
        total <- map["total"]
        hasMore <- map["has_more"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == HistoryMessages.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
