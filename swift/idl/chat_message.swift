import ObjectMapper
//43ae80c4a1faffdd7343b1b753e7ae15
// This file is generated by tsn.
import TSNFoundation

public class ChatPatchable: TSNSerializable {

    public func applyPatch(_ delta: SSEDeltaMessage, _ pathComponents: [String]?) {
        var _pathComponents =
            pathComponents
            ?? ({ () in
                let deltaPath = delta.path
                guard let deltaPath = deltaPath else {
                    var v: [String] = []
                    return v
                }
                var p = deltaPath.split("/")
                if p.tsn_safeGet(0) == "" {
                    p.shift()
                }
                if p.tsn_safeGet(0) == "message" {
                    p.shift()
                }
                return p
            })()
        let currentPath = _pathComponents.shift()
        if let _currentPath = currentPath {
            self.applyPatchPath(_currentPath, delta, _pathComponents)
        }
    }

    public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {

    }

    public func applyToArrayAny<T>(
        _ delta: SSEDeltaMessage, _ pathComponents: [String], _ newValueBuilder: @escaping (String?) -> T,
        _ originValue: [T]?
    ) -> [T] {
        var _pathComponents = pathComponents
        let indexPath = _pathComponents.shift()
        guard let indexPath = indexPath else {
            return []
        }
        let index = parseInt(indexPath)
        var parts = originValue ?? []
        if parts.tsn_safeGet(index) == nil {
            for j in parts.length..<index + 1 {
                if parts.tsn_safeGet(j) == nil {
                    parts.push(newValueBuilder(nil))
                }
            }
        }
        if (delta.op == "replace" || delta.op == "add") && _pathComponents.length == 0 {
            parts[index] = newValueBuilder(delta.value)
        }
        let newValue = parts.tsn_safeGet(index)
        if let newValue = newValue as? ChatPatchable {
            newValue.applyPatch(delta, _pathComponents)
        }
        return parts
    }

    public func applyToArrayString(_ delta: SSEDeltaMessage, _ pathComponents: [String], _ originValue: [String]?)
        -> [String]
    {
        var _pathComponents = pathComponents
        let indexPath = _pathComponents.shift()
        guard let indexPath = indexPath else {
            return []
        }
        let index = parseInt(indexPath)
        var parts = originValue ?? []
        if parts.tsn_safeGet(index) == nil {
            for j in parts.length..<index + 1 {
                if parts.tsn_safeGet(j) == nil {
                    parts.push("")
                }
            }
        }
        if (delta.op == "replace" || delta.op == "add") && _pathComponents.length == 0 {
            if let _delta_value = delta.value {
                parts[index] = _delta_value
            }
        } else if delta.op == "append" {
            parts[index] = (parts.tsn_safeGet(index) ?? "") + (delta.value ?? "")
        }
        return parts
    }

    public func applyToString(_ delta: SSEDeltaMessage, _ originValue: String?) -> String {
        if delta.op == "replace" {
            if let _delta_value = delta.value {
                return _delta_value
            }
        } else if delta.op == "append" {
            return (originValue ?? "") + (delta.value ?? "")
        }
        return originValue ?? ""
    }

}

public enum ChatMessageStatus: String {
    /**
       * 消息状态, finished_successfully
       */
    case finished_successfully = "finished_successfully"
    /**
       * 消息状态,  in_progress
       */
    case in_progress = "in_progress"
    /**
       * 推流前异常错误
       * 推流失败，比如第一条事件就推送失败了
       */
    case interrupt_status = "interrupt_status"
    /**
       * 推流前异常错误
       * pe策略失败
       */
    case pe_policy_failed_status = "pe_policy_failed_status"
    /**
       * 推流前异常错误
       * 获取流失败
       */
    case chat_stream_failed_status = "chat_stream_failed_status"
    /**
       * 安全审核拦截
       * 输入文本审核拦截
       */
    case input_text_block_status = "input_text_block_status"
    /**
       * 安全审核拦截
       * 输出文本审核拦截
       */
    case output_text_block_status = "output_text_block_status"
    /**
       * 推流异常状态
       * 推送思考内容截止
       */
    case send_reasoning_content_stop_status = "send_reasoning_content_stop_status"
    /**
       * 推流异常状态
       * 推送内容截止
       */
    case send_content_stop_status = "send_content_stop_status"
}

extension ChatMessageStatus {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public enum ChatContentType: String {
    case text = "text"
    case image_url = "image_url"
}

extension ChatContentType {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public enum ChatMessageFileType: Int {
    /**
       * txt
       */
    case TXT = 1
    /**
       * pdf
       */
    case PDF = 2
    /**
       * doc
       */
    case DOC = 3
    /**
       * docx
       */
    case DOCX = 4
    /**
       * image
       */
    case IMAGE = 5
    /**
       * video
       */
    case VIDEO = 6
    /**
       * audio
       */
    case AUDIO = 7
}

extension ChatMessageFileType {
    public func valueOf() -> Int {
        return self.rawValue
    }
}

public class ChatMessageImageMetadata: ChatPatchable {

    public var image_width: Int? = nil

    public var image_height: Int? = nil

    public var image_format: String? = nil

    public var image_prompt: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        image_width <- map["image_width"]
        image_height <- map["image_height"]
        image_format <- map["image_format"]
        image_prompt <- map["image_prompt"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatMessageImageMetadata.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatMessageFileURI: ChatPatchable {

    public var uri: String? = nil

    public var file_type: ChatMessageFileType? = nil

    public var file_name: String? = nil

    public var url: String? = nil

    public var image_metadata: ChatMessageImageMetadata? = nil

    public var extra: [String: Any]? = nil

    public var file_description: String? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "uri" {
            self.uri = self.applyToString(delta, self.uri)
        } else if name == "file_type" {
            if delta.op == "replace" {
                if let _delta_value = delta.value {
                    self.file_type = ChatMessage.convertValueToChatMessageFileType(parseInt(_delta_value))
                }
            }
        } else if name == "file_name" {
            self.file_name = self.applyToString(delta, self.file_name)
        } else if name == "url" {
            self.url = self.applyToString(delta, self.url)
        } else if name == "image_metadata" {
            if self.image_metadata == nil {
                self.image_metadata = ChatMessageImageMetadata()
            }
            self.image_metadata?.applyPatch(delta, pathComponents)
        } else if name == "file_description" {
            self.file_description = self.applyToString(delta, self.file_description)
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        uri <- map["uri"]
        file_type <- map["file_type"]
        file_name <- map["file_name"]
        url <- map["url"]
        image_metadata <- map["image_metadata"]
        extra <- map["extra"]
        file_description <- map["file_description"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatMessageFileURI.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatContentPart: ChatPatchable {

    public var text: String? = nil

    public var file: ChatMessageFileURI? = nil

    public var is_referenced: Bool? = nil

    public var reasoning_content: String? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "text" {
            self.text = self.applyToString(delta, self.text)
        } else if name == "reasoning_content" {
            self.reasoning_content = self.applyToString(delta, self.reasoning_content)
        } else if name == "file" {
            if self.file == nil {
                self.file = ChatMessageFileURI(JSONString: delta.value ?? "{}")
            }
            self.file?.applyPatch(delta, pathComponents)
        } else if name == "is_referenced" {
            if delta.value == "true" {
                self.is_referenced = true
            }
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        text <- map["text"]
        file <- map["file"]
        is_referenced <- map["is_referenced"]
        reasoning_content <- map["reasoning_content"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatContentPart.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatContent: ChatPatchable {

    /**
       * 内容类型：text、image_url
       */
    public var content_type: ChatContentType? = nil

    /**
       * 消息内容
       */
    public var content_parts: [ChatContentPart]? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "content_parts" {
            self.content_parts = self.applyToArrayAny(
                delta, pathComponents,
                { (value) in
                    if let _value = value, _value.indexOf("{") == 0 {
                        return ChatContentPart(JSONString: _value ?? "{}")
                    } else {
                        return ChatContentPart()
                    }
                }, self.content_parts)
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        content_type <- map["content_type"]
        content_parts <- map["content_parts"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatContent.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public enum ChatToolCallType: String {
    case client_function = "client_function"
    case server_function = "server_function"
}

extension ChatToolCallType {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class ChatToolCallFunc: ChatPatchable {

    /**
       * 函数名称
       */
    public var name: String? = nil

    /**
       * 函数调用参数
       */
    public var arguments: String? = nil

    /**
       * 内部使用，上一次成功修复的 JSON 参数，流式传输使用。
       */
    public var _lastRepairedArguments: String? = "{}"

    /**
       * 其他附带的内容
       */
    public var extra: [String: String]? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "arguments" {
            self.arguments = self.applyToString(delta, self.arguments)
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        name <- map["name"]
        arguments <- map["arguments"]
        extra <- map["extra"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatToolCallFunc.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

/// 工具调用定义
public class ChatToolCall: ChatPatchable {

    /**
       * 工具调用ID
       */
    public var id: String? = nil

    /**
       * 类型：client_function、server_function
       */
    public var type: ChatToolCallType? = nil

    /**
       * 是否正在流式返回工具参数
       */
    public var streaming: Bool? = nil

    /**
       * func
       */
    public var _func: ChatToolCallFunc? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "func" {
            if self._func == nil {
                self._func = ChatToolCallFunc()
            }
            self._func?.applyPatch(delta, pathComponents)
        } else if name == "streaming" {
            if delta.op == "replace" {
                if let _delta_value = delta.value {
                    self.streaming = _delta_value == "true"
                }
            }
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        id <- map["id"]
        type <- map["type"]
        streaming <- map["streaming"]
        _func <- map["func"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatToolCall.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public enum ChatToolType: String {
    case client_function = "client_function"
    case server_function = "server_function"
}

extension ChatToolType {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class ChatTool: ChatPatchable {

    /**
       * 工具ID
       */
    public var id: String? = nil

    /**
       * 工具类型
       */
    public var type: ChatToolType? = nil

    /**
       * 工具名称
       */
    public var name: String? = nil

    /**
       * 工具描述
       */
    public var description: String? = nil

    /**
       * 工具参数（JSONSchema）
       */
    public var parameters: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        id <- map["id"]
        type <- map["type"]
        name <- map["name"]
        description <- map["description"]
        parameters <- map["parameters"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatTool.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatAuthor: ChatPatchable {

    /**
       * 角色
       */
    public var role: String? = nil

    /**
       * 名称
       */
    public var name: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        role <- map["role"]
        name <- map["name"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatAuthor.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class FinishDetails: ChatPatchable {

    /**
       * 类型,eg: stop, interrupted
       */
    public var type: String = ""

    public func isEqualToObject(_ obj: Any) -> Bool {
        if let obj = obj as? FinishDetails {
            return obj.type == self.type
        }
        return false
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        type <- map["type"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == FinishDetails.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatMessageMetadata: ChatPatchable {

    /**
       * 是否在会话中隐藏，有些消息不需要展示
       */
    public var is_visually_hidden_from_conversation: Bool = false

    /**
       * 对话ID
       */
    public var conversation_id: String = ""

    /**
       * 父消息ID
       */
    public var parent_message_id: String = ""

    /**
       * 工具调用ID
       */
    public var tool_call_id: String? = nil

    /**
       * 完成详情(FinishDetails结构见公共结构)
       */
    public var finish_details: FinishDetails? = nil

    /**
       * 用户GUI操作
       */
    public var action: String? = nil

    /**
       * 回复类型：follow_up、answer
       * 1. follow_up: 表示推荐问
       * 2. answer表示：模型正常回答
       */
    public var message_type: String? = nil

    /**
       * 模型想起
       */
    public var model_detail: String? = nil

    /**
       * payload
       */
    public var payload: String? = nil

    /**
       * 是否对话已经完结，不是的话需要 续连
       */
    public var end_turn: Bool? = nil

    /**
       * 埋点透传字段
       */
    public var metricsExtra: String? = nil

    public func isEqualToObject(_ obj: Any) -> Bool {
        if let obj = obj as? ChatMessageMetadata {
            var finish_details_is_equal = true
            if let _obj_finish_details = obj.finish_details, let _this_finish_details = self.finish_details {
                finish_details_is_equal = _obj_finish_details.isEqualToObject(_this_finish_details)
            } else if let _obj_finish_details = obj.finish_details {
                finish_details_is_equal = false
            } else if let _this_finish_details = self.finish_details {
                finish_details_is_equal = false
            }
            return
                (obj.conversation_id == self.conversation_id && obj.parent_message_id == self.parent_message_id
                && obj.tool_call_id == self.tool_call_id
                && obj.is_visually_hidden_from_conversation == self.is_visually_hidden_from_conversation
                && obj.action == self.action && obj.message_type == self.message_type
                && obj.model_detail == self.model_detail && obj.payload == self.payload && obj.end_turn == self.end_turn
                && finish_details_is_equal == true)
        }
        return false
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        is_visually_hidden_from_conversation <- map["is_visually_hidden_from_conversation"]
        conversation_id <- map["conversation_id"]
        parent_message_id <- map["parent_message_id"]
        tool_call_id <- map["tool_call_id"]
        finish_details <- map["finish_details"]
        action <- map["action"]
        message_type <- map["message_type"]
        model_detail <- map["model_detail"]
        payload <- map["payload"]
        end_turn <- map["end_turn"]
        metricsExtra <- map["metrics_extra"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatMessageMetadata.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class ChatMessage: ChatPatchable {

    /**
       * 消息作者
       */
    public var author: ChatAuthor? = nil

    /**
       * 消息元数据
       */
    public var metadata: ChatMessageMetadata? = nil

    /**
       * 消息状态, finished_successfully, in_progress
       */
    public var status: ChatMessageStatus? = nil

    /**
       * 消息ID, UUID
       */
    public var id: String? = nil

    /**
       * 消息内容, 展示给用户查看
       */
    public var content: ChatContent? = nil

    /**
       * 更新时间，unix毫秒时间戳
       */
    public var update_time: Int? = nil

    /**
       * 创建时间, unix毫秒时间戳
       */
    public var create_time: Int64? = 0

    /**
       * 是否结束本轮对话，一轮对话可能由多个message构成
       */
    public var end_turn: Bool? = nil

    /**
       * 下发到端上需要执行的操作，端上被视为Agent的工具，可以被进行工具调用
       */
    public var tool_calls: [ChatToolCall]? = nil

    public var tools: [ChatTool]? = nil

    override public func applyPatchPath(_ name: String, _ delta: SSEDeltaMessage, _ pathComponents: [String]) {
        if name == "content" {
            if self.content == nil {
                self.content = ChatContent()
            }
            self.content?.applyPatch(delta, pathComponents)
        } else if name == "status" {
            if delta.op == "replace" {
                if let _delta_value = delta.value {
                    self.status = ChatMessage.convertValueToChatMessageStatus(_delta_value)
                }
            }
        } else if name == "tool_calls" {
            self.tool_calls = self.applyToArrayAny(
                delta, pathComponents,
                { (value) in
                    if let _value = value, _value.indexOf("{") == 0 {
                        return ChatToolCall(JSONString: _value)
                    }
                    return ChatToolCall()
                }, self.tool_calls)
        } else if name == "end_turn" {
            if delta.op == "replace" {
                if delta.value == "true" {
                    self.end_turn = true
                } else {
                    self.end_turn = false
                }
            }
        }
    }

    static public func convertValueToChatMessageStatus(_ value: String) -> ChatMessageStatus {
        switch value {
        case "finished_successfully":
            return ChatMessageStatus.finished_successfully
        case "in_progress":
            return ChatMessageStatus.in_progress
        case "interrupt_status":
            return ChatMessageStatus.interrupt_status
        case "pe_policy_failed_status":
            return ChatMessageStatus.pe_policy_failed_status
        case "chat_stream_failed_status":
            return ChatMessageStatus.chat_stream_failed_status
        case "input_text_block_status":
            return ChatMessageStatus.input_text_block_status
        case "output_text_block_status":
            return ChatMessageStatus.output_text_block_status
        case "send_reasoning_content_stop_status":
            return ChatMessageStatus.send_reasoning_content_stop_status
        case "send_content_stop_status":
            return ChatMessageStatus.send_content_stop_status
        default:
            return ChatMessageStatus.finished_successfully
        }
    }

    static public func convertValueToChatMessageFileType(_ value: Int) -> ChatMessageFileType {
        switch value {
        case 1:
            return ChatMessageFileType.TXT
        case 2:
            return ChatMessageFileType.PDF
        case 3:
            return ChatMessageFileType.DOC
        case 4:
            return ChatMessageFileType.DOCX
        case 5:
            return ChatMessageFileType.IMAGE
        case 6:
            return ChatMessageFileType.VIDEO
        case 7:
            return ChatMessageFileType.AUDIO
        default:
            return ChatMessageFileType.IMAGE
        }
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        author <- map["author"]
        metadata <- map["metadata"]
        status <- map["status"]
        id <- map["id"]
        content <- map["content"]
        update_time <- map["update_time"]
        create_time <- (
            map["create_time"], TransformOf<Int64, Any>(fromJSON: { self.parseInt64(value: $0) }, toJSON: { $0 })
        )
        end_turn <- map["end_turn"]
        tool_calls <- map["tool_calls"]
        tools <- map["tools"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ChatMessage.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
