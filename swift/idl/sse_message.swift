import ObjectMapper
//f943e4901641107add691c5af33ca850
// This file is generated by tsn.
import TSNFoundation

public class SSEDeltaMessage: TSNSerializable {

    public var op: String? = nil

    public var path: String? = nil

    public var value: String? = nil

    public var original_value: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        op <- map["op"]
        path <- map["path"]
        value <- map["value"]
        original_value <- map["original_value"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == SSEDeltaMessage.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class SSEFinalMessage: TSNSerializable {

    public var ret: String? = nil

    public var errmsg: String? = nil

    public var systime: String? = nil

    public var logid: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        ret <- map["ret"]
        errmsg <- map["errmsg"]
        systime <- map["systime"]
        logid <- map["logid"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == SSEFinalMessage.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
