//
//  mcpserver.swift
//  agent_client_ios
//
//  Created by ByteDance on 2025/4/10.
//

import Foundation

/**
 * The lite mcp server, totally not conforms MCP Protocol.
 * Because the offical MCP Swift-SDK can not define tool, and it can not run over iOS 12.
 */
public class MCPLiteServer: NuroMCPClientAdapter {
    
    public let name: String
    public let version: String
    private(set) var tools: [String: MCPLiteTool] = [:]
    
    public init(name: String, version: String) {
        self.name = name
        self.version = version
    }
    
    /**
     * Define a tool
     */
    public func tool(name: String,
                     description: String,
                     inputSchema: MCPToolDefineProperty?,
              toolCallHandler: @escaping MCPLiteTool.ToolCallHandler) {
        self.tools[name] = MCPLiteTool(name: name,
                                       description: description,
                                       inputSchema: inputSchema,
                                       toolCallHandler: toolCallHandler)
    }
    
    public func listTools(_ callback: @escaping ([NuroMCPToolItem]) -> Void) {
        callback(self.tools.values.map({ it in
            return NuroMCPToolItem(self.name,
                                   it.name,
                                   it.description,
                                   it.inputSchema?.toJSONString() ?? "{}")
        }))
    }
    
    public func callTool(_ toolCallId: String, _ toolName: String, _ toolArgs: String?, _ callback: @escaping (String) -> Void) {
        if let tool = self.tools[toolName] {
            var params: Any?
            do {
                if let data = toolArgs?.data(using: .utf8) {
                    params = try JSONSerialization.jsonObject(with: data)
                }
            } catch {}
            tool.toolCallHandler(params, { result in
                if let str = result.toJSONString() {
                    callback(str)
                } else {
                    callback("{}")
                }
            })
        }
    }
}

public struct MCPLiteTool {
    
    public typealias ToolCallHandler = (_ params: Any?,
                                        _ resultCallback: @escaping (MCPToolCallResult) -> Void) -> Void
    
    public let name: String
    public let description: String
    public let inputSchema: MCPToolDefineProperty?
    public let toolCallHandler: ToolCallHandler
    
    public init(name: String, description: String, inputSchema: MCPToolDefineProperty?, toolCallHandler: @escaping ToolCallHandler) {
        self.name = name
        self.description = description
        self.inputSchema = inputSchema
        self.toolCallHandler = toolCallHandler
    }
}
