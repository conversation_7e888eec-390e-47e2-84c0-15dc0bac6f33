//8910dbacf297c50c207986962574ceff
// This file is generated by tsn.
import TSNFoundation

public class TOSFileUploadConfig {

    public var nuroFile: NuroFile

    public var localFile: NuroLocalFile

    public var onFinish: (() -> Void)?

    public var onError: ((Int, String?) -> Void)?

    public var onCancel: ((Int, String?) -> Void)?

    public init(_ nuroFile: NuroFile, _ localFile: NuroLocalFile) {
        self.nuroFile = nuroFile
        self.localFile = localFile
    }

}

public class TOSFileUploadAdapter {

    static public var upload: ((TOSFileUploadConfig) -> String)?

    static public var cancel: ((String) -> Void)?

    public init() {}

}

public class TOSFileUploadImpl {

    static public func upload(_ config: TOSFileUploadConfig) -> String? {
        let uploader = TOSFileUploadAdapter.upload
        if let _uploader = uploader {
            return _uploader(config)
        }
        return nil
    }

    static public func cancel(_ cancelToken: String) {
        let canceler = TOSFileUploadAdapter.cancel
        if let _canceler = canceler {
            _canceler(cancelToken)
        }
    }

    public init() {}

}
