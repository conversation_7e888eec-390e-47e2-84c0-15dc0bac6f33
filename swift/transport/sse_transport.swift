//c352a518111cb9c009b6f606103dd1db
// This file is generated by tsn.
import TSNFoundation

open class SSETransport: CommonTransport {

    private weak var _conversationManager: NuroConversationManager?

    private var endpoint: String

    private var headers: [String: String] = [:]

    private var currentChunked: String = ""

    private var currentChatMessage: ChatMessage?

    private var messageDataType: String = ""  // reasoning, text, tool.

    public init(_ endpoint: String, _ headers: [String: String]?) {
        self.endpoint = endpoint
        super.init()
        if let _headers = headers {
            self.headers = _headers
        }
    }

    override open func setConversationManager(_ conversationManager: NuroConversationManager) {
        self._conversationManager = conversationManager
    }

    override open func sendMessage(_ message: NuroMessage, _ tools: [NuroMCPToolItem]) {
        self.sendMessages([message], tools)
    }

    override open func resumeMessage() {
        var headers: [String: String] = ["Content-Type": "application/json"]
        for __item__ in self.headers {
            let key = __item__.key
            headers[key] = self.headers.tsn_safeGet(key) ?? ""
        }
        var resumeData: ResumeData = ResumeData()
        resumeData.conversationId = self._conversationManager?.conversation.conversationId ?? ""
        var length = self._conversationManager?.conversation.messages.length ?? 0
        if length > 0 {
            var message = self._conversationManager?.conversation.messages.tsn_safeGet(length - 1)
            var rawId = ""
            if let _message = message {
                rawId = _message.getResumeMsgId()
            }
            resumeData.messageId = rawId
        }
        self.currentChunked = ""
        self.sendSSERequest(
            EventStreamAdapter.reconnectEndpoint, resumeData.toJSONString(), headers,
            { () in

            },
            { () in
                MessageProcessor.markInProgressMessagesAsFailed(self._conversationManager)
                self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
            },
            { () in
                MessageProcessor.markInProgressMessagesAsCancel(self._conversationManager)
                self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
            })
    }

    override open func sendMessages(_ messages: [NuroMessage], _ tools: [NuroMCPToolItem]) {
        if messages.length == 0 {
            self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
            return
        }
        self._conversationManager?.conversation.messages.forEach({ (message) in
            if message.isFinalStatus() == false {
                if let message = message as? NuroToolCallMessage {
                    message.setMsgStatus(NuroToolCallMessageStatus.skipped)
                }
            }
        })
        for index in 0..<messages.length {
            let msg = messages[index]
            if let msg = msg as? NuroUserMessage {
                msg.setMsgStatus(NuroUserMessageStatus.sending)
            }
        }
        let chatRequest = ChatRequest()
        chatRequest.conversationId = self._conversationManager?.conversation.conversationId
        chatRequest.parentMessageId =
            ({ () in
                var messages = self._conversationManager?.conversation.messages
                if let _messages = messages, _messages.length > 0 {
                    do {
                        var index = _messages.length - 1
                        var __first__ = true
                        while index >= 0 {
                            if !__first__ {
                                index = index - 1
                            }
                            __first__ = false
                            if index >= 0 {
                                let msg = _messages.tsn_safeGet(index)
                                if let _msg = msg {
                                    if _msg is NuroAssistantMessage || _msg is NuroToolCallMessage {
                                        return _msg._rawId
                                    }
                                }
                            } else {
                                break
                            }
                        }
                    }
                }
                return nil
            })()
        if chatRequest.parentMessageId == nil {
            chatRequest.systemPrompt = self._conversationManager?.conversation.systemPrompt
        }
        chatRequest.messages = []
        var previousMessageId: String? = nil
        for index in 0..<messages.length {
            let message = messages[index]
            let chatMessage = ChatMessage()
            chatMessage.id = message.id
            let chatAuthor = ChatAuthor()
            chatAuthor.role = message is NuroToolCallMessage ? "tool" : "user"
            chatMessage.author = chatAuthor
            let content = ChatContent()
            let metadata = ChatMessageMetadata()
            if let message = message as? NuroUserMessage {
                var contentParts: [ChatContentPart] = []
                if let _message_text = message.text, _message_text.length > 0 {
                    var contentPart: ChatContentPart = ChatContentPart()
                    contentPart.text = _message_text
                    contentParts.push(contentPart)
                }
                if let _message_files = message.files, _message_files.length > 0 {
                    _message_files.forEach({ (it) in
                        var contentPart: ChatContentPart = ChatContentPart()
                        var file = ChatMessageFileURI()
                        file.file_type =
                            it.type == NuroFileType.image ? ChatMessageFileType.IMAGE : ChatMessageFileType.VIDEO
                        file.url = it.url
                        file.uri = it.uri
                        file.extra = it.extra
                        file.file_description = it.file_description
                        var nuroImageMetadata = it.metadata?.image_metadata ?? nil
                        if let _nuroImageMetadata = nuroImageMetadata, ChatMessageFileType.IMAGE {
                            var imageMetadata = ChatMessageImageMetadata()
                            imageMetadata.image_width = _nuroImageMetadata.width
                            imageMetadata.image_height = _nuroImageMetadata.height
                            imageMetadata.image_prompt = _nuroImageMetadata.prompt
                            imageMetadata.image_format = _nuroImageMetadata.format
                            file.image_metadata = imageMetadata
                        }
                        contentPart.file = file
                        contentParts.push(contentPart)
                    })
                }
                metadata.metricsExtra = message.metadata.metricsExtra
                if let _message_referenceInfo = message.referenceInfo, _message_referenceInfo.length > 0 {
                    _message_referenceInfo.forEach({ (it) in
                        var contentPart: ChatContentPart = ChatContentPart()
                        if let _it_text = it.text, _it_text.length > 0 {
                            contentPart.text = _it_text
                        }
                        if let _it_file = it.file {
                            var file = ChatMessageFileURI()
                            file.file_type =
                                _it_file.type == NuroFileType.image
                                ? ChatMessageFileType.IMAGE : ChatMessageFileType.VIDEO
                            file.url = _it_file.url
                            file.uri = _it_file.uri
                            file.extra = _it_file.extra
                            file.file_description = _it_file.file_description
                            var nuroImageMetadata = _it_file.metadata?.image_metadata ?? nil
                            if let _nuroImageMetadata = nuroImageMetadata, ChatMessageFileType.IMAGE {
                                var imageMetadata = ChatMessageImageMetadata()
                                imageMetadata.image_width = _nuroImageMetadata.width
                                imageMetadata.image_height = _nuroImageMetadata.height
                                imageMetadata.image_prompt = _nuroImageMetadata.prompt
                                imageMetadata.image_format = _nuroImageMetadata.format
                                file.image_metadata = imageMetadata
                            }
                            contentPart.file = file
                        }
                        contentPart.is_referenced = true
                        contentParts.push(contentPart)
                    })
                }
                content.content_parts = contentParts
            } else if let message = message as? NuroToolCallMessage {
                var tollmsg: NuroToolCallMessage = message as! NuroToolCallMessage
                var contentPart: ChatContentPart = ChatContentPart()
                contentPart.text = tollmsg.toolResult ?? ""
                content.content_parts = [contentPart]
                metadata.tool_call_id = tollmsg.toolCallId
            }
            chatMessage.content = content
            chatMessage.tools = tools.map({ (it) in
                let toolItem = ChatTool()
                toolItem.id = it.name
                toolItem.type = ChatToolType.client_function
                toolItem.name = it.serverName + "_" + it.name
                toolItem.description = it.description
                toolItem.parameters = it.inputSchema
                return toolItem
            })
            metadata.conversation_id = chatRequest.conversationId ?? ""
            metadata.parent_message_id = previousMessageId ?? chatRequest.parentMessageId ?? ""
            chatMessage.metadata = metadata
            chatMessage.create_time = message.createTime
            chatRequest.messages?.push(chatMessage)
            previousMessageId = message.id
        }
        var headers: [String: String] = ["Content-Type": "application/json"]
        for __item__ in self.headers {
            let key = __item__.key
            headers[key] = self.headers.tsn_safeGet(key) ?? ""
        }
        self.currentChunked = ""
        self.sendSSERequest(
            self.endpoint, chatRequest.toJSONString(), headers,
            { () in

            },
            { () in
                MessageProcessor.markInProgressMessagesAsFailed(self._conversationManager)
                self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
            },
            { () in
                MessageProcessor.markInProgressMessagesAsCancel(self._conversationManager)
                self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
            })
    }

    public func sendSSERequest(
        _ endpoint: String?, _ chatRequest: String?, _ headers: [String: String], _ onChunkStart: @escaping () -> Void,
        _ onMessageFailed: @escaping () -> Void, _ onConversationCancel: @escaping () -> Void
    ) {
        if NuroMockManager.isMocking() {
            return
        }
        guard let endpoint = endpoint else {
            return
        }
        let esConfig = EventStreamConfig()
        esConfig.endpoint = endpoint
        esConfig.method = "POST"
        esConfig.headers = headers
        esConfig.data = chatRequest ?? ""
        var chunkedStarted = false
        esConfig.onChunk = { (text: String) in
            NuroLogger.debug(
                "SSETransport",
                { () in
                    "received chunk, \(text)"
                })
            if chunkedStarted == false {
                chunkedStarted = true
                onChunkStart()
            }
            self.currentChunked = self.currentChunked + text
            self.flushChuck(false)
        }
        esConfig.onFinish = { () in
            NuroLogger.debug(
                "SSETransport",
                { () in
                    """
                    received finish
                    """
                })
            self.flushChuck(true)
        }
        esConfig.onError = { (code: Int, message: String?) in
            NuroLogger.debug(
                "SSETransport",
                { () in
                    "received error, code = \(code.toString()), message = \(message ?? "")"
                })
            self.flushChuck(true)
            onMessageFailed()
        }
        esConfig.onCancel = { (code: Int, message: String?) in
            NuroLogger.debug(
                "SSETransport",
                { () in
                    """
                    received cancel
                    """
                })
            self.flushChuck(true)
            onConversationCancel()
        }
        NuroLogger.debug(
            "SSETransport",
            { () in
                "send sse request, \(chatRequest)"
            })
        self.token = SSEImpl.fetch(esConfig)
    }

    public func flushChuck(_ ended: Bool) {
        var originalParts = self.currentChunked.split("\n\n")
        var partsLength = originalParts.length
        var currentIndex = -1
        originalParts.forEach({ (part) in
            currentIndex = currentIndex + 1
            if currentIndex == partsLength - 1 {
                if ended == false {
                    self.currentChunked = part
                    return
                }
            }
            if part.indexOf("logid") > 0 && part.indexOf("ret") > 0 && part.indexOf("{") == 0 {
                var res: SSEFinalMessage = SSEFinalMessage(JSONString: part)
                if let _res_ret = res.ret {
                    if _res_ret == "0" {
                        MessageProcessor.markLastUserMessageAsFinished(self._conversationManager)
                    } else {
                        MessageProcessor.markInProgressMessagesAsFailed(self._conversationManager)
                    }
                    self._conversationManager?.conversation.updateState(NuroConversationState.readyToSendMessage)
                    return
                }
            }
            var id: String = ""
            var event: String = ""
            var data: String = ""
            part.split("\n").forEach({ (line) in
                if line.indexOf("id:") == 0 {
                    id = line.substr(3).trim()
                } else if line.indexOf("event:") == 0 {
                    event = line.substr(6).trim()
                } else if line.indexOf("data:") == 0 {
                    data = line.substr(5).trim()
                }
                if id != "" && event != "" && data != "" {
                    self.onSSEEvent(id, event, data)
                }
            })
        })
        if ended == true {
            self.currentChunked = ""
        }
    }

    public func onSSEEvent(_ id: String, _ event: String, _ data: String) {
        if event == "message" {
            NuroLogger.debug(
                "SSETransport",
                { () in
                    "received message, \(data)"
                })
            let _chatMessage = ChatMessage(JSONString: data)
            let coversationId = _chatMessage.metadata?.conversation_id
            self.currentChatMessage = _chatMessage
            if let _this__conversationManager = self._conversationManager, let _coversationId = coversationId {
                _this__conversationManager.conversation.conversationId = _coversationId
            }
            var contentParts = _chatMessage.content?.content_parts
            if let _contentParts = contentParts, _contentParts.length > 0 {
                MessageProcessor.convertChatMessageToNuroMessage(
                    self._conversationManager, _chatMessage, ConvertType.new_message
                ).forEach({ (it) in
                    MessageProcessor.markLastUserMessageAsFinished(self._conversationManager)
                    self._conversationManager?.receivedMessage(it)
                })
            }
        } else if event == "delta" {
            NuroLogger.debug(
                "SSETransport",
                { () in
                    "received delta, \(data)"
                })
            var delta = SSEDeltaMessage(JSONString: data)
            var deltaPath = delta.path
            if let _deltaPath = deltaPath, _deltaPath.indexOf("/message/content/content_parts/") >= 0 {
                self.messageDataType = "assistant"
            } else if let _deltaPath = deltaPath, _deltaPath.indexOf("/message/tool_calls/") >= 0 {
                MessageProcessor.markMessagesAsFinished(self._conversationManager)
                self.messageDataType = "tool_call"
            }
            if let _this_currentChatMessage = self.currentChatMessage {
                _this_currentChatMessage.applyPatch(delta, nil)
                MessageProcessor.convertChatMessageToNuroMessage(
                    self._conversationManager, _this_currentChatMessage, ConvertType.new_message
                ).forEach({ (it) in
                    MessageProcessor.markLastUserMessageAsFinished(self._conversationManager)
                    self._conversationManager?.receivedMessage(it)
                })
            }
        } else if event == "system" && data.indexOf("stream_error") > 0 {
            NuroLogger.error(
                "SSETransport",
                { () in
                    "received system stream_error, \(data)"
                })
            MessageProcessor.markInProgressMessagesAsFailed(self._conversationManager)
        } else if event == "system" {
            var sysData = SystemData(JSONString: data)
            NuroLogger.debug(
                "SSETransport",
                { () in
                    "received system data, \(data)"
                })
            if sysData.type == SystemDataType.stream_complete {
                MessageProcessor.markMessagesAsFinished(self._conversationManager)
            }
            if let _this__conversationManager = self._conversationManager,
                let _this__conversationManager_conversation = _this__conversationManager.conversation,
                SystemDataType.summary
            {
                _this__conversationManager.conversation.summary = sysData.content
            }
            var listeners = self._conversationManager?.conversation.systemDataListeners
            if let _listeners = listeners {
                TSNMapUtils.forEach(
                    _listeners,
                    { (key, listener) in
                        listener(sysData)
                    })
            }
        }
    }

}
