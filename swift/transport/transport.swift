//c64a7e7dc32f3c308b855356a1245a11
// This file is generated by tsn.
import TSNFoundation

open class CommonTransport {

    public func setConversationManager(_ conversationManager: NuroConversationManager) {
        fatalError("Abstract method MUST BE IMPL BY SUBCLASS!")
    }

    public func sendMessage(_ message: NuroMessage, _ tools: [NuroMCPToolItem]) {
        fatalError("Abstract method MUST BE IMPL BY SUBCLASS!")
    }

    public func sendMessages(_ messages: [NuroMessage], _ tools: [NuroMCPToolItem]) {
        fatalError("Abstract method MUST BE IMPL BY SUBCLASS!")
    }

    public var token: String? = nil

    public func resumeMessage() {
        fatalError("Abstract method MUST BE IMPL BY SUBCLASS!")
    }

    public init() {}

}
