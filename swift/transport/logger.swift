//a9e9fa67a2d6d9c7d7808274744ad2ab
// This file is generated by tsn.
import TSNFoundation

// Define log levels
public enum NuroLogLevel: Int {
    case DEBUG = 0
    case INFO = 1
    case ERROR = 2
    case NONE = 3  // No logs
}

extension NuroLogLevel {
    public func valueOf() -> Int {
        return self.rawValue
    }
}

public class NuroLoggerAdapter {

    static public var debug: ((String, String) -> Void)?

    static public var info: ((String, String) -> Void)?

    static public var error: ((String, String) -> Void)?

    public init() {}

}

public class NuroLogger {

    static private var currentLevel: NuroLogLevel = NuroLogLevel.ERROR  // Default log level

    static public func setLogLevel(_ level: NuroLogLevel) {
        NuroLogger.currentLevel = level
    }

    static public func debug(_ tag: String, _ msg: @escaping () -> String) {
        if NuroLogger.currentLevel.valueOf() <= NuroLogLevel.DEBUG.valueOf() {
            let logger = NuroLoggerAdapter.debug
            if let _logger = logger {
                _logger(tag, "[DEBUG] \(msg())")
            }
        }
    }

    static public func info(_ tag: String, _ msg: @escaping () -> String) {
        if NuroLogger.currentLevel.valueOf() <= NuroLogLevel.INFO.valueOf() {
            let logger = NuroLoggerAdapter.info
            if let _logger = logger {
                _logger(tag, "[INFO] \(msg())")
            }
        }
    }

    static public func error(_ tag: String, _ msg: @escaping () -> String) {
        if NuroLogger.currentLevel.valueOf() <= NuroLogLevel.ERROR.valueOf() {
            let logger = NuroLoggerAdapter.error
            if let _logger = logger {
                _logger(tag, "[ERROR] \(msg())")
            }
        }
    }

    public init() {}

}
