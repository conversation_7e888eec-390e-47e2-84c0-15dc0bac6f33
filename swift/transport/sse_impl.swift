//52c5c69bf035f54fc93c384ccd9f73cc
// This file is generated by tsn.
import TSNFoundation

public class EventStreamConfig {

    public var endpoint: String = ""

    public var method: String = "POST"

    public var headers: [String: String]?

    public var data: String?

    public var onStart: (() -> Void)?

    public var onChunk: ((String) -> Void)?

    public var onError: ((Int, String?) -> Void)?

    public var onFinish: (() -> Void)?

    public var onCancel: ((Int, String?) -> Void)?

    public init() {}

}

public class EventStreamAdapter {

    static public var fetch: ((EventStreamConfig) -> String)?

    static public var cancel: ((String) -> Void)?

    static public var reconnectEndpoint: String?  // 重连的 endpoint

    static public var interruptEndpoint: String?  // 中断的 endpoint

    static public var payloadEndpoint: String?  // payload// 的 endpoint

    public init() {}

}

public class SSEImpl {

    static public func fetch(_ config: EventStreamConfig) -> String? {
        let fetcher = EventStreamAdapter.fetch
        if let _fetcher = fetcher {
            return _fetcher(config)
        }
        return nil
    }

    static public func cancel(_ cancelToken: String) {
        let canceler = EventStreamAdapter.cancel
        if let _canceler = canceler {
            _canceler(cancelToken)
        }
    }

    public init() {}

}
