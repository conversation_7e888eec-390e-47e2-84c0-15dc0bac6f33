import ObjectMapper
//a4b3b5cd0aef91080661689efb61b960
// This file is generated by tsn.
import TSNFoundation

public class HttpTransport {

    private var currentChunked: String = ""

    public func sendRequest(
        _ endpoint: String?, _ data: String? = "", _ headers: [String: String] = [:],
        _ successCallback: @escaping (String) -> Void, _ failCallback: @escaping (String, String?) -> Void
    ) {
        guard let endpoint = endpoint else {
            return
        }
        let esConfig = EventStreamConfig()
        esConfig.endpoint = endpoint
        esConfig.method = "POST"
        esConfig.headers = headers
        esConfig.data = data ?? ""
        esConfig.onChunk = { (text: String) in
            self.currentChunked = self.currentChunked + text
        }
        esConfig.onFinish = { () in
            var result = HttpData(JSONString: self.currentChunked)
            if result.ret == "0" {
                NuroLogger.debug(
                    "HttpTransport",
                    { () in
                        "sendRequest: onFinish: ret = \(result.ret), endpoint = \(endpoint), data = \(data)"
                    })
                successCallback(self.currentChunked)
            } else {
                NuroLogger.error(
                    "HttpTransport",
                    { () in
                        "sendRequest: onFinish: ret = \(result.ret), errmsg = \(result.errmsg), endpoint = \(endpoint), data = \(data)"
                    })
                failCallback(result.ret, result.errmsg)
            }
        }
        esConfig.onError = { (code: Int, message: String?) in
            NuroLogger.error(
                "HttpTransport",
                { () in
                    "sendRequest: onError: code = \(code), message = \(message), endpoint = \(endpoint), data = \(data)"
                })
            failCallback(code.toString(), message)
        }
        NuroLogger.debug(
            "HttpTransport",
            { () in
                "sendRequest: endpoint = \(endpoint), data = \(data), method = \(esConfig.method)"
            })
        SSEImpl.fetch(esConfig)
    }

    public init() {}

}

public class HttpData: TSNSerializable {

    public var ret: String = "0"

    public var errmsg: String? = nil

    public var systime: String? = nil

    public var logid: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        ret <- map["ret"]
        errmsg <- map["errmsg"]
        systime <- map["systime"]
        logid <- map["logid"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == HttpData.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
