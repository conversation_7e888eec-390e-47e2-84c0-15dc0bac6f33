//7b0be8f81606c4543107f04b8034a35e
// This file is generated by tsn.
import TSNFoundation
import ObjectMapper

public enum ChatMessageError: String {
    /**
       * 推流前异常错误
       * 推流失败，比如第一条事件就推送失败了
       */
    case interrupt_status = "interrupt_status"
    /**
       * 推流前异常错误
       * pe策略失败
       */
    case pe_policy_failed_status = "pe_policy_failed_status"
    /**
       * 推流前异常错误
       * 获取流失败
       */
    case chat_stream_failed_status = "chat_stream_failed_status"
    /**
       * 安全审核拦截
       * 输入文本审核拦截
       */
    case input_text_block_status = "input_text_block_status"
    /**
       * 安全审核拦截
       * 输出文本审核拦截
       */
    case output_text_block_status = "output_text_block_status"
    /**
       * 推流异常状态
       * 推送思考内容截止
       */
    case send_reasoning_content_stop_status = "send_reasoning_content_stop_status"
    /**
       * 推流异常状态
       * 推送内容截止
       */
    case send_content_stop_status = "send_content_stop_status"
    /**
       * sdk返回值错误
       * sse请求错误
       */
    case send_failed = "send_failed"
    /**
       * sdk返回值错误
       * 上传文件失败
       */
    case upload_failed = "upload_failed"
}

public extension ChatMessageError {
    func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroUserMessageStatus: String {
    /**
       * 刚创建状态
       */
    case none = "none"
    /**
       * 正在上传图片/视频
       */
    case uploading_files = "uploading_files"
    /**
       * 正在发送
       */
    case sending = "sending"
    /**
       * 收到返回的 AssistantMessage
       * 收到一点点立即变状态，不会等AssistantMessage完整收到再改变改状态
       */
    case finished_successfully = "finished_successfully"
    /**
       * 失败
       */
    case failed = "failed"
    /**
       * 取消
       */
    case cancelled = "cancelled"
}

public extension NuroUserMessageStatus {
    func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroAssistantMessageStatus: String {
    /**
       * 刚创建状态
       */
    case none = "none"
    /**
       * 正在回传数据流
       */
    case streaming = "streaming"
    /**
       * 数据流返回完成
       */
    case finished_successfully = "finished_successfully"
    /**
       * 失败
       */
    case failed = "failed"
    /**
       * 取消
       */
    case cancelled = "cancelled"
}

public extension NuroAssistantMessageStatus {
    func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroReasoningMessageStatus: String {
    /**
       * 刚创建状态
       */
    case none = "none"
    /**
       * 正在回传数据流
       */
    case streaming = "streaming"
    /**
       * 数据流返回完成
       */
    case finished_successfully = "finished_successfully"
    /**
       * 失败
       */
    case failed = "failed"
    /**
       * 取消
       */
    case cancelled = "cancelled"
}

public extension NuroReasoningMessageStatus {
    func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroToolCallMessageStatus: String {
    /**
       * 刚创建状态
       */
    case none = "none"
    /**
       * 工具参数正在流式传输中，上层业务不应允许用户操作本工具。
       */
    case streaming = "streaming"
    /**
       * 流式传输失败，可能是 LLM 或 Host Agent 或网络异常原因。
       */
    case streaming_failed = "streaming_failed"
    /**
       * 流式传输取消，可能是用户主动取消
       */
    case streaming_cancelled = "streaming_cancelled"
    /**
       * 正在调用本地或远端方法
       * 此处执行过程中，toolResult 有可能被流式更新。
       */
    case invoking = "invoking"
    /**
       * 本地方法无法直接返回结果
       * 等 sendToolResultMessage 方法调用返回结果
       */
    case wait_user_response = "wait_user_response"
    /**
       * 用户发送其他消息，不再通过sendToolResultMessage 回调结果
       */
    case skipped = "skipped"
    /**
       * 方法成功回调结果
       */
    case finished_successfully = "finished_successfully"
}

public extension NuroToolCallMessageStatus {
    func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroMessageType: String {
    case user = "user"
    case assistant = "assistant"
    case reasoning = "reasoning"
    case toolcall = "tool_call"
    case canvas = "canvas"
}

public extension NuroMessageType {
    func valueOf() -> String {
        return self.rawValue
    }
}

/**
 *  PayLoadData
 */
public class PayLoadData: TSNSerializable {

    public var conversationId: String = ""

    public var messageId: String = ""

    public var payload: String = "" //传submit_id

    override public func mapping(map: Map) {
        super.mapping(map: map)
        conversationId <- map["conversation_id"]
        messageId <- map["message_id"]
        payload <- map["payload"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == PayLoadData.self { afterParse();deferInitAndParse() }
    }

}

/**
 *  InterruptData
 */
public class InterruptData: TSNSerializable {

    public var conversationId: String = ""

    public var messageId: String = ""

    override public func mapping(map: Map) {
        super.mapping(map: map)
        conversationId <- map["conversation_id"]
        messageId <- map["message_id"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == InterruptData.self { afterParse();deferInitAndParse() }
    }

}

/**
 *  ResumeData
 */
public class ResumeData: TSNSerializable {

    public var conversationId: String = ""

    public var messageId: String = ""

    public var from_first_message: Bool? = true

    override public func mapping(map: Map) {
        super.mapping(map: map)
        conversationId <- map["conversation_id"]
        messageId <- map["message_id"]
        from_first_message <- map["from_first_message"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == ResumeData.self { afterParse();deferInitAndParse() }
    }

}

public enum NuroTaskMessageType: String {
    case promptMessage = "promptMessage"
    case middlewareMessage = "middlewareMessage"
    case artifactMessage = "artifactMessage"
    case shieldMessage = "shieldMessage"
}

public extension NuroTaskMessageType {
    func valueOf() -> String {
        return self.rawValue
    }
}

public class NuroMessage {

    public var id: String

    public var type: String

    public var updated: Int = 0

    public var createTime: Int64 = 0

    public var errorMsg: String?

    public var metadata: ChatMessageMetadata = ChatMessageMetadata()

    public var taskMessageType: NuroTaskMessageType?

    // 此消息是否为最后一轮
    public var endTurn: Bool?

    /**
       * 原始的消息 id，用于标识服务端消息的唯一性，请勿在前端使用！！！
       */
    public var _rawId: String = ""

    /**
       * 会话管理器，用于发送消息，上层业务勿用。
       */
    public weak var _conversationManager: NuroConversationManager?

    /**
       * 消息对应的task
       */
    public weak var _task: NuroTask?

    /**
       * 消息索引值，用于排序
       */
    public var _messageIndex: Int = 0

    public init(_ id: String, _ type: String) {
        self.id = id
        self.type = type
        self.updated = 0
    }

    public func setMessagePayload(_ payload: String, _ successCallback: @escaping (String) -> Void, _ failCallback: @escaping (String, String?) -> Void) -> Void {
        guard let EventStreamAdapter_payloadEndpoint = EventStreamAdapter.payloadEndpoint else {
            return
        }
        self.metadata.payload = payload
        var payLoadData: PayLoadData = PayLoadData()
        payLoadData.messageId = self._rawId
        payLoadData.conversationId = self._conversationManager?.conversation.conversationId ?? ""
        payLoadData.payload = payload
        HttpTransport().sendRequest(EventStreamAdapter.payloadEndpoint, payLoadData.toJSONString() ?? "", [:], successCallback, { (code: String, reason: String?) in
            failCallback(code, reason)
        })
    }

    public func baseCopy(_ message: NuroMessage) -> NuroMessage {
        message.updated = self.updated
        message.createTime = self.createTime
        message.errorMsg = self.errorMsg
        message.metadata = self.metadata
        message._rawId = self._rawId
        message._conversationManager = self._conversationManager
        message.taskMessageType = self.taskMessageType
        message._task = self._task
        message.endTurn = self.endTurn
        return message
    }

    public func copy() -> NuroMessage {
        let message = NuroMessage(self.id, self.type)
        self.baseCopy(message)
        return message
    }

    public func isDisplay() -> Bool {
        if self.taskMessageType == NuroTaskMessageType.middlewareMessage {
            var show = self._task?.isDisplayingMiddlewareMessage(self) ?? true
            return show
        }
        else {
            return true
        }
    }

    public func isFinalStatus() -> Bool {
        return true
    }

    public func isCancelledStatus() -> Bool {
        return true
    }

    public func isFailedStatus() -> Bool {
        return true
    }

    public func isEqualToObject(_ message: Any) -> Bool {
        if let message = message as? NuroMessage {
            return (self.id == message.id && self.type == message.type && self.createTime == message.createTime && self.errorMsg == message.errorMsg && self.metadata.isEqualToObject(message.metadata) == true && self.taskMessageType == message.taskMessageType && self.endTurn == message.endTurn)
        }
        return false
    }

    /**
       * 获取这条消息 对应的 这轮回话里面的 相关消息（包含 NuroUserMessage）
       */
    public func getMessageGroup() -> [NuroMessage] {
        var msgs: [NuroMessage] = self._conversationManager?.conversation.messages ?? []
        var targetIndex = -1
        do {
          var i = 0
          var __first__ = true
          while i < msgs.length {
            if !__first__ {
              i = i + 1
            }
            __first__ = false
            if i < msgs.length {
                  var id = msgs.tsn_safeGet(i)?.id ?? ""
            if id == self.id {
                targetIndex = i
                break
            }
            }
            else {
              break
            }
          }
        }
        if targetIndex == -1 {
            return []
        }
        // 向前查找：包含第一个遇到的 type='a'
            var startIndex = 0
        do {
          var i = targetIndex
          var __first__ = true
          while i >= 0 {
            if !__first__ {
              i = i - 1
            }
            __first__ = false
            if i >= 0 {
                  if msgs.tsn_safeGet(i) is NuroUserMessage {
                startIndex = i
                break
            }
            if i == 0 {
                startIndex = 0
            }
            }
            else {
              break
            }
          }
        }
        // 向后查找：不包含第一个遇到的 type='a'
            var endIndex = msgs.length
        do {
          var i = targetIndex + 1
          var __first__ = true
          while i < msgs.length {
            if !__first__ {
              i = i + 1
            }
            __first__ = false
            if i < msgs.length {
                  if msgs.tsn_safeGet(i) is NuroUserMessage {
                endIndex = i
                break
            }
            }
            else {
              break
            }
          }
        }
        return msgs.slice(startIndex, endIndex)
    }

    public func needResume() -> Bool {
        guard let this = self else {
            return false
        }
        if self is NuroUserMessage {
            return false
        }
        return !self.isFinalStatus()
    }

    public func getResumeMsgId() -> String {
        return self._rawId
    }

}

public enum NuroFileType: String {
    case image = "image"
    case video = "video"
}

public extension NuroFileType {
    func valueOf() -> String {
        return self.rawValue
    }
}

public class NuroLocalFile {

    public var localPath: String

    public var localFileObject: Any?

    public init(_ localPath: String, _ localFileObject: Any?) {
        self.localPath = localPath
        self.localFileObject = localFileObject
    }

    public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroLocalFile {
            return self.localPath == other.localPath
        }
        return false
    }

}

public class NuroImageMetadata {

    public var width: Int?

    public var height: Int?

    public var format: String?

    public var prompt: String?

    public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroImageMetadata {
            return (self.width == other.width && self.height == other.height && self.format == other.format && self.prompt == other.prompt)
        }
        return false
    }

    public init() {}

}

public class NuroFileMetadata {

    // 图片
    public var image_metadata: NuroImageMetadata?

    public init(_ imageMetadata: NuroImageMetadata) {
        self.image_metadata = imageMetadata
    }

    public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroFileMetadata {
            if self.image_metadata == nil && other.image_metadata == nil {
                return true
            }
            else if let _this_image_metadata = self.image_metadata, let _other_image_metadata = other.image_metadata {
                return (_this_image_metadata?.isEqualToObject(_other_image_metadata) == true)
            }
            else {
                return false
            }
        }
        return false
    }

}

public class NuroFile {

    /**
       * image, video, etc.
       */
    public var type: NuroFileType

    /**
       * url of the file.
       */
    public var url: String?

    /**
       * uri of the file.
       */
    public var uri: String?

    /**
       *
       */
    public var extra: [String: Any]?

    /**
       * 本地文件信息，当文件被用户选择后，并且未被上传时，会有该字段。
       * 该信息将透传至 TOS Uploader 方法，用于上传文件，文件上传后，url 字段会被更新。
       */
    public var localFile: NuroLocalFile?

    /**
       * 增加imagemeta信息
       */
    public var mimeType: String?

    /**
       * 文件描述, 业务需要添加的描述，可以用来描述相对关系
       */
    public var file_description: String?

    /**
       * metadata, 用来存prompt等信息
       */
    public var metadata: NuroFileMetadata?

    public init(_ type: NuroFileType, _ url: String?, _ mimeType: String?, _ localFile: NuroLocalFile? = nil, _ file_description: String?) {
        self.type = type
        self.url = url
        self.mimeType = mimeType
        self.localFile = localFile
        self.file_description = file_description
    }

    public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroFile {
            var localFileEqual = false
            if let _this_localFile = self.localFile, let _other_localFile = other.localFile {
                localFileEqual = _this_localFile.isEqualToObject(_other_localFile) == true
            }
            else if self.localFile == nil && other.localFile == nil {
                localFileEqual = true
            }
            var localMetadataEqual = false
            if let _this_metadata = self.metadata, let _other_metadata = other.metadata {
                localMetadataEqual = _this_metadata.isEqualToObject(_other_metadata) == true
            }
            else if self.metadata == nil && other.metadata == nil {
                localMetadataEqual = true
            }
            return (self.type == other.type && self.url == other.url && self.uri == other.uri && self.file_description == other.file_description && self.mimeType == other.mimeType && localMetadataEqual && localFileEqual)
        }
        return false
    }

}

public enum ReferenceRole: Int {
    case User
    case Assistant
}

public extension ReferenceRole {
    func valueOf() -> Int {
        return self.rawValue
    }
}

public class RefContent {

    public var text: String?

    public var file: NuroFile?

    public init(_ text: String?, _ file: NuroFile?) {
        self.text = text
        self.file = file
    }

}

public class NuroUserMessage: NuroMessage {

    public var text: String?

    public var files: [NuroFile]?

    public var messageStatus: NuroUserMessageStatus

    public var referenceInfo: [RefContent]?

    public init(_ id: String, _ text: String?, _ files: [NuroFile]? = nil, _ messageStatus: NuroUserMessageStatus = NuroUserMessageStatus.none, _ referenceInfo: [RefContent]? = nil) {
        self.messageStatus = messageStatus
        super.init(id, NuroMessageType.user)
        self.text = text
        self.files = files
        self.createTime = TSNNumberConverter.toInt64(Date.now())
        self.referenceInfo = referenceInfo
    }

    override public func copy() -> NuroMessage {
        let message = NuroUserMessage(self.id, self.text, self.files, self.messageStatus, self.referenceInfo)
        self.baseCopy(message)
        return message
    }

    override public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroUserMessage {
            var filesEqual = true
            if let _this_files = self.files, let _other_files = other.files, _this_files.length == _other_files.length {
                for i in 0..<_this_files.length {
                    if _this_files[i].isEqualToObject(_other_files[i]) != true {
                        filesEqual = false
                        break
                    }
                }
            }
            else if self.files?.length != other.files?.length {
                filesEqual = false
            }
            return (super.isEqualToObject(other) == true && self.text == other.text && self.messageStatus == other.messageStatus && filesEqual)
        }
        return false
    }

    public func setMsgStatus(_ status: NuroUserMessageStatus) -> Void {
        var newmsg = self.copy()
        if let newmsg = newmsg as? NuroUserMessage {
            newmsg.messageStatus = status
        }
        self._conversationManager?.receivedMessage(newmsg)
    }

    override public func isFinalStatus() -> Bool {
        return (self.messageStatus == NuroUserMessageStatus.finished_successfully || self.messageStatus == NuroUserMessageStatus.failed || self.messageStatus == NuroUserMessageStatus.cancelled)
    }

    override public func isCancelledStatus() -> Bool {
        return self.messageStatus == NuroUserMessageStatus.cancelled
    }

    override public func isFailedStatus() -> Bool {
        return self.messageStatus == NuroUserMessageStatus.failed
    }

}

public class NuroReasoningMessage: NuroMessage {

    public var text: String

    public var messageStatus: NuroReasoningMessageStatus

    public init(_ id: String, _ text: String, _ messageStatus: NuroReasoningMessageStatus = NuroReasoningMessageStatus.none) {
        self.text = text
        self.messageStatus = messageStatus
        super.init(id, NuroMessageType.reasoning)
    }

    override public func copy() -> NuroMessage {
        let message = NuroReasoningMessage(self.id, self.text, self.messageStatus)
        self.baseCopy(message)
        return message
    }

    override public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroReasoningMessage {
            return (super.isEqualToObject(other) == true && self.text == other.text && self.messageStatus == other.messageStatus)
        }
        return false
    }

    override public func isFinalStatus() -> Bool {
        return (self.messageStatus == NuroReasoningMessageStatus.finished_successfully || self.messageStatus == NuroReasoningMessageStatus.failed || self.messageStatus == NuroReasoningMessageStatus.cancelled)
    }

    override public func isCancelledStatus() -> Bool {
        return self.messageStatus == NuroReasoningMessageStatus.cancelled
    }

    override public func isFailedStatus() -> Bool {
        return self.messageStatus == NuroReasoningMessageStatus.failed
    }

    public func setMsgStatus(_ status: NuroReasoningMessageStatus) -> Void {
        var newmsg = self.copy()
        if let newmsg = newmsg as? NuroReasoningMessage {
            newmsg.messageStatus = status
        }
        self._conversationManager?.receivedMessage(newmsg)
    }

    public func setStatus(_ status: ChatMessageStatus?) -> Void {
        guard let status = status else {
            return
        }
        switch status { 
            case ChatMessageStatus.finished_successfully:
                self.messageStatus = NuroReasoningMessageStatus.finished_successfully
                break
            case ChatMessageStatus.in_progress:
                self.messageStatus = NuroReasoningMessageStatus.streaming
                break
            default:
                self.messageStatus = NuroReasoningMessageStatus.failed
                self.errorMsg = status.valueOf()
                break
        }
    }

}

public class NuroAssistantMessage: NuroMessage {

    public var name: String?

    public var text: String?

    public var files: [NuroFile]?

    public var messageStatus: NuroAssistantMessageStatus

    public var relateToolCalls: [NuroToolCallMessage] = []

    public init(_ id: String, _ name: String?, _ text: String?, _ files: [NuroFile]? = nil, _ messageStatus: NuroAssistantMessageStatus = NuroAssistantMessageStatus.none) {
        self.messageStatus = messageStatus
        super.init(id, NuroMessageType.assistant)
        self.text = text
        self.files = files
        self.name = name
    }

    override public func copy() -> NuroMessage {
        let message = NuroAssistantMessage(self.id, self.name, self.text, self.files, self.messageStatus)
        message.relateToolCalls = self.relateToolCalls
        self.baseCopy(message)
        return message
    }

    override public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroAssistantMessage {
            var filesEqual = true
            if let _this_files = self.files, let _other_files = other.files, _this_files.length == _other_files.length {
                for i in 0..<_this_files.length {
                    if _this_files[i].isEqualToObject(_other_files[i]) != true {
                        filesEqual = false
                        break
                    }
                }
            }
            else if self.files?.length != other.files?.length {
                filesEqual = false
            }
            return (super.isEqualToObject(other) == true && self.name == other.name && self.text == other.text && self.messageStatus == other.messageStatus && filesEqual)
        }
        return false
    }

    public func setMsgStatus(_ status: NuroAssistantMessageStatus) -> Void {
        var newmsg = self.copy()
        if let newmsg = newmsg as? NuroAssistantMessage {
            newmsg.messageStatus = status
        }
        self._conversationManager?.receivedMessage(newmsg)
    }

    override public func isFinalStatus() -> Bool {
        return (self.messageStatus == NuroAssistantMessageStatus.finished_successfully || self.messageStatus == NuroAssistantMessageStatus.failed || self.messageStatus == NuroAssistantMessageStatus.cancelled)
    }

    override public func isCancelledStatus() -> Bool {
        return self.messageStatus == NuroAssistantMessageStatus.cancelled
    }

    override public func isFailedStatus() -> Bool {
        return self.messageStatus == NuroAssistantMessageStatus.failed
    }

    public func setStatus(_ status: ChatMessageStatus?) -> Void {
        guard let status = status else {
            return
        }
        switch status { 
            case ChatMessageStatus.finished_successfully:
                self.messageStatus = NuroAssistantMessageStatus.finished_successfully
                break
            case ChatMessageStatus.in_progress:
                self.messageStatus = NuroAssistantMessageStatus.streaming
                break
            default:
                self.messageStatus = NuroAssistantMessageStatus.failed
                self.errorMsg = status.valueOf()
                break
        }
    }

    // 标识是否是对话结束
    public func isFinalTurn() -> Bool {
        if self.endTurn == true || self.metadata?.end_turn == true {
            return true
        }
        return false
    }

}

public class NuroToolCallMessage: NuroMessage {

    /**
       * functionType: server_function, client_function.
       */
    public var toolCallId: String

    public var toolType: String

    public var toolName: String // text2image_form

    public var toolArgs: String? // <- json

    public var toolResult: String?

    public var toolExtra: [String: String]?

    public var messageStatus: NuroToolCallMessageStatus

    public init(_ id: String, _ toolCallId: String, _ toolType: String, _ toolName: String, _ toolArgs: String?, _ toolExtra: [String: String]?, _ toolResult: String?, _ messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none) {
        self.toolCallId = toolCallId
        self.toolType = toolType
        self.toolName = toolName
        self.messageStatus = messageStatus
        super.init(id, NuroMessageType.toolcall)
        self.toolArgs = toolArgs
        self.toolExtra = toolExtra
        self.toolResult = toolResult
        if let _toolResult = toolResult, _toolResult.length > 0 {
            let toolCallResult = MCPToolCallResult(JSONString: _toolResult)
            if toolCallResult.content.length > 0 && self.messageStatus != NuroToolCallMessageStatus.invoking {
                self.messageStatus = NuroToolCallMessageStatus.finished_successfully
            }
        }
    }

    public func sendToolCallResult(_ result: String, _ userMessage: NuroUserMessage? = nil) -> Void {
        self.toolResult = result
        if let _this_toolResult = self.toolResult, let _this__conversationManager = self._conversationManager, _this_toolResult != "" {
            self.setMsgStatus(NuroToolCallMessageStatus.finished_successfully)
        }
        self._conversationManager?.sendToolResultMessage(self, userMessage)
    }

    public func sendToolCallResultFromMCPFormat(_ callResult: MCPToolCallResult, _ userMessage: NuroUserMessage? = nil) -> Void {
        if NuroMockManager.isMocking() {
            return
        }
        self.sendToolCallResult(callResult.toJSONString() ?? "", userMessage)
    }

    public func decodeToolCallResultAsPlainText() -> String? {
        if let _this_toolResult = self.toolResult {
            if _this_toolResult.trim().indexOf("{") == 0 {
                let formattedResult = self.decodeToolCallResultToMCPFormat()
                var content = ""
                for index in 0..<formattedResult.content.length {
                    let element = formattedResult.content.tsn_safeGet(index)
                    if let element = element as? MCPToolCallTextContent {
                        content += element.text
                    }
                }
                return content
            }
            else {
                return _this_toolResult
            }
        }
        else {
            return nil
        }
    }

    public func decodeToolCallResultToMCPFormat() -> MCPToolCallResult {
        return MCPToolCallResult(JSONString: self.toolResult ?? "{}")
    }

    public func setMsgStatus(_ status: NuroToolCallMessageStatus) -> Void {
        var newmsg = self.copy()
        if let newmsg = newmsg as? NuroToolCallMessage {
            newmsg.messageStatus = status
        }
        self._conversationManager?.receivedMessage(newmsg)
    }

    override public func isFinalStatus() -> Bool {
        return (self.messageStatus == NuroToolCallMessageStatus.skipped || self.messageStatus == NuroToolCallMessageStatus.finished_successfully || self.messageStatus == NuroToolCallMessageStatus.streaming_failed || self.messageStatus == NuroToolCallMessageStatus.streaming_cancelled)
    }

    override public func isCancelledStatus() -> Bool {
        return self.messageStatus == NuroToolCallMessageStatus.streaming_cancelled
    }

    override public func isFailedStatus() -> Bool {
        return self.messageStatus == NuroToolCallMessageStatus.streaming_failed
    }

    override public func copy() -> NuroToolCallMessage {
        let message = NuroToolCallMessage(self.id, self.toolCallId, self.toolType, self.toolName, self.toolArgs, self.toolExtra, self.toolResult, self.messageStatus)
        self.baseCopy(message)
        return message
    }

    override public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroToolCallMessage {
            return (super.isEqualToObject(other) == true && self.toolCallId == other.toolCallId && self.toolType == other.toolType && self.toolName == other.toolName && self.toolArgs == other.toolArgs && self.toolExtra == other.toolExtra && self.toolResult == other.toolResult && self.messageStatus == other.messageStatus && self.endTurn == other.endTurn)
        }
        return false
    }

    public func isClientToolSkipped() -> Bool {
        if self.toolType == ChatToolCallType.client_function {
            return self.messageStatus == NuroToolCallMessageStatus.skipped
        }
        return false
    }

}

public let CANVAS_DEFAULT: Int = -10000

public let CANVAS_ADD_TO_END: Int = -10001

public func getNodeFromNuroToolCallMsg(_ id: String, _ nodeIndex: Int, _ toolCall: NuroToolCallMessage) -> NuroCanvasNode {
    let node = NuroCanvasNode(id, nodeIndex, toolCall.toolCallId, toolCall.toolType, toolCall.toolName, toolCall.toolArgs, toolCall.toolExtra, toolCall.toolResult, toolCall.messageStatus)
    node.nodeIndex = nodeIndex
    node._rawId = toolCall._rawId ?? ""
    node._conversationManager = toolCall._conversationManager
    node.createTime = toolCall.createTime ?? 0
    node._messageIndex = toolCall._messageIndex
    node.messageStatus = toolCall.messageStatus
    node.endTurn = toolCall.endTurn
    return node
}

// NuroCanvasNode，代表中间插入的节点信息
public class NuroCanvasNode: NuroToolCallMessage {

    public var nodeIndex: Int = CANVAS_DEFAULT // 标识插入节点的相对位置

    public init(_ id: String, _ nodeIndex: Int, _ toolCallId: String, _ toolType: String, _ toolName: String, _ toolArgs: String?, _ toolExtra: [String: String]?, _ toolResult: String?, _ messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none) {
        super.init(id, toolCallId, toolType, toolName, toolArgs, toolExtra, toolResult, messageStatus)
        self.nodeIndex = nodeIndex
    }

    override public func copy() -> NuroCanvasNode {
        let message = NuroCanvasNode(self.id, self.nodeIndex, self.toolCallId, self.toolType, self.toolName, self.toolArgs, self.toolExtra, self.toolResult, self.messageStatus)
        self.baseCopy(message)
        return message
    }

    override public func isCancelledStatus() -> Bool {
        // 生成节点没有取消状态
        return super.isCancelledStatus()
    }

    override public func isFailedStatus() -> Bool {
        // 生成节点没有fail状态
        return super.isFailedStatus()
    }

    override public func isFinalStatus() -> Bool {
        return super.isFinalStatus()
    }

    override public func getResumeMsgId() -> String {
        return self._rawId
    }

    override public func isEqualToObject(_ other: Any) -> Bool {
        if let other = other as? NuroCanvasNode {
            return (super.isEqualToObject(other) == true && self.nodeIndex == other.nodeIndex)
        }
        return false
    }

}

public enum NuroCanvasStatus: Int {
    case none
    case init
    case streaming
    case cancel // 用户取消
    case end
}

public extension NuroCanvasStatus {
    func valueOf() -> Int {
        return self.rawValue
    }
}

public class NuroCanvasMessage: NuroMessage {

    public var status: NuroCanvasStatus = NuroCanvasStatus.none

    public var startNode: NuroToolCallMessage?

    public var endNode: NuroToolCallMessage?

    // 标识对应的图片
    public var nodes: [NuroCanvasNode]? = []

    public init(_ id: String) {
        super.init(id, NuroMessageType.canvas)
    }

    public func updateStartNode(_ node: NuroToolCallMessage) -> Void {
        self.startNode = node
    }

    public func updateCanvasStatus(_ status: NuroCanvasStatus) -> Void {
        self.status = status
        self.setMsgStatus(self.status)
    }

    public func updateEndNode(_ node: NuroToolCallMessage) -> Void {
        self.endNode = node
    }

    /*
       ** 增加一个finish接口，因为收到end的时候，有可能还在streaming
       */
    public func finish() -> Void {
        self.status = NuroCanvasStatus.end
        self.setMsgStatus(NuroCanvasStatus.end)
    }

    public func addOrReplaceNode(_ node: NuroCanvasNode) -> Void {
        if self.nodes == nil {
            self.nodes = []
        }
        if node.nodeIndex > self.nodes?.length {
            self.nodes.push(node)
        }
        else {
            if node.nodeIndex == CANVAS_ADD_TO_END {
                var length = self.nodes.length
                self.nodes[length] = node
            }
            else {
                var index = node.nodeIndex - 1
                self.nodes[index] = node
            }
        }
    }

    public func findNodeByToolCallId(_ toolCallId: String) -> NuroCanvasNode? {
        guard let this_nodes = self.nodes else {
            return nil
        }
        var targetNode: NuroCanvasNode? = nil
        self.nodes.forEach({ (it) in
            if it.toolCallId == toolCallId {
                targetNode = it
            }
        })
        return targetNode
    }

    public func findEndNode(_ toolCallId: String) -> NuroToolCallMessage? {
        guard let this_endNode = self.endNode else {
            return nil
        }
        if self.endNode.toolCallId == toolCallId {
            return self.endNode
        }
        return nil
    }

    public func findStartNode(_ toolCallId: String) -> NuroToolCallMessage? {
        guard let this_startNode = self.startNode else {
            return nil
        }
        if self.startNode.toolCallId == toolCallId {
            return self.startNode
        }
        return nil
    }

    public func isCanvasOpen() -> Bool {
        return (self.status != NuroCanvasStatus.end && self.status != NuroCanvasStatus.cancel)
    }

    override public func copy() -> NuroCanvasMessage {
        let message = NuroCanvasMessage(self.id)
        message.status = self.status
        message.nodes = self.nodes?.map({ (it) in
            it.copy()
        })
        message.startNode = self.startNode?.copy()
        message.endNode = self.endNode?.copy()
        self.baseCopy(message)
        return message
    }

    override public func isCancelledStatus() -> Bool {
        // 没有cancel状态，生成过程中，交互需要禁止
        return self.status == NuroCanvasStatus.cancel
    }

    override public func isFailedStatus() -> Bool {
        // 画布消息不维护失败状态，由业务自行判断。
        return false
    }

    override public func isFinalStatus() -> Bool {
        return self.status == NuroCanvasStatus.end
    }

    public func setMsgStatus(_ status: NuroCanvasStatus) -> Void {
        self.status = status
        var newmsg = self.copy()
        if let newmsg = newmsg as? NuroCanvasMessage {
            newmsg.status = status
        }
        self._conversationManager?.receivedMessage(newmsg)
    }

    override public func getResumeMsgId() -> String {
        var result = ""
        if let _this_startNode = self.startNode {
            if _this_startNode.needResume() {
                result = _this_startNode._rawId
            }
        }
        if let _this_nodes = self.nodes {
            _this_nodes.forEach({ (node) in
                if let _node = node {
                    if _node.needResume() {
                        result = _node._rawId
                    }
                }
            })
        }
        if let _this_endNode = self.endNode {
            if _this_endNode.needResume() {
                result = _this_endNode._rawId
            }
        }
        if result == "" {
            if let _this_startNode = self.startNode {
                result = _this_startNode._rawId
            }
        }
        return result
    }

    override public func isEqualToObject(_ message: Any) -> Bool {
        if let message = message as? NuroCanvasMessage {
            var result = super.isEqualToObject(message) == true && self.status == message.status && self.startNode?.isEqualToObject(message.startNode) == true && self.endNode?.isEqualToObject(message.endNode) == true
            if result == false {
                return false
            }
            if self.nodes == nil {
                if let _message_nodes = message.nodes {
                    return false
                }
                else {
                    return true
                }
            }
            else if self.nodes.length == 0 {
                if message.nodes == nil {
                    return false
                }
                else if message.nodes.length > 0 {
                    return false
                }
                return true
            }
            else {
                if message.nodes == nil {
                    return false
                }
                else if message.nodes.length == 0 {
                    return false
                }
                else if self.nodes.length != message.nodes.length {
                    return false
                }
                else {
                    for i in 0..<self.nodes.length {
                        if self.nodes.tsn_safeGet(i)?.isEqualToObject(message.nodes.tsn_safeGet(i)) == false {
                            return false
                        }
                    }
                    return true
                }
            }
        }
        return false
    }

}
