//df2671a84c9f98929c56cd926bba2331
// This file is generated by tsn.
import TSNFoundation

public class NuroConversationManager {

    public let conversation: NuroConversation

    private var activeTransport: CommonTransport?

    /**
       * Client Side MCP
       */
    public var mcpManager: NuroMCPManager?

    private var toolCalled: [String: Bool] = [:]

    public init(_ conversationId: String = NuroUtils.randomUUIDString()) {
        self.conversation = NuroConversation(conversationId)
    }

    /**
       * 注册需要链接的后端通道
       * @param transport
       */
    public func connect(_ transport: CommonTransport) {
        self.activeTransport = transport
        transport.setConversationManager(self)
        self.conversation.conversationState = NuroConversationState.readyToSendMessage
    }

    /**
       * 使能本地工具调用
       */
    public func enableMCPTools() {
        weak var _self: NuroConversationManager? = self
        self.conversation.addStateUpdateListener({ (state) in
            guard let _self = _self else {
                return
            }
            if state == NuroConversationState.readyToSendMessage {
                var msgs: [NuroToolCallMessage] = []
                self.conversation.messages.forEach({ (msg) in
                    if let msg = msg as? NuroToolCallMessage {
                        if msg.messageStatus == NuroToolCallMessageStatus.invoking {
                            if msg.toolType == ChatToolCallType.client_function.valueOf() {
                                if msg.toolResult == "" {
                                    msgs.push(msg)
                                } else if msg.toolResult == nil {
                                    msgs.push(msg)
                                }
                            }
                        }
                    }
                })
                if msgs.length > 0 {
                    _self.onToolCall(msgs)
                }
            }
        })
    }

    /**
       * 解析会话历史数据
       * @param str 会话历史数据 jsonstr
       */
    public func decodeConversationFromJSONString(_ str: String, _ needResume: Bool = false) {
        var history = HistoryMessages(JSONString: str)
        self.conversation.conversationId = history.conversation?.conversation_id ?? ""
        var messages = history.getChatMessages()
        self.conversation.summary = history.conversation?.summary ?? ""
        messages.sort({ (a, b) in
            let timeA = TSNNumberConverter.toDouble(a.create_time ?? 0)
            let timeB = TSNNumberConverter.toDouble(b.create_time ?? 0)
            if timeA < timeB {
                return -1
            } else if timeA > timeB {
                return 1
            } else {
                return 0
            }
        })
        var nuroMessagesR: [String: NuroMessage] = [:]
        messages.forEach({ (message) in
            var msgs = MessageProcessor.convertChatMessageToNuroMessage(self, message, ConvertType.history)
            msgs.forEach({ (message) in
                nuroMessagesR[message.id] = message
            })
            var nuroMessages: [NuroMessage] = TSNMapUtils.values(nuroMessagesR)
            var length: Int = nuroMessages.length
            if length > 1 {
                var parentid = nuroMessages.tsn_safeGet(0)?.id ?? ""
                do {
                    var index = 1
                    var __first__ = true
                    while index < length {
                        if !__first__ {
                            index = index + 1
                        }
                        __first__ = false
                        if index < length {
                            var msg = nuroMessages.tsn_safeGet(index)
                            if let _msg = msg {
                                _msg.metadata.parent_message_id = parentid
                                parentid = _msg.id
                            }
                        } else {
                            break
                        }
                    }
                }
            }
            nuroMessages.forEach({ (msg) in
                self.receivedMessage(msg, false)
            })
        })
        if needResume {
            self.resumeMessageIfNeed()
        }
    }

    /**
       * 会话开始时需要提前填入的信息
       * @param value
       */
    public func setSystemPrompt(_ value: String) {
        self.conversation.systemPrompt = value
    }

    public func resumeMessageIfNeed() {
        if self.conversation.needResume() == false {
            return
        }
        self.conversation.updateState(NuroConversationState.streamingResponse)
        self.activeTransport?.resumeMessage()
    }

    public func sendUserMessage(_ message: NuroUserMessage?) {
        guard let message = message else {
            return
        }
        if NuroMockManager.checkMock(message, self) {
            return
        }
        if message._conversationManager == nil {
            message._conversationManager = self
        }
        self.conversation.updateState(NuroConversationState.streamingResponse)
        message.setMsgStatus(NuroUserMessageStatus.none)
        if let _message_files = message.files, _message_files.length > 0 {
            var _files: [NuroFile] = []
            for index in 0..<_message_files.length {
                let file = _message_files[index]
                _files.push(file)
            }
            message.setMsgStatus(NuroUserMessageStatus.uploading_files)
            self.uploadFiles(
                _files,
                { () in
                    self._doSendUserMessage(message)
                },
                { () in
                    message.errorMsg = ChatMessageError.upload_failed.valueOf()
                    message.setMsgStatus(NuroUserMessageStatus.failed)
                    self.conversation.updateState(NuroConversationState.readyToSendMessage)
                })
        } else {
            self._doSendUserMessage(message)
        }
    }

    private func _doSendUserMessage(_ message: NuroUserMessage) {
        if let _this_mcpManager = self.mcpManager {
            _this_mcpManager.getAllTools({ (tools) in
                self.activeTransport?.sendMessage(message, tools)
            })
        } else {
            self.activeTransport?.sendMessage(message, [])
        }
    }

    private func noUpload(_ file: NuroFile) -> Bool {
        return file.url != nil && file.uri != nil
    }

    private func uploadFiles(
        _ files: [NuroFile], _ successCallback: @escaping () -> Void, _ failCallback: @escaping () -> Void
    ) {
        var uploadQueueFiles = files
        var file = uploadQueueFiles.tsn_safeGet(0)
        if let _file = file {
            var noUp: Bool = self.noUpload(_file)
            if noUp == true {
                uploadQueueFiles.shift()
                self.uploadFiles(uploadQueueFiles, successCallback, failCallback)
            } else {
                if let __file_localFile = _file.localFile {
                    var config = TOSFileUploadConfig(_file, __file_localFile)
                    config.onFinish = { () in
                        uploadQueueFiles.shift()
                        self.uploadFiles(uploadQueueFiles, successCallback, failCallback)
                    }
                    config.onError = { (code, message) in
                        failCallback()
                    }
                    TOSFileUploadImpl.upload(config)
                } else {
                    uploadQueueFiles.shift()
                    self.uploadFiles(uploadQueueFiles, successCallback, failCallback)
                }
            }
        } else {
            successCallback()
        }
    }

    /**
       * 本地工具方法 结果回调
       * @param message 回调的工具
       * @param userMessage 额外需要带的用户消息
       */
    public func sendToolResultMessage(_ message: NuroToolCallMessage, _ userMessage: NuroUserMessage? = nil) {
        self.conversation.updateState(NuroConversationState.streamingResponse)
        var msgs: [NuroMessage] = []
        msgs.push(message)
        if let _userMessage = userMessage {
            msgs.push(_userMessage)
        }
        if let _this_mcpManager = self.mcpManager {
            _this_mcpManager.getAllTools({ (tools) in
                self.activeTransport?.sendMessages(msgs, tools)
            })
        } else {
            self.activeTransport?.sendMessages(msgs, [])
        }
    }

    public func sendToolResultMessages(_ messages: [NuroToolCallMessage]) {
        messages.forEach({ (message) in
            self.receivedMessage(message)
        })
        self.conversation.updateState(NuroConversationState.streamingResponse)
        if let _this_mcpManager = self.mcpManager {
            _this_mcpManager.getAllTools({ (tools) in
                self.activeTransport?.sendMessages(messages, tools)
            })
        } else {
            self.activeTransport?.sendMessages(messages, [])
        }
    }

    /**
       * 重试操作
       * @param message 需要被重试的消息
       *
       * @param message 是否需要删除重试消息
       */
    public func regenerateMessage(_ message: NuroMessage, _ needDelete: Bool) {
        var hasTool = false
        var index: Int = -1
        var userMessage: NuroUserMessage? = nil
        var indexuser: Int = -1
        do {
            var i = self.conversation.messages.length - 1
            var __first__ = true
            while i > -1 {
                if !__first__ {
                    i = i - 1
                }
                __first__ = false
                if i > -1 {
                    var msg = self.conversation.messages.tsn_safeGet(i)
                    guard let msg = msg else {
                        continue
                    }
                    if msg.id == message.id && msg.type == message.type {
                        index = i
                    }
                    if index > -1 {
                        if let msg = msg as? NuroToolCallMessage {
                            hasTool = true
                        } else if let msg = msg as? NuroUserMessage {
                            userMessage = msg
                            indexuser = i
                            break
                        }
                    }
                } else {
                    break
                }
            }
        }
        if index != -1 {
            if let _userMessage = userMessage {
                if needDelete == true {
                    self.conversation.messages.splice(index, 1)
                    self.notifyConversationUpdate(message, NuroMessageOp.delete)
                }
                if needDelete == true && index != indexuser {
                    self.conversation.messages.splice(indexuser, 1)
                    self.notifyConversationUpdate(_userMessage, NuroMessageOp.delete)
                }
                var newMessage: NuroUserMessage?
                if hasTool == true && needDelete == false {
                    newMessage = NuroUserMessage(NuroUtils.randomUUIDString(), "再次生成")
                } else {
                    newMessage = _userMessage.copy() as! NuroUserMessage
                    if let _newMessage = newMessage {
                        _newMessage.id = NuroUtils.randomUUIDString()
                    }
                }
                self.sendUserMessage(newMessage)
            }
        }
    }

    /**
       * 中断 当前会话请求
       */
    public func interruptResponse() {
        var token = self.activeTransport?.token
        MessageProcessor.markInProgressMessagesAsCancel(self)
        if let _token = token {
            SSEImpl.cancel(_token)
        }
        var interruptData: InterruptData = InterruptData()
        interruptData.conversationId = self.conversation.conversationId ?? ""
        var lastUserMessage = self.conversation.findLastUserMessage()
        interruptData.messageId = lastUserMessage?.id ?? ""
        HttpTransport().sendRequest(
            EventStreamAdapter.interruptEndpoint, interruptData.toJSONString(), [:],
            { (result: String) in

            },
            { (code: String, reason: String?) in

            })
    }

    public func receivedMessage(_ _message: NuroMessage, _ fromSSE: Bool = true) {
        var message = _message.copy()
        message._conversationManager = self
        var index = -1
        for i in 0..<self.conversation.messages.length {
            if self.conversation.messages.tsn_safeGet(i)?.id == message.id
                && self.conversation.messages.tsn_safeGet(i)?.type == message.type
            {
                index = i
                break
            }
        }
        if index >= 0 {
            // message already received, current is a streaming message.
            var oldmsg = self.conversation.messages.tsn_safeGet(index)
            if let _oldmsg = oldmsg {
                if _oldmsg.isEqualToObject(message) == true {
                    return
                }
                if _oldmsg.isFinalStatus() == true && fromSSE == true {
                    return
                }
                message.updated = _oldmsg.updated + 1
            }
            self.conversation.messages[index] = message
            self.notifyConversationUpdate(message, NuroMessageOp.update)
        } else {
            var length: Int = self.conversation.messages.length
            if length > 0 {
                message.metadata.parent_message_id = self.conversation.messages.tsn_safeGet(length - 1)?.id ?? ""
            }
            self.conversation.messages.push(message)
            self.notifyConversationUpdate(message, NuroMessageOp.add)
        }
    }

    public func notifyConversationUpdate(_ message: NuroMessage, _ op: NuroMessageOp) {
        self.conversation.updateMessage(message, op)
    }

    public func onToolCall(_ toolCallMessages: [NuroToolCallMessage]) {
        var _toolCallMessages = toolCallMessages
        var toolCallCount = toolCallMessages.length
        for index in 0..<toolCallMessages.length {
            let toolCallMessage = toolCallMessages[index]
            if self.toolCalled.tsn_safeGet(toolCallMessage.id) == true {
                toolCallCount = toolCallCount - 1
                return
            }
            self.toolCalled[toolCallMessage.id] = true
            self.mcpManager?.callTool(
                toolCallMessage.toolName, toolCallMessage.toolArgs, toolCallMessage.id,
                { (result) in
                    let toolCallResult = MCPToolCallResult(JSONString: result)
                    if toolCallResult.content.length <= 0 {
                        toolCallMessage._conversationManager = self
                        toolCallMessage.setMsgStatus(NuroToolCallMessageStatus.wait_user_response)
                    } else {
                        var toolMsgCopy = toolCallMessage.copy() as! NuroToolCallMessage
                        toolMsgCopy.toolResult = result
                        toolMsgCopy.messageStatus = NuroToolCallMessageStatus.finished_successfully
                        _toolCallMessages[index] = toolMsgCopy
                        toolCallCount = toolCallCount - 1
                        if toolCallCount <= 0 {
                            self.sendToolResultMessages(_toolCallMessages)
                        }
                    }
                })
        }
    }

}
