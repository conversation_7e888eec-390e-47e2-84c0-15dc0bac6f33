//7791bbbcccee8cf157122fa856467390
// This file is generated by tsn.
import TSNFoundation

public protocol NuroMCPClientAdapter {
    func listTools(_ callback: @escaping ([NuroMCPToolItem]) -> Void)
    func callTool(_ toolCallId: String, _ toolName: String, _ toolArgs: String?, _ callback: @escaping (String) -> Void)
}

public class NuroMCPServerConfig {

    public var name: String

    public var adapter: NuroMCPClientAdapter

    public init(_ name: String, _ adapter: NuroMCPClientAdapter) {
        self.name = name
        self.adapter = adapter
    }

}

public class NuroMCPToolItem {

    /**
       * 服务名称
       */
    public var serverName: String

    /**
       * 工具名称
       */
    public var name: String

    /**
       * 工具描述
       */
    public var description: String

    /**
       * JSON Schema
       */
    public var inputSchema: String

    public init(_ serverName: String, _ name: String, _ description: String, _ inputSchema: String) {
        self.serverName = serverName
        self.name = name
        self.description = description
        self.inputSchema = inputSchema
    }

}

public class NuroMCPManager {

    private var servers: [String: NuroMCPServerConfig] = [:]

    private var toolsServerMapping: [String: String] = [:]

    public func registerServer(_ config: NuroMCPServerConfig) {
        self.servers[config.name] = config
    }

    public func getAllTools(_ callback: @escaping ([NuroMCPToolItem]) -> Void) {
        var allTools: [NuroMCPToolItem] = []
        var taskCount = TSNMapUtils.size(self.servers)
        if taskCount == 0 {
            callback(allTools)
            return
        }
        TSNMapUtils.forEach(
            self.servers,
            { (serverName, server) in
                server.adapter.listTools({ (tools) in
                    tools.forEach({ (tool) in
                        allTools.push(tool)
                        self.toolsServerMapping[serverName + "_" + tool.name] = serverName
                    })
                    taskCount = taskCount - 1
                    if taskCount == 0 {
                        TSNConsole.log(allTools)
                        callback(allTools)
                    }
                })
            })
    }

    public func callTool(
        _ functionNameWithPrefix: String, _ functionCallArguments: String?, _ functionCallId: String,
        _ callback: @escaping (String) -> Void
    ) {
        let serverName = self.toolsServerMapping.tsn_safeGet(functionNameWithPrefix)
        guard let serverName = serverName else {
            return
        }
        let server = self.servers.tsn_safeGet(serverName)
        guard let server = server else {
            return
        }
        server.adapter.callTool(
            functionCallId, functionNameWithPrefix.substr(serverName.length + 1), functionCallArguments,
            { (result) in
                callback(result)
            })
    }

    public init() {}

}
