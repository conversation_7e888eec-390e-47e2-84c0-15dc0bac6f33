//8d87ec4c047d5ffc5ea43c919d969d45
// This file is generated by tsn.
import TSNFoundation

public protocol INuroMocker {
    func checkMock(_ userMessage: NuroUserMessage, _ manager: NuroConversationManager) -> Bool
}

public class NuroMockManager {

    static private var mocker: INuroMocker? = nil

    static public func setMocker(_ mocker: INuroMocker?) {
        NuroMockManager.mocker = mocker
    }

    static public func checkMock(_ userMessage: NuroUserMessage, _ manager: NuroConversationManager) -> Bool {
        return NuroMockManager.mocker?.checkMock(userMessage, manager) ?? false
    }

    static public func isMocking() -> <PERSON>ol {
        return NuroMockManager.mocker != nil
    }

    public init() {}

}
