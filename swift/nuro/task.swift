//21e3d70bc4a8a6730007ebeeca268789
// This file is generated by tsn.
import TSNFoundation

public enum NuroMessageOp: String {
    case add = "add"
    case update = "update"
    case delete = "delete"
}

extension NuroMessageOp {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroTaskOp: String {
    case add = "add"
    case update = "update"
    case delete = "delete"
}

extension NuroTaskOp {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public enum NuroTaskStatus: String {
    case none = "none"
    case running = "running"
    case finished = "finished"
    case failed = "failed"
    case cancelled = "cancelled"
}

extension NuroTaskStatus {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class NuroMessageOut {

    public var message: NuroMessage

    public var messageOp: NuroMessageOp

    public init(_ message: NuroMessage, _ op: NuroMessageOp) {
        self.message = message
        self.messageOp = op
    }

}

public class NuroTaskOut {

    public var task: NuroTask

    public var taskOp: NuroTaskOp

    public var messages: [NuroMessageOut] = []

    public init(_ task: NuroTask, _ taskOp: NuroTaskOp) {
        self.task = task
        self.taskOp = taskOp
    }

    public func updateMessage(_ messages: [NuroMessageOut]) {
        messages.forEach({ (msg) in
            if self.task.containMessage(msg.message) {
                self.messages.push(msg)
            } else {
                self.messages.push(NuroMessageOut(msg.message, NuroMessageOp.delete))
            }
        })
    }

}

public class NuroTask {

    /**
       * 任务ID
       */
    public var taskId: String

    /**
       * 任务状态
       */
    public var taskStatus: NuroTaskStatus = NuroTaskStatus.none

    /**
       * 消息哈希
       * 用于判断消息是否发生了变化
       */
    public var messageHash: String = ""

    /**
       * 用户输入的问题
       * 可能包含多个 UserMessage
       */
    public var promptMessages: [NuroMessage] = []

    /**
       * 中间消息
       * 可能包含多个 ReasoningMessage、AssistantMessage、ToolCallMessage
       * 这些消息表示了生成产物的过程
       */
    public var middlewareMessages: [NuroMessage] = []

    /**
       * 产物消息
       * 可能包含多个 AssistantMessage、ToolCallMessage
       */
    public var artifactMessages: [NuroMessage] = []

    /**
       *
       * 屏蔽掉的消息信息
       */
    public var shieldMessages: [NuroMessage] = []

    public init(_ taskId: String) {
        self.taskId = taskId
    }

    public func recalculateMessageHash() {
        var messageHashPart = self.promptMessages.concat(self.middlewareMessages).concat(self.shieldMessages).concat(
            self.artifactMessages)
        let messageHash = messageHashPart.map({ (it) in
            it.id + "_" + it.updated.toString(10)
        }).join(",")
        self.messageHash = messageHash
    }

    public func displayingMiddlewareMessages() -> [NuroMessage] {
        var addFirstAssistant: Bool = false
        return self.middlewareMessages.filter({ (msg: NuroMessage) in
            if let msg = msg as? NuroAssistantMessage {
                if addFirstAssistant == false {
                    addFirstAssistant = true
                    return true
                }
            } else if let msg = msg as? NuroToolCallMessage {
                if msg.toolType == ChatToolCallType.client_function.valueOf() {
                    return true
                }
            }
            return false
        })
    }

    public func isDisplayingMiddlewareMessage(_ msg: NuroMessage) -> Bool {
        var showMsgs: [NuroMessage] = self.displayingMiddlewareMessages()
        return showMsgs.some({ (it: NuroMessage) in
            it.id == msg.id
        })
    }

    public func addMessage(_ msg: NuroMessage, _ taskMsgType: NuroTaskMessageType) {
        msg._task = self
        msg.taskMessageType = taskMsgType
        if taskMsgType == NuroTaskMessageType.promptMessage {
            self.promptMessages.push(msg)
        } else if taskMsgType == NuroTaskMessageType.middlewareMessage {
            self.middlewareMessages.push(msg)
        } else if taskMsgType == NuroTaskMessageType.artifactMessage {
            self.artifactMessages.push(msg)
        } else if taskMsgType == NuroTaskMessageType.shieldMessage {
            self.shieldMessages.push(msg)
        }
    }

    public func containMessage(_ msg: NuroMessage) -> Bool {
        for i in 0..<self.promptMessages.length {
            if self.promptMessages.tsn_safeGet(i)?.id == msg.id {
                return true
            }
        }
        for i in 0..<self.middlewareMessages.length {
            if self.middlewareMessages.tsn_safeGet(i)?.id == msg.id {
                return true
            }
        }
        for i in 0..<self.artifactMessages.length {
            if self.artifactMessages.tsn_safeGet(i)?.id == msg.id {
                return true
            }
        }
        for i in 0..<self.shieldMessages.length {
            if self.shieldMessages.tsn_safeGet(i)?.id == msg.id {
                return true
            }
        }
        return false
    }

    public func needResume() -> Bool {
        return self.taskStatus == NuroTaskStatus.running
    }

}

open class NuroTaskChecker {

    open func isPromptMessage(_ message: NuroMessage) -> Bool {
        return message is NuroUserMessage
    }

    open func isArtifactMessage(_ message: NuroMessage) -> Bool {
        return !(message is NuroUserMessage)
    }

    open func isShieldMessage(_ message: NuroMessage) -> Bool {
        if let message = message as? NuroToolCallMessage {
            return (NuroSetting.shieldToolCall.indexOf((message as! NuroToolCallMessage).toolName) >= 0)
        }
        return false
    }

    public init() {}

}

public class NuroCanvasChecker {

    public func isNuroCanvasMessage(_ message: NuroToolCallMessage) -> Bool {
        return false
    }

    public init() {}

}
