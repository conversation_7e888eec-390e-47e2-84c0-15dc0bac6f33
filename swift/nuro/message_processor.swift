//21fbe27a7a0eea77f8d85bd8246a6c93
// This file is generated by tsn.
import TSNFoundation

public enum ConvertType: String {
    case new_message = "new_message"
    case history = "history"
}

extension ConvertType {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class MessageProcessor {

    static public func convertChatMessageToNuroMessage(
        _ conversationManager: NuroConversationManager?, _ chatMessage: ChatMessage, _ type: ConvertType
    ) -> [NuroMessage] {
        let is_visually_hidden_from_conversation = chatMessage.metadata?.is_visually_hidden_from_conversation
        if let _is_visually_hidden_from_conversation = is_visually_hidden_from_conversation,
            _is_visually_hidden_from_conversation == true
        {
            // Server 端要求该消息不展示在会话中，所以不返回任何消息。
            return []
        }
        let role = chatMessage.author?.role
        let name = chatMessage.author?.name
        var msgs: [NuroMessage] = []
        if let _role = role {
            if _role == "user" {
                var text = ""
                var files: [NuroFile] = []
                var referenceInfo: [RefContent] = []
                chatMessage.content?.content_parts?.forEach({ (it) in
                    if let _it_text = it.text, _it_text.length > 0 {
                        if it.is_referenced == true {
                            referenceInfo.push(text: _it_text)
                        } else {
                            text = text + _it_text.trim()
                        }
                    }
                    if let _it_file = it.file {
                        var file = NuroFile(NuroFileType.image, _it_file.url, nil)
                        file.uri = _it_file.uri
                        if let __it_file_image_metadata = _it_file.image_metadata {
                            var fileImageMetadata = NuroImageMetadata()
                            fileImageMetadata.width = __it_file_image_metadata.image_width
                            fileImageMetadata.height = __it_file_image_metadata.image_height
                            fileImageMetadata.prompt = __it_file_image_metadata.image_prompt
                            fileImageMetadata.format = __it_file_image_metadata.image_format
                            var fileMetadata = NuroFileMetadata(fileImageMetadata)
                            file.metadata = fileMetadata
                        }
                        if it.is_referenced == true {
                            referenceInfo.push(file: file)
                        } else {
                            files.push(file)
                        }
                    }
                })
                var msg = NuroUserMessage(
                    chatMessage.id ?? "", text, files.length > 0 ? files : nil, nil,
                    referenceInfo.length > 0 ? referenceInfo : nil)
                msg._conversationManager = conversationManager
                msg.createTime = chatMessage.create_time ?? 0
                msg._rawId = chatMessage.id ?? ""
                msg._messageIndex = msgs.length
                if chatMessage.status == ChatMessageStatus.in_progress {
                    msg.messageStatus = NuroUserMessageStatus.sending
                } else {
                    msg.messageStatus = NuroUserMessageStatus.finished_successfully
                }
                if let _chatMessage_metadata = chatMessage.metadata {
                    msg.metadata = _chatMessage_metadata
                }
                msgs.push(msg)
            } else if _role == "assistant" {
                var reasoning = ""
                var text = ""
                var files: [NuroFile] = []
                chatMessage.content?.content_parts?.forEach({ (it) in
                    if let _it_reasoning_content = it.reasoning_content, _it_reasoning_content.length > 0 {
                        reasoning = reasoning + _it_reasoning_content
                    }
                    if let _it_text = it.text, _it_text.length > 0 {
                        text = text + _it_text.trim()
                    }
                    if let _it_file = it.file {
                        var file = NuroFile(NuroFileType.image, _it_file.url, nil)
                        file.uri = _it_file.uri
                        if let __it_file_image_metadata = _it_file.image_metadata {
                            var fileImageMetadata = NuroImageMetadata()
                            fileImageMetadata.width = __it_file_image_metadata.image_width
                            fileImageMetadata.height = __it_file_image_metadata.image_height
                            fileImageMetadata.prompt = __it_file_image_metadata.image_prompt
                            fileImageMetadata.format = __it_file_image_metadata.image_format
                            var fileMetadata = NuroFileMetadata(fileImageMetadata)
                            file.metadata = fileMetadata
                        }
                        files.push(file)
                    }
                })
                if reasoning.length > 0 {
                    var reasoningMsg = NuroReasoningMessage((chatMessage.id ?? "") + "_reasoning", reasoning)
                    reasoningMsg._conversationManager = conversationManager
                    reasoningMsg.createTime = chatMessage.create_time ?? 0
                    reasoningMsg._rawId = chatMessage.id ?? ""
                    reasoningMsg._messageIndex = msgs.length
                    if text.length > 0 || files.length > 0 {
                        reasoningMsg.messageStatus = NuroReasoningMessageStatus.finished_successfully
                    } else {
                        reasoningMsg.setStatus(chatMessage.status)
                    }
                    if let _chatMessage_metadata = chatMessage.metadata {
                        reasoningMsg.metadata = ChatMessageMetadata(
                            JSONString: _chatMessage_metadata.toJSONString() ?? "{}")
                    }
                    msgs.push(reasoningMsg)
                }
                var assistantMsg: NuroAssistantMessage?
                var isLastTurnMsg = false
                if chatMessage.end_turn == true {
                    isLastTurnMsg = true
                    var canvasMsgList =
                        conversationManager?.conversation.findLOpenNuroCanvasMessage(msgs)
                        ?? conversationManager?.conversation.findLOpenNuroCanvasMessage(
                            conversationManager?.conversation.messages)
                    if let _canvasMsgList = canvasMsgList {
                        _canvasMsgList.forEach({ (it) in
                            it.setMsgStatus(NuroCanvasStatus.end)
                            msgs.push(it)
                        })
                    }
                }
                if text.length > 0 || files.length > 0 || isLastTurnMsg == true {
                    var msg = NuroAssistantMessage(
                        (chatMessage.id ?? "") + "_assistant", name, text, files.length > 0 ? files : nil)
                    msg._conversationManager = conversationManager
                    msg.createTime = chatMessage.create_time ?? 0
                    msg._rawId = chatMessage.id ?? ""
                    msg._messageIndex = msgs.length
                    msg.setStatus(chatMessage.status)
                    if let _chatMessage_metadata = chatMessage.metadata {
                        msg.metadata = ChatMessageMetadata(JSONString: _chatMessage_metadata.toJSONString() ?? "{}")
                    }
                    if isLastTurnMsg {
                        msg.endTurn = true
                    }
                    assistantMsg = msg
                    msgs.push(msg)
                }
                chatMessage.tool_calls?.forEach({ (it) in
                    var itFunc = it._func
                    guard let itFunc = itFunc else {
                        return
                    }
                    if it.type == ChatToolCallType.server_function
                        && NuroSetting.needDisplayServerFunctionMessage == false
                    {
                        return
                    }
                    var jsonrepairSrc = itFunc.arguments ?? ""
                    var jsonrepairResult = NuroUtils.jsonrepair(jsonrepairSrc)
                    if jsonrepairResult == nil {
                        jsonrepairResult = itFunc._lastRepairedArguments ?? jsonrepairSrc
                    } else if jsonrepairResult == "" {
                        jsonrepairResult = itFunc._lastRepairedArguments ?? jsonrepairSrc
                    } else {
                        itFunc._lastRepairedArguments = jsonrepairResult
                    }
                    var toolName = itFunc.name ?? ""
                    // 先统一转成toolCallMessage
                    var toolCall = NuroToolCallMessage(
                        (chatMessage.id ?? "") + "_toolcall_" + (it.id ?? ""), it.id ?? "",
                        it.type?.valueOf() ?? ChatToolCallType.client_function.valueOf(), toolName, jsonrepairResult,
                        itFunc.extra, "")
                    toolCall._conversationManager = conversationManager
                    toolCall.createTime = chatMessage.create_time ?? 0
                    toolCall._rawId = chatMessage.id ?? ""
                    toolCall._messageIndex = msgs.length
                    if type == ConvertType.new_message {
                        toolCall.messageStatus = NuroToolCallMessageStatus.invoking
                        if it.streaming == true {
                            if chatMessage.status == ChatMessageStatus.send_content_stop_status {
                                toolCall.messageStatus = NuroToolCallMessageStatus.streaming_failed
                            } else {
                                toolCall.messageStatus = NuroToolCallMessageStatus.streaming
                            }
                        }
                    } else {
                        toolCall.messageStatus = NuroToolCallMessageStatus.skipped
                    }
                    if toolName.indexOf(NuroSetting.canvasSettings.startNode) >= 0 {
                        // 如果收到一个打开画布的指令，就新建一个画布消息 等到streaming=false的时候，才能新建,否则流失输出会收到多条消息
                        var canvasMsgId = (chatMessage.id ?? "") + "_canvas"
                        var existCanvasMsg =
                            conversationManager?.conversation?.findOpenNuroCanvasMessageById(canvasMsgId, msgs)
                            ?? conversationManager?.conversation?.findOpenNuroCanvasMessageById(
                                canvasMsgId, conversationManager?.conversation.messages)
                        var msg: NuroCanvasMessage? = nil
                        if existCanvasMsg == nil {
                            msg = NuroCanvasMessage(canvasMsgId)
                            msg._conversationManager = conversationManager
                            msg.createTime = chatMessage.create_time ?? 0
                            msg._rawId = chatMessage.id ?? ""
                            msg._messageIndex = msgs.length
                        } else {
                            msg = existCanvasMsg.copy()
                        }
                        if let _chatMessage_metadata = chatMessage.metadata {
                            msg.metadata = ChatMessageMetadata(JSONString: _chatMessage_metadata.toJSONString() ?? "{}")
                        }
                        if type == ConvertType.history {
                            msg.status = NuroCanvasStatus.streaming
                        } else {
                            msg.updateCanvasStatus(NuroCanvasStatus.streaming)
                        }
                        msg.updateStartNode(toolCall)
                        msgs.push(msg)
                    } else if toolName.indexOf(NuroSetting.canvasSettings.endNode) >= 0 {
                        // 如果收到一个关闭画布的指令，找到最近的NuroCanvasMessage,设置它的status为end
                        var msg =
                            conversationManager?.conversation.findLastOpenNuroCanvasMessage(msgs)
                            ?? conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                                conversationManager?.conversation.messages)
                        if let _msg = msg {
                            var update_msg = _msg.copy() as! NuroCanvasMessage
                            _msg.endNode = toolCall
                            update__msg.updateEndNode(toolCall)
                            _msgs.push(update__msg)
                        }
                    } else if NuroSetting.canvasSettings.nodes.some({ (node) in
                        node.indexOf(toolName) >= 0
                    }) {
                        // 如果收到一个插入内容的指令，找到最近的NuroCanvasMessage,执行插入Node操作
                        var msg =
                            conversationManager?.conversation.findLastOpenNuroCanvasMessage(msgs)
                            ?? conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                                conversationManager?.conversation.messages)
                        if let _msg = msg {
                            var update_msg = _msg.copy() as! NuroCanvasMessage
                            var nodeIndex = CANVAS_DEFAULT
                            if let _itFunc_arguments = itFunc.arguments {
                                if NuroUtils.isJSONValid(_itFunc_arguments) {
                                    nodeIndex = JSON.parse(_itFunc_arguments).canvas_content_id ?? CANVAS_DEFAULT
                                } else {
                                    // 有可能被转义影响到了，处理一下, 临时修复逻辑，单独起一个分支处理，减少影响, 后面随着canvas的升级干掉
                                    var repairResult = NuroUtils.completeQuotes(_itFunc_arguments) ?? ""
                                    if NuroUtils.isJSONValid(repairResult) {
                                        nodeIndex = JSON.parse(_itFunc_arguments).canvas_content_id ?? CANVAS_DEFAULT
                                    }
                                }
                            }
                            if nodeIndex != CANVAS_DEFAULT {
                                let targetNode = getNodeFromNuroToolCallMsg(toolCall.id, nodeIndex, toolCall)
                                update__msg.addOrReplaceNode(targetNode)
                                _msgs.push(update__msg)
                            } else {
                                if type == ConvertType.history {
                                    let node = getNodeFromNuroToolCallMsg(toolCall.id, CANVAS_ADD_TO_END, toolCall)
                                    update__msg.addOrReplaceNode(node)
                                    _msgs.push(update__msg)
                                }
                            }
                        } else {
                            assistantMsg?.relateToolCalls.push(toolCall)
                            msgs.push(toolCall)
                        }
                    } else {
                        assistantMsg?.relateToolCalls.push(toolCall)
                        msgs.push(toolCall)
                    }
                })
            } else if _role == "tool" {
                var content_parts = chatMessage.content?.content_parts ?? []
                var toolCallId = chatMessage.metadata?.tool_call_id
                var canvasMessageList =
                    conversationManager?.conversation.findLOpenNuroCanvasMessage(msgs)
                    ?? conversationManager?.conversation.findLOpenNuroCanvasMessage(
                        conversationManager?.conversation.messages)
                var len = content_parts.length
                if len > 0 {
                    if let _toolCallId = toolCallId {
                        var part = content_parts.tsn_safeGet(0)
                        if let _part = part {
                            var toolResult: String? = _part.text
                            // 找到对应的工具调用消息，更新它的结果
                            var updateCanvasMsg: NuroCanvasMessage? = nil
                            if let _canvasMessageList = canvasMessageList {
                                _canvasMessageList.forEach({ (lastCanvasMsg) in
                                    var endNode = lastCanvasMsg.findEndNode(_toolCallId ?? "")
                                    var startNode = lastCanvasMsg.findStartNode(_toolCallId ?? "")
                                    if let _endNode = endNode {
                                        updateCanvasMsg = lastCanvasMsg.copy() as! NuroCanvasMessage
                                        if let _toolResult = toolResult {
                                            _endNode.toolResult = _toolResult
                                        }
                                        updateCanvasMsg.updateEndNode(_endNode.copy())
                                        if chatMessage.status == ChatMessageStatus.finished_successfully {
                                            if type == ConvertType.history {
                                                updateCanvasMsg.status = NuroCanvasStatus.end
                                            } else {
                                                updateCanvasMsg.finish()
                                            }
                                        }
                                        msgs.push(updateCanvasMsg)
                                    } else if let _startNode = startNode {
                                        updateCanvasMsg = lastCanvasMsg.copy() as! NuroCanvasMessage
                                        if let _toolResult = toolResult {
                                            _startNode.toolResult = _toolResult
                                        }
                                        updateCanvasMsg.updateStartNode(_startNode.copy())
                                        msgs.push(updateCanvasMsg)
                                    } else {
                                        var node = lastCanvasMsg.findNodeByToolCallId(_toolCallId ?? "")
                                        if let _node = node {
                                            updateCanvasMsg = lastCanvasMsg.copy() as! NuroCanvasMessage
                                            var targetNode = _node.copy() as! NuroCanvasNode
                                            if let _toolResult = toolResult, _toolResult.trim().indexOf("{") != 0 {
                                                // 不是一个合法的 JSON，兼容处理成 mcp text
                                                var r = MCPToolCallResult()
                                                r.content = [MCPToolCallTextContent.create(_toolResult ?? "")]
                                                targetNode.toolResult = r.toJSONString()
                                            } else {
                                                let firstJson = self.extractFirstJsonObject(toolResult ?? "")
                                                targetNode.toolResult = firstJson ?? ""
                                            }
                                            if chatMessage.status == ChatMessageStatus.in_progress {
                                                targetNode.messageStatus = NuroToolCallMessageStatus.invoking
                                            } else {
                                                targetNode.messageStatus =
                                                    NuroToolCallMessageStatus.finished_successfully
                                            }
                                            updateCanvasMsg.addOrReplaceNode(targetNode)
                                            msgs.push(updateCanvasMsg)
                                        } else {
                                            TSNConsole.log(
                                                "[NuroCanvasMsgToolCall] cannot find node with _toolCallId "
                                                    + _toolCallId)
                                        }
                                    }
                                })
                            }
                            if updateCanvasMsg == nil {
                                var toolMsg = conversationManager?.conversation.findToolCallMessageByToolCallId(
                                    _toolCallId)
                                if let _toolMsg = toolMsg, _toolMsg is NuroToolCallMessage {
                                    let _toolMsgCopy = _toolMsg.copy() as! NuroToolCallMessage
                                    if let _toolResult = toolResult, _toolResult.trim().indexOf("{") != 0 {
                                        // 不是一个合法的 JSON，兼容处理成 mcp text
                                        var r = MCPToolCallResult()
                                        r.content = [MCPToolCallTextContent.create(_toolResult)]
                                        _toolMsgCopy.toolResult = r.toJSONString()
                                    } else {
                                        _toolMsgCopy.toolResult = toolResult
                                    }
                                    if chatMessage.status == ChatMessageStatus.in_progress {
                                        _toolMsgCopy.messageStatus = NuroToolCallMessageStatus.invoking
                                    } else {
                                        _toolMsgCopy.messageStatus = NuroToolCallMessageStatus.finished_successfully
                                    }
                                    msgs.push(_toolMsgCopy)
                                }
                            }
                        }
                    }
                }
            }
        }
        return msgs
    }

    static public func markMessagesAsFinished(_ conversationManager: NuroConversationManager?) {
        guard let conversationManager = conversationManager else {
            return
        }
        conversationManager.conversation.messages.forEach({ (it) in
            if it.isFinalStatus() == false {
                if let it = it as? NuroReasoningMessage {
                    it.setMsgStatus(NuroReasoningMessageStatus.finished_successfully)
                }
                if let it = it as? NuroAssistantMessage {
                    it.setMsgStatus(NuroAssistantMessageStatus.finished_successfully)
                }
            }
        })
    }

    static public func markLastUserMessageAsFinished(_ conversationManager: NuroConversationManager?) {
        guard let conversationManager = conversationManager else {
            return
        }
        conversationManager.conversation.messages.forEach({ (it) in
            if let it = it as? NuroUserMessage {
                if it.isFinalStatus() == false {
                    it.setMsgStatus(NuroUserMessageStatus.finished_successfully)
                }
            }
        })
    }

    static public func markInProgressMessagesAsFailed(_ conversationManager: NuroConversationManager?) {
        guard let conversationManager = conversationManager else {
            return
        }
        var errorMsg = ChatMessageError.send_failed.valueOf()
        conversationManager.conversation.messages.forEach({ (it) in
            if it.isFinalStatus() == false {
                it.errorMsg = errorMsg
                if let it = it as? NuroUserMessage {
                    it.setMsgStatus(NuroUserMessageStatus.failed)
                }
                if let it = it as? NuroReasoningMessage {
                    it.setMsgStatus(NuroReasoningMessageStatus.failed)
                }
                if let it = it as? NuroAssistantMessage {
                    it.setMsgStatus(NuroAssistantMessageStatus.failed)
                }
                if let it = it as? NuroToolCallMessage {
                    if it.messageStatus == NuroToolCallMessageStatus.streaming {
                        it.setMsgStatus(NuroToolCallMessageStatus.streaming_failed)
                    } else {
                        it.setMsgStatus(NuroToolCallMessageStatus.skipped)
                    }
                }
            }
        })
    }

    static public func markInProgressMessagesAsCancel(_ conversationManager: NuroConversationManager?) {
        guard let conversationManager = conversationManager else {
            return
        }
        conversationManager.conversation.messages.forEach({ (it) in
            if it.isFinalStatus() == false {
                if let it = it as? NuroUserMessage {
                    it.setMsgStatus(NuroUserMessageStatus.cancelled)
                }
                if let it = it as? NuroReasoningMessage {
                    it.setMsgStatus(NuroReasoningMessageStatus.cancelled)
                }
                if let it = it as? NuroAssistantMessage {
                    it.setMsgStatus(NuroAssistantMessageStatus.cancelled)
                }
                if let it = it as? NuroToolCallMessage {
                    if it.messageStatus == NuroToolCallMessageStatus.streaming
                        || it.messageStatus == NuroToolCallMessageStatus.streaming_cancelled
                        || it.messageStatus == NuroToolCallMessageStatus.wait_user_response
                    {
                        it.setMsgStatus(NuroToolCallMessageStatus.streaming_cancelled)
                    } else {
                        it.setMsgStatus(NuroToolCallMessageStatus.skipped)
                    }
                }
                if let it = it as? NuroCanvasMessage {
                    it.setMsgStatus(NuroCanvasStatus.cancel)
                }
            }
        })
    }

    static public func isUserMessage(_ message: NuroMessage) -> Bool {
        return message is NuroUserMessage
    }

    static public func isToolCallMessage(_ message: NuroMessage) -> Bool {
        return message is NuroToolCallMessage
    }

    static public func extractFirstJsonObject(_ jsonString: String) -> String {
        var balance = 0
        var startIndex = -1
        for i in 0..<jsonString.length {
            if jsonString.charAt(i) == "{" {
                if balance == 0 {
                    startIndex = i
                }
                balance += 1
            } else if jsonString.charAt(i) == "}" {
                balance--
                if balance == 0 && startIndex != -1 {
                    return jsonString.substring(startIndex, i + 1)
                }
            }
        }
        return ""  // 没有找到完整的 JSON 对象
    }

    public init() {}

}
