import Foundation
//23625ac6df9e5fbb7a59b84de333d5ff
// This file is generated by tsn.
import TSNFoundation

public class NuroUtils {

    static public func randomUUIDString() -> String {
        return UUID().uuidString
    }

    static public func isJSONValid(_ json: String) -> Bool {

        return JSONVerifier.isValid(json)
    }

    static public func stringEndsWith(_ str: String, _ suffix: String) -> Bool {
        if str.length < suffix.length {
            return false
        }
        return str.substr(str.length - suffix.length) == suffix
    }

    // for Canvas做的临时逻辑
    static public func completeQuotes(_ str: String) -> String? {
        let len = str.length
        if len == 0 {
            return ""
        }
        var output: [String] = []
        var inString = false
        for i in 0..<len {
            let char = str.substring(i, i + 1)
            if char == "\"" && (i == 0 || str.substring(i - 1, i) != "\\") {
                if inString {
                    // Potentially the end of a string.
                    // Peek ahead to see if the next non-whitespace character is a valid JSON delimiter.
                    var nextNonWhitespaceChar: String? = nil
                    for j in i + 1..<len {
                        let nextChar = str.substring(j, j + 1)
                        if nextChar != " " && nextChar != "\n" && nextChar != "\r" && nextChar != "\t" {
                            nextNonWhitespaceChar = nextChar
                            break
                        }
                    }
                    if let _nextNonWhitespaceChar = nextNonWhitespaceChar, _nextNonWhitespaceChar != ":",
                        _nextNonWhitespaceChar != "]", _nextNonWhitespaceChar != "}", _nextNonWhitespaceChar != ","
                    {
                        output.push("\\", char)
                    } else {
                        output.push(char)
                        inString = false
                    }
                } else {
                    output.push(char)
                    inString = true
                }
            } else {
                output.push(char)
            }
        }
        if inString {
            output.push("\"")
        }
        let result = output.join("")
        return result
    }

    static public func jsonrepair(_ str: String) -> String? {
        if self.isJSONValid(str) == true {
            return str
        }
        // Heuristic to remove a single trailing comma (Chinese or ASCII)
        // if it's the very last character of the trimmed string.
        var tempStr = str.trim()
        if tempStr.length > 0 && NuroUtils.stringEndsWith(tempStr, ",") == true {
            tempStr = tempStr.substring(0, tempStr.length - 1)
        }
        let parseStr = tempStr
        let len = parseStr.length
        if len == 0 {
            return ""
        }
        var output: [String] = []
        var stack: [String] = []  // Stack will store '{', '[', or '"'
        var inString = false
        for i in 0..<len {
            let char = parseStr.substring(i, i + 1)
            output.push(char)
            if char == "\"" && (i == 0 || parseStr.substring(i - 1, i) != "\\") {
                if inString {
                    if stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "\"" {
                        stack.pop()
                    }
                    inString = false
                } else {
                    stack.push("\"")
                    inString = true
                }
            } else if !inString {
                if char == "{" {
                    stack.push("{")
                } else if char == "[" {
                    stack.push("[")
                } else if char == "}" {
                    if stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "{" {
                        stack.pop()
                    }
                } else if char == "]" {
                    if stack.length > 0 && stack.tsn_safeGet(stack.length - 1) == "[" {
                        stack.pop()
                    }
                }
            }
        }
        while stack.length > 0 {
            let openSymbol = stack.pop()
            if openSymbol == "\"" {
                output.push("\"")
            } else if openSymbol == "{" {
                let currentOutputStr = output.join("")
                if self.stringEndsWith(currentOutputStr, ":") == true {
                    output.push("\"\"")
                }
                output.push("}")
            } else if openSymbol == "[" {
                output.push("]")
            }
        }
        var repairedString = output.join("")
        if self.isJSONValid(repairedString) == true {
            return repairedString
        } else {
            // 处理一下转义
            var quoteRepairedString = self.completeQuotes(repairedString) ?? ""
            if self.isJSONValid(quoteRepairedString) == true {
                return quoteRepairedString
            }
        }
        return nil
    }

    public init() {}

}
