//f6b05ad298fc4d5d4add920938849113
// This file is generated by tsn.
import TSNFoundation

public class NuroSetting {

    /**
       * 是否需要展示 Server 端的 Function 调用消息
       * 如果为 true，则会在 Conversation.messages 中带有对应的 ToolCallMessage。
       */
    static public var needDisplayServerFunctionMessage: Bool = true

    /**
       * 请求版本
       */
    static public var version: String = "3.0.0"

    /**
       *
       */
    static public var canvasSettings: CanvasSettings = [
        "startNode": "",
        "endNode": "",
        "nodes": [],
    ]

    /**
       * agent之间的屏蔽toolCall
       */
    static public var shieldToolCall: [String] = ["handoff_to_planner", "handoff_to_host"]

    public init() {}

}

public protocol CanvasSettings {
    var startNode: String { get set }
    var endNode: String { get set }
    var nodes: [String] { get set }
}
