//046844f93a19431703445fc3ab30b82b
// This file is generated by tsn.
import TSNFoundation

public enum NuroConversationState: String {
    /**
       * The conversation is preparing to send the first message.
       * UI should show a loading indicator, should not allow user to send a message.
       */
    case preparing = "preparing"
    /**
       * The conversation is streaming the response.
       * UI should show the response with streaming, should not allow user to send new message.
       * UI can display a pause button allow user to stop streaming.
       */
    case streamingResponse = "streamingResponse"
    /**
       * The conversation is ready to send the first message.
       * UI should allow the user to send the first message.
       */
    case readyToSendMessage = "readyToSendMessage"
}

extension NuroConversationState {
    public func valueOf() -> String {
        return self.rawValue
    }
}

public class NuroConversation {

    public var conversationId: String

    public var systemPrompt: String?

    public var conversationState: NuroConversationState = NuroConversationState.preparing

    public var messages: [NuroMessage] = []

    public var tasks: [NuroTask] = []

    public var taskChecker: NuroTaskChecker? = nil

    public var summary: String? = nil

    public init(_ conversationId: String) {
        self.conversationId = conversationId
    }

    private var stateUpdateListeners: [String: (NuroConversationState) -> Void] = [:]

    private var messageUpdateListeners: [String: (NuroMessage, NuroMessageOp) -> Void] = [:]

    private var taskUpdateListeners: [String: ([NuroTaskOut]) -> Void] = [:]

    public var systemDataListeners: [String: (SystemData) -> Void] = [:]

    public func addStateUpdateListener(_ listener: @escaping (NuroConversationState) -> Void) -> String {
        let uuid = NuroUtils.randomUUIDString()
        self.stateUpdateListeners[uuid] = listener
        return uuid
    }

    public func removeStateUpdateListener(_ token: String) {
        self.stateUpdateListeners[token] = nil
    }

    public func removeAllStateUpdateListeners() {
        self.stateUpdateListeners = [:]
    }

    public func addSystemDataListener(_ listener: @escaping (SystemData) -> Void) -> String {
        let uuid = NuroUtils.randomUUIDString()
        self.systemDataListeners[uuid] = listener
        return uuid
    }

    public func removeSystemDataListener(_ token: String) {
        self.systemDataListeners[token] = nil
    }

    public func removeSystemDataListeners() {
        self.systemDataListeners = [:]
    }

    public func addMessageUpdateListener(_ listener: @escaping (NuroMessage, NuroMessageOp) -> Void) -> String {
        let uuid = NuroUtils.randomUUIDString()
        self.messageUpdateListeners[uuid] = listener
        return uuid
    }

    public func removeMessageUpdateListener(_ token: String) {
        self.messageUpdateListeners[token] = nil
    }

    public func removeAllMessageUpdateListeners() {
        self.messageUpdateListeners = [:]
    }

    public func addTaskUpdateListener(_ listener: @escaping ([NuroTaskOut]) -> Void) -> String {
        let uuid = NuroUtils.randomUUIDString()
        self.taskUpdateListeners[uuid] = listener
        return uuid
    }

    public func removeTaskUpdateListener(_ token: String) {
        self.taskUpdateListeners[token] = nil
    }

    public func removeAllTaskUpdateListeners() {
        self.taskUpdateListeners = [:]
    }

    public func needResume() -> Bool {
        guard let EventStreamAdapter_reconnectEndpoint = EventStreamAdapter.reconnectEndpoint else {
            return false
        }
        if NuroSetting.version == "3.0.0" {
            // 3.0.0按任务来分
            var length = self.tasks.length
            if length <= 0 {
                return false
            }
            var lastTask = self.tasks.tsn_safeGet(length - 1)
            return lastTask?.taskStatus == NuroTaskStatus.running
        } else {
            var length = self.messages.length
            if length <= 0 {
                return false
            }
            var lastMessage = self.messages.tsn_safeGet(length - 1)
            return lastMessage?.needResume() ?? false
        }
    }

    public func updateState(_ state: NuroConversationState) {
        NuroLogger.info(
            "NuroConversation",
            { () in
                "updateConversationState: state = \(state), conversationId = \(self.conversationId)"
            })
        self.conversationState = state
        TSNMapUtils.forEach(
            self.stateUpdateListeners,
            { (key, listener) in
                listener(state)
            })
    }

    public func updateMessage(_ message: NuroMessage, _ op: NuroMessageOp) {
        NuroLogger.info(
            "NuroConversation",
            { () in
                "updateMessage: conversationId = \(self.conversationId), msgType = \(message.type), id = \(message.id), op = \(op)"
            })
        TSNMapUtils.forEach(
            self.messageUpdateListeners,
            { (key, listener) in
                listener(message, op)
            })
        var messageOut = NuroMessageOut(message, op)
        self.processTask([messageOut], true)
    }

    public func processTask(_ msgOut: [NuroMessageOut], _ needUpdate: Bool = true) {
        let taskChecker = self.taskChecker
        if let _taskChecker = taskChecker {
            var newTasks: [NuroTask] = []
            var currentTask: NuroTask?
            self.messages.forEach({ (it, index) in
                if _taskChecker.isArtifactMessage(it) {
                    if currentTask == nil {
                        let newTask = NuroTask(it.id)
                        currentTask = newTask
                        newTask.taskStatus = NuroTaskStatus.running
                        newTasks.push(newTask)
                    }
                    if _taskChecker.isShieldMessage(it) {
                        currentTask?.addMessage(it, NuroTaskMessageType.shieldMessage)
                    } else {
                        currentTask?.addMessage(it, NuroTaskMessageType.artifactMessage)
                    }
                } else {
                    if let _currentTask = currentTask, _currentTask.artifactMessages.length > 0 {
                        currentTask = nil
                    }
                    if currentTask == nil {
                        let newTask = NuroTask(it.id)
                        currentTask = newTask
                        newTask.taskStatus = NuroTaskStatus.running
                        newTasks.push(newTask)
                    }
                    if let _currentTask = currentTask {
                        if _taskChecker.isPromptMessage(it) == true {
                            if _currentTask.middlewareMessages.length <= 0 {
                                _currentTask.addMessage(it, NuroTaskMessageType.promptMessage)
                            } else {
                                let newTask = NuroTask(it.id)
                                newTask.taskStatus = NuroTaskStatus.running
                                newTask.addMessage(it, NuroTaskMessageType.promptMessage)
                                newTasks.push(newTask)
                                currentTask = newTask
                            }
                        } else {
                            _currentTask.addMessage(it, NuroTaskMessageType.middlewareMessage)
                        }
                    }
                }
                if let _currentTask = currentTask {
                    if it.isCancelledStatus() {
                        _currentTask.taskStatus = NuroTaskStatus.cancelled
                        currentTask = nil
                    } else if it.isFailedStatus() {
                        _currentTask.taskStatus = NuroTaskStatus.failed
                        currentTask = nil
                    } else {
                        if index == self.messages.length - 1 {
                            if let it = it as? NuroAssistantMessage {
                                if it.isFinalTurn() {
                                    _currentTask.taskStatus = NuroTaskStatus.finished
                                }
                            } else if let it = it as? NuroToolCallMessage {
                                if (it as! NuroToolCallMessage)?.isClientToolSkipped() {
                                    _currentTask.taskStatus = NuroTaskStatus.finished
                                }
                            }
                        }
                    }
                }
            })
            newTasks.forEach({ (task) in
                task.recalculateMessageHash()
            })
            let newTasksLength = newTasks.length
            var changeTasks: [NuroTaskOut] = []
            for i in 0..<newTasksLength {
                if i < self.tasks.length {
                    if newTasks[i].messageHash != self.tasks[i].messageHash {
                        self.tasks[i] = newTasks[i]
                        if needUpdate {
                            var taskOut = NuroTaskOut(self.tasks[i], NuroTaskOp.update)
                            changeTasks.push(taskOut)
                        }
                    }
                } else {
                    self.tasks.push(newTasks[i])
                    if needUpdate {
                        var taskOut = NuroTaskOut(self.tasks[self.tasks.length - 1], NuroTaskOp.add)
                        changeTasks.push(taskOut)
                    }
                }
            }
            if self.tasks.length > newTasksLength {
                let tasksToRemoveCount = self.tasks.length - newTasksLength
                var removedTasks = self.tasks.splice(newTasksLength, tasksToRemoveCount)
                removedTasks.forEach({ (removedTask) in
                    if needUpdate {
                        var taskOut = NuroTaskOut(removedTask, NuroTaskOp.delete)
                        changeTasks.push(taskOut)
                    }
                })
            }
            changeTasks.forEach({ (changeTask) in
                changeTask.updateMessage(msgOut)
            })
            TSNMapUtils.forEach(
                self.taskUpdateListeners,
                { (key, listener) in
                    listener(changeTasks)
                })
        }
    }

    public func findToolCallMessageByToolCallId(_ toolCallId: String) -> NuroToolCallMessage? {
        var result: NuroToolCallMessage? = nil
        self.messages.forEach({ (it) in
            if let it = it as? NuroToolCallMessage {
                if it.toolCallId == toolCallId {
                    result = it
                }
            }
        })
        return result
    }

    public func findLastOpenNuroCanvasMessage(_ messageList: [NuroMessage]?) -> NuroCanvasMessage? {
        guard let messageList = messageList else {
            TSNConsole.log(" message list is empty! ")
            return nil
        }
        var result: NuroCanvasMessage? = nil
        var index = messageList.length - 1
        while index >= 0 {
            if messageList.tsn_safeGet(index) is NuroCanvasMessage {
                var message = messageList.tsn_safeGet(index) as! NuroCanvasMessage
                if message.isCanvasOpen() {
                    result = message
                    break
                }
                break
            }
            index--
        }
        return result
    }

    public func findLOpenNuroCanvasMessage(_ messageList: [NuroMessage]?) -> [NuroCanvasMessage]? {
        guard let messageList = messageList else {
            TSNConsole.log(" message list is empty! ")
            return nil
        }
        var result: [NuroCanvasMessage]? = nil
        var index = messageList.length - 1
        while index >= 0 {
            if messageList.tsn_safeGet(index) is NuroCanvasMessage {
                var message = messageList.tsn_safeGet(index) as! NuroCanvasMessage
                if message.isCanvasOpen() {
                    if result == nil {
                        result = []
                    }
                    result.push(message)
                }
            }
            index--
        }
        return result
    }

    public func findOpenNuroCanvasMessageById(_ canvasMsgId: String, _ messageList: [NuroMessage]?)
        -> NuroCanvasMessage?
    {
        guard let messageList = messageList else {
            TSNConsole.log(" message list is empty! ")
            return nil
        }
        var result: NuroCanvasMessage? = nil
        var index = self.messages.length - 1
        while index >= 0 {
            if self.messages.tsn_safeGet(index) is NuroCanvasMessage {
                var message = self.messages.tsn_safeGet(index) as! NuroCanvasMessage
                if message.id == canvasMsgId {
                    result = message
                    break
                }
            }
            index--
        }
        return result
    }

    public func findLastUserMessage() -> NuroUserMessage? {
        var length = self.messages.length
        do {
            var i = length - 1
            var __first__ = true
            while i >= 0 {
                if !__first__ {
                    i = i - 1
                }
                __first__ = false
                if i >= 0 {
                    var msg = self.messages.tsn_safeGet(i)
                    if let msg = msg as? NuroUserMessage {
                        return msg
                    }
                } else {
                    break
                }
            }
        }
        return nil
    }

}
