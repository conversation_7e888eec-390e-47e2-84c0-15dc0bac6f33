import ObjectMapper
//94473c81e3ddaa604dcaa4fcde865174
// This file is generated by tsn.
import TSNFoundation

public class MCPToolCallContent: TSNSerializable {

    public var type: String = "text"

    override public func mapping(map: Map) {
        super.mapping(map: map)
        type <- map["type"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolCallContent.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolCallResource: MCPToolCallContent {

    public var uri: String? = nil

    public var name: String? = nil

    public var text: String? = nil

    public var mimeType: String? = nil

    override public func mapping(map: Map) {
        super.mapping(map: map)
        uri <- map["uri"]
        name <- map["name"]
        text <- map["text"]
        mimeType <- map["mimeType"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolCallResource.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolCallResourceContent: MCPToolCallContent {

    public var resource: MCPToolCallResource? = MCPToolCallResource()

    override public func afterInit() {
        super.afterInit()
        self.type = "resource"
    }

    static public func create(_ uri: String, _ name: String, _ text: String?, _ mimeType: String?)
        -> MCPToolCallResourceContent
    {
        let content = MCPToolCallResourceContent()
        let resource = MCPToolCallResource()
        resource.uri = uri
        resource.name = name
        resource.text = text
        resource.mimeType = mimeType
        content.resource = resource
        return content
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        resource <- map["resource"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolCallResourceContent.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolCallResult: TSNSerializable {

    public var content: [MCPToolCallContent] = []

    override public func afterParse() {
        self.content = self.content.map({ (it) in
            if it.type == "text" {
                return MCPToolCallTextContent(JSONObject: it.rawData)
            } else if it.type == "resource" {
                return MCPToolCallResourceContent(JSONObject: it.rawData)
            } else {
                return it
            }
        })
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        content <- map["content"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolCallResult.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolCallTextContent: MCPToolCallContent {

    public var text: String = ""

    override public func afterInit() {
        super.afterInit()
        self.type = "text"
    }

    static public func create(_ text: String) -> MCPToolCallTextContent {
        let content = MCPToolCallTextContent()
        content.text = text
        return content
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        text <- map["text"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolCallTextContent.self {
            afterParse()
            deferInitAndParse()
        }
    }

}
