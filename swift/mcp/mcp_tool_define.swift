import ObjectMapper
//1be27a9d5ee6a05d49b63a5b04e1c506
// This file is generated by tsn.
import TSNFoundation

public class MCPToolDefine {

    public var name: String

    public var description: String

    public var inputSchema: MCPToolDefineObjectProperty?

    public init(_ name: String, _ description: String, _ inputSchema: MCPToolDefineObjectProperty?) {
        self.name = name
        self.description = description
        self.inputSchema = inputSchema
    }

}

public class MCPToolDefineProperty: TSNSerializable {

    public var type: String = ""

    public var title: String? = nil

    public var description: String? = nil

    public var deprecated: Bool? = nil

    public func defTitle(_ value: String) -> MCPToolDefineProperty {
        self.title = value
        return self
    }

    public func defDescription(_ value: String) -> MCPToolDefineProperty {
        self.description = value
        return self
    }

    public func defDeprecated(_ value: Bool) -> MCPToolDefineProperty {
        self.deprecated = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        type <- map["type"]
        title <- map["title"]
        description <- map["description"]
        deprecated <- map["deprecated"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineObjectProperty: MCPToolDefineProperty {

    public var properties: [String: MCPToolDefineProperty] = [:]

    public var required: [String] = []

    override public func afterInit() {
        super.afterInit()
        self.type = "object"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineObjectProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineObjectProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineObjectProperty {
        self.deprecated = value
        return self
    }

    public func defProperty(_ name: String, _ value: MCPToolDefineProperty) -> MCPToolDefineObjectProperty {
        self.properties[name] = value
        return self
    }

    public func defRequired(_ required: [String]) -> MCPToolDefineObjectProperty {
        self.required = required
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        properties <- map["properties"]
        required <- map["required"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineObjectProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineStringProperty: MCPToolDefineProperty {

    public var _default: String? = nil

    public var examples: [String]? = nil

    public var _enum: [String]? = nil

    public var _const: String? = nil

    public var minLength: Int? = nil

    public var maxLength: Int? = nil

    public var pattern: String? = nil

    override public func afterInit() {
        super.afterInit()
        self.type = "string"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineStringProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineStringProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineStringProperty {
        self.deprecated = value
        return self
    }

    public func defDefault(_ value: String) -> MCPToolDefineStringProperty {
        self._default = value
        return self
    }

    public func defExamples(_ values: [String]) -> MCPToolDefineStringProperty {
        self.examples = values
        return self
    }

    public func defEnum(_ values: [String]) -> MCPToolDefineStringProperty {
        self._enum = values
        return self
    }

    public func defConst(_ value: String) -> MCPToolDefineStringProperty {
        self._const = value
        return self
    }

    public func defMinLength(_ value: Int) -> MCPToolDefineStringProperty {
        self.minLength = value
        return self
    }

    public func defMaxLength(_ value: Int) -> MCPToolDefineStringProperty {
        self.maxLength = value
        return self
    }

    public func defPattern(_ value: String) -> MCPToolDefineStringProperty {
        self.pattern = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        _default <- map["default"]
        examples <- map["examples"]
        _enum <- map["enum"]
        _const <- map["const"]
        minLength <- map["minLength"]
        maxLength <- map["maxLength"]
        pattern <- map["pattern"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineStringProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineIntegerProperty: MCPToolDefineProperty {

    public var _default: Int? = nil

    public var examples: [Int]? = nil

    public var _enum: [Int]? = nil

    public var _const: Int? = nil

    public var minimum: Int? = nil

    public var maximum: Int? = nil

    override public func afterInit() {
        super.afterInit()
        self.type = "integer"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineIntegerProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineIntegerProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineIntegerProperty {
        self.deprecated = value
        return self
    }

    public func defDefault(_ value: Int) -> MCPToolDefineIntegerProperty {
        self._default = value
        return self
    }

    public func defExamples(_ values: [Int]) -> MCPToolDefineIntegerProperty {
        self.examples = values
        return self
    }

    public func defEnum(_ values: [Int]) -> MCPToolDefineIntegerProperty {
        self._enum = values
        return self
    }

    public func defConst(_ value: Int) -> MCPToolDefineIntegerProperty {
        self._const = value
        return self
    }

    public func defMinimum(_ value: Int) -> MCPToolDefineIntegerProperty {
        self.minimum = value
        return self
    }

    public func defMaximum(_ value: Int) -> MCPToolDefineIntegerProperty {
        self.maximum = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        _default <- map["default"]
        examples <- map["examples"]
        _enum <- map["enum"]
        _const <- map["const"]
        minimum <- map["minimum"]
        maximum <- map["maximum"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineIntegerProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineNumberProperty: MCPToolDefineProperty {

    public var _default: Double? = nil

    public var examples: [Double]? = nil

    public var _enum: [Double]? = nil

    public var _const: Double? = nil

    public var minimum: Double? = nil

    public var maximum: Double? = nil

    override public func afterInit() {
        super.afterInit()
        self.type = "number"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineNumberProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineNumberProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineNumberProperty {
        self.deprecated = value
        return self
    }

    public func defDefault(_ value: Double) -> MCPToolDefineNumberProperty {
        self._default = value
        return self
    }

    public func defExamples(_ values: [Double]) -> MCPToolDefineNumberProperty {
        self.examples = values
        return self
    }

    public func defEnum(_ values: [Double]) -> MCPToolDefineNumberProperty {
        self._enum = values
        return self
    }

    public func defConst(_ value: Double) -> MCPToolDefineNumberProperty {
        self._const = value
        return self
    }

    public func defMinimum(_ value: Double) -> MCPToolDefineNumberProperty {
        self.minimum = value
        return self
    }

    public func defMaximum(_ value: Double) -> MCPToolDefineNumberProperty {
        self.maximum = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        _default <- map["default"]
        examples <- map["examples"]
        _enum <- map["enum"]
        _const <- map["const"]
        minimum <- map["minimum"]
        maximum <- map["maximum"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineNumberProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineBooleanProperty: MCPToolDefineProperty {

    public var _default: Bool? = nil

    override public func afterInit() {
        super.afterInit()
        self.type = "boolean"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineBooleanProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineBooleanProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineBooleanProperty {
        self.deprecated = value
        return self
    }

    public func defDefault(_ value: Bool) -> MCPToolDefineBooleanProperty {
        self._default = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        _default <- map["default"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineBooleanProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public class MCPToolDefineArrayProperty: MCPToolDefineProperty {

    public var items: MCPToolDefineProperty? = nil

    public var _enum: [Any]? = nil

    public var maxItems: Int? = nil

    public var minItems: Int? = nil

    override public func afterInit() {
        super.afterInit()
        self.type = "array"
    }

    override public func defDescription(_ value: String) -> MCPToolDefineArrayProperty {
        self.description = value
        return self
    }

    override public func defTitle(_ value: String) -> MCPToolDefineArrayProperty {
        self.title = value
        return self
    }

    override public func defDeprecated(_ value: Bool) -> MCPToolDefineArrayProperty {
        self.deprecated = value
        return self
    }

    public func defItems(_ value: MCPToolDefineProperty) -> MCPToolDefineArrayProperty {
        self.items = value
        return self
    }

    public func defEnum(_ values: [Any]) -> MCPToolDefineArrayProperty {
        self._enum = values
        return self
    }

    public func defMaxItems(_ value: Int) -> MCPToolDefineArrayProperty {
        self.maxItems = value
        return self
    }

    public func defMinItems(_ value: Int) -> MCPToolDefineArrayProperty {
        self.minItems = value
        return self
    }

    override public func mapping(map: Map) {
        super.mapping(map: map)
        items <- map["items"]
        _enum <- map["enum"]
        maxItems <- map["maxItems"]
        minItems <- map["minItems"]
        if map.mappingType == .fromJSON && Swift.type(of: self) == MCPToolDefineArrayProperty.self {
            afterParse()
            deferInitAndParse()
        }
    }

}

public func transformToZodSchema(_ Zod: Any, _ value: MCPToolDefineProperty?, _ isRoot: Bool = true) -> [String: Any] {
    guard let value = value else {
        return [:]
    }
    return [:]
}
