# NuroSDK 用户文档

本文档面向 NuroSDK 的使用者，旨在帮助您快速理解 SDK 的核心概念、如何在您的项目中集成 SDK，以及如何使用 SDK 与大语言模型（LLM）进行交互。

如果你希望开发 NuroSDK，请参考 [NuroSDK 开发指南](./DEV.md)。

## SDK ChangeLog & Features

### 最新版本

- Web @byted/nurosdk-js@0.0.2-alpha.2
- iOS NuroSDK,0.0.1-alpha.12
- Android com.bytedance:nurosdk:0.0.1-alpha.16

已支持功能列表：
- 会话管理
    - 创建会话
    - 连接到底层通信传输层（例如 SSE）
    - 会话恢复（从服务端获取 ConversationJSON 恢复一个 Conversation 数据）
    - 中断输出（用户主动要求 Host Agent 停止输出）
- 消息发送
    - 图片消息
    - 消息重发（用户主动要求重发消息）
    - 文本消息
    - 消息 Payload 绑定
- 响应来自 Host Agent 的消息
    - 思考消息
    - 文本消息
    - 工具调用消息
- 工具调用分发
    - 设备端 MCP 服务注册
    - 设备端 MCP Tool Call 消息处理及结果自动回传
    - 并发的工具调用

待支持功能列表：
- 会话管理
    - 会话重连（断网、Host Agent 异常时，自动重连）
- 消息发送
    - 文件消息
    - 消息重发后的树状数据结构

## SDK 示例代码

### 各平台示例代码

示例代码位于 test 目录下，包含以下几个示例工程：

- **`test/agent_client`**: 一个基于 TypeScript 的 Web/Node.js 示例项目，演示如何在 Web 或 Node.js 环境中使用 NuroSDK JS 库与 Host Agent 进行交互。
- **`test/agent_client_android`**: 一个 Android 示例项目，演示如何在 Android 应用中集成和使用 NuroSDK Kotlin 库。
- **`test/agent_client_ios`**: 一个 iOS 示例项目，演示如何在 iOS 应用中集成和使用 NuroSDK Swift 库。
- **`test/agent_host`**: 一个基于 TypeScript 的 Node.js 项目，模拟了一个简单的 Host Agent 服务端，用于配合客户端示例进行本地测试。

### Host Agent 测试服务

见 https://bytedance.larkoffice.com/docx/AnVSdDCixoxEE1xVhe1cuzvWnig

简单来说，你只需要设置 ```const transport = new SSETransport("http://************:8888/sse", {});``` 即可开始测试，不要从零开始部署 Host Agent 服务。

该 Host Agent 服务已支持本 SDK 说明的全部功能。

同时， Host Agent 提供 Web UI Demo 可在 Chrome 中在线体验。

## SDK 概念说明

NuroSDK 的核心设计围绕着“会话”（Conversation）展开，模拟了用户与 LLM 助手之间的交互流程。以下是几个关键概念：

### 1. NuroConversationManager (会话管理器)

`NuroConversationManager` 是您与 NuroSDK 交互的入口点。它负责管理整个会话的生命周期和配置。

- **职责**: 
    - 创建和管理会话 (`NuroConversation`)。
    - 连接到底层通信传输层（例如 SSE）。
    - 发送用户消息 (`NuroUserMessage`)。
    - 接收来自 LLM 的消息（`NuroAssistantMessage`, `NuroReasoningMessage`, `NuroToolCallMessage`）。
    - （可选）管理客户端工具调用 (MCP - Model Context Protocol)。
- **类比**: 可以将其想象成一个聊天应用的控制器，它处理消息的发送、接收和状态管理。

### 2. NuroConversation (会话)

`NuroConversation` 代表一个具体的对话实例。它包含了该会话的所有消息记录和当前状态。

- **职责**:
    - 持有会话中的所有消息 (`NuroMessage` 数组)。
    - 维护会话的当前状态 (`NuroConverstaionState`)，例如 `preparing`（准备中）、`readyToSendMessage`（准备好发送消息）、`streamingResponse`（正在接收流式响应）。
    - 提供监听器接口，允许您在会话状态或消息列表发生变化时收到通知。
- **类比**: 可以将其视为一个聊天窗口，里面包含了所有的聊天记录和当前的聊天状态。

### 3. NuroMessage (消息)

`NuroMessage` 是所有消息类型的基类，代表会话中的单条信息。它有几个重要的子类：

- **`NuroUserMessage` (用户消息)**: 代表用户发送给 LLM 的消息。可以包含文本 (`text`) 和文件 (`files`)。
- **`NuroAssistantMessage` (助手消息)**: 代表 LLM 返回给用户的消息。通常包含 LLM 生成的文本 (`text`)，也可能包含文件 (`files`)。
- **`NuroReasoningMessage` (思考过程消息)**: 代表 LLM 在生成最终回复之前的中间思考过程或状态更新。通常用于向用户展示 LLM 正在“思考”或执行某个步骤。
- **`NuroToolCallMessage` (工具调用消息)**: 代表 LLM 请求调用一个外部工具（Function Calling）。这可以是服务端定义的工具，也可以是客户端（您的 App）定义的工具 (MCP)。
    - `toolName`: 要调用的工具名称。
    - `toolArgs`: 调用工具所需的参数 (JSON 字符串)。
    - `toolResult`: 工具执行后的结果 (JSON 字符串)。如果是客户端工具，您需要执行相应的逻辑，并将结果通过 `sendToolCallResult` 或 `sendToolResultMessages` 方法发送回去。
- **类比**: 每条 `NuroMessage` 就如同聊天记录中的一条消息，根据发送者和内容的不同，区分为不同的类型。

### 关系图

```mermaid
graph LR
    User --> AppUI[应用界面]
    AppUI --> ConversationManager[NuroConversationManager]
    ConversationManager -- 创建/管理 --> Conversation[NuroConversation]
    ConversationManager -- 发送消息 --> Transport[通信传输层 SSE]
    Transport -- 接收消息 --> ConversationManager
    ConversationManager -- 更新 --> Conversation
    Conversation -- 持有 --> Messages[NuroMessage 列表]
    Messages -- 包含 --> UserMsg[NuroUserMessage]
    Messages -- 包含 --> AssistantMsg[NuroAssistantMessage]
    Messages -- 包含 --> ReasoningMsg[NuroReasoningMessage]
    Messages -- 包含 --> ToolCallMsg[NuroToolCallMessage]
    Conversation -- 通知更新 --> AppUI
```

### 上层业务应怎么渲染 Messages

上层业务（您的 App UI）在收到 `addMessageUpdateListener` 回调时，需要根据 `NuroMessage` 的具体类型和状态来更新界面。以下是一些常见的渲染建议：

1.  **`NuroUserMessage` (用户消息)**:
    *   **核心状态 (`status`)**: `NuroMessageStatus` 枚举值 (`none`, `in_progress`, `finished_successfully`, `send_failed`)。
    *   **UI 渲染建议**: 
        *   `sending` (发送中): 当 `status` 为 `in_progress` 时。可以在消息旁显示加载指示器。
        *   `sent` (已发送): 当 `status` 为 `finished_successfully` 时。移除加载指示器。
        *   `failed` (发送失败): 当 `status` 为 `send_failed` 时。显示错误提示，提供重试选项。
        *   `uploading_files` (文件上传中): 如果消息包含文件 (`files`) 且正在上传，这通常发生在 `status` 为 `in_progress` 期间。可以在文件附件旁显示上传进度。需要结合 `TOSFileUploadAdapter` 的回调来精确控制。
        *   `upload_failed` (文件上传失败): 文件上传失败通常会导致消息发送失败，即 `status` 变为 `send_failed`。显示错误提示。
    *   **内容 (`text`, `files`)**: 
        *   通常显示在聊天界面的右侧（或用户侧）。
        *   渲染文本内容。
        *   如果包含文件 (`files`)，根据文件类型 (`NuroFile.type`) 显示相应的预览。点击文件可触发查看或下载。

2.  **`NuroAssistantMessage` (助手消息)**:
    *   **核心状态 (`status`)**: `NuroMessageStatus` 枚举值 (`none`, `in_progress`, `finished_successfully`)。
    *   **UI 渲染建议**: 
        *   `streaming` (流式接收中): 当 `status` 为 `in_progress` 时。持续更新 UI 上的文本内容以展示 LLM 逐步生成的回复。
        *   `done` (接收完成): 当 `status` 为 `finished_successfully` 时。流式传输结束。
        *   `failed` (接收失败): SDK 当前没有特定的接收失败状态。如果流式传输中断或出错，消息状态可能停留在 `in_progress` 或根据错误处理逻辑变为 `finished_successfully`（即使内容不完整）。UI 层需要根据具体错误信息或超时逻辑来判断并显示失败状态。
    *   **内容 (`text`, `files`)**: 
        *   通常显示在聊天界面的左侧（或助手侧）。
        *   渲染文本内容。对于 `streaming` 状态，不断追加或替换文本。
        *   如果包含文件，处理方式类似用户消息。

3.  **`NuroReasoningMessage` (思考过程消息)**:
    *   **核心状态 (`status`)**: `NuroMessageStatus` 枚举值 (`none`, `in_progress`, `finished_successfully`)。
    *   **UI 渲染建议**: 
        *   `streaming` (流式接收中): 当 `status` 为 `in_progress` 时。
        *   `done` (接收完成): 当 `status` 为 `finished_successfully` 时。
    *   **内容 (`text`)**: 
        *   这通常不是最终的聊天回复。可以选择不同的 UI 样式（如灰色文本、特定图标）或显示在单独区域。
        *   当 `status` 为 `done` 时，可考虑隐藏、替换或标记为已完成。

4.  **`NuroToolCallMessage` (工具调用消息)**:
    *   **核心状态 (`status`)**: `NuroMessageStatus` 枚举值 (`none`, `in_progress`, `finished_successfully`)。
    *   **UI 渲染建议**: 
        *   `pending` (等待执行): 当消息刚创建，`status` 为 `none` 或 `in_progress`，且工具尚未开始执行时。
        *   `running` (执行中): 当 `status` 为 `in_progress`，且工具（特别是客户端 MCP 工具）正在执行时。可以显示加载指示器。
        *   `done` (执行完成): 当 `status` 为 `finished_successfully`。这通常意味着 `toolResult` 字段已包含有效结果。
        *   `failed` (执行失败): 工具执行失败可能不会直接反映在核心 `status` 上。如果 `toolResult` 包含错误信息，`status` 可能仍是 `finished_successfully`。UI 层需要检查 `toolResult` 内容或依赖 MCP Server 的错误回调来判断失败，并显示错误信息。
    *   **内容 (`toolName`, `toolArgs`, `toolResult`)**: 
        *   通常不是直接的聊天回复。可以显示为特殊消息类型，告知用户正在调用工具。
        *   可根据 `toolName` 和 `toolArgs` 提供具体信息。
        *   当 `status` 为 `done` 时，可选择性展示 `toolResult` 的部分内容或仅显示完成状态。
        *   对于 MCP 工具调用 (`toolType` 为 `client_function`)，SDK 会自动处理调用流程，UI 主要关注状态变化。

**通用建议**: 

*   **唯一 ID (`id`)**: 使用消息的 `id` 作为 UI 元素的 key，便于高效地更新和管理列表。
*   **操作 (`op`)**: `addMessageUpdateListener` 回调提供操作类型 (`add`, `update`, `delete`)，据此执行 UI 操作。
*   **错误处理**: 结合核心 `status` 和具体错误信息（如 `send_failed` 或 `toolResult` 中的错误）提供清晰的用户反馈和恢复操作。
*   **UI 一致性**: 保持不同消息类型渲染风格的一致性，同时又能清晰地区分它们。

### MCP Server 以及端云 MCP Server 的设计

NuroSDK 完全遵循 https://bytedance.larkoffice.com/docx/Jd9FdVXXAoyMjUxD23IcxUyjn0F 的设计，并已融合其中的思想到 SDK 的设计中。

你可以在上层业务创建自己的 MCP Server 实现，然后将其注册到 NuroSDK 中，NuroSDK 会自动在发送消息时，将 MCP Tools 定义带到 Host Agent 中，并在 LLM 要求调用 Client MCP Tools 时，将工具调用转发到本地的 MCP Server 中。

本文档将在各平台使用说明中，详细介绍如何在各平台上创建和注册 MCPServer。

### Host Agent 与 NuroSDK 的关系

NuroSDK 是遵循 https://bytedance.larkoffice.com/wiki/MiDawLLWOizlzrkFyXwceyFcnqL IDL 与 Host Agent 交互的。

该 IDL 是剪映系通用的 Agent 会话 IDL。

## Web 平台使用说明

### 依赖安装

使用 npm 安装 SDK：

```bash
npm install @byted/nurosdk-js@latest
```

### 接入示例

```typescript
import {
  NuroAssistantMessage,
  NuroConversationManager,
  NuroMCPManager,
  NuroMCPServerConfig,
  NuroReasoningMessage,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
  EventStreamAdapter, // 引入 Adapter
  TOSFileUploadAdapter, // 引入 Adapter
  NuroFile, // 用于文件消息
  NuroFileType, // 用于文件消息
  NuroLocalFile, // 用于文件消息
  // 假设你有一个 TestLocationMCPServer 实现
  // import { TestLocationMCPServer } from "./test_mcp_server"; 
} from "@byted/nurosdk-js";

// --- 1. 注入能力 ---
// NuroSDK 允许您注入自定义的网络请求和文件上传实现。

// EventStreamAdapter: 要求上层业务实现 SSE 请求的方法
// 您需要提供一个 fetch 方法来处理 SSE 连接。
EventStreamAdapter.fetch = (config) => { 
  console.log("[Web] EventStreamAdapter.fetch called with config:", config);
  // ... 实现 SSE 请求逻辑，例如使用 fetch API 或 EventSource ... 
  // 需要在收到数据块时调用 config.onChunk(chunk)
  // 在连接结束时调用 config.onFinish()
  // 在发生错误时调用 config.onError(code, message)
  const cancelToken = "your_cancel_token"; // 返回一个可用于取消请求的标识符
  return cancelToken; 
};
EventStreamAdapter.cancel = (token) => { 
  console.log("[Web] EventStreamAdapter.cancel called with token:", token);
  // ... 实现取消 SSE 请求的逻辑 ... 
};

// TOSFileUploadAdapter: 要求上层业务实现 TOS 上传的方法
// 您需要提供一个 upload 方法来处理文件上传。
TOSFileUploadAdapter.upload = (config) => { 
  console.log("[Web] TOSFileUploadAdapter.upload called with config:", config);
  // ... 实现文件上传逻辑，例如使用 TOS SDK 或 fetch API ...
  // 需要在上传成功时调用 config.onSuccess(fileId, url)
  // 在上传失败时调用 config.onError(message)
  // (可选) 在上传过程中调用 config.onProgress(loaded, total)
  const uploadId = "your_upload_id"; // 返回一个可用于取消上传的标识符
  return uploadId; 
};
TOSFileUploadAdapter.cancel = (id) => { 
  console.log("[Web] TOSFileUploadAdapter.cancel called with id:", id);
  // ... 实现取消文件上传的逻辑 ... 
};

// --- 2. 初始化会话 ---
const conversationManager = new NuroConversationManager();
// 确保替换为你的 Host Agent SSE 地址
const transport = new SSETransport("http://your-host-agent-sse-endpoint", {}); 
conversationManager.connect(transport);

const mcpManager = new NuroMCPManager();
const locationServerInstance = new TestLocationMCPServer();
// "location" 是您为这个 MCP Server 定义的唯一名称
const locationServerConfig = new NuroMCPServerConfig("location", locationServerInstance);
mcpManager.registerServer(locationServerConfig);

// 将 MCP Manager 注入到会话管理器中
conversationManager.mcpManager = mcpManager;
// 启用 MCP 功能
conversationManager.enableMCPTools();

// --- 4. 添加更新回调 ---
conversationManager.conversation.addStateUpdateListener((state) => {
  console.log(`[Web] Conversation state updated: ${state}`);
  // 更新 UI 状态，例如显示/隐藏加载指示器、启用/禁用输入框
});

conversationManager.conversation.addMessageUpdateListener((message, op) => {
  console.log(`[Web] Message ${op}: ${message.id}, type: ${message.type}`);
  // 根据消息类型和操作 (add/update/delete) 更新 UI 消息列表
  if (message instanceof NuroUserMessage) {
    console.log(`  User: ${message.text}, files: ${message.files?.length ?? 0}, status: ${message.status}`);
  } else if (message instanceof NuroAssistantMessage) {
    console.log(`  Assistant: ${message.text}, files: ${message.files?.length ?? 0}, status: ${message.status}`);
  } else if (message instanceof NuroReasoningMessage) {
    console.log(`  Reasoning: ${message.text}, status: ${message.status}`);
  } else if (message instanceof NuroToolCallMessage) {
    console.log(
      `  Tool Call: ${message.toolName}, Args: ${message.toolArgs}, Result: ${message.toolResult}, Status: ${message.status}`
    );
  }
});

// --- 5. 发送文本消息 ---
function askSomething(text: string) {
  const userMessage = new NuroUserMessage(
    NuroUtils.randomUUIDString(), // 为每条消息生成唯一 ID
    text
  );
  console.log(`[Web] Sending text message: ${userMessage.id}`);
  conversationManager.sendUserMessage(userMessage);
}

// --- 6. 发送图文消息 ---
function sendWithFile(text: string, file: File) {
  // 1. 创建 NuroLocalFile 对象，包含文件名和 File 对象
  const localFile = new NuroLocalFile(file.name, file);
  // 2. 创建 NuroFile 对象，指定文件类型 (例如 image)，并关联 NuroLocalFile
  //    这里的 fileId (第一个参数) 通常在上传成功后由 TOSFileUploadAdapter 提供，
  //    但在发送前可以为空，SDK 会在上传成功后自动填充。
  const nuroFile = new NuroFile(NuroFileType.image, undefined, localFile);
  // 3. 创建包含文本和 NuroFile 数组的 NuroUserMessage
  const userMessage = new NuroUserMessage(NuroUtils.randomUUIDString(), text, [nuroFile]);
  console.log(`[Web] Sending message with file: ${userMessage.id}`);
  // 4. 发送消息，SDK 会自动处理文件上传（如果配置了 TOSFileUploadAdapter）
  conversationManager.sendUserMessage(userMessage);
}

// --- 示例调用 ---
askSomething("你好，请介绍一下你自己。");

// 假设有一个文件输入 <input type="file" id="fileInput" />
// const fileInput = document.getElementById('fileInput') as HTMLInputElement;
// fileInput.onchange = (event) => {
//   const file = (event.target as HTMLInputElement).files?.[0];
//   if (file) {
//     sendWithFile("这是我发送的图片", file);
//   }
// };

```

### 设备侧 MCP Server 定义举例

首先需要先通过 npm install @modelcontextprotocol/sdk 安装 MCP SDK。

然后，我们可以定义一个 MCP Server 来处理 `get_user_location` 工具调用。

```typescript
import { NuroMCPClientAdapter, NuroMCPToolItem } from "@byted/nurosdk-js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { Client as McpClient } from "@modelcontextprotocol/sdk/client/index.js";
import { InMemoryTransport } from "@modelcontextprotocol/sdk/inMemory.js";
import { z } from "zod";

const server = new McpServer({
  name: "location",
  version: "1.0.0",
});

server.tool(
  "get_user_location",
  "获取用户的地理位置",
  {
    reason: z.string({ description: "获取用户的地理位置原因" }),
  },
  async ({ reason: string }) => ({
    content: [{ type: "text", text: `The user location is 「佛山」.` }],
  }) // 你可以在这里得到 reason 的值，这个值是 LLM 生成的，你可以在这里使用它，然后返回对应的结果。
);

// 实现 NuroMCPClientAdapter 接口，建议直接 Copy 下面的代码即可。
export class TestLocationMCPServer implements NuroMCPClientAdapter {
  client: McpClient;

  constructor() {
    const [clientTransport, serverTransport] =
      InMemoryTransport.createLinkedPair();
    server.connect(serverTransport);
    const client = new McpClient({
      name: "location",
      version: "1.0.0",
    });
    this.client = client;
    client.connect(clientTransport);
  }

  async listTools(callback: (tools: NuroMCPToolItem[]) => void) {
    const tools = (await this.client.listTools()).tools;
    callback(
      tools.map((tool) => {
        return new NuroMCPToolItem(
          "location",
          tool.name,
          tool.description ?? "",
          JSON.stringify(tool.inputSchema),
          JSON.stringify(tool)
        );
      })
    );
  }

  callTool(
    toolCallId: string,
    toolName: string,
    toolArgs: string,
    callback: (result: string) => void
  ): void {
    this.client
      .callTool({
        name: toolName,
        arguments: (() => {
          try {
            return JSON.parse(toolArgs);
          } catch (error) {
            return {};
          }
        })(),
      })
      .then((result) => {
        callback(JSON.stringify(result));
      });
  }
}
```

之后，我们可以注册到 NuroConversationManager 中。

```typescript
const mcpManager = new NuroMCPManager();
const locationServerInstance = new TestLocationMCPServer();
// "location" 是您为这个 MCP Server 定义的唯一名称
const locationServerConfig = new NuroMCPServerConfig("location", locationServerInstance);
mcpManager.registerServer(locationServerConfig);
// 将 MCP Manager 注入到会话管理器中
conversationManager.mcpManager = mcpManager;
// 启用 MCP 功能
conversationManager.enableMCPTools();
```

然后，LLM 就可以在适当的时候调用 `get_user_location` 工具了。

## iOS 平台使用说明

### 依赖安装

使用 CocoaPods 将 NuroSDK 添加到您的 `Podfile`。

```ruby
platform :ios, '13.0' # 根据您的项目要求设置
use_frameworks!

target 'YourAppTarget' do
  pod 'NuroSDK', '0.0.1-alpha.12', :source => '******************:iOS_Library/lv-ios_source_repo.git'
end
```

或者添加到 Seer 文件

```yaml
- NuroSDK (0.0.1-alpha.12, from '******************:iOS_Library/lv-ios_source_repo.git')
```

然后运行 `pod install` 或 `jojo install`。

### 接入示例

```swift
import UIKit
import NuroSDK // 确保已导入

class ChatViewController: UIViewController {

    var conversationManager: NuroConversationManager?
    var locationServer: MCPLiteServer? // 如果需要 MCP

    override func viewDidLoad() {
        super.viewDidLoad()
        setupNuroSDK()
        askSomething(text: "你好，请介绍一下你自己。")
    }

    func setupNuroSDK() {
        // --- 1. 注入能力 ---
        // EventStreamAdapter: 要求上层业务实现 SSE 请求的方法
        EventStreamAdapter.fetch = { config in 
            // ... 实现 SSE 请求逻辑，例如使用 URLSession 或 Alamofire ...
            // 需要调用 config.onChunk, config.onFinish, config.onError
            return "your_cancel_token" // 返回取消标识
        }
        EventStreamAdapter.cancel = { token in
            print("[iOS] EventStreamAdapter.cancel called")
            // ... 实现取消 SSE 请求 ...
        }

        // TOSFileUploadAdapter: 要求上层业务实现 TOS 上传的方法
        TOSFileUploadAdapter.upload = { config in
            // ... 实现文件上传逻辑
            // 需要调用 config.onSuccess, config.onError, config.onProgress
            return "your_upload_id" // 返回取消标识
        }
        TOSFileUploadAdapter.cancel = { id in
            print("[iOS] TOSFileUploadAdapter.cancel called")
            // ... 实现取消文件上传 ...
        }

        // --- 2. 初始化会话 ---
        let conversationManager = NuroConversationManager()
        // 确保替换为你的 Host Agent SSE 地址
        let transport = SSETransport("http://your-host-agent-sse-endpoint", [:]) 
        conversationManager.connect(transport)

        // --- 3. 定义和初始化 MCP 服务器 (如果需要) ---
        installLocalMCPServer() // 假设此方法创建并配置 MCPLiteServer
        if let server = self.locationServer {
            let mcpManager = NuroMCPManager()
            // "user" 是您为这个 MCP Server 定义的唯一名称
            mcpManager.registerServer(NuroMCPServerConfig("location", server))
            
            // 将 MCP Manager 注入到会话管理器中
            conversationManager.mcpManager = mcpManager
            // 启用 MCP 功能
            conversationManager.enableMCPTools()
        }

        // --- 4. 添加更新回调 ---
        conversationManager.conversation.addStateUpdateListener { [weak self] state in
            DispatchQueue.main.async {
                print("[iOS] Conversation state updated: \(state)")
                // 更新 UI 状态
            }
        }
        conversationManager.conversation.addMessageUpdateListener { [weak self] (message, op) in
            DispatchQueue.main.async {
                print("[iOS] Message \(op): \(message.id), type: \(message.type)")
                // 更新 UI 消息列表
                switch message {
                case let userMessage as NuroUserMessage:
                    print("  User: \(userMessage.text ?? ""), files: \(userMessage.files?.count ?? 0), status: \(userMessage.status)")
                case let assistantMessage as NuroAssistantMessage:
                    print("  Assistant: \(assistantMessage.text ?? ""), files: \(assistantMessage.files?.count ?? 0), status: \(assistantMessage.status)")
                case let reasoningMessage as NuroReasoningMessage:
                    print("  Reasoning: \(reasoningMessage.text), status: \(reasoningMessage.status)")
                case let toolCallMessage as NuroToolCallMessage:
                    print("  Tool Call: \(toolCallMessage.toolName), Args: \(toolCallMessage.toolArgs ?? ""), Result: \(toolCallMessage.toolResult ?? ""), Status: \(toolCallMessage.status)")
                    // --- 7. 响应工具回调 (结合 MCP 回调) ---
                    // NuroSDK 会自动处理已注册的 MCP 工具调用
                    if toolCallMessage.isMCPToolCall && toolCallMessage.toolResult == nil && toolCallMessage.status == .pending {
                        print("[iOS] MCP Tool call \(toolCallMessage.toolName) is being handled by registered MCP server.")
                    } else if !toolCallMessage.isMCPToolCall {
                        print("[iOS] Server-side tool call \(toolCallMessage.toolName) received.")
                        // 处理服务端工具调用逻辑 (如果需要)
                    }
                default:
                    break
                }
            }
        }
        
        self.conversationManager = conversationManager
    }

    // --- 5. 发送文本消息 ---
    func askSomething(text: String) {
        guard let conversationManager = self.conversationManager else { return }
        let userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text, nil)
        print("[iOS] Sending text message: \(userMessage.id)")
        conversationManager.sendUserMessage(userMessage)
    }
    
    // --- 6. 发送图文消息 ---
    // func sendWithImage(text: String, image: UIImage) {
    //     guard let conversationManager = self.conversationManager else { return }
    //     guard let imageData = image.jpegData(compressionQuality: 0.8) else { return }
    //     
    //     // 1. 创建 NuroLocalFile
    //     let localFile = NuroLocalFile(name: "image.jpg", data: imageData)
    //     // 2. 创建 NuroFile
    //     let nuroFile = NuroFile(type: .image, fileId: nil, localFile: localFile)
    //     // 3. 创建 NuroUserMessage
    //     let userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text, [nuroFile])
    //     print("[iOS] Sending message with image: \(userMessage.id)")
    //     // 4. 发送消息
    //     conversationManager.sendUserMessage(userMessage)
    // }
}
```

### 设备侧 MCP Server 定义举例

不要安装官方提供的 swift-sdk，它对 iOS 的版本要求过高。

在 NuroSDK 中提供了一个极简版本的 MCPLiteServer，目前仅支持定义工具和处理工具回调。

```swift
import NuroSDK // 确保已导入

func installLocalMCPServer() {
    let s = MCPLiteServer(name: "location", version: "1.0.0")
    s.tool(name: "get_location",
            description: "获取用户的当前位置",
            inputSchema: JSONSchema.object(
            properties: ["reason": .string(description: "获取位置的原因")],
            required: ["reason"])
    ) { params, resultCallback in
        print("[MCP Server iOS] Received call to get_location with params:", params)
        // 解析参数
        let reason = params["reason"]?.stringValue ?? "Unknown reason"
        print("[MCP Server iOS] Reason: \(reason)")
        // 执行获取位置的逻辑 (模拟)
        DispatchQueue.global().asyncAfter(deadline: .now() + 0.5) { 
            let result = MCPLiteServer.ToolResult(content: [
                .init(type: "text", text: "The user current location is「佛山」", resource: nil)
            ])
            // 回调结果
            resultCallback(result)
        }
    }
    self.locationServer = s
}
```

然后你可以直接在 NuroConversationManager 中注册这个 MCP Server。

```swift
let mcpManager = NuroMCPManager()
mcpManager.registerServer(NuroMCPServerConfig("location", self.locationServer))
conversationManager.mcpManager = mcpManager
conversationManager.enableMCPTools()
```

## Android 平台使用说明

### 依赖安装

在您的 `build.gradle.kts` (app 级别) 文件中添加依赖。

```kotlin
dependencies {
    implementation("com.bytedance:nurosdk:0.0.1-alpha.16")
}
```

确保您的项目根目录的 `settings.gradle.kts` 配置正确（如果是本地模块依赖）。

同步您的 Gradle 项目。

### 接入示例

```kotlin
package com.example.yourapp

import android.net.Uri
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.lifecycle.lifecycleScope
import com.bytedance.nurosdk.*
import io.modelcontextprotocol.kotlin.sdk.* // 如果使用 MCP
import io.modelcontextprotocol.kotlin.sdk.server.* // 如果使用 MCP
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.serialization.json.* // 如果使用 MCP
import okhttp3.* // 示例：用于网络适配
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okio.BufferedSource // 示例：用于网络适配
import java.io.IOException // 示例：用于网络适配
import java.util.UUID

class MainActivity : ComponentActivity() {

    private val conversationManager = NuroConversationManager()
    private lateinit var mcpServer: Server // 如果需要 MCP
    private val okHttpClient = OkHttpClient() // 示例

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // setContentView(R.layout.activity_main) // 设置你的布局

        setupNuroSDK()
        askSomething("你好，请介绍一下你自己。")
    }

    private fun setupNuroSDK() {
        // --- 1. 注入能力 ---
        installNetworkAdapters() // 示例方法

        // --- 2. 初始化会话 ---
        // conversationManager 已在类属性中初始化
        // 确保替换为你的 Host Agent SSE 地址
        val transport = SSETransport("http://your-host-agent-sse-endpoint", mutableMapOf()) 
        conversationManager.connect(transport)

        // --- 3. 定义和初始化 MCP 服务器 (如果需要) ---
        mcpServer = createTheMCPServer() // 假设此方法创建并配置 MCP Server
        val mcpManager = NuroMCPManager()
        // "user" 是您为这个 MCP Server 定义的唯一名称
        // NuroMCPClientAdapterImpl 用于将 mcp-sdk-lite 的 Server 适配给 NuroSDK
        mcpManager.registerServer(NuroMCPClientAdapterImpl(mcpServer, "user").asNuroMCPServerConfig())

        // 将 MCP Manager 注入到会话管理器中
        conversationManager.mcpManager = mcpManager
        // 启用 MCP 功能
        conversationManager.enableMCPTools()

        // --- 4. 添加更新回调 ---
        conversationManager.conversation.addStateUpdateListener { state ->
            lifecycleScope.launch(Dispatchers.Main) {
                println("[Android] Conversation state updated: $state")
                // 更新 UI 状态
            }
        }

        conversationManager.conversation.addMessageUpdateListener { message, op ->
            lifecycleScope.launch(Dispatchers.Main) {
                println("[Android] Message $op: ${message.id}, type: ${message.type}")
                // 更新 UI 消息列表
                when (message) {
                    is NuroUserMessage -> {
                        println("  User: ${message.text}, files: ${message.files?.size ?: 0}, status: ${message.status}")
                    }
                    is NuroAssistantMessage -> {
                        println("  Assistant: ${message.text}, files: ${message.files?.size ?: 0}, status: ${message.status}")
                    }
                    is NuroReasoningMessage -> {
                        println("  Reasoning: ${message.text}, status: ${message.status}")
                    }
                    is NuroToolCallMessage -> {
                        println("  Tool Call: ${message.toolName}, Args: ${message.toolArgs}, Result: ${message.toolResult}, Status: ${message.status}")
                    }
                }
            }
        }
    }

    // --- 5. 发送文本消息 ---
    private fun askSomething(text: String) {
        val userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text)
        println("[Android] Sending text message: ${userMessage.id}")
        conversationManager.sendUserMessage(userMessage)
    }

    // --- 6. 发送图文消息 ---
    // private fun sendWithImage(text: String, imageUri: Uri) {
    //     // 1. 从 Uri 获取 InputStream
    //     contentResolver.openInputStream(imageUri)?.use { inputStream ->
    //         val fileName = "image_${System.currentTimeMillis()}.jpg" // Or get from Uri
    //         // 2. 创建 NuroLocalFile
    //         val localFile = NuroLocalFile(name = fileName, dataStream = inputStream)
    //         // 3. 创建 NuroFile
    //         val nuroFile = NuroFile(type = NuroFileType.Image, fileId = null, localFile = localFile)
    //         // 4. 创建 NuroUserMessage
    //         val userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text, listOf(nuroFile))
    //         println("[Android] Sending message with image: ${userMessage.id}")
    //         // 5. 发送消息
    //         conversationManager.sendUserMessage(userMessage)
    //     } ?: println("[Android] Error: Could not open InputStream for Uri: $imageUri")
    // }

    // --- (可选) 网络适配器实现 (OkHttp 示例) ---
    private fun installNetworkAdapters() {
        // EventStreamAdapter: 要求上层业务实现 SSE 请求的方法
        EventStreamAdapter.fetch = { config ->
            println("[Android] EventStreamAdapter.fetch called")
            val requestBody = config.data?.toRequestBody("application/json".toMediaType())
            val request = Request.Builder()
                .url(config.endpoint)
                .method(config.method, requestBody)
                .headers(Headers.of(config.headers ?: mutableMapOf()))
                .tag(UUID.randomUUID().toString()) // 使用 UUID 作为取消 token 示例
                .build()

            val call = okHttpClient.newCall(request)
            call.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    println("[Android] SSE Error: ${e.message}")
                    // 确保在主线程回调
                    lifecycleScope.launch(Dispatchers.Main) {
                        config.onError?.invoke(-1, e.message ?: "Unknown network error")
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    if (!response.isSuccessful) {
                        println("[Android] SSE Error: ${response.code} ${response.message}")
                        // 确保在主线程回调
                        lifecycleScope.launch(Dispatchers.Main) {
                            config.onError?.invoke(response.code, response.message)
                        }
                        response.close()
                        return
                    }
                    val source: BufferedSource = response.body?.source() ?: run {
                        println("[Android] SSE Error: Response body source is null")
                        // 确保在主线程回调
                        lifecycleScope.launch(Dispatchers.Main) {
                            config.onError?.invoke(-1, "Response body source is null")
                        }
                        response.close()
                        return
                    }
                    try {
                        // Alternative: Read in chunks if the stream is not strictly line-delimited
                        val buffer = ByteArray(1024)
                        while (!source.exhausted()) {
                            val bytesRead = source.read(buffer)
                            if (bytesRead == -1) break
                            val chunk = String(buffer, 0, bytesRead, Charsets.UTF_8)
                            println("[Android] SSE Chunk: $chunk")
                            // 确保在主线程回调
                            lifecycleScope.launch(Dispatchers.Main) {
                                config.onChunk?.invoke(chunk)
                            }
                        }
                        println("[Android] SSE Finished")
                        // 确保在主线程回调
                        lifecycleScope.launch(Dispatchers.Main) {
                            config.onFinish?.invoke()
                        }
                    } catch (e: IOException) {
                        // Handle cancellation or stream closure
                        println("[Android] SSE stream closed or error: ${e.message}")
                        // Optionally call onError if it's an unexpected error
                        // lifecycleScope.launch(Dispatchers.Main) {
                        //     config.onError?.invoke(-1, e.message ?: "Stream error")
                        // }
                    } finally {
                        response.close()
                    }
                }
            })
            return@fetch call.request().tag()?.toString() ?: UUID.randomUUID().toString() // Return the tag as cancel token
        }
        EventStreamAdapter.cancel = { token ->
            // Cancel Network Call
        }

        // TOSFileUploadAdapter: 要求上层业务实现 TOS 上传的方法
        TOSFileUploadAdapter.upload = { config ->
            // 上传文件到 TOS
            UploadLogic {
                config.uri = "tosuri"
                config.url = "http://url"
                config.onFinish?.invoke()
            }        
            return@upload uploadId // 返回上传 ID (用于取消)
            // --- 示例结束 ---
        }
        TOSFileUploadAdapter.cancel = { id ->
            // 取消上传
            config.onCancel?.invoke()
            // --- 示例结束 ---
        }
    }

}
```

### 设备侧 MCP Server 定义举例

定义 MCP Server
```kotlin
val server = MCPLiteServer("user", "1.0.0")
server.tool(
    "get_location",
    "get user current location",
        MCPToolDefineObjectProperty()
            .defProperty(
                "reason",
                MCPToolDefineStringProperty().defDescription("为什么你需要获取用户的地理位置")
            )
            .defRequired(mutableListOf("reason")),
    toolCallHandler = { params, resultCallback ->
        val result = MCPToolCallResult()
        result.content = mutableListOf(
            MCPToolCallTextContent.create("用户当前位置是佛山")
        )
        resultCallback(result)
    }
)
return server
```

然后可以使用 NuroMCPClientAdapterImpl 适配给 NuroConversationManager

```kotlin
conversationManager.mcpManager = NuroMCPManager()
conversationManager.mcpManager?.registerServer(createTheMCPServer().asNuroMCPServerConfig())
conversationManager.enableMCPTools()
```

## 单测

在本工程 unittest 目录下，提供了跨端的测试用例，每次发布版本前，这些用例都会被执行，然后才会发布新版本。

如果你有新的逻辑需要测试，你可以在这个目录下添加新的测试用例。

最基本的用例是通过 `sh test.sh` 执行的。

## Mocker

NuroSDK 提供了灵活的 Mock 机制，可用于在开发测试阶段模拟 LLM 响应。以下是使用步骤：

### 1. 实现 Mock 接口
首先需要实现 <mcsymbol name="INuroMocker" filename="mocker.ts" path="src/nuro/mocker.ts" startline="4" type="interface">INuroMocker</mcsymbol> 接口：

```typescript
class MyMocker implements INuroMocker {
  checkMock(
    userMessage: NuroUserMessage,
    manager: NuroConversationManager
  ): boolean {
    // 在此实现 Mock 判断逻辑
    return userMessage.text.includes("[TEST]"); // 示例：包含 [TEST] 的消息触发 Mock
  }
}
```
### 2. 注册 Mock 实例
在初始化会话之前，将 Mock 实例注册到会话管理器中：
```typescript
const mocker = new MyMocker();
NuroMockManager.setMocker(mocker);
```

### 3. Mock 工作流程
1. 用户发送消息时，SDK 会自动调用 checkMock() 方法
2. 当返回 true 时，SDK 将进入 Mock 模式，不会连接真实 Host Agent
3. 开发者可在 checkMock() 中实现自定义 Mock 响应逻辑