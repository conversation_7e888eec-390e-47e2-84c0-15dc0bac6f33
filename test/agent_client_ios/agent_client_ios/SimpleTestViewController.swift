//
//  ViewController.swift
//  agent_client_ios
//
//  Created by ByteDance on 2025/4/8.
//

import UIKit
import NuroSDK

class ViewController: UIViewController {

    var conversationManager: NuroConversationManager?
    var locationServer: MCPLiteServer?
    
    func installLocalMCPServer() {
        let s = MCPLiteServer(name: "user", version: "1.0.0")
        
        s.tool(name: "get_location",
               description: "get user current location",
               inputSchema: MCPToolDefineObjectProperty()
                .defProperty("reason", MCPToolDefineStringProperty().defDescription("为什么你需要请求用户的地理位置"))
                .defRequired(["reason"])
        ) { params, resultCallback in
            print(params)
            let result = MCPToolCallResult()
            result.content = [
                MCPToolCallTextContent.create("用户当前位置是佛山")
            ]
            DispatchQueue.main.async {
                resultCallback(result)
            }
        }
        self.locationServer = s
    }
    
    func installLogger() {
        NuroLoggerAdapter.info = { (tag, message) in
            print("\(tag) \(message)")
        }
        NuroLoggerAdapter.error = { (tag, message) in
            print("\(tag) \(message)")
        }
        NuroLoggerAdapter.debug = { (tag, message) in
            print("\(tag) \(message)")
        }
        NuroLogger.setLogLevel(.ERROR)
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        NuroSetting.needDisplayServerFunctionMessage = true
        
        installLogger()
        
        let transport = SSETransport("https://7c026eti.cn-boe2-fn.bytedance.net/sse", [:])
        let conversationManager = NuroConversationManager()
        conversationManager.connect(transport)
        
        installLocalMCPServer()
        let mcpManager = NuroMCPManager()
        mcpManager.registerServer(NuroMCPServerConfig("user", locationServer!))
        conversationManager.mcpManager = mcpManager
        conversationManager.enableMCPTools()
        
        conversationManager.conversation.addStateUpdateListener { state  in
            print("Conversation state updated: \(state)")
        }
        conversationManager.conversation.addMessageUpdateListener { (message, op) in
            switch message {
            case let userMessage as NuroUserMessage:
                print("User message: \(userMessage.id), content: \(userMessage.text), status: \(userMessage.messageStatus)")
            case let assistantMessage as NuroAssistantMessage:
                print("Assistant message: \(assistantMessage.id), content: \(assistantMessage.text), status: \(assistantMessage.messageStatus)")
            case let reasoningMessage as NuroReasoningMessage:
                print("Reasoning message: \(reasoningMessage.id), content: \(reasoningMessage.text), status: \(reasoningMessage.messageStatus)")
            case let toolCallMessage as NuroToolCallMessage:
                print("Tool call message: \(toolCallMessage.id), name: \(toolCallMessage.toolName), args: \(toolCallMessage.toolArgs), result: \(toolCallMessage.toolResult), status: \(toolCallMessage.messageStatus)")
            default:
                break
            }
        }
        self.conversationManager = conversationManager
        let userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), "你好，DeepSeek V3，告诉我城市「中华人民共和国华南地区广东省广州市荔湾区逢源街道」的天气怎么样。", nil)
        conversationManager.sendUserMessage(userMessage)
        
        // Do any additional setup after loading the view.
    }


}

