//
//  tos_upload.swift
//  agent_client_ios
//
//  Created by ByteDance on 2025/4/14.
//

import Foundation
import NuroSDK

// Define your uploader endpoint here
let uploaderEndpoint = "https://7c026eti.cn-boe2-fn.bytedance.net" // Example: Replace with your actual endpoint

// Dictionary to keep track of active upload tasks for cancellation
private var activeUploadTasks: [Int: URLSessionDataTask] = [:]
private let taskQueue = DispatchQueue(label: "com.nurosdk.uploadTaskQueue") // Serial queue for thread-safe access to activeUploadTasks

func installTOSUpload() {
    TOSFileUploadAdapter.upload = { c in
        let localPath = c.localFile.localPath
        
        guard let image = c.localFile.localFileObject as? UIImage else {
            c.onError?(-1, "upload failed: localFileObject is not UIImage")
            return "invalid_data_token"
        }
        
        guard let base64String = image.jpegData(compressionQuality: 0.8)?.base64EncodedString() else {
            c.onError?(-1, "upload failed: invalid image")
            return "invalid_endpoint_token"
        }
        
        guard let url = URL(string: uploaderEndpoint + "/upload") else {
            c.onError?(-1, "upload failed: invalid uploader endpoint")
            return "invalid_endpoint_token"
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: Any] = [
            "filename": localPath,
            "filedata": base64String
        ]
        
        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body, options: [])
        } catch {
            c.onError?(-1, "upload failed: could not serialize json body")
            return "json_serialization_error_token"
        }
        
        var task: URLSessionDataTask? // Declare task as an optional
        task = URLSession.shared.dataTask(with: request) { data, response, error in
            // Ensure task is removed from active tasks upon completion or error
            // Use task?.taskIdentifier for safe access
            if let taskIdentifier = task?.taskIdentifier {
                taskQueue.async {
                    activeUploadTasks.removeValue(forKey: taskIdentifier)
                }
            }
            
            if let error = error {
                // Check if the error is due to cancellation
                if (error as NSError).code == NSURLErrorCancelled {
                    // Use task?.taskIdentifier for safe access
                    let taskIDString = task.map { String($0.taskIdentifier) } ?? "unknown"
                    print("Upload task \(taskIDString) was cancelled.")
                    // Optionally call a specific callback for cancellation, e.g., c.onCancel?()
                    // For now, we'll treat it as an error as per original logic if no specific cancel callback exists
                    c.onError?(-1, "upload cancelled")
                } else {
                    c.onError?(-1, "upload failed: \(error.localizedDescription)")
                }
                return
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                c.onError?(-1, "upload failed: invalid response")
                return
            }
            
            if (200...299).contains(httpResponse.statusCode) {
                if let responseData = data, let responseString = String(data: responseData, encoding: .utf8) {
                    // Assuming the response text is the relative path of the uploaded file
                    c.nuroFile.url = uploaderEndpoint + responseString
                    c.onFinish?()
                } else {
                    c.onError?(-1, "upload failed: could not parse response data")
                }
            } else {
                var errorMessage = "upload failed with status code: \(httpResponse.statusCode)"
                if let responseData = data, let errorDetail = String(data: responseData, encoding: .utf8) {
                    errorMessage += " - \(errorDetail)"
                }
                c.onError?(-1, errorMessage)
            }
        }
        
        // Store the task before resuming it, ensure task is not nil
        if let actualTask = task {
            taskQueue.async {
                activeUploadTasks[actualTask.taskIdentifier] = actualTask
            }
            actualTask.resume()
            return String(actualTask.taskIdentifier)
        } else {
            // This case should ideally not happen if dataTask returns a valid task
            c.onError?(-1, "upload failed: could not create upload task")
            return "task_creation_failed_token"
        }
    }
    
    TOSFileUploadAdapter.cancel = { cancelToken in
        guard let taskIdentifier = Int(cancelToken) else {
            print("Invalid cancel token format: \(cancelToken)")
            return
        }
        
        taskQueue.async {
            if let taskToCancel = activeUploadTasks[taskIdentifier] {
                taskToCancel.cancel() // This will trigger the error block in dataTask with NSURLErrorCancelled
                // activeUploadTasks.removeValue(forKey: taskIdentifier) // Task removal is now handled in the dataTask completion
                print("Upload task \(taskIdentifier) cancellation requested.")
            } else {
                print("No active upload task found for token: \(taskIdentifier)")
            }
        }
    }
}
