//
//  jsonrepair_tests.swift
//  agent_client_ios
//
//  Created by ByteDance on 2025/5/15.
//

import Foundation
import NuroSDK

struct TestCase {
    let name: String
    let input: String
    let expected: String
}

func test_jsonrepair() {
    let testCases: [TestCase] = [
        TestCase(name: "Unclosed string value", input: "{\"abcds\":\"abc", expected: "{\"abcds\":\"abc\"}"),
        TestCase(name: "Unclosed number value", input: "{\"abcds\": 123", expected: "{\"abcds\": 123}"),
        TestCase(name: "Streaming JSON - unclosed object", input: "{\"key1\":\"value1\", \"key2\":", expected: "{\"key1\":\"value1\", \"key2\":\"\"}"), // 假设对于未关闭的值使用空字符串
        TestCase(name: "Streaming JSON - unclosed array", input: "[1, 2, \"hello\",", expected: "[1, 2, \"hello\"]"), // 假设数组关闭
        TestCase(name: "Streaming JSON - complex unclosed object", input: "{\"a\": \"b\", \"c\": {\"d\": \"e\", \"f\":", expected: "{\"a\": \"b\", \"c\": {\"d\": \"e\", \"f\":\"\"}}"), // 假设对于嵌套的未关闭值使用空字符串
        TestCase(name: "Streaming JSON - object with trailing comma (ASCII)", input: "{\"key1\":\"value1\", \"key2\":\"value2\",", expected: "{\"key1\":\"value1\", \"key2\":\"value2\"}"),
        TestCase(name: "Streaming JSON - object with trailing comma (Chinese)", input: "{\"key1\":\"value1\", \"key2\":\"value2\",", expected: "{\"key1\":\"value1\", \"key2\":\"value2\"}"),
        TestCase(name: "Streaming JSON - array with trailing comma (Chinese)", input: "[1, 2, 3", expected: "[1, 2, 3]"),
        TestCase(name: "Empty string", input: "", expected: ""),
        TestCase(name: "Already valid JSON", input: "{\"valid\": true}", expected: "{\"valid\": true}"),
        TestCase(name: "JSON with escaped quotes inside string", input: "{\"text\":\"this is a \\\"quoted\\\" string\"", expected: "{\"text\":\"this is a \\\"quoted\\\" string\"}"),
        TestCase(name: "JSON with unclosed string containing escaped quote", input: "{\"text\":\"this is a \\\"quoted\\\" string", expected: "{\"text\":\"this is a \\\"quoted\\\" string\"}"),
        TestCase(name: "Complex nested unclosed JSON - mixed array and object", input: "{\"data\": [{\"id\": 1, \"value\": \"test\", \"details\": {\"status\": \"pending\", \"items\": [10, 20, {\"subItem\": \"subValue\"}]}}, {\"id\": 2, \"value\": \"another\"", expected: "{\"data\": [{\"id\": 1, \"value\": \"test\", \"details\": {\"status\": \"pending\", \"items\": [10, 20, {\"subItem\": \"subValue\"}]}}, {\"id\": 2, \"value\": \"another\"}]}"),
        TestCase(name: "Complex nested unclosed JSON - unclosed inner array", input: "{\"outer\": {\"innerArray\": [1, 2, {\"key\": \"value\"}, ", expected: "{\"outer\": {\"innerArray\": [1, 2, {\"key\": \"value\"}]}}"),
        TestCase(name: "Complex nested unclosed JSON - unclosed inner object", input: "{\"outer\": [\"item1\", {\"innerKey1\": \"innerValue1\", \"innerKey2\":", expected: "{\"outer\": [\"item1\", {\"innerKey1\": \"innerValue1\", \"innerKey2\":\"\"}]}"),
        TestCase(name: "Valid JSON - simple object", input: "{\"name\": \"John Doe\", \"age\": 30, \"isStudent\": false}", expected: "{\"name\": \"John Doe\", \"age\": 30, \"isStudent\": false}"),
        TestCase(name: "Valid JSON - nested object and array", input: "{\"id\": \"001\", \"type\": \"donut\", \"name\": \"Cake\", \"ppu\": 0.55, \"batters\": {\"batter\": [{ \"id\": \"1001\", \"type\": \"Regular\" },{ \"id\": \"1002\", \"type\": \"Chocolate\" }]}, \"topping\": [{ \"id\": \"5001\", \"type\": \"None\" },{ \"id\": \"5002\", \"type\": \"Glazed\" }]}", expected: "{\"id\": \"001\", \"type\": \"donut\", \"name\": \"Cake\", \"ppu\": 0.55, \"batters\": {\"batter\": [{ \"id\": \"1001\", \"type\": \"Regular\" },{ \"id\": \"1002\", \"type\": \"Chocolate\" }]}, \"topping\": [{ \"id\": \"5001\", \"type\": \"None\" },{ \"id\": \"5002\", \"type\": \"Glazed\" }]}"),
        TestCase(name: "Valid JSON - array of objects", input: "[{\"item\": \"apple\", \"price\": 1.0}, {\"item\": \"banana\", \"price\": 0.5}]", expected: "[{\"item\": \"apple\", \"price\": 1.0}, {\"item\": \"banana\", \"price\": 0.5}]"),
        TestCase(name: "String with escaped double quotes", input: "{\"text\":\"He said \\\"Hello World!\\\"\"", expected: "{\"text\":\"He said \\\"Hello World!\\\"\"}"),
        TestCase(name: "String with newline character", input: "{\"message\":\"First line\\nSecond line\"", expected: "{\"message\":\"First line\\nSecond line\"}"),
        TestCase(name: "String with carriage return", input: "{\"message\":\"First line\\rSecond line\"", expected: "{\"message\":\"First line\\rSecond line\"}"),
        TestCase(name: "String with tab character", input: "{\"data\":\"Column1\\tColumn2\"", expected: "{\"data\":\"Column1\\tColumn2\"}"),
        TestCase(name: "String containing unicode characters", input: "{\"unicode_test\":\"\\u0048\\u0065\\u006c\\u006c\\u006f \\u4e16\\u754c\"", expected: "{\"unicode_test\":\"Hello 世界\"}"), // Hello 世界
        TestCase(name: "String with leading/trailing whitespace and escaped chars", input: "{\"padded_string\":\"  \\\"text\\\"  \"", expected: "{\"padded_string\":\"  \\\"text\\\"  \"}")
    ]

    print("--- 正在运行 JSON 修复测试 ---")
    for testCase in testCases {
        print("\n正在测试: \(testCase.name)")
        print("输入:    '\(testCase.input)'")
        // 假设 NuroUtils.jsonrepair 在 Swift 中是这样调用的
        // 如果 NuroUtils 不是一个静态类/单例，您可能需要先实例化它
        if let repairedJson = NuroUtils.jsonrepair(testCase.input) {
            print("修复后: '\(repairedJson)'")
            print("期望值: '\(testCase.expected)'")
            if repairedJson == testCase.expected {
                print("结果: 通过")
            } else {
                // 在 Swift 中，通常使用 print 来输出错误到控制台，
                // 或者使用更专业的日志库
                print("结果: 失败")
            }
        } else {
            print("结果: 失败")
        }
    }
    print("\n--- JSON 修复测试完成 ---")
}

// 要运行测试，您可以调用该函数：
// test_jsonrepair()
