import UIKit
import NuroSDK
import NuroSDK

class AssistantMessageCell: UITableViewCell {

    static let identifier = "AssistantMessageCell"

    private let bubbleView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 18
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let messageLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private var leadingConstraint: NSLayoutConstraint!
    private var trailingConstraint: NSLayoutConstraint!

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        contentView.addSubview(bubbleView)
        bubbleView.addSubview(messageLabel)
        backgroundColor = .clear // Ensure cell background is clear
        selectionStyle = .none
        separatorInset = .init(top: 0, left: 9999, bottom: 0, right: 0)

        setupConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupConstraints() {
        leadingConstraint = bubbleView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8)
        trailingConstraint = bubbleView.trailingAnchor.constraint(lessThanOrEqualTo: contentView.trailingAnchor, constant: -contentView.frame.width * 0.25) // Ensure bubble doesn't stretch too far right
        leadingConstraint.priority = .defaultHigh // Give priority to leading constraint for left alignment

        NSLayoutConstraint.activate([
            bubbleView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            bubbleView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            bubbleView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor, multiplier: 0.75), // Max width for bubble
            leadingConstraint, // Activate leading constraint for left alignment
            trailingConstraint, // Activate trailing constraint

            messageLabel.topAnchor.constraint(equalTo: bubbleView.topAnchor, constant: 10),
            messageLabel.bottomAnchor.constraint(equalTo: bubbleView.bottomAnchor, constant: -10),
            messageLabel.leadingAnchor.constraint(equalTo: bubbleView.leadingAnchor, constant: 12),
            messageLabel.trailingAnchor.constraint(equalTo: bubbleView.trailingAnchor, constant: -12)
        ])
    }

    func configure(with message: NuroAssistantMessage) {
        messageLabel.text = message.text ?? ""
        // Assistant messages typically align left
        bubbleView.backgroundColor = UIColor(red: 229/255, green: 229/255, blue: 234/255, alpha: 1) // Light gray for receiver
        messageLabel.textColor = .black
        bubbleView.layer.maskedCorners = [.layerMaxXMinYCorner, .layerMinXMaxYCorner, .layerMaxXMaxYCorner]

        // Ensure constraints are set for left alignment
        leadingConstraint.isActive = true
        trailingConstraint.isActive = true
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        messageLabel.text = nil
        // Similar to UserMessageCell, deactivation might not be strictly necessary if configure always sets them.
        // leadingConstraint.isActive = false
        // trailingConstraint.isActive = false
    }
}
