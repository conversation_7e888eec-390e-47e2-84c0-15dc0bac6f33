import UIKit
import NuroSDK

class UserMessageCell: UITableViewCell {

    static let identifier = "UserMessageCell"

    private let bubbleView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 18
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let contentStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 8 // Increased spacing for multiple items
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }()

    // Removed single fileImageView

    private let messageLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private var leadingConstraint: NSLayoutConstraint!
    private var trailingConstraint: NSLayoutConstraint!

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        contentView.addSubview(bubbleView)
        bubbleView.addSubview(contentStackView)

        // Add messageLabel to stackView once during initialization
        // Images will be added dynamically before this label in configure(with:)
        contentStackView.addArrangedSubview(messageLabel)

        backgroundColor = .clear
        selectionStyle = .none
        separatorInset = .init(top: 0, left: 9999, bottom: 0, right: 0)

        setupConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupConstraints() {
        leadingConstraint = bubbleView.leadingAnchor.constraint(greaterThanOrEqualTo: contentView.leadingAnchor, constant: contentView.frame.width * 0.25)
        trailingConstraint = bubbleView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8)
        trailingConstraint.priority = .defaultHigh

        NSLayoutConstraint.activate([
            bubbleView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            bubbleView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
            bubbleView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor, multiplier: 0.75),
            trailingConstraint,
            leadingConstraint,

            contentStackView.topAnchor.constraint(equalTo: bubbleView.topAnchor, constant: 10),
            contentStackView.bottomAnchor.constraint(equalTo: bubbleView.bottomAnchor, constant: -10),
            contentStackView.leadingAnchor.constraint(equalTo: bubbleView.leadingAnchor, constant: 12),
            contentStackView.trailingAnchor.constraint(equalTo: bubbleView.trailingAnchor, constant: -12)
        ])
    }

    func configure(with message: NuroUserMessage) {
        // Clear previously added image views before adding new ones
        // Keep messageLabel, remove others if they are UIImageViews
        for arrangedSubview in contentStackView.arrangedSubviews {
            if arrangedSubview is UIImageView {
                contentStackView.removeArrangedSubview(arrangedSubview)
                arrangedSubview.removeFromSuperview()
            }
        }

        messageLabel.text = message.text ?? ""
        messageLabel.isHidden = message.text?.isEmpty ?? true

        // Handle file display for multiple images
        var imageFiles = [NuroFile]()
        if let files = message.files {
            imageFiles = files.filter { $0.type.rawValue == "image" || $0.url?.isImageType() == true }
        }

        if imageFiles.isEmpty {
            // No images to show
        } else {
            for (index, file) in imageFiles.enumerated() {
                let imageView = UIImageView()
                imageView.contentMode = .scaleAspectFit
                imageView.translatesAutoresizingMaskIntoConstraints = false
                imageView.heightAnchor.constraint(lessThanOrEqualToConstant: 200).isActive = true // Max height for each image
                imageView.isHidden = true // Initially hidden until image loads
                
                // Insert image view before the message label
                // The messageLabel is always the last element added in init, so inserting at index `index` works.
                contentStackView.insertArrangedSubview(imageView, at: index)

                if let image = file.localFile?.localFileObject as? UIImage {
                    imageView.image = image
                    imageView.isHidden = false
                } else if let urlString = file.url, let url = URL(string: urlString) {
                    DispatchQueue.global().async {
                        if let data = try? Data(contentsOf: url) {
                            DispatchQueue.main.async {
                                imageView.image = UIImage(data: data)
                                imageView.isHidden = false
                            }
                        } else {
                            print("Failed to load image from URL: \(urlString)")
                            // Optionally, keep it hidden or show a placeholder
                        }
                    }
                }
            }
        }
        
        // If there are no images and no text, the stack view might be empty.
        // The bubbleView will still appear due to its own constraints and padding.
        // Consider hiding bubbleView if contentStackView is truly empty (no visible arranged subviews).
        let hasVisibleContent = !messageLabel.isHidden || imageFiles.count > 0
        bubbleView.isHidden = !hasVisibleContent // Hide bubble if no text and no images

        bubbleView.backgroundColor = UIColor.systemBlue
        messageLabel.textColor = .white
        bubbleView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner, .layerMinXMaxYCorner]
        
        leadingConstraint.isActive = true
        trailingConstraint.isActive = true
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        
        // Remove all dynamically added UIImageViews
        for arrangedSubview in contentStackView.arrangedSubviews {
            if arrangedSubview is UIImageView {
                contentStackView.removeArrangedSubview(arrangedSubview)
                arrangedSubview.removeFromSuperview()
            }
        }
        messageLabel.text = nil
        messageLabel.isHidden = false
        bubbleView.isHidden = false // Reset bubble visibility
    }
}

// Helper extension (optional, can be elsewhere)
extension String {
    func isImageType() -> Bool {
        let imageExtensions = ["jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp"]
        guard let url = URL(string: self) else { return false }
        let pathExtension = url.pathExtension.lowercased()
        return imageExtensions.contains(pathExtension)
    }
}
