import UIKit
import NuroSDK

import PhotosUI // Import PhotosUI for PHPickerViewController

class ChatViewController: UIViewController, UITableViewDataSource, UITableViewDelegate, UITextFieldDelegate, UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    var conversationManager: NuroConversationManager?
    var conversationId: String? // Add conversationId property
    
    func installConversationManager() {
        NuroSetting.needDisplayServerFunctionMessage = true
        
        let setting = loadSettings()
        let hostAgentEndpoint = setting["x-hostagent-endpoint"] ?? ""
        let transport = SSETransport(hostAgentEndpoint.isEmpty ? "https://7c026eti.cn-boe2-fn.bytedance.net/sse" : hostAgentEndpoint, setting)
        let conversationManager = NuroConversationManager()
        conversationManager.connect(transport)
        
        conversationManager.conversation.addStateUpdateListener { [weak self] state  in
            guard let self = self else { return }
            print("Conversation state updated: \(state)")
            DispatchQueue.main.async {
                self.updateUIForConversationState(state)
            }
        }
        conversationManager.conversation.addMessageUpdateListener { (message, op) in
            // Update messages and UI
            self.messages = conversationManager.conversation.messages
            DispatchQueue.main.async {
                self.tableView.reloadData()
                if !self.messages.isEmpty {
                    self.scrollToBottom(animated: true)
                }
            }
        }
        self.conversationManager = conversationManager
    }
    
    private func loadSettings() -> [String: String] {
        guard let jsonData = UserDefaults.standard.data(forKey: "nuro_llm_options") else {
            return [:]
        }
        do {
            if let settingsDict = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] {
                var filterdDict: [String: String] = [:]
                for key in settingsDict.keys {
                    if let v = settingsDict[key] as? String, !v.isEmpty {
                        filterdDict[key] = v
                    }
                    if let v = settingsDict[key] as? [Any], !v.isEmpty, let e = try? JSONSerialization.data(withJSONObject: v) {
                        filterdDict[key] = String(data: e, encoding: .utf8)
                    }
                }
                return filterdDict
            }
        } catch { }
        return [:]
    }
    
    private let tableView: UITableView = {
        let tableView = UITableView()
        tableView.translatesAutoresizingMaskIntoConstraints = false
        tableView.separatorStyle = .none
        tableView.allowsSelection = false
        tableView.register(UserMessageCell.self, forCellReuseIdentifier: UserMessageCell.identifier)
        tableView.register(AssistantMessageCell.self, forCellReuseIdentifier: AssistantMessageCell.identifier)
        tableView.register(ToolCallMessageCell.self, forCellReuseIdentifier: ToolCallMessageCell.identifier)
        tableView.register(ReasoningMessageCell.self, forCellReuseIdentifier: ReasoningMessageCell.identifier)
        return tableView
    }()

    private let messageInputContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white // Changed to white
        view.translatesAutoresizingMaskIntoConstraints = false
        view.layer.cornerRadius = 25 // Rounded corners for the container
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowRadius = 4
        return view
    }()

    private let attachmentButton: UIButton = {
        let button = UIButton(type: .system)
        if #available(iOS 13.0, *) {
            button.setImage(UIImage(systemName: "photo"), for: .normal)
        } else {
            button.setTitle("📎", for: .normal) // Fallback for older iOS versions
        }
        button.tintColor = .gray
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()

    private let messageTextField: UITextField = {
        let textField = UITextField()
        textField.placeholder = "请输入你的想法..."
        textField.borderStyle = .none // Removed border
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.translatesAutoresizingMaskIntoConstraints = false
        return textField
    }()

    private let selectedImagesContainerView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        // view.backgroundColor = .lightGray // For debugging
        return view
    }()

    private let sendButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("发送", for: .normal)
        button.backgroundColor = UIColor.systemBlue // Blue color from image
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        button.layer.cornerRadius = 18 // Rounded corners for the button
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }()

    private var messages: [NuroMessage] = []
    private var selectedImages: [UIImage] = [] // Array to store selected images
    private var selectedImagesContainerViewHeightConstraint: NSLayoutConstraint?
    private var keyboardHeight: CGFloat = 0
    private var currentConversationState: NuroConversationState = .preparing

    private let loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .gray)
        indicator.translatesAutoresizingMaskIntoConstraints = false
        indicator.hidesWhenStopped = true
        return indicator
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        title = "Nuro Agent AppUI"

        // Add navigation bar buttons
        if #available(iOS 13.0, *) {
            navigationItem.leftBarButtonItem = UIBarButtonItem(image: UIImage(systemName: "plus"), style: .plain, target: self, action: #selector(newConversationTapped))
            navigationItem.rightBarButtonItem = UIBarButtonItem(image: UIImage(systemName: "gearshape"), style: .plain, target: self, action: #selector(settingsButtonTapped))
        } else {
            navigationItem.leftBarButtonItem = UIBarButtonItem(title: "+", style: .plain, target: self, action: #selector(newConversationTapped))
            navigationItem.rightBarButtonItem = UIBarButtonItem(title: "设置", style: .plain, target: self, action: #selector(settingsButtonTapped))
        }
        
        installConversationManager()
        
        if let conversationId = conversationId {
            loadConversationData(conversationId: conversationId)
        }

        setupViews()
        setupConstraints()

        tableView.dataSource = self
        tableView.delegate = self
        messageTextField.delegate = self

        // loadMockMessages() // Moved to viewWillAppear
        view.layoutIfNeeded() // Ensure initial layout pass before viewWillAppear
        
        attachmentButton.addTarget(self, action: #selector(attachmentButtonTapped), for: .touchUpInside)
        sendButton.addTarget(self, action: #selector(sendButtonTapped), for: .touchUpInside)
        messageTextField.addTarget(self, action: #selector(textFieldDidChange(_:)), for: .editingChanged)
        updateSendButtonState() // Initial state

        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow(notification:)), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide(notification:)), name: UIResponder.keyboardWillHideNotification, object: nil)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tableView.addGestureRecognizer(tapGesture)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        // Messages will be loaded via NuroConversationManager listener
    }

    @objc private func newConversationTapped() {
        let newChatViewController = ChatViewController()
        if let navigationController = navigationController {
            var viewControllers = navigationController.viewControllers
            if let index = viewControllers.firstIndex(of: self) {
                viewControllers[index] = newChatViewController
                navigationController.setViewControllers(viewControllers, animated: false) // Set to false for immediate replacement without animation
            } else {
                // Fallback if current VC is not in stack (should not happen)
                navigationController.setViewControllers([newChatViewController], animated: true)
            }
        } else {
            // Handle case where ChatViewController is not embedded in a UINavigationController
            // This might involve presenting it modally or replacing the root view controller
            if let window = view.window {
                window.rootViewController = newChatViewController
            }
        }
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    private func setupViews() {
        view.addSubview(tableView)
        view.addSubview(loadingIndicator) // Add loading indicator
        view.addSubview(selectedImagesContainerView) // Add selected images container
        view.addSubview(messageInputContainerView)
        messageInputContainerView.addSubview(attachmentButton)
        messageInputContainerView.addSubview(messageTextField)
        messageInputContainerView.addSubview(sendButton)

        // Initialize and activate the height constraint for selectedImagesContainerView
        selectedImagesContainerViewHeightConstraint = selectedImagesContainerView.heightAnchor.constraint(equalToConstant: 0)
        selectedImagesContainerViewHeightConstraint?.isActive = true
    }

    @objc private func attachmentButtonTapped() {
        let imagePickerController = UIImagePickerController()
        imagePickerController.delegate = self
        imagePickerController.sourceType = .photoLibrary
        present(imagePickerController, animated: true, completion: nil)
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tableView.bottomAnchor.constraint(equalTo: selectedImagesContainerView.topAnchor), // tableView bottom to selectedImagesContainerView top

            selectedImagesContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            selectedImagesContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            selectedImagesContainerView.bottomAnchor.constraint(equalTo: messageInputContainerView.topAnchor), // selectedImagesContainerView bottom to messageInputContainerView top
            // selectedImagesContainerView.heightAnchor.constraint(equalToConstant: 0).isActive = true, // Initial height, will be updated

            loadingIndicator.centerXAnchor.constraint(equalTo: tableView.centerXAnchor),
            loadingIndicator.bottomAnchor.constraint(equalTo: tableView.bottomAnchor, constant: -10), // Position above input but within table view area

            messageInputContainerView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 8),
            messageInputContainerView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -8),
            messageInputContainerView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -8), // Adjusted for padding
            messageInputContainerView.heightAnchor.constraint(equalToConstant: 50), // Adjusted height

            attachmentButton.leadingAnchor.constraint(equalTo: messageInputContainerView.leadingAnchor, constant: 16),
            attachmentButton.centerYAnchor.constraint(equalTo: messageInputContainerView.centerYAnchor),
            attachmentButton.widthAnchor.constraint(equalToConstant: 30),
            attachmentButton.heightAnchor.constraint(equalToConstant: 30),

            messageTextField.leadingAnchor.constraint(equalTo: attachmentButton.trailingAnchor, constant: 8),
            messageTextField.centerYAnchor.constraint(equalTo: messageInputContainerView.centerYAnchor),
            messageTextField.heightAnchor.constraint(equalToConstant: 36),

            sendButton.leadingAnchor.constraint(equalTo: messageTextField.trailingAnchor, constant: 8),
            sendButton.trailingAnchor.constraint(equalTo: messageInputContainerView.trailingAnchor, constant: -12),
            sendButton.centerYAnchor.constraint(equalTo: messageInputContainerView.centerYAnchor),
            sendButton.widthAnchor.constraint(equalToConstant: 70), // Adjusted width
            sendButton.heightAnchor.constraint(equalToConstant: 36) // Adjusted height
        ])
    }


    
    @objc private func sendButtonTapped() {
        if currentConversationState == .streamingResponse {
            conversationManager?.interruptResponse()
            // UI will be updated by the state listener
            return
        }
        
        guard let text = messageTextField.text, (!text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || !selectedImages.isEmpty) else { return }
        let files = selectedImages.map { image in
            let localFile = NuroLocalFile(image.hash.toString() + ".jpg", image)
            return NuroFile(NuroFileType.image, nil, localFile)
        }
        let userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text, files)
        conversationManager?.sendUserMessage(userMessage)
        DispatchQueue.main.async {
            self.messageTextField.text = nil
            self.selectedImages.removeAll()
            self.refreshSelectedImagesView()
            self.updateSendButtonState() // Update button state after sending
        }
    }

    private func scrollToBottom(animated: Bool) {
        guard !messages.isEmpty else { return }
        
        let targetRow = messages.count - 1
        // Ensure the target row is valid for the current state of the table view
        if self.tableView.numberOfSections > 0 && targetRow >= 0 && targetRow < self.tableView.numberOfRows(inSection: 0) {
            let indexPath = IndexPath(row: targetRow, section: 0)
            tableView.scrollToRow(at: indexPath, at: .bottom, animated: animated)
        } else {
            // This log can help if issues persist
            // print("ChatViewController: Scroll to row \(targetRow) aborted. TableView rows: \(self.tableView.numberOfRows(inSection: 0))")
        }
    }

    // MARK: - UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let nuroMessage = messages[indexPath.row]

        if let userMessage = nuroMessage as? NuroUserMessage {
            let cell = tableView.dequeueReusableCell(withIdentifier: "UserMessageCell", for: indexPath) as! UserMessageCell
            cell.configure(with: userMessage)
            return cell
        } else if let assistantMessage = nuroMessage as? NuroAssistantMessage {
            let cell = tableView.dequeueReusableCell(withIdentifier: "AssistantMessageCell", for: indexPath) as! AssistantMessageCell
            cell.configure(with: assistantMessage)
            return cell
        } else if let toolCallMessage = nuroMessage as? NuroToolCallMessage {
            let cell = tableView.dequeueReusableCell(withIdentifier: "ToolCallMessageCell", for: indexPath) as! ToolCallMessageCell
            cell.configure(with: toolCallMessage)
            return cell
        } else if let reasoningMessage = nuroMessage as? NuroReasoningMessage {
            let cell = tableView.dequeueReusableCell(withIdentifier: "ReasoningMessageCell", for: indexPath) as! ReasoningMessageCell
            cell.configure(with: reasoningMessage)
            return cell
        } else {
            // Fallback for any other message types, though ideally all covered
            let cell = UITableViewCell()
            cell.textLabel?.text = "Unsupported message type"
            cell.textLabel?.numberOfLines = 0
            return cell
        }
    }

    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath) -> CGFloat {
        return 50 // Provide an estimated height
    }
    
    // MARK: - UITextFieldDelegate
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        sendButtonTapped()
        return true
    }
    
    // MARK: - Keyboard Handling
    @objc func keyboardWillShow(notification: NSNotification) {
        if let keyboardSize = (notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            keyboardHeight = keyboardSize.height
            updateViewForKeyboard()
        }
    }

    @objc func keyboardWillHide(notification: NSNotification) {
        keyboardHeight = 0
        updateViewForKeyboard()
    }

    private func updateViewForKeyboard() {
        // Calculate the actual bottom offset for the input container
        // When keyboard is hidden, it should be -8 from safe area bottom.
        // When keyboard is shown, it should be -keyboardHeight (which already includes safe area if applicable from system).
        let inputContainerBottomConstant = keyboardHeight > 0 ? -keyboardHeight : -(view.safeAreaInsets.bottom + 8) // Adjusted to include padding
        
        // Update tableView contentInset and scrollIndicatorInsets
        // The inset should be the height of the input container + keyboard height (if any) + padding
        let totalInputAreaHeight = messageInputContainerView.frame.height + 8 // 8 is the bottom padding for container
        let tableViewBottomInset = keyboardHeight > 0 ? keyboardHeight : totalInputAreaHeight
        tableView.contentInset.bottom = tableViewBottomInset
        tableView.verticalScrollIndicatorInsets.bottom = tableViewBottomInset

        // Deactivate old bottom constraint for messageInputContainerView
        view.constraints.filter { $0.firstItem === messageInputContainerView && $0.firstAttribute == .bottom }.forEach { $0.isActive = false }
        
        // Activate new bottom constraint for messageInputContainerView
        if keyboardHeight > 0 {
             messageInputContainerView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -keyboardHeight).isActive = true
        } else {
            messageInputContainerView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -8).isActive = true
        }
        
        view.layoutIfNeeded()
        if keyboardHeight > 0 || !messages.isEmpty { // Scroll to bottom if keyboard is up or if there are messages
            scrollToBottom(animated: true)
        }
    }
    
    @objc func dismissKeyboard() {
        view.endEditing(true)
    }

    // MARK: - UIImagePickerControllerDelegate
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true, completion: nil)
        // Handle the selected image here
        if let selectedImage = info[.originalImage] as? UIImage {
            selectedImages.append(selectedImage)
            refreshSelectedImagesView() // Refresh the view to show thumbnails
            print("Image selected and added. Total: \(selectedImages.count)")
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true, completion: nil)
    }

    @objc private func textFieldDidChange(_ textField: UITextField) {
        updateSendButtonState()
    }

    private func refreshSelectedImagesView() {
        // Clear existing thumbnails
        selectedImagesContainerView.subviews.forEach { $0.removeFromSuperview() }

        if selectedImages.isEmpty {
            // Hide container if no images
            selectedImagesContainerViewHeightConstraint?.constant = 0
            UIView.animate(withDuration: 0.3) { // Animate the height change
                self.view.layoutIfNeeded()
            }
            return
        }

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.alignment = .center
        stackView.distribution = .fillProportionally // Changed to fillProportionally for better sizing
        stackView.translatesAutoresizingMaskIntoConstraints = false
        selectedImagesContainerView.addSubview(stackView)

        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: selectedImagesContainerView.leadingAnchor, constant: 8),
            stackView.trailingAnchor.constraint(lessThanOrEqualTo: selectedImagesContainerView.trailingAnchor, constant: -8), // Allow stack view to not fill entire width if not needed
            stackView.topAnchor.constraint(equalTo: selectedImagesContainerView.topAnchor, constant: 8),
            stackView.bottomAnchor.constraint(equalTo: selectedImagesContainerView.bottomAnchor, constant: -8)
        ])

        let thumbnailHeight: CGFloat = 60 // Define thumbnail height

        for (index, image) in selectedImages.enumerated() {
            let imageView = UIImageView(image: image)
            imageView.contentMode = .scaleAspectFill
            imageView.clipsToBounds = true
            imageView.layer.cornerRadius = 5
            imageView.translatesAutoresizingMaskIntoConstraints = false
            imageView.widthAnchor.constraint(equalToConstant: thumbnailHeight * (image.size.width / image.size.height)).isActive = true // Maintain aspect ratio
            imageView.heightAnchor.constraint(equalToConstant: thumbnailHeight).isActive = true

            let deleteButton = UIButton(type: .custom)
            if #available(iOS 13.0, *) {
                deleteButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
                deleteButton.tintColor = .white
                deleteButton.backgroundColor = UIColor.black.withAlphaComponent(0.5)
            } else {
                deleteButton.setTitle("X", for: .normal)
                deleteButton.setTitleColor(.white, for: .normal)
                deleteButton.backgroundColor = .red
            }
            deleteButton.layer.cornerRadius = 10
            deleteButton.tag = index // Store index to know which image to delete
            deleteButton.addTarget(self, action: #selector(deleteImageTapped(_:)), for: .touchUpInside)
            deleteButton.translatesAutoresizingMaskIntoConstraints = false

            let thumbnailContainer = UIView() // Container for image and delete button
            thumbnailContainer.translatesAutoresizingMaskIntoConstraints = false
            thumbnailContainer.addSubview(imageView)
            thumbnailContainer.addSubview(deleteButton)

            NSLayoutConstraint.activate([
                imageView.leadingAnchor.constraint(equalTo: thumbnailContainer.leadingAnchor),
                imageView.trailingAnchor.constraint(equalTo: thumbnailContainer.trailingAnchor),
                imageView.topAnchor.constraint(equalTo: thumbnailContainer.topAnchor),
                imageView.bottomAnchor.constraint(equalTo: thumbnailContainer.bottomAnchor),

                deleteButton.topAnchor.constraint(equalTo: thumbnailContainer.topAnchor, constant: -5),
                deleteButton.trailingAnchor.constraint(equalTo: thumbnailContainer.trailingAnchor, constant: 5),
                deleteButton.widthAnchor.constraint(equalToConstant: 20),
                deleteButton.heightAnchor.constraint(equalToConstant: 20)
            ])
            stackView.addArrangedSubview(thumbnailContainer)
        }

        // Update container height
        selectedImagesContainerViewHeightConstraint?.constant = thumbnailHeight + 16 // 8 padding top and bottom
        UIView.animate(withDuration: 0.3) { // Animate the height change
            self.view.layoutIfNeeded()
        }
    }

    @objc private func deleteImageTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < selectedImages.count else { return }
        selectedImages.remove(at: index)
        refreshSelectedImagesView()
    }

    private func updateSendButtonState() {
        if currentConversationState == .streamingResponse {
            sendButton.setTitle("停止", for: .normal)
            sendButton.isEnabled = true
            sendButton.alpha = 1.0
        } else {
            let hasText = !(messageTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true)
            let hasImages = !selectedImages.isEmpty
            sendButton.setTitle("发送", for: .normal)
            sendButton.isEnabled = hasText || hasImages
            sendButton.alpha = (hasText || hasImages) ? 1.0 : 0.5 // Visually indicate disabled state
        }
    }

    @objc private func settingsButtonTapped() {
        let settingVC = ChatSettingViewController()
        settingVC.onSettingSaved = { [weak self] in
            guard let setting = self?.loadSettings() else {
                return
            }
            let hostAgentEndpoint = setting["x-hostagent-endpoint"] ?? ""
            let transport = SSETransport(hostAgentEndpoint.isEmpty ? "https://7c026eti.cn-boe2-fn.bytedance.net/sse" : hostAgentEndpoint, setting)
            self?.conversationManager?.connect(transport)
        }
        let navController = UINavigationController(rootViewController: settingVC)
        navController.modalPresentationStyle = .formSheet // Or .pageSheet, .fullScreen
        present(navController, animated: true, completion: nil)
    }

    private func loadConversationData(conversationId: String) {
        // Assuming NuroConversationManager has a method to load or switch to a conversation by ID
        // This is a placeholder for the actual implementation based on NuroSDK's capabilities
        print("Attempting to load conversation with ID: \(conversationId)")
        // Example: conversationManager?.loadConversation(withId: conversationId)
        // After loading, messages should be updated by the NuroConversationManager's listeners
        // If direct fetching is needed, implement it here and update self.messages and tableView
    }
    
    private func updateUIForConversationState(_ state: NuroConversationState) {
        currentConversationState = state
        switch state {
        case .readyToSendMessage:
            sendButton.setTitle("发送", for: .normal)
            let hasText = !(messageTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ?? true)
            let hasImages = !selectedImages.isEmpty
            sendButton.isEnabled = hasText || hasImages
            sendButton.alpha = sendButton.isEnabled ? 1.0 : 0.5
            messageTextField.isEnabled = true
            loadingIndicator.stopAnimating()
        case .streamingResponse:
            sendButton.setTitle("停止", for: .normal)
            sendButton.isEnabled = true
            sendButton.alpha = 1.0
            messageTextField.isEnabled = false
            loadingIndicator.startAnimating()
            // Ensure loading indicator is visible if table view is scrolled down
            if !messages.isEmpty {
                // We might need to adjust tableview's contentInset or scroll to make loadingIndicator visible
                // For now, just starting it. If it's not visible, further adjustments might be needed.
            }
        default:
            // Handle other states if necessary, or keep current UI
            break
        }
        updateSendButtonState() // Re-evaluate send button based on text field and state
    }
}
