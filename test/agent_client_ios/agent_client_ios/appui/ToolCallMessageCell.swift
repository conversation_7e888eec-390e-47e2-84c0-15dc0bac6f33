import UIKit
import NuroSDK

class ToolCallMessageCell: UITableViewCell {
    static let identifier = "ToolCallMessageCell"

    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 10
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.20
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let iconView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(red: 255/255, green: 152/255, blue: 0/255, alpha: 1.0) // Orange color
        view.layer.cornerRadius = 10 // Half of width/height
        view.translatesAutoresizingMaskIntoConstraints = false
        
        let label = UILabel()
        label.text = "T"
        label.textColor = .white
        label.font = UIFont.boldSystemFont(ofSize: 12)
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(label)
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.centerYAnchor.constraint(equalTo: view.centerYAnchor)
        ])
        return view
    }()

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.textColor = UIColor(white: 0.2, alpha: 1.0)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let paramsTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "工具调用参数"
        label.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        label.textColor = .gray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let paramsContentLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.monospacedSystemFont(ofSize: 13, weight: .regular)
        label.textColor = UIColor(white: 0.3, alpha: 1.0)
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let resultTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "工具调用结果"
        label.font = UIFont.systemFont(ofSize: 13, weight: .regular)
        label.textColor = .gray
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    private let resultContentLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14, weight: .regular)
        label.textColor = UIColor(white: 0.1, alpha: 1.0)
        label.numberOfLines = 0
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        backgroundColor = .white // Cell background
        
        separatorInset = .init(top: 0, left: 9999, bottom: 0, right: 0)
        
        contentView.backgroundColor = .white // Match table view background if needed

        contentView.addSubview(containerView)
        containerView.addSubview(iconView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(paramsTitleLabel)
        containerView.addSubview(paramsContentLabel)
        containerView.addSubview(resultTitleLabel)
        containerView.addSubview(resultContentLabel)
        
        setupConstraints()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupConstraints() {
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 8),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 8),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -8),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),

            iconView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            iconView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            iconView.widthAnchor.constraint(equalToConstant: 20),
            iconView.heightAnchor.constraint(equalToConstant: 20),

            titleLabel.centerYAnchor.constraint(equalTo: iconView.centerYAnchor),
            titleLabel.leadingAnchor.constraint(equalTo: iconView.trailingAnchor, constant: 8),
            titleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),

            paramsTitleLabel.topAnchor.constraint(equalTo: iconView.bottomAnchor, constant: 12),
            paramsTitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            paramsTitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),

            paramsContentLabel.topAnchor.constraint(equalTo: paramsTitleLabel.bottomAnchor, constant: 4),
            paramsContentLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            paramsContentLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),

            resultTitleLabel.topAnchor.constraint(equalTo: paramsContentLabel.bottomAnchor, constant: 12),
            resultTitleLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            resultTitleLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),

            resultContentLabel.topAnchor.constraint(equalTo: resultTitleLabel.bottomAnchor, constant: 4),
            resultContentLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 12),
            resultContentLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -12),
            resultContentLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12)
        ])
    }

    func configure(with message: NuroToolCallMessage) {
        titleLabel.text = "Tool Call - \(message.toolName)"
        paramsContentLabel.text = message.toolArgs
        
        if let result = message.toolResult, !result.isEmpty {
            resultTitleLabel.isHidden = false
            resultContentLabel.isHidden = false
            resultContentLabel.text = result
        } else {
            resultTitleLabel.isHidden = true
            resultContentLabel.isHidden = true
        }
    }
}
