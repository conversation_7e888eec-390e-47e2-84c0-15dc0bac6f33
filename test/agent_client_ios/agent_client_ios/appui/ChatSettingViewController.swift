import UIKit

class ChatSettingViewController: UIViewController, UITextViewDelegate {

    public var onSettingSaved: (() -> Void)?
    
    // MARK: - UI Elements

    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        return scrollView
    }()

    private let contentView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        return view
    }()

    private let segmentedControl: UISegmentedControl = {
        let items = ["LLM", "MCP", "Host Agent"]
        let segmentedControl = UISegmentedControl(items: items)
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.translatesAutoresizingMaskIntoConstraints = false
        segmentedControl.addTarget(self, action: #selector(segmentedControlValueChanged(_:)), for: .valueChanged)
        return segmentedControl
    }()

    // LLM Section UI Elements
    private func createLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        label.translatesAutoresizingMaskIntoConstraints = false
        return label
    }

    private func createTextField(placeholder: String) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.borderStyle = .roundedRect
        textField.font = UIFont.systemFont(ofSize: 14)
        textField.translatesAutoresizingMaskIntoConstraints = false
        textField.autocorrectionType = .no
        textField.autocapitalizationType = .none
        return textField
    }
    
    private func createTextView(placeholder: String) -> UITextView {
        let textView = UITextView()
        textView.text = placeholder // Use text for placeholder, manage color via delegate
        textView.font = UIFont.systemFont(ofSize: 14)
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.layer.borderWidth = 1.0
        textView.layer.cornerRadius = 5.0
        textView.translatesAutoresizingMaskIntoConstraints = false
        // Set initial placeholder color
        textView.textColor = .placeholderText
        textView.delegate = self // Set delegate to manage placeholder behavior
        textView.autocorrectionType = .no
        textView.autocapitalizationType = .none
        return textView
    }

    private lazy var modelNameLabel = createLabel(text: "Model Name (x-llm-model)")
    private lazy var modelNameTextField = createTextField(placeholder: "e.g., gpt-4, deepseek-r1")

    private lazy var endpointLabel = createLabel(text: "Endpoint (x-llm-endpoint)")
    private lazy var endpointTextField = createTextField(placeholder: "e.g., https://api.example.com/v1")

    private lazy var apiKeyLabel = createLabel(text: "API Key (x-llm-apikey)")
    private lazy var apiKeyTextField: UITextField = {
        let textField = createTextField(placeholder: "Enter your API key")
        return textField
    }()

    private lazy var systemPromptLabel = createLabel(text: "System Prompt (x-llm-systemprompt)")
    private lazy var systemPromptTextView = createTextView(placeholder: "Enter system prompt here...")

    private lazy var temperatureLabel = createLabel(text: "Temperature (x-llm-temperature)")
    private lazy var temperatureTextField = createTextField(placeholder: "e.g., 0.7 (leave empty for default)")

    // MARK: - MCP Section UI Elements
    private let mcpSectionContainerView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.isHidden = true // Initially hidden
        return view
    }()
    private lazy var mcpServersLabel = createLabel(text: "MCP Servers (x-llm-mcpservers)")

    private let mcpServersStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.translatesAutoresizingMaskIntoConstraints = false
        return stackView
    }()

    private lazy var addMcpServerButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Add Server", for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .systemGreen // Changed color for distinction
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(addMcpServerButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Host Agent Section UI Elements
    private let hostAgentSectionContainerView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        view.isHidden = true // Initially hidden
        return view
    }()
    private lazy var hostAgentUrlLabel = createLabel(text: "Host Agent URL (x-hostagent-url)")
    private lazy var hostAgentUrlTextField = createTextField(placeholder: "e.g., http://localhost:8000")

    // Container for LLM specific fields
    private let llmSectionContainerView: UIView = {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false
        // LLM is visible by default, so isHidden is false
        return view
    }()

    private let cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("取消", for: .normal)
        button.layer.borderColor = UIColor.systemGray2.cgColor
        button.layer.borderWidth = 1
        button.layer.cornerRadius = 8
        button.setTitleColor(.label, for: .normal)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        return button
    }()

    private let saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("保存", for: .normal)
        button.backgroundColor = .systemBlue
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = UIFont.systemFont(ofSize: 16, weight: .semibold)
        button.translatesAutoresizingMaskIntoConstraints = false
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "设置"
        view.backgroundColor = .systemBackground
        setupNavigationBar()
        setupScrollView()
        setupUI()
        setupConstraints()
        loadSettings()
        
        // Add tap gesture to dismiss keyboard
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        view.addGestureRecognizer(tapGesture)
    }

    // MARK: - MCP Server Management

    private func createMcpServerRowView(name: String?, url: String?) -> UIStackView {
        let nameTextField = createTextField(placeholder: "Server Name (e.g., my-tool-server)")
        nameTextField.text = name

        let urlTextField = createTextField(placeholder: "Server URL (e.g., http://localhost:8080)")
        urlTextField.text = url

        let deleteButton = UIButton(type: .system)
        deleteButton.setImage(UIImage(systemName: "trash.fill"), for: .normal)
        deleteButton.tintColor = .systemRed
        deleteButton.addTarget(self, action: #selector(deleteMcpServerRow(sender:)), for: .touchUpInside)
        deleteButton.setContentHuggingPriority(.required, for: .horizontal)
        deleteButton.setContentCompressionResistancePriority(.required, for: .horizontal)

        let rowStackView = UIStackView(arrangedSubviews: [nameTextField, urlTextField, deleteButton])
        rowStackView.axis = .horizontal
        rowStackView.spacing = 8
        rowStackView.distribution = .fill
        // Ensure text fields take up available space and are of equal width, delete button is fixed width
        nameTextField.setContentHuggingPriority(.defaultLow, for: .horizontal)
        urlTextField.setContentHuggingPriority(.defaultLow, for: .horizontal)
        deleteButton.setContentHuggingPriority(.required, for: .horizontal)
        deleteButton.setContentCompressionResistancePriority(.required, for: .horizontal)

        // Add constraint for equal width
        nameTextField.widthAnchor.constraint(equalTo: urlTextField.widthAnchor).isActive = true

        NSLayoutConstraint.activate([
            deleteButton.widthAnchor.constraint(equalToConstant: 30), // Fixed width for delete button
            nameTextField.heightAnchor.constraint(equalToConstant: 40), // Standard text field height
            urlTextField.heightAnchor.constraint(equalToConstant: 40)    // Standard text field height
        ])

        return rowStackView
    }

    private func addMcpServerRow(name: String?, url: String?) {
        let rowView = createMcpServerRowView(name: name, url: url)
        mcpServersStackView.addArrangedSubview(rowView)
    }

    @objc private func addMcpServerButtonTapped() {
        addMcpServerRow(name: nil, url: nil)
        // Scroll to make the new row visible if the content is large
        DispatchQueue.main.async {
            // Ensure layout is updated before calculating content size
            self.view.layoutIfNeeded()
            if self.scrollView.contentSize.height > self.scrollView.bounds.height {
                let bottomOffset = CGPoint(x: 0, y: self.scrollView.contentSize.height - self.scrollView.bounds.height + self.scrollView.contentInset.bottom)
                self.scrollView.setContentOffset(bottomOffset, animated: true)
            }
        }
    }

    @objc private func deleteMcpServerRow(sender: UIButton) {
        guard let rowView = sender.superview as? UIStackView else { return }
        // Animate removal
        UIView.animate(withDuration: 0.25, animations: {
            rowView.alpha = 0 // Fade out
            rowView.isHidden = true // Hide it for stack view to adjust
        }) { _ in
            self.mcpServersStackView.removeArrangedSubview(rowView)
            rowView.removeFromSuperview()
            // Optional: Update layout if needed
            // self.view.layoutIfNeeded()
        }
    }

    private func loadSettings() {
        guard let jsonData = UserDefaults.standard.data(forKey: "nuro_llm_options") else {
            print("No saved settings found.")
            // Set default placeholder for systemPromptTextView if no settings are found
            systemPromptTextView.text = "Enter system prompt here..."
            systemPromptTextView.textColor = .placeholderText
            return
        }

        do {
            if let settingsDict = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] {
                print("Loaded settings: \(settingsDict)")

                // LLM Settings
                modelNameTextField.text = settingsDict["x-llm-model"] as? String
                endpointTextField.text = settingsDict["x-llm-endpoint"] as? String
                apiKeyTextField.text = settingsDict["x-llm-apikey"] as? String
                if let systemPrompt = settingsDict["x-llm-systemprompt"] as? String, !systemPrompt.isEmpty {
                    systemPromptTextView.text = systemPrompt
                    systemPromptTextView.textColor = .label // Or your default text color
                } else {
                    systemPromptTextView.text = "Enter system prompt here..."
                    systemPromptTextView.textColor = .placeholderText
                }
                temperatureTextField.text = settingsDict["x-llm-temperature"] as? String

                // MCP Settings
                // Clear existing server rows before loading new ones
                mcpServersStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

                if let mcpServers = settingsDict["x-llm-mcpservers"] as? [[String: String]] {
                    for server in mcpServers {
                        addMcpServerRow(name: server["name"], url: server["url"])
                    }
                } else {
                    // Add one empty row if no servers are saved, or leave it empty
                    // addMcpServerRow(name: nil, url: nil) 
                }

                // Host Agent Settings
                hostAgentUrlTextField.text = settingsDict["x-hostagent-url"] as? String
                // TODO: Load dynamic headers
            }
        } catch {
            print("Error loading settings from UserDefaults: \(error)")
            // Set default placeholder for systemPromptTextView on error
            systemPromptTextView.text = "Enter system prompt here..."
            systemPromptTextView.textColor = .placeholderText
        }
    }

    // MARK: - Setup
    private func setupNavigationBar() {
        navigationItem.rightBarButtonItem = UIBarButtonItem(barButtonSystemItem: .close, target: self, action: #selector(closeButtonTapped))
    }
    
    private func setupScrollView(){
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        NSLayoutConstraint.activate([
            scrollView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            scrollView.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor),
            
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.bottomAnchor.constraint(equalTo: scrollView.bottomAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor)
        ])
    }

    private func setupUI() {
        contentView.addSubview(segmentedControl)

        // LLM Section Container
        contentView.addSubview(llmSectionContainerView)
        llmSectionContainerView.addSubview(modelNameLabel)
        llmSectionContainerView.addSubview(modelNameTextField)
        llmSectionContainerView.addSubview(endpointLabel)
        llmSectionContainerView.addSubview(endpointTextField)
        llmSectionContainerView.addSubview(apiKeyLabel)
        llmSectionContainerView.addSubview(apiKeyTextField)
        llmSectionContainerView.addSubview(systemPromptLabel)
        llmSectionContainerView.addSubview(systemPromptTextView)
        llmSectionContainerView.addSubview(temperatureLabel)
        llmSectionContainerView.addSubview(temperatureTextField)

        // MCP Section Container
        contentView.addSubview(mcpSectionContainerView)
        mcpSectionContainerView.addSubview(mcpServersLabel)
        mcpSectionContainerView.addSubview(mcpServersStackView)
        mcpSectionContainerView.addSubview(addMcpServerButton)

        // Host Agent Section Container
        contentView.addSubview(hostAgentSectionContainerView)
        hostAgentSectionContainerView.addSubview(hostAgentUrlLabel)
        hostAgentSectionContainerView.addSubview(hostAgentUrlTextField)
        
        contentView.addSubview(cancelButton)
        contentView.addSubview(saveButton)
    }

    private func setupConstraints() {
        let padding: CGFloat = 16
        let textFieldHeight: CGFloat = 40
        let textViewHeight: CGFloat = 100
        let buttonHeight: CGFloat = 44

        NSLayoutConstraint.activate([
            segmentedControl.topAnchor.constraint(equalTo: contentView.safeAreaLayoutGuide.topAnchor, constant: padding),
            segmentedControl.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: padding),
            segmentedControl.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -padding),

            // Section Container Views Constraints
            llmSectionContainerView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: padding),
            llmSectionContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            llmSectionContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            llmSectionContainerView.bottomAnchor.constraint(lessThanOrEqualTo: cancelButton.topAnchor, constant: -padding),

            mcpSectionContainerView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: padding),
            mcpSectionContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            mcpSectionContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            mcpSectionContainerView.bottomAnchor.constraint(lessThanOrEqualTo: cancelButton.topAnchor, constant: -padding),

            hostAgentSectionContainerView.topAnchor.constraint(equalTo: segmentedControl.bottomAnchor, constant: padding),
            hostAgentSectionContainerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            hostAgentSectionContainerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            hostAgentSectionContainerView.bottomAnchor.constraint(lessThanOrEqualTo: cancelButton.topAnchor, constant: -padding),

            // LLM Section Constraints (within llmSectionContainerView)
            modelNameLabel.topAnchor.constraint(equalTo: llmSectionContainerView.topAnchor, constant: padding * 0.5),
            modelNameLabel.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            modelNameLabel.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            modelNameTextField.topAnchor.constraint(equalTo: modelNameLabel.bottomAnchor, constant: padding / 2),
            modelNameTextField.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            modelNameTextField.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            modelNameTextField.heightAnchor.constraint(equalToConstant: textFieldHeight),

            endpointLabel.topAnchor.constraint(equalTo: modelNameTextField.bottomAnchor, constant: padding),
            endpointLabel.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            endpointLabel.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            endpointTextField.topAnchor.constraint(equalTo: endpointLabel.bottomAnchor, constant: padding / 2),
            endpointTextField.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            endpointTextField.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            endpointTextField.heightAnchor.constraint(equalToConstant: textFieldHeight),

            // MCP Section Constraints (within mcpSectionContainerView)
            mcpServersLabel.topAnchor.constraint(equalTo: mcpSectionContainerView.topAnchor, constant: padding * 0.5),
            mcpServersLabel.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            mcpServersLabel.trailingAnchor.constraint(equalTo: mcpSectionContainerView.trailingAnchor, constant: -padding),

            mcpServersStackView.topAnchor.constraint(equalTo: mcpServersLabel.bottomAnchor, constant: padding),
            mcpServersStackView.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            mcpServersStackView.trailingAnchor.constraint(equalTo: mcpSectionContainerView.trailingAnchor, constant: -padding),

            addMcpServerButton.topAnchor.constraint(equalTo: mcpServersStackView.bottomAnchor, constant: padding),
            addMcpServerButton.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            addMcpServerButton.heightAnchor.constraint(equalToConstant: buttonHeight),
            // Ensure addMcpServerButton is not hidden by other elements if mcpServersStackView is empty
            addMcpServerButton.bottomAnchor.constraint(lessThanOrEqualTo: mcpSectionContainerView.bottomAnchor, constant: -padding),

            apiKeyLabel.topAnchor.constraint(equalTo: endpointTextField.bottomAnchor, constant: padding),
            apiKeyLabel.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            apiKeyLabel.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            apiKeyTextField.topAnchor.constraint(equalTo: apiKeyLabel.bottomAnchor, constant: padding / 2),
            apiKeyTextField.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            apiKeyTextField.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            apiKeyTextField.heightAnchor.constraint(equalToConstant: textFieldHeight),

            systemPromptLabel.topAnchor.constraint(equalTo: apiKeyTextField.bottomAnchor, constant: padding),
            systemPromptLabel.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            systemPromptLabel.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            systemPromptTextView.topAnchor.constraint(equalTo: systemPromptLabel.bottomAnchor, constant: padding / 2),
            systemPromptTextView.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            systemPromptTextView.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            systemPromptTextView.heightAnchor.constraint(equalToConstant: textViewHeight),

            temperatureLabel.topAnchor.constraint(equalTo: systemPromptTextView.bottomAnchor, constant: padding),
            temperatureLabel.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            temperatureLabel.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            temperatureTextField.topAnchor.constraint(equalTo: temperatureLabel.bottomAnchor, constant: padding / 2),
            temperatureTextField.leadingAnchor.constraint(equalTo: llmSectionContainerView.leadingAnchor, constant: padding),
            temperatureTextField.trailingAnchor.constraint(equalTo: llmSectionContainerView.trailingAnchor, constant: -padding),
            temperatureTextField.heightAnchor.constraint(equalToConstant: textFieldHeight),
            temperatureTextField.bottomAnchor.constraint(equalTo: llmSectionContainerView.bottomAnchor, constant: -padding), // Pin to bottom of LLM container

            // MCP Section Constraints (within mcpSectionContainerView)
            mcpServersLabel.topAnchor.constraint(equalTo: mcpSectionContainerView.topAnchor, constant: padding * 0.5),
            mcpServersLabel.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            mcpServersLabel.trailingAnchor.constraint(equalTo: mcpSectionContainerView.trailingAnchor, constant: -padding),

            mcpServersStackView.topAnchor.constraint(equalTo: mcpServersLabel.bottomAnchor, constant: padding),
            mcpServersStackView.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            mcpServersStackView.trailingAnchor.constraint(equalTo: mcpSectionContainerView.trailingAnchor, constant: -padding),

        ])



        NSLayoutConstraint.activate([
            addMcpServerButton.topAnchor.constraint(equalTo: mcpServersStackView.bottomAnchor, constant: padding),
            addMcpServerButton.leadingAnchor.constraint(equalTo: mcpSectionContainerView.leadingAnchor, constant: padding),
            addMcpServerButton.trailingAnchor.constraint(equalTo: mcpSectionContainerView.trailingAnchor, constant: -padding),
            addMcpServerButton.heightAnchor.constraint(equalToConstant: buttonHeight),
            addMcpServerButton.bottomAnchor.constraint(lessThanOrEqualTo: mcpSectionContainerView.bottomAnchor, constant: -padding),

            // Host Agent Section Constraints (within hostAgentSectionContainerView)
            hostAgentUrlLabel.topAnchor.constraint(equalTo: hostAgentSectionContainerView.topAnchor, constant: padding * 0.5),
            hostAgentUrlLabel.leadingAnchor.constraint(equalTo: hostAgentSectionContainerView.leadingAnchor, constant: padding),
            hostAgentUrlLabel.trailingAnchor.constraint(equalTo: hostAgentSectionContainerView.trailingAnchor, constant: -padding),
            hostAgentUrlTextField.topAnchor.constraint(equalTo: hostAgentUrlLabel.bottomAnchor, constant: padding / 2),
            hostAgentUrlTextField.leadingAnchor.constraint(equalTo: hostAgentSectionContainerView.leadingAnchor, constant: padding),
            hostAgentUrlTextField.trailingAnchor.constraint(equalTo: hostAgentSectionContainerView.trailingAnchor, constant: -padding),
            hostAgentUrlTextField.heightAnchor.constraint(equalToConstant: textFieldHeight),
            hostAgentUrlTextField.bottomAnchor.constraint(equalTo: hostAgentSectionContainerView.bottomAnchor, constant: -padding), // Pin to bottom of Host Agent container
            
            // Buttons
            cancelButton.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: padding),
            cancelButton.trailingAnchor.constraint(equalTo: contentView.centerXAnchor, constant: -padding/2),
            cancelButton.heightAnchor.constraint(equalToConstant: buttonHeight),
            cancelButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -padding),

            saveButton.leadingAnchor.constraint(equalTo: contentView.centerXAnchor, constant: padding/2),
            saveButton.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -padding),
            saveButton.heightAnchor.constraint(equalToConstant: buttonHeight),
            saveButton.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -padding)
        ])
        
        // Initial section visibility
        updateSectionVisibility()
    }

    // MARK: - Actions
    @objc private func segmentedControlValueChanged(_ sender: UISegmentedControl) {
        updateSectionVisibility()
    }
    
    // MARK: - UITextViewDelegate
    func textViewDidBeginEditing(_ textView: UITextView) {
        if textView.textColor == .placeholderText {
            textView.text = nil
            textView.textColor = .label // Or your default text color
        }
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        if textView.text.isEmpty {
            if textView == systemPromptTextView {
                textView.text = "Enter system prompt here..."
            }
            // Add more else if blocks here if you have other UITextViews with placeholders
            textView.textColor = .placeholderText
        }
    }

    private func updateSectionVisibility() {
        llmSectionContainerView.isHidden = segmentedControl.selectedSegmentIndex != 0
        mcpSectionContainerView.isHidden = segmentedControl.selectedSegmentIndex != 1
        hostAgentSectionContainerView.isHidden = segmentedControl.selectedSegmentIndex != 2
    }

    // MARK: - Actions
    @objc private func closeButtonTapped() {
        dismiss(animated: true, completion: nil)
    }

    @objc private func cancelButtonTapped() {
        // Handle cancel action, e.g., dismiss or reset fields
        print("Cancel button tapped")
        dismiss(animated: true, completion: nil)
    }

    @objc private func saveButtonTapped() {
        var settingsToSave: [String: Any] = [:]

        // LLM Settings
        if let modelName = modelNameTextField.text, !modelName.isEmpty { settingsToSave["x-llm-model"] = modelName }
        if let endpoint = endpointTextField.text, !endpoint.isEmpty { settingsToSave["x-llm-endpoint"] = endpoint }
        if let apiKey = apiKeyTextField.text, !apiKey.isEmpty { settingsToSave["x-llm-apikey"] = apiKey }
        if systemPromptTextView.textColor != .placeholderText, let systemPrompt = systemPromptTextView.text, !systemPrompt.isEmpty {
            settingsToSave["x-llm-systemprompt"] = systemPrompt
        } else {
            settingsToSave["x-llm-systemprompt"] = "" // Save empty string if it was placeholder or empty
        }
        if let temperature = temperatureTextField.text, !temperature.isEmpty { settingsToSave["x-llm-temperature"] = temperature }

        // MCP Settings (Dynamic)
        var mcpServersArray: [[String: String]] = []
        for case let rowStackView as UIStackView in mcpServersStackView.arrangedSubviews {
            if rowStackView.isHidden { continue } // Skip rows marked for deletion
            guard rowStackView.arrangedSubviews.count == 3, // nameField, urlField, deleteButton
                  let nameTextField = rowStackView.arrangedSubviews[0] as? UITextField,
                  let urlTextField = rowStackView.arrangedSubviews[1] as? UITextField else {
                print("Skipping malformed MCP server row.")
                continue
            }
            
            let serverName = nameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
            let serverUrl = urlTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

            if !serverName.isEmpty && !serverUrl.isEmpty {
                mcpServersArray.append(["name": serverName, "url": serverUrl])
            }
        }
        settingsToSave["x-llm-mcpservers"] = mcpServersArray // Always save, even if empty

        // Host Agent Settings
        settingsToSave["x-hostagent-url"] = hostAgentUrlTextField.text ?? ""
        // TODO: Implement dynamic header saving. For now, an empty array or placeholder.
        settingsToSave["x-hostagent-headers"] = [] // Placeholder for headers

        do {
            let jsonData = try JSONSerialization.data(withJSONObject: settingsToSave, options: .prettyPrinted)
            UserDefaults.standard.set(jsonData, forKey: "nuro_llm_options")
            print("Settings saved to UserDefaults: \(settingsToSave)")
        } catch {
            print("Error saving settings to UserDefaults: \(error)")
        }
        
        onSettingSaved?()

        dismiss(animated: true, completion: nil)
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
}
