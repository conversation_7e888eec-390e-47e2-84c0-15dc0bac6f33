//
//  fetch_with_eventsource.swift
//  agent_client_ios
//
//  Created by ByteDance on 2025/4/8.
//

import Foundation
import NuroSDK

var eventSourceInstances: [String: ChunkedURLSession] = [:]
var eventStreamConfig: [String: EventStreamConfig] = [:]

func installEventStream() {
    EventStreamAdapter.fetch = { (c: EventStreamConfig) in
        let session = ChunkedURLSession()
        var request = URLRequest(url: URL(string: c.endpoint)!)
        request.httpMethod = c.method
        request.allHTTPHeaderFields = c.headers
        request.httpBody = c.data?.data(using: .utf8)
        session.startRequest(request: request) { chunk in
            if let onChunk = c.onChunk, let str = String(data: chunk, encoding: String.Encoding.utf8) {
                onChunk(str)
            }
        } onComplete: { error in
            if let onFinish = c.onFinish {
                onFinish()
            }
        }
        let esId = UUID.init().uuidString
        eventSourceInstances[esId] = session
        eventStreamConfig[esId] = c
        return esId
    }
    
    EventStreamAdapter.cancel = { cancelToken in
        if let session = eventSourceInstances[cancelToken] {
            session.stop()
            eventSourceInstances.removeValue(forKey: cancelToken)
            print("EventSource with token \(cancelToken) cancelled and removed.")
        } else {
            print("EventSource with token \(cancelToken) not found for cancellation.")
        }
        if let c = eventStreamConfig[cancelToken] {
            c.onCancel?(-1, "user cancel")
        }
    }
}

class ChunkedURLSession: NSObject {
    typealias ChunkHandler = (_ chunk: Data) -> Void
    typealias CompletionHandler = (_ error: Error?) -> Void

    private var urlSession: URLSession!
    private var dataTask: URLSessionDataTask?
    
    private var onChunk: ChunkHandler?
    private var onComplete: CompletionHandler?
    
    override init() {
        super.init()
        let configuration = URLSessionConfiguration.default
        // 不缓存响应，适合流式接收
        configuration.requestCachePolicy = .reloadIgnoringLocalCacheData
        urlSession = URLSession(configuration: configuration, delegate: self, delegateQueue: nil)
    }

    /// 开始请求并监听数据块
    func startRequest(
        request: URLRequest,
        onChunk: @escaping ChunkHandler,
        onComplete: @escaping CompletionHandler
    ) {
        self.onChunk = onChunk
        self.onComplete = onComplete
        dataTask = urlSession.dataTask(with: request)
        dataTask?.resume()
    }

    /// 停止请求
    func stop() {
        dataTask?.cancel()
        dataTask = nil
    }
}

extension ChunkedURLSession: URLSessionDataDelegate {
    // 每当有数据到达时被调用（可能是 chunk）
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        onChunk?(data)
    }

    // 请求完成或失败时被调用
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        onComplete?(error)
    }
}
