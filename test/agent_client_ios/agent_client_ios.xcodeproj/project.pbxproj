// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		22873A3291F0C6CAA8F69199 /* Pods_agent_client_ios.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 9B360404690A75D7DC252C5C /* Pods_agent_client_ios.framework */; };
		2BBC5CF92DE4604C003399A9 /* ChatViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5CF52DE4604C003399A9 /* ChatViewController.swift */; };
		2BBC5CFC2DE4670E003399A9 /* AssistantMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5CFA2DE4670E003399A9 /* AssistantMessageCell.swift */; };
		2BBC5CFD2DE4670E003399A9 /* UserMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5CFB2DE4670E003399A9 /* UserMessageCell.swift */; };
		2BBC5D002DE467C5003399A9 /* ReasoningMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5CFE2DE467C5003399A9 /* ReasoningMessageCell.swift */; };
		2BBC5D012DE467C5003399A9 /* ToolCallMessageCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5CFF2DE467C5003399A9 /* ToolCallMessageCell.swift */; };
		2BBC5D072DE47532003399A9 /* ChatSettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BBC5D062DE47532003399A9 /* ChatSettingViewController.swift */; };
		2BC3683B2DD632CF002D26F3 /* jsonrepair_tests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC3683A2DD632CF002D26F3 /* jsonrepair_tests.swift */; };
		2BC3684B2DDCB3C9002D26F3 /* nuroIosTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BC368492DDCB3C9002D26F3 /* nuroIosTests.swift */; };
		2BCC70BA2DA4D552005A6EBE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 2BCC70B12DA4D552005A6EBE /* Assets.xcassets */; };
		2BCC70BC2DA4D552005A6EBE /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2BCC70B42DA4D552005A6EBE /* LaunchScreen.storyboard */; };
		2BCC70BD2DA4D552005A6EBE /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 2BCC70B62DA4D552005A6EBE /* Main.storyboard */; };
		2BCC70BE2DA4D552005A6EBE /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BCC70B02DA4D552005A6EBE /* AppDelegate.swift */; };
		2BCC70BF2DA4D552005A6EBE /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BCC70B72DA4D552005A6EBE /* SceneDelegate.swift */; };
		2BCC70C02DA4D552005A6EBE /* SimpleTestViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BCC70B82DA4D552005A6EBE /* SimpleTestViewController.swift */; };
		2BEABC6E2DA525E500CD743E /* fetch_with_eventsource.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BEABC6D2DA525E500CD743E /* fetch_with_eventsource.swift */; };
		2BEABD5C2DACF8E500CD743E /* tos_upload.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2BEABD5B2DACF8E500CD743E /* tos_upload.swift */; };
		862C3659EA36AD9DAED7D32B /* Pods_nuroIosTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 61AF8B37DFE2DF895D44658A /* Pods_nuroIosTests.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		2BC368442DDCB39D002D26F3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2BCC70802DA4D0E5005A6EBE /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 2BCC70872DA4D0E5005A6EBE;
			remoteInfo = agent_client_ios;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0228CE67484DD2AEEB2D2464 /* Pods-nuroIosTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-nuroIosTests.debug.xcconfig"; path = "Target Support Files/Pods-nuroIosTests/Pods-nuroIosTests.debug.xcconfig"; sourceTree = "<group>"; };
		2B5559C12DD21E3C00841450 /* nuroIosTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = nuroIosTests.swift; sourceTree = "<group>"; };
		2BBC5CF52DE4604C003399A9 /* ChatViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatViewController.swift; sourceTree = "<group>"; };
		2BBC5CFA2DE4670E003399A9 /* AssistantMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AssistantMessageCell.swift; sourceTree = "<group>"; };
		2BBC5CFB2DE4670E003399A9 /* UserMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserMessageCell.swift; sourceTree = "<group>"; };
		2BBC5CFE2DE467C5003399A9 /* ReasoningMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ReasoningMessageCell.swift; sourceTree = "<group>"; };
		2BBC5CFF2DE467C5003399A9 /* ToolCallMessageCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ToolCallMessageCell.swift; sourceTree = "<group>"; };
		2BBC5D062DE47532003399A9 /* ChatSettingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatSettingViewController.swift; sourceTree = "<group>"; };
		2BC3683A2DD632CF002D26F3 /* jsonrepair_tests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = jsonrepair_tests.swift; sourceTree = "<group>"; };
		2BC368402DDCB39C002D26F3 /* nuroIosTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = nuroIosTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		2BC368492DDCB3C9002D26F3 /* nuroIosTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = nuroIosTests.swift; sourceTree = "<group>"; };
		2BCC70882DA4D0E5005A6EBE /* agent_client_ios.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = agent_client_ios.app; sourceTree = BUILT_PRODUCTS_DIR; };
		2BCC70B02DA4D552005A6EBE /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		2BCC70B12DA4D552005A6EBE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		2BCC70B22DA4D552005A6EBE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		2BCC70B32DA4D552005A6EBE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		2BCC70B52DA4D552005A6EBE /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		2BCC70B72DA4D552005A6EBE /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		2BCC70B82DA4D552005A6EBE /* SimpleTestViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SimpleTestViewController.swift; sourceTree = "<group>"; };
		2BEABC6D2DA525E500CD743E /* fetch_with_eventsource.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = fetch_with_eventsource.swift; sourceTree = "<group>"; };
		2BEABD5B2DACF8E500CD743E /* tos_upload.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = tos_upload.swift; sourceTree = "<group>"; };
		344B4D577147C76DD9EFFA0F /* Pods-nuroIosTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-nuroIosTests.release.xcconfig"; path = "Target Support Files/Pods-nuroIosTests/Pods-nuroIosTests.release.xcconfig"; sourceTree = "<group>"; };
		61AF8B37DFE2DF895D44658A /* Pods_nuroIosTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_nuroIosTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7A0A5B012B4A0483C7208380 /* Pods-agent_client_ios.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-agent_client_ios.release.xcconfig"; path = "Target Support Files/Pods-agent_client_ios/Pods-agent_client_ios.release.xcconfig"; sourceTree = "<group>"; };
		87347182B48495E17C6484E3 /* Pods-agent_client_ios.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-agent_client_ios.debug.xcconfig"; path = "Target Support Files/Pods-agent_client_ios/Pods-agent_client_ios.debug.xcconfig"; sourceTree = "<group>"; };
		9B360404690A75D7DC252C5C /* Pods_agent_client_ios.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_agent_client_ios.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		2BC3683D2DDCB39C002D26F3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				862C3659EA36AD9DAED7D32B /* Pods_nuroIosTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BCC70852DA4D0E5005A6EBE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22873A3291F0C6CAA8F69199 /* Pods_agent_client_ios.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		2B5559C22DD21E3C00841450 /* nuroIosTests */ = {
			isa = PBXGroup;
			children = (
				2B5559C12DD21E3C00841450 /* nuroIosTests.swift */,
			);
			path = nuroIosTests;
			sourceTree = "<group>";
		};
		2BBC5CF72DE4604C003399A9 /* appui */ = {
			isa = PBXGroup;
			children = (
				2BBC5D062DE47532003399A9 /* ChatSettingViewController.swift */,
				2BBC5CFE2DE467C5003399A9 /* ReasoningMessageCell.swift */,
				2BBC5CFF2DE467C5003399A9 /* ToolCallMessageCell.swift */,
				2BBC5CFA2DE4670E003399A9 /* AssistantMessageCell.swift */,
				2BBC5CFB2DE4670E003399A9 /* UserMessageCell.swift */,
				2BBC5CF52DE4604C003399A9 /* ChatViewController.swift */,
			);
			path = appui;
			sourceTree = "<group>";
		};
		2BC3684A2DDCB3C9002D26F3 /* nuroIosTests */ = {
			isa = PBXGroup;
			children = (
				2BC368492DDCB3C9002D26F3 /* nuroIosTests.swift */,
			);
			path = nuroIosTests;
			sourceTree = "<group>";
		};
		2BCC707F2DA4D0E5005A6EBE = {
			isa = PBXGroup;
			children = (
				2BCC70B92DA4D552005A6EBE /* agent_client_ios */,
				2B5559C22DD21E3C00841450 /* nuroIosTests */,
				2BC3684A2DDCB3C9002D26F3 /* nuroIosTests */,
				2BCC70892DA4D0E5005A6EBE /* Products */,
				3E0D00F1FD47ED268341B361 /* Pods */,
				87D83C61ABCF5103CA6FE9C1 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		2BCC70892DA4D0E5005A6EBE /* Products */ = {
			isa = PBXGroup;
			children = (
				2BCC70882DA4D0E5005A6EBE /* agent_client_ios.app */,
				2BC368402DDCB39C002D26F3 /* nuroIosTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		2BCC70B92DA4D552005A6EBE /* agent_client_ios */ = {
			isa = PBXGroup;
			children = (
				2BBC5CF72DE4604C003399A9 /* appui */,
				2BCC70B02DA4D552005A6EBE /* AppDelegate.swift */,
				2BCC70B12DA4D552005A6EBE /* Assets.xcassets */,
				2BCC70B22DA4D552005A6EBE /* Info.plist */,
				2BCC70B42DA4D552005A6EBE /* LaunchScreen.storyboard */,
				2BCC70B62DA4D552005A6EBE /* Main.storyboard */,
				2BCC70B72DA4D552005A6EBE /* SceneDelegate.swift */,
				2BCC70B82DA4D552005A6EBE /* SimpleTestViewController.swift */,
				2BEABC6D2DA525E500CD743E /* fetch_with_eventsource.swift */,
				2BEABD5B2DACF8E500CD743E /* tos_upload.swift */,
				2BC3683A2DD632CF002D26F3 /* jsonrepair_tests.swift */,
			);
			path = agent_client_ios;
			sourceTree = "<group>";
		};
		3E0D00F1FD47ED268341B361 /* Pods */ = {
			isa = PBXGroup;
			children = (
				87347182B48495E17C6484E3 /* Pods-agent_client_ios.debug.xcconfig */,
				7A0A5B012B4A0483C7208380 /* Pods-agent_client_ios.release.xcconfig */,
				0228CE67484DD2AEEB2D2464 /* Pods-nuroIosTests.debug.xcconfig */,
				344B4D577147C76DD9EFFA0F /* Pods-nuroIosTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		87D83C61ABCF5103CA6FE9C1 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				9B360404690A75D7DC252C5C /* Pods_agent_client_ios.framework */,
				61AF8B37DFE2DF895D44658A /* Pods_nuroIosTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		2BC3683F2DDCB39C002D26F3 /* nuroIosTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2BC368462DDCB39D002D26F3 /* Build configuration list for PBXNativeTarget "nuroIosTests" */;
			buildPhases = (
				6BA23D277EC7AD3E97644E3B /* [CP] Check Pods Manifest.lock */,
				2BC3683C2DDCB39C002D26F3 /* Sources */,
				2BC3683D2DDCB39C002D26F3 /* Frameworks */,
				2BC3683E2DDCB39C002D26F3 /* Resources */,
				84F57E5384FA1FA3AE8F8065 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				2BC368452DDCB39D002D26F3 /* PBXTargetDependency */,
			);
			name = nuroIosTests;
			productName = nuroIosTests;
			productReference = 2BC368402DDCB39C002D26F3 /* nuroIosTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		2BCC70872DA4D0E5005A6EBE /* agent_client_ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 2BCC709B2DA4D0E7005A6EBE /* Build configuration list for PBXNativeTarget "agent_client_ios" */;
			buildPhases = (
				DE9209C308A398711994B7D6 /* [CP] Check Pods Manifest.lock */,
				2BCC70842DA4D0E5005A6EBE /* Sources */,
				2BCC70852DA4D0E5005A6EBE /* Frameworks */,
				2BCC70862DA4D0E5005A6EBE /* Resources */,
				C25DFB8297D8FB3D289EA5D0 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = agent_client_ios;
			productName = agent_client_ios;
			productReference = 2BCC70882DA4D0E5005A6EBE /* agent_client_ios.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2BCC70802DA4D0E5005A6EBE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					2BC3683F2DDCB39C002D26F3 = {
						CreatedOnToolsVersion = 16.1;
						TestTargetID = 2BCC70872DA4D0E5005A6EBE;
					};
					2BCC70872DA4D0E5005A6EBE = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 2BCC70832DA4D0E5005A6EBE /* Build configuration list for PBXProject "agent_client_ios" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 2BCC707F2DA4D0E5005A6EBE;
			productRefGroup = 2BCC70892DA4D0E5005A6EBE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				2BCC70872DA4D0E5005A6EBE /* agent_client_ios */,
				2BC3683F2DDCB39C002D26F3 /* nuroIosTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		2BC3683E2DDCB39C002D26F3 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BCC70862DA4D0E5005A6EBE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BCC70BA2DA4D552005A6EBE /* Assets.xcassets in Resources */,
				2BCC70BC2DA4D552005A6EBE /* LaunchScreen.storyboard in Resources */,
				2BCC70BD2DA4D552005A6EBE /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		6BA23D277EC7AD3E97644E3B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-nuroIosTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		84F57E5384FA1FA3AE8F8065 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-nuroIosTests/Pods-nuroIosTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-nuroIosTests/Pods-nuroIosTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-nuroIosTests/Pods-nuroIosTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		C25DFB8297D8FB3D289EA5D0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-agent_client_ios/Pods-agent_client_ios-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-agent_client_ios/Pods-agent_client_ios-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-agent_client_ios/Pods-agent_client_ios-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		DE9209C308A398711994B7D6 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-agent_client_ios-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		2BC3683C2DDCB39C002D26F3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BC3684B2DDCB3C9002D26F3 /* nuroIosTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		2BCC70842DA4D0E5005A6EBE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				2BBC5CFC2DE4670E003399A9 /* AssistantMessageCell.swift in Sources */,
				2BBC5D002DE467C5003399A9 /* ReasoningMessageCell.swift in Sources */,
				2BBC5D012DE467C5003399A9 /* ToolCallMessageCell.swift in Sources */,
				2BBC5CFD2DE4670E003399A9 /* UserMessageCell.swift in Sources */,
				2BCC70BE2DA4D552005A6EBE /* AppDelegate.swift in Sources */,
				2BCC70BF2DA4D552005A6EBE /* SceneDelegate.swift in Sources */,
				2BC3683B2DD632CF002D26F3 /* jsonrepair_tests.swift in Sources */,
				2BCC70C02DA4D552005A6EBE /* SimpleTestViewController.swift in Sources */,
				2BEABD5C2DACF8E500CD743E /* tos_upload.swift in Sources */,
				2BBC5D072DE47532003399A9 /* ChatSettingViewController.swift in Sources */,
				2BBC5CF92DE4604C003399A9 /* ChatViewController.swift in Sources */,
				2BEABC6E2DA525E500CD743E /* fetch_with_eventsource.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		2BC368452DDCB39D002D26F3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 2BCC70872DA4D0E5005A6EBE /* agent_client_ios */;
			targetProxy = 2BC368442DDCB39D002D26F3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		2BCC70B42DA4D552005A6EBE /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2BCC70B32DA4D552005A6EBE /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		2BCC70B62DA4D552005A6EBE /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				2BCC70B52DA4D552005A6EBE /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		2BC368472DDCB39D002D26F3 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0228CE67484DD2AEEB2D2464 /* Pods-nuroIosTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.byted.nuroIosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/agent_client_ios.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/agent_client_ios";
			};
			name = Debug;
		};
		2BC368482DDCB39D002D26F3 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 344B4D577147C76DD9EFFA0F /* Pods-nuroIosTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.1;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = org.byted.nuroIosTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/agent_client_ios.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/agent_client_ios";
			};
			name = Release;
		};
		2BCC709C2DA4D0E7005A6EBE /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 87347182B48495E17C6484E3 /* Pods-agent_client_ios.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = agent_client_ios/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "org.byted.agent-client-ios";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		2BCC709D2DA4D0E7005A6EBE /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7A0A5B012B4A0483C7208380 /* Pods-agent_client_ios.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = agent_client_ios/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "org.byted.agent-client-ios";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		2BCC709E2DA4D0E7005A6EBE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		2BCC709F2DA4D0E7005A6EBE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		2BC368462DDCB39D002D26F3 /* Build configuration list for PBXNativeTarget "nuroIosTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BC368472DDCB39D002D26F3 /* Debug */,
				2BC368482DDCB39D002D26F3 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BCC70832DA4D0E5005A6EBE /* Build configuration list for PBXProject "agent_client_ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BCC709E2DA4D0E7005A6EBE /* Debug */,
				2BCC709F2DA4D0E7005A6EBE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		2BCC709B2DA4D0E7005A6EBE /* Build configuration list for PBXNativeTarget "agent_client_ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				2BCC709C2DA4D0E7005A6EBE /* Debug */,
				2BCC709D2DA4D0E7005A6EBE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 2BCC70802DA4D0E5005A6EBE /* Project object */;
}
