//
//  nuroIosTests.swift
//  nuroIosTests
//
//  Created by ByteDance on 2025/5/20.
//

import Testing
import NuroSDK
import NuroSDKUnitTest

struct nuroIosTests {

    func installDevice() {
        NuroSetting.needDisplayServerFunctionMessage = true
        let mcpManager = NuroMCPManager()
        let localServer = MCPLiteServer(name: "local", version: "1.0.0")
        localServer.tool(name: "get_location",
                         description: "获取用户当前位置",
                         inputSchema:MCPToolDefineObjectProperty()
            .defProperty("reason", MCPToolDefineStringProperty().defDescription("为什么你需要请求用户的地理位置"))
            .defRequired(["reason"])
        ) { params, resultCallback in
            if let params = params as? [String: Any],
                let reason = params["reason"] as? String {
                if reason == "不需要用户确认" {
                    let result = MCPToolCallResult()
                    result.content = [
                        MCPToolCallTextContent.create("佛山市")
                    ]
                    resultCallback(result)
                    return
                }
            }
            let result = MCPToolCallResult()
            result.content = []
            resultCallback(result) // 留空是因为我们需要通过自定义工具界面，请求用户授权获取用户的地理位置信息。
        }
        mcpManager.registerServer(NuroMCPServerConfig("local", localServer))
        TestDevice.testMCPManager = mcpManager
    }
    
    @Test func runTests() async throws {
        installDevice()
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            NuroSDKUnitTest.runTests { device in
                let failedTests = TestContext.globalContext.getFailedTests()
                
                if failedTests.count > 0 {
                    for test in failedTests {
                        print("Test \(test.functionName) failed: \(test.errorMessage)")
                    }
                    // 终止 continuation，抛出错误
                    continuation.resume(throwing: NSError(
                        domain: "nuroIosTests",
                        code: 1,
                        userInfo: [NSLocalizedDescriptionKey: "Some tests failed."]
                    ))
                } else {
                    continuation.resume()
                }
            }
        }
    }

}
