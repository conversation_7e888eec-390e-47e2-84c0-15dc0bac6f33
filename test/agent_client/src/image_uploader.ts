import { Axios } from "axios";
import BytedUploader, { UploadResultImageInfo } from "@byted/uploader";

interface ImageUploaderInfo {
  space_name: string;
  space_type: number;
  secret_access_key: string;
  region: string;
  session_token: string;
  upload_domain: string;
  access_key_id: string;
  current_time: string;
  expired_time: string;
}

export class ImageUploader {
  constructor() {}

  async uploadImageByFile(file: File): Promise<{ uri: string; width: number; height: number }> {
    return new Promise(async (resolve, reject) => {
      try {
        const uploaderInfo = await this.getUploaderInfo();
        const bytedUploader = new BytedUploader({
          userId: "unknown",
          appId: 513695,
          imageHost: "https://" + uploaderInfo.upload_domain + "/",
          imageConfig: {
            serviceId: uploaderInfo.space_name,
          },
          objectConfig: {
            serviceId: uploaderInfo.space_name,
          },
        });

        const fileKey = bytedUploader.addFile({
          file: file,
          stsToken: {
            CurrentTime: uploaderInfo.current_time,
            ExpiredTime: uploaderInfo.expired_time,
            SessionToken: uploaderInfo.session_token,
            AccessKeyId: uploaderInfo.access_key_id,
            SecretAccessKey: uploaderInfo.secret_access_key,
          },
          type: "image",
        });
        bytedUploader.on("complete", (info) => {
          console.log("info", info);
          resolve({
            uri: "dreamina://image/" + info.oid,
            width: (info.uploadResult as UploadResultImageInfo).ImageWidth ?? 0,
            height: (info.uploadResult as UploadResultImageInfo).ImageHeight ?? 0,
          });
        });
        bytedUploader.on("error", () => {
          reject("图片上传失败");
        });
        bytedUploader.start(fileKey);
      } catch (error) {
        reject("图片上传失败");
      }
    });
  }

  private async getUploaderInfo() {
    let axios = new Axios({});
    const uri = "http://10.37.73.141:12345/api/imagex_sts"; // sts token fetcher
    const authTokenResponse = await axios.post(uri, {
      headers: {
        "Content-Type": "application/json",
      },
      responseType: "text",
    });
    const authTokenData = JSON.parse(authTokenResponse.data).data;
    return authTokenData as ImageUploaderInfo;
  }
}
