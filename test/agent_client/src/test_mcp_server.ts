import {
  MCPToolDefine,
  MCPToolDefineArrayProperty,
  MCPToolDefineObjectProperty,
  MCPToolDefineStringProperty,
  NuroMCPClientAdapterImpl,
  transformToZodSchema,
} from "@byted/nurosdk-js";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import Zod from "zod";

const server = new McpServer({
  name: "location",
  version: "1.0.0",
});

let get_user_location_args = new MCPToolDefineObjectProperty()
  .defProperty(
    "reason",
    new MCPToolDefineStringProperty().defDescription(
      "The reason for getting the user location."
    )
  )
  .defRequired(["reason"]);

server.tool(
  "get_user_location",
  "Get the user location.",
  transformToZodSchema(Zod, get_user_location_args),
  async ({}) => ({
    content: [{ type: "text", text: `The user location is 「佛山」.` }],
  })
);


export const mcpToolText2ImageForm = new MCPToolDefine(
    "text2image_form",
    `根据用户输入的文本描述（prompt）生成一张图像。适用于从零创建图像，不依赖任何参考图片。
# 注意：
1. 该工具看不到你与用户对话过程中的图片信息，因此你需要将该工具所需要的图片信息全部通过prompt描述出来。
1.1  错误的示例包括"比原图色彩更浓厚一些的风景图"、“图片中的两个人，地点换成海边”，因为该生图工具无法获取到原图信息
2. 如果用户是想基于对话过程中已有的图片创建新的图片，你可以将已有图片的信息通过文字重新表达，与用户的需求融合之后填入到prompt中。
2.1 如果用户想基于对话过程中已有的**人像**图片创建新的图片，你应该把握原图片的人像特征（例如：性别、年龄、肤色、发型、五官、表情、服饰、配饰等），并且按照新的要求生成新图片的prompt，注意用户希望你保持人像的哪些特征、修改人像的哪些特征。`,
    new MCPToolDefineObjectProperty()
        .defProperty(
            "title",
            new MCPToolDefineStringProperty().defDescription(
                "一句话说明为什么推荐这个提示词。",
            ),
        )
        .defProperty(
            "prompt",
            new MCPToolDefineStringProperty().defDescription(
                `用于生成图片的提示词

## 内容要求
1. Prompt由 “主体描述，场景描述，风格词，镜头词，氛围词，修饰词” 组成，所有的部分都用逗号分隔，每个部分可能有0个、1个或者多个，不能出现除逗号、引号以外的其他符号。
2. 主体描述\\场景描述 使用自然语言描述，完整且具体，不带复杂表达和修辞手法。
3. 风格词\\镜头词\\氛围词\\修饰词 使用简短的专业短词汇来描述。
4. Prompt 保持结构简单并且只能包含中文和标点符号，不能包含特殊符号。
5. 当用户需要在图片中呈现文字时，使用引号标注需生成的文字。
6. 给出的 Prompt 应该始终围绕用户的核心意图和要素，并且进行质量、创意上的合理发散。

## Best practices：
1. 用简单直接的语言表达，在表达准确的基础上，短提示词也能发挥惊艳的效果
2. 用连贯的自然语言描述画面内容（主体+行为+环境等），用短词语描述画面美学（风格、色彩、光影、构图等）
3. 推荐写出图像用途和类型, 例如：用途PPT封面背景图、背景素材图/ 类型广告海报设计、纪实摄影
4. 文字：把想要生成的文字内容插入“”引号中，可以提升文字准确率

## 示例
一位亚洲年轻女子身穿白色安检制服面带微笑，有着大大的眼睛站在机坪仰望蔚蓝的天空，微风吹动着头发，背景是双重曝光的梦幻般的场景，标题”我的理想“，电影海报摄影，大远景，冷色调，宫崎骏动漫风格，上下构图`,
            ),
        )
        .defProperty(
            "ratio",
            new MCPToolDefineStringProperty()
                .defDescription("生成图片的{宽/高}比例")
                .defEnum(["1:1", "3:4", "16:9", "4:3", "9:16", "2:3", "3:2", "21:9"]),
        )
        .defProperty(
            "instruction",
            new MCPToolDefineStringProperty().defDescription(
                "一句话提示用户还可以提供怎样的参数优化生图提示词",
            ),
        )
        .defProperty(
            "image_tags",
            new MCPToolDefineArrayProperty()
                .defItems(new MCPToolDefineStringProperty())
                .defDescription(
                    "本次图片生成的相关主题，请 LLM 根据以下规则总结并给出。例如：潮玩,表情包",
                ),
        )
        .defRequired(["title", "prompt", "ratio", "instruction", "image_tags"]),
);

server.tool(
    mcpToolText2ImageForm.name,
    mcpToolText2ImageForm.description,
    transformToZodSchema(Zod, mcpToolText2ImageForm.inputSchema),
    async ({}) => ({
      content: [],
    })
);


export const mcpToolUploadImageForm = new MCPToolDefine(
    "upload_image_form",
    "如果需要用户提供图片，调用该工具，请求用户上传图片。",
    new MCPToolDefineObjectProperty()
        .defProperty(
            "reason",
            new MCPToolDefineStringProperty().defDescription(
                "一句话说明为什么需要用户上传图片",
            ),
        )
        .defRequired(["reason"]),
);

server.tool(
    mcpToolUploadImageForm.name,
    mcpToolUploadImageForm.description,
    transformToZodSchema(Zod, mcpToolUploadImageForm.inputSchema),
    async ({}) => ({
      content: [],
    })
);


export const mcpToolText2Image = new MCPToolDefine(
    "text2image",
    "根据用户输入的文本描述（prompt）生成一张图像。用途：适用于从零创建图像，不依赖任何参考图片。",
    new MCPToolDefineObjectProperty()
        .defProperty(
            "prompt",
            new MCPToolDefineStringProperty().defDescription("用于生成图片的描述"),
        )
        .defProperty(
            "ratio",
            new MCPToolDefineStringProperty()
                .defDescription("生成图片的{宽/高}比例")
                .defEnum(["1:1", "3:4", "16:9", "4:3", "9:16", "2:3", "3:2", "21:9"]),
        )
        .defProperty(
            "model",
            new MCPToolDefineStringProperty().defDescription("生成图片的模型名字"),
        )
        .defProperty(
            "resolution",
            new MCPToolDefineStringProperty()
                .defDescription("生成图片的分辨率质量")
                .defEnum(["1k", "2k"]),
        )
        .defProperty(
            "image_tags",
            new MCPToolDefineArrayProperty()
                .defItems(new MCPToolDefineStringProperty())
                .defDescription(
                    "本次图片生成的相关主题，请 LLM 根据以下规则总结并给出。例如：潮玩,表情包",
                ),
        )
        .defRequired(["prompt", "ratio", "model", "image_tags"]),
);
server.tool(
    mcpToolText2Image.name,
    mcpToolText2Image.description,
    transformToZodSchema(Zod, mcpToolText2Image.inputSchema),
    async ({}) => ({
      content: [],
    })
);

export const mcpToolPointsConsumptionReminder = new MCPToolDefine(
    "points_consumption_reminder",
    ` 预计消耗大于某一阈值，提醒用户会消耗较大积分。
    Description: 该工具用于向用户发出提示，提醒用户后续的生图、生视频链路可能会消耗较多积分。使用条件：当且仅当在你刚调用过"get_plans"工具规划完后续的生成链路，并且生成链路里包含了**大于等于2次视频生成任务**时，你才需要使用该工具向用户发出积分消耗提示。注意：每次调用过"get_plans"工具后，最多向用户发出一次提示即可，不需要重复提示。
    例如：
    1. 你最近没有调用get_plans函数，则不可以使用该函数向用户发出提醒
    2. 你刚调用完get_plans函数，计划为用户生成5张图片和1个视频，则不需要向用户发出提醒
    3. 你刚调用完get_plans函数，计划为用户生成2个视频，则需要向用户发出提醒`,
    new MCPToolDefineObjectProperty(),
);
server.tool(
    mcpToolPointsConsumptionReminder.name,
    mcpToolPointsConsumptionReminder.description,
    transformToZodSchema(Zod, mcpToolPointsConsumptionReminder.inputSchema),
    async ({}) => ({
      content: [],
    })
);

export class TestLocationMCPServer extends NuroMCPClientAdapterImpl {
  constructor() {
    super("location", server);
  }
}
