import { NuroUtils } from "@byted/nurosdk-js";

export function test_jsonrepair() {
  const testCases = [
    {
      name: "Unclosed string value",
      input: '{"abcds":"abc',
      expected: '{"abcds":"abc"}',
    },
    {
      name: "Unclosed number value",
      input: '{"abcds": 123',
      expected: '{"abcds": 123}',
    },
    {
      name: "Streaming JSON - unclosed object",
      input: '{"key1":"value1", "key2":',
      expected: '{"key1":"value1", "key2":""}', // Assuming empty string for unclosed value
    },
    {
      name: "Streaming JSON - unclosed array",
      input: '[1, 2, "hello",',
      expected: '[1, 2, "hello"]', // Assuming array closes
    },
    {
      name: "Streaming JSON - complex unclosed object",
      input: '{"a": "b", "c": {"d": "e", "f":',
      expected: '{"a": "b", "c": {"d": "e", "f":""}}', // Assuming empty string for nested unclosed value
    },
    {
      name: "Streaming JSON - object with trailing comma (ASCII)",
      input: '{"key1":"value1", "key2":"value2",',
      expected: '{"key1":"value1", "key2":"value2"}',
    },
    {
      name: "Streaming JSON - object with trailing comma (Chinese)",
      input: '{"key1":"value1", "key2":"value2",',
      expected: '{"key1":"value1", "key2":"value2"}',
    },
    {
      name: "Streaming JSON - array with trailing comma (Chinese)",
      input: "[1, 2, 3",
      expected: "[1, 2, 3]",
    },
    {
      name: "Empty string",
      input: "",
      expected: "",
    },
    {
      name: "Already valid JSON",
      input: '{"valid": true}',
      expected: '{"valid": true}',
    },
    {
      name: "JSON with escaped quotes inside string",
      input: '{"text":"this is a \\"quoted\\" string"',
      expected: '{"text":"this is a \\"quoted\\" string"}',
    },
    {
      name: "JSON with unclosed string containing escaped quote",
      input: '{"text":"this is a \\"quoted\\" string',
      expected: '{"text":"this is a \\"quoted\\" string"}',
    },
    {
      name: "Complex nested unclosed JSON - mixed array and object",
      input: '{"data": [{"id": 1, "value": "test", "details": {"status": "pending", "items": [10, 20, {"subItem": "subValue"}]}}, {"id": 2, "value": "another"',
      expected: '{"data": [{"id": 1, "value": "test", "details": {"status": "pending", "items": [10, 20, {"subItem": "subValue"}]}}, {"id": 2, "value": "another"}]}',
    },
    {
      name: "Complex nested unclosed JSON - unclosed inner array",
      input: '{"outer": {"innerArray": [1, 2, {"key": "value"}, ',
      expected: '{"outer": {"innerArray": [1, 2, {"key": "value"}]}}',
    },
    {
      name: "Complex nested unclosed JSON - unclosed inner object",
      input: '{"outer": ["item1", {"innerKey1": "innerValue1", "innerKey2":',
      expected: '{"outer": ["item1", {"innerKey1": "innerValue1", "innerKey2":""}]}',
    },
    {
      name: "Valid JSON - simple object",
      input: '{"name": "John Doe", "age": 30, "isStudent": false}',
      expected: '{"name": "John Doe", "age": 30, "isStudent": false}',
    },
    {
      name: "Valid JSON - nested object and array",
      input: '{"id": "001", "type": "donut", "name": "Cake", "ppu": 0.55, "batters": {"batter": [{ "id": "1001", "type": "Regular" },{ "id": "1002", "type": "Chocolate" }]}, "topping": [{ "id": "5001", "type": "None" },{ "id": "5002", "type": "Glazed" }]}',
      expected: '{"id": "001", "type": "donut", "name": "Cake", "ppu": 0.55, "batters": {"batter": [{ "id": "1001", "type": "Regular" },{ "id": "1002", "type": "Chocolate" }]}, "topping": [{ "id": "5001", "type": "None" },{ "id": "5002", "type": "Glazed" }]}',
    },
    {
      name: "Valid JSON - array of objects",
      input: '[{"item": "apple", "price": 1.0}, {"item": "banana", "price": 0.5}]',
      expected: '[{"item": "apple", "price": 1.0}, {"item": "banana", "price": 0.5}]',
    },
    {
      name: "String with escaped double quotes",
      input: '{"text":"He said \\"Hello World!\\""',
      expected: '{"text":"He said \\"Hello World!\\""}',
    },
    {
      name: "String with newline character",
      input: '{"message":"First line\\nSecond line"',
      expected: '{"message":"First line\\nSecond line"}',
    },
    {
      name: "String with carriage return",
      input: '{"message":"First line\\rSecond line"',
      expected: '{"message":"First line\\rSecond line"}',
    },
    {
      name: "String with tab character",
      input: '{"data":"Column1\\tColumn2"',
      expected: '{"data":"Column1\\tColumn2"}',
    },
    {
      name: "String containing unicode characters",
      input: '{"unicode_test":"\u0048\u0065\u006c\u006c\u006f \u4e16\u754c"', // Hello 世界
      expected: '{"unicode_test":"Hello 世界"}',
    },
    {
      name: "String with leading/trailing whitespace and escaped chars",
      input: '{"padded_string":"  \\"text\\"  "',
      expected: '{"padded_string":"  \\"text\\"  "}',
    }
  ];

  console.log("--- Running JSON Repair Tests ---");
  testCases.forEach((testCase) => {
    console.log(`\nTesting: ${testCase.name}`);
    console.log(`Input:    '${testCase.input}'`);
    const repairedJson = NuroUtils.jsonrepair(testCase.input);
    console.log(`Repaired: '${repairedJson}'`);
    console.log(`Expected: '${testCase.expected}'`);
    if (repairedJson === testCase.expected) {
      console.log("Result: PASSED");
    } else {
      console.error("Result: FAILED");
    }
  });
  console.log("\n--- JSON Repair Tests Complete ---");
}
