import {
  NuroAssistantMessage,
  NuroConversationManager,
  NuroMCPManager,
  NuroMCPServerConfig,
  NuroReasoningMessage,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk-js";
import { TestLocationMCPServer } from "./test_mcp_server";

const mcpManager = new NuroMCPManager();
const testLocationMCPServer = new NuroMCPServerConfig(
  "location",
  new TestLocationMCPServer()
);
mcpManager.registerServer(testLocationMCPServer);
const conversationManager = new NuroConversationManager();
conversationManager.mcpManager = mcpManager;
conversationManager.enableMCPTools();

const transport = new SSETransport("https://7c026eti.cn-boe2-fn.bytedance.net/sse", {});
conversationManager.connect(transport);
conversationManager.conversation.addStateUpdateListener((state) => {
  console.log(`Conversation state updated: ${state}`);
});
conversationManager.conversation.addMessageUpdateListener((message) => {
  if (message instanceof NuroUserMessage) {
    console.log(
      Date.now(),
      `User message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroAssistantMessage) {
    console.log(
      Date.now(),
      `Assistant message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroReasoningMessage) {
    console.log(
      Date.now(),
      `Reasoning message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroToolCallMessage) {
    console.log(
      Date.now(),
      `Tool call message: ${message.id}, name: ${message.toolName}, args: ${message.toolArgs}, result: ${message.toolResult}, status: ${message.messageStatus}`
    );
  }
});

conversationManager.conversation.addTaskUpdateListener((tasks) => {
  tasks.forEach((taskOut) => {
    let task = taskOut.task
    let taskop = taskOut.taskOp
    let showmid = task.displayingMiddlewareMessages()
    console.log(
        Date.now(),
        `task: ${task.taskId},op:${taskop}, promptMessages.length : ${task.promptMessages.length}, middlewareMessages.length: ${task.middlewareMessages.length}, artifactMessages.length: ${task.artifactMessages.length},showMid.length:${showmid}`
    );
  })


});

export async function askWeather() {
  return new Promise<void>((resolve) => {
    const userMessage = new NuroUserMessage(
      NuroUtils.randomUUIDString(),
      "你好，DeepSeek V3，告诉我当前城市的天气怎么样。"
    );
    conversationManager.sendUserMessage(userMessage);
  });
}

export async function askWritePoem() {
  return new Promise<void>((resolve) => {
    const userMessage = new NuroUserMessage(
      NuroUtils.randomUUIDString(),
      "你可以基于上述天气作一首诗吗？"
    );
    conversationManager.sendUserMessage(userMessage);
  });
}

export async function askImage() {
  return new Promise<void>((resolve) => {
    const userMessage = new NuroUserMessage(
        NuroUtils.randomUUIDString(),
        "生成小狗图片？"
    );
    conversationManager.sendUserMessage(userMessage);
  });
}

export async function main() {
  askWeather();
  //askImage()
  // setTimeout(async () => {
  //   askWritePoem();
  // }, 10000);
}
