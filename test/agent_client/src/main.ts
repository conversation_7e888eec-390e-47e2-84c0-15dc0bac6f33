import { EventStreamAdapter, EventStreamConfig, NuroLogger, NuroLoggerAdapter, NuroLogLevel, NuroSetting, TOSFileUploadAdapter, TOSFileUploadConfig } from "@byted/nurosdk-js";
import { main as test_plain_text } from "./test_plain_text";
import { main as task_task } from "./test_task";
import { test_jsonrepair } from "./test_jsonrepair";
import { ImageUploader } from "./image_uploader";

(async () => {

  NuroSetting.needDisplayServerFunctionMessage = true;

  NuroLoggerAdapter.debug = (tag: string, msg: string) => {
    console.log(`[DEBUG] ${tag}: ${msg}`);
  };
  NuroLoggerAdapter.info = (tag: string, msg: string) => {
    console.log(`[INFO] ${tag}: ${msg}`);
  }
  NuroLoggerAdapter.error = (tag: string, msg: string) => {
    console.log(`[ERROR] ${tag}: ${msg}`);
  }
  NuroLogger.setLogLevel(NuroLogLevel.ERROR);

  EventStreamAdapter.fetch = (config: EventStreamConfig) => {
    fetch(config.endpoint, {
      method: "POST",
      headers: config.headers,
      body: config.data,
    }).then(async (res: any) => {
      if (res.ok) {
        let decoder = new TextDecoderStream();
        let iter = res.body.pipeThrough(decoder);
        let reader = iter.getReader();
        for (;;) {
          let chunk = await reader.read();
          if (typeof chunk.value === "string") {
            config.onChunk?.(chunk.value);
          }
          if (chunk.done) {
            config.onFinish?.();
            break;
          }
        }
      }
    }).catch((err: any) => {
      config.onError?.(-1, `${err}`);
    });
    return ""; // todo: return a cancel token
  };

  // TOSFileUploadAdapter.upload = (config: TOSFileUploadConfig) => {
  //   const uploader = new ImageUploader();
  //   uploader
  //     .uploadImageByFile(config.localFile.localFileObject as File)
  //     .then((res) => {
  //       config.onFinish?.();
  //     })
  //     .catch((err) => {
  //       config.onError?.(-1, `${err}`);
  //     });
  //   return "";
  // };
  // test_jsonrepair();
  await test_plain_text();
  // await task_task();
})();
