import {
  NuroAssistantMessage,
  NuroConversationManager,
  NuroFile,
  NuroFileType,
  NuroLocalFile,
  NuroReasoningMessage,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk-js";
import axios from "axios";

const conversationManager = new NuroConversationManager();

const transport = new SSETransport("https://7c026eti.cn-boe2-fn.bytedance.net/sse", {});
conversationManager.connect(transport);
conversationManager.conversation.addStateUpdateListener((state) => {
  console.log(`Conversation state updated: ${state}`);
});
conversationManager.conversation.addMessageUpdateListener((message) => {
  if (message instanceof NuroUserMessage) {
    console.log(`User message: ${message.id}, content: ${message.text}, files: ${message.files}`);
  } else if (message instanceof NuroAssistantMessage) {
    console.log(`Assistant message: ${message.id}, content: ${message.text}`);
  } else if (message instanceof NuroReasoningMessage) {
    console.log(`Reasoning message: ${message.id}, content: ${message.text}`);
  } else if (message instanceof NuroToolCallMessage) {
    console.log(
      `Tool call message: ${message.id}, name: ${message.toolName}, args: ${message.toolArgs}, result: ${message.toolResult}`
    );
  }
});

export async function askPicture() {
  return new Promise<void>(async (resolve) => {
    const file = await fetchImageAsFile(
      "https://tosv.byted.org/obj/ccdataplatform-risk/image/6d4ce8fb5d6614651e3ad4869993ffa4"
    );
    let nuroFile = new NuroFile(NuroFileType.image, undefined, new NuroLocalFile("test.jpg", file));
    const userMessage = new NuroUserMessage(NuroUtils.randomUUIDString(), "你好，请告诉我这张图片说了什么。", [
      nuroFile,
    ]);
    conversationManager.sendUserMessage(userMessage);
  });
}

async function fetchImageAsFile(imageUrl: string, fileName = "image.jpg") {
  try {
    const response = await axios.get(imageUrl, {
      responseType: "blob", // 关键：返回类型为 blob
    });

    const contentType = response.headers["content-type"] || "image/jpeg";
    const blob = response.data;

    // 创建 File 对象
    const file = new File([blob], fileName, { type: contentType });

    return file;
  } catch (error) {
    console.error("下载图片失败:", error);
    throw error;
  }
}

export async function main() {
  askPicture();
}
