import {
  NuroAssistantMessage,
  NuroConversationManager,
  NuroMCPManager,
  NuroMCPServerConfig,
  NuroMessage,
  NuroReasoningMessage,
  NuroTaskChecker,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroUtils,
  SSETransport,
} from "@byted/nurosdk-js";
import { TestLocationMCPServer } from "./test_mcp_server";

class TestTaskChecker extends NuroTaskChecker {}

const mcpManager = new NuroMCPManager();
const testLocationMCPServer = new NuroMCPServerConfig(
  "location",
  new TestLocationMCPServer()
);
mcpManager.registerServer(testLocationMCPServer);
const conversationManager = new NuroConversationManager();
conversationManager.mcpManager = mcpManager;
conversationManager.enableMCPTools();

conversationManager.conversation.taskChecker = new TestTaskChecker();

const transport = new SSETransport(
  "https://7c026eti.cn-boe2-fn.bytedance.net/sse",
  {}
);
conversationManager.connect(transport);
conversationManager.conversation.addStateUpdateListener((state) => {
  console.log(`Conversation state updated: ${state}`);
});

conversationManager.conversation.addMessageUpdateListener((message) => {
  if (message instanceof NuroUserMessage) {
    console.log(
      Date.now(),
      `User message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroAssistantMessage) {
    console.log(
      Date.now(),
      `Assistant message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroReasoningMessage) {
    console.log(
      Date.now(),
      `Reasoning message: ${message.id}, content: ${message.text}, status: ${message.messageStatus}`
    );
  } else if (message instanceof NuroToolCallMessage) {
    console.log(
      Date.now(),
      `Tool call message: ${message.id}, name: ${message.toolName}, args: ${message.toolArgs}, result: ${message.toolResult}, status: ${message.messageStatus}`
    );
  }
});

conversationManager.conversation.addTaskUpdateListener((task) => {
  console.log(`Task updated: ${task[0].task.taskId}`, task[0].task.taskStatus);
  console.log(`Task promptMessages:`, task[0].task.promptMessages);
  console.log(`Task middlewareMessages:`, task[0].task.middlewareMessages);
  console.log(`Task artifactMessages:`, task[0].task.artifactMessages);
});

export async function askWeather() {
  return new Promise<void>((resolve) => {
    const userMessage = new NuroUserMessage(
      NuroUtils.randomUUIDString(),
      "你好，DeepSeek V3，告诉我当前城市的天气怎么样，请在收集工具调用结果后，直接回复「嘿，你所在的城市{city}的天气是{weather_status}」。"
    );
    conversationManager.sendUserMessage(userMessage);
  });
}

export async function main() {
  askWeather();
}
