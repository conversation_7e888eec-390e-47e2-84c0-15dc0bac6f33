import Tos from "@byted-service/tos";

const options = {
  clientPSM: "videocut.nuro.mockhostagent", // TOS-Server 端用来查请求来源
  bucket: process.env.TOS_BUCKET ?? "lv-mcpbase-webui-store-boe",
  signatureVersion: process.env.TOS_IDC !== "boe" ? "sign_plain" : "sign_v1",
  accessKey: process.env.TOS_AK,
  secretKey: process.env.TOS_SK,
  idleTimeout: 60000, //(单位:毫秒) 设置为60秒; 如果不设置，默认值也是0秒，即:不超时 (Node.js版本>v13.x HTTP处理请求超时时间)
  reqTimeout: 10000, // (单位:毫秒) 设置为10秒; 如果不设置，默认值也是10秒(TOS处理请求超时时间)
  psm: "toutiao.tos.tosapi", // 明确指定访问TOS的该PSM (默认值: 'toutiao.tos.tosapi')
  consulLookUpOption: {
    env: "prod",
    cluster: "default", // 明确指定访问TOS该PSM下的某个cluster
    idc: process.env.TOS_IDC ?? "boe", // 明确指定访问IDC (默认值: 与consul节点相同IDC)
    addrfam: process.env.TOS_IDC !== "boe" ? "v6" : "dual-stack",
    unique: process.env.TOS_IDC !== "boe" ? "v6" : "v4", // 明确指定仅用IPv4
  },
  enableCrc64: false, // 使用Crc64进行数据一致性检验
};

// 初始化 tosClient
export const tosClient = new Tos(options); // 每个桶初始化1次即可复用 (不要反复初始化)
