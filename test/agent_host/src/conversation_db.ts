import * as express from "express";
// import * as fs from "fs"; // Removed fs
// import * as path from "path"; // Removed path
import { tosClient } from "./services/tosService"; // Added tosClient
import {
  BaseMessage,
  mapChatMessagesToStoredMessages,
  mapStoredMessagesToChatMessages,
  MessageContentText,
  SystemMessage,
} from "@langchain/core/messages";
import { UserInfo } from "./user"; // Assuming UserInfo is exported from user.ts

// const baseFilesDir = path.join(__dirname, "../conversation_db"); // Removed

// function getUserFilesDir(uid: string): string { // Removed
//   const userDir = path.join(baseFilesDir, uid);
//   if (!fs.existsSync(userDir)) {
//     fs.mkdirSync(userDir, { recursive: true });
//   }
//   return userDir;
// }

function getUserIndexObjectKey(uid: string): string {
  // Renamed and updated
  return `${uid}/conversation_index.json`;
}

function getConversationObjectKey(uid: string, conversationId: string): string {
  // Renamed and updated
  return `${uid}/${conversationId}.json`;
}

interface ConversationIndexEntry {
  conversationId: string;
  uid: string;
  updatedAt: string;
  firstMessageText: string;
}

type ConversationIndex = ConversationIndexEntry[];

export const conversationMemoryDB: Record<
  string,
  Record<string, BaseMessage[]>
> = {};

export const conversationDBRouter = express.Router();

conversationDBRouter.post("/conversation_list", async (req: any, res: any) => {
  // Made async
  const userInfo = (req as any).userInfo as UserInfo;
  if (!userInfo || !userInfo.uid) {
    return res.status(400).send("User ID (uid) is missing from userInfo.");
  }
  try {
    const index = await readConversationIndex(userInfo.uid); // Awaited
    res.json(index);
  } catch (error) {
    console.error(
      `Error fetching conversation list for uid ${userInfo.uid}:`,
      error
    );
    res.status(500).send("Error fetching conversation list.");
  }
});

conversationDBRouter.post(
  "/conversation/:conversationId/:uid",
  async (req: any, res: any) => {
    // Made async
    const conversationId = req.params.conversationId;
    const uid = req.params.uid;
    const objectKey = getConversationObjectKey(uid, conversationId);
    try {
      const result = await tosClient.getObject(objectKey);
      if (result && result.objectBuffer) {
        res.send(result.objectBuffer.toString("utf-8"));
      } else {
        res.status(404).send("Conversation not found");
      }
    } catch (error: any) {
      // Assuming tosClient.getObject throws an error for non-existent keys (e.g., NoSuchKey)
      // This error handling might need adjustment based on the specific errors thrown by tosClient
      if (error.name === "NoSuchKey" || error.statusCode === 404) {
        // Example error check
        res.status(404).send("Conversation not found");
      } else {
        console.error(
          `Error fetching conversation ${conversationId} for uid ${uid} from TOS:`,
          error
        );
        res.status(500).send("Error fetching conversation.");
      }
    }
  }
);

async function readConversationIndex(uid: string): Promise<ConversationIndex> {
  // Made async
  const objectKey = getUserIndexObjectKey(uid);
  try {
    const result = await tosClient.getObject(objectKey);
    if (result && result.objectBuffer) {
      return JSON.parse(
        result.objectBuffer.toString("utf-8")
      ) as ConversationIndex;
    }
    return []; // Should not happen if object exists and has content, but as a fallback
  } catch (error: any) {
    return []; // Return empty index on other errors
  }
}

async function writeConversationIndex(
  uid: string,
  index: ConversationIndex
): Promise<void> {
  // Made async
  const objectKey = getUserIndexObjectKey(uid);
  try {
    await tosClient.putObject(
      objectKey,
      Buffer.from(JSON.stringify(index, null, 2))
    );
  } catch (error) {
    console.error(
      `Error writing conversation index to TOS for uid ${uid}, key ${objectKey}:`,
      error
    );
    // Potentially re-throw or handle more gracefully depending on requirements
  }
}

async function updateConversationIndex( // Made async
  uid: string,
  conversationId: string,
  messages: BaseMessage[]
): Promise<void> {
  // Added Promise<void>
  const index = await readConversationIndex(uid); // Awaited
  const existingEntryIndex = index.findIndex(
    (entry) => entry.conversationId === conversationId
  );

  let firstUserMessageText = "";
  for (const msg of messages) {
    if (!(msg instanceof SystemMessage) && typeof msg.content === "string") {
      firstUserMessageText = msg.content.substring(0, 100);
      break;
    }
    if (!(msg instanceof SystemMessage) && Array.isArray(msg.content)) {
      const textPart = msg.content.find((part) => part.type === "text");
      if (
        textPart &&
        typeof (textPart as MessageContentText).text === "string"
      ) {
        firstUserMessageText = (textPart as MessageContentText).text.substring(
          0,
          100
        );
        break;
      } else if (typeof (msg as any).text === "string") {
        // Adjusted to (msg as any).text for safety
        firstUserMessageText = (msg as any).text.substring(0, 100);
        break;
      }
    }
  }

  const now = new Date().toISOString(); // Using ISOString for consistency

  if (existingEntryIndex !== -1) {
    index[existingEntryIndex].updatedAt = now;
    if (!index[existingEntryIndex].firstMessageText && firstUserMessageText) {
      index[existingEntryIndex].firstMessageText = firstUserMessageText;
    }
  } else {
    index.push({
      conversationId,
      uid,
      updatedAt: now,
      firstMessageText: firstUserMessageText,
    });
  }

  await writeConversationIndex(uid, index); // Awaited
}

export async function dumpConversationToTos( // Renamed and made async
  uid: string,
  conversationId: string
): Promise<void> {
  // Added Promise<void>
  if (
    !conversationMemoryDB[uid] ||
    !conversationMemoryDB[uid][conversationId]
  ) {
    console.warn(
      `No messages in memory for uid ${uid}, conversationId ${conversationId} to dump.`
    );
    return;
  }
  const messages = conversationMemoryDB[uid][conversationId];
  if (messages) {
    await updateConversationIndex(uid, conversationId, messages); // Awaited
    const serialized = mapChatMessagesToStoredMessages(messages);
    const objectKey = getConversationObjectKey(uid, conversationId);
    try {
      await tosClient.putObject(
        objectKey,
        Buffer.from(JSON.stringify(serialized, null, 2))
      );
    } catch (err) {
      console.error(
        `Error writing conversation to TOS for uid ${uid}, conversationId ${conversationId}, key ${objectKey}:`,
        err
      );
    }
  }
}

export async function loadConversationFromTos( // Renamed and made async
  uid: string,
  conversationId: string
): Promise<void> {
  // Added Promise<void>
  const objectKey = getConversationObjectKey(uid, conversationId);
  try {
    const result = await tosClient.getObject(objectKey);
    if (result && result.objectBuffer) {
      const serialized = JSON.parse(result.objectBuffer.toString("utf-8"));
      const deserialized = mapStoredMessagesToChatMessages(serialized);
      if (!conversationMemoryDB[uid]) {
        conversationMemoryDB[uid] = {};
      }
      conversationMemoryDB[uid][conversationId] = deserialized;
    }
  } catch (error: any) {
    // If the object does not exist, tosClient.getObject might throw an error (e.g., NoSuchKey)
    if (error.name === "NoSuchKey" || error.statusCode === 404) {
      // Example error check
      // Conversation file doesn't exist, which is a valid scenario (e.g., new conversation)
      // No need to log an error here, just means no data to load.
      return;
    }
  }
}

export async function updateMessagePayload(option: {
  uid: string;
  conversationId: string;
  messageId: string;
  payload: string;
}) {
  const { uid, conversationId, messageId, payload } = option;

  // 1. Load conversation from TOS (this will also update in-memory DB)
  await loadConversationFromTos(uid, conversationId);

  const messages = conversationMemoryDB[uid]?.[conversationId];

  if (!messages) {
    console.error(
      `Conversation not found in memory for uid ${uid}, conversationId ${conversationId} after attempting to load.`
    );
    // Optionally, you could throw an error here or handle it as per your application's needs
    return;
  }

  // 2. Find the message by messageId
  const messageIndex = messages.findIndex((msg) => msg.id === messageId);

  if (messageIndex === -1) {
    console.error(
      `Message with id ${messageId} not found in conversation ${conversationId} for uid ${uid}.`
    );
    // Optionally, throw an error or handle
    return;
  }

  // 3. Update the additional_kwargs.payload
  const messageToUpdate = messages[messageIndex];
  if (!messageToUpdate.additional_kwargs) {
    messageToUpdate.additional_kwargs = {};
  }
  messageToUpdate.additional_kwargs.payload = payload;

  // Update the message in the array
  conversationMemoryDB[uid][conversationId][messageIndex] = messageToUpdate;

  // 4. Save the updated conversation back to TOS (this also updates the index via dumpConversationToTos's internal call to updateConversationIndex)
  await dumpConversationToTos(uid, conversationId);

  // 5. Explicitly update the conversation index again to ensure the updatedAt timestamp is fresh if dumpConversationToTos doesn't cover all cases
  // or if a more direct update is preferred after a specific message modification.
  // However, dumpConversationToTos already calls updateConversationIndex. If the goal is just to reflect the change,
  // the call within dumpConversationToTos should be sufficient. If a separate, more immediate index update is needed
  // for other reasons (e.g., specific logic tied to payload updates), it can be done here.
  // For now, relying on dumpConversationToTos's existing behavior.
  // await updateConversationIndex(uid, conversationId, messages); // This might be redundant if dumpConversationToTos handles it

  console.log(
    `Payload updated for message ${messageId} in conversation ${conversationId} for uid ${uid}.`
  );
}