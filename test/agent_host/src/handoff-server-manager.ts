import { ChatAnthropicToolType } from "@langchain/anthropic/dist/types";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenAI from "openai";
import { BytedMcpClient, MCP_GATEWAY_REGION } from "@byted/mcp-client-sdk";
import Logger from "@byted-service/logger";
import { LLMOptions } from "./llm_options";
import { UserInfo } from "./user";
const logger = new Logger();

type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;

export class HandoffTool {
  def: OpenAIChatCompletionTool;

  constructor(def: OpenAIChatCompletionTool) {
    this.def = def;
  }

  call(option: {
    functionCallArguments?: any;
    functionCallId: string;
    streamingCallback: (result: string) => void;
    finishCallback: (result: string) => void;
  }) {}
}

export class HandoffServerManager {
  private toolsMapping: Map<string, HandoffTool> = new Map();

  registerTool(tool: HandoffTool): void {
    this.toolsMapping.set(tool.def.function.name, tool);
  }

  hasTool(name: string): boolean {
    return this.toolsMapping.has(name);
  }

  getTools(): OpenAIChatCompletionTool[] {
    let tools: OpenAIChatCompletionTool[] = [];
    this.toolsMapping.forEach((tool) => {
      tools.push(tool.def);
    });
    return tools;
  }

  async callTool(option: {
    functionNameWithPrefix: string;
    functionCallArguments?: any;
    functionCallId: string;
    streamingCallback?: (result: string) => void;
    finishCallback?: (result: string) => void;
  }): Promise<void>  {
    const tool = this.toolsMapping.get(option.functionNameWithPrefix);
    if (!tool) {
      throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
    }
    await tool.call({
      functionCallArguments: option.functionCallArguments,
      functionCallId: option.functionCallId,
      streamingCallback: option.streamingCallback!,
      finishCallback: option.finishCallback!,
    });
  }
}
