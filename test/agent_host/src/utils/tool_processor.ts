import { BaseMessage, AIMessage, ToolMessage } from "@langchain/core/messages";
import { ID<PERSON>hat, IDLSSE } from "@byted/nurosdk-js";
import { MCPCloudServerManager } from "../mcp-cloud-server-manager";
import { IDLChatRequest } from "@byted/nurosdk-js";
import { ServerResponse } from "http";
import { HandoffServerManager } from "src/handoff-server-manager";

export interface ToolCall {
  action: "init_call" | "update_call";
  tool_call_id: string;
  function_name: string;
  function_args: Record<string, any>; // Changed to any to handle parsed JSON
  function_args_str: string;
  function_args_str_delta: string;
}

/**
 * Formats client-defined tools based on the target model (Claude or OpenAI).
 *
 * @param requestBody The request body containing potential client tools.
 * @param isClaude Boolean indicating if the target model is Claude.
 * @returns An array of formatted tools compatible with the target model.
 */
export function formatClientTools(
  requestBody: IDLChatRequest.ChatRequest,
  isClaude: boolean
): any[] {
  return (
    requestBody.messages?.[0].tools
      ?.filter((it) => it.type === "client_function")
      .map((it) => {
        const toolData = JSON.parse(it.parameters ?? "{}");
        const inputSchema = toolData.inputSchema
          ? toolData.inputSchema
          : toolData;
        // Clean up common schema properties that might cause issues
        delete inputSchema["$schema"];
        delete inputSchema["additionalProperties"];

        if (isClaude) {
          return {
            name: `${it.name}_${toolData.name}`, // Claude might require unique names
            description: it.description ?? toolData.description,
            input_schema: inputSchema,
          };
        } else {
          return {
            type: "function",
            function: {
              name: it.name ?? toolData.name,
              description: it.description ?? toolData.description,
              parameters: inputSchema,
            },
          };
        }
      }) ?? []
  );
}

/**
 * Processes tool call chunks from the LLM stream, assembling complete tool calls.
 *
 * @param chunk The current chunk from the LLM stream.
 * @param currentToolCalls The array of tool calls being assembled.
 * @param isClaude Boolean indicating if the model is Claude (affects indexing).
 * @returns The updated array of tool calls.
 */
export function processToolCallChunks(
  chunk: any, // Type depends on the specific LLM SDK
  currentToolCalls: ToolCall[],
  isClaude: boolean
): ToolCall[] {
  const updatedToolCalls = [...currentToolCalls];

  // Handle initial tool call information (name, id, initial args)
  if (
    chunk.tool_calls &&
    chunk.tool_calls.length > 0 &&
    chunk.tool_calls.some((it: any) => it.name) // Check if at least one tool call has a name
  ) {
    chunk.tool_calls.forEach((it: any) => {
      const toolCall: ToolCall = {
        action: "init_call",
        tool_call_id:
          it.id || `tool_${Math.random().toString(36).substring(2, 9)}`, // Generate ID if missing
        function_name: it.name,
        function_args: it.args || {}, // Initialize args as object
        function_args_str:
          typeof it.args === "object"
            ? JSON.stringify(it.args)
            : it.args || "{}",
        function_args_str_delta: "",
      };

      const existingIndex = updatedToolCalls.findIndex(
        (tc) => tc.tool_call_id === toolCall.tool_call_id
      );

      if (existingIndex === -1) {
        updatedToolCalls.push(toolCall);
      } else {
        // Update existing entry if necessary (e.g., if initial args were incomplete)
        updatedToolCalls[existingIndex] = {
          ...updatedToolCalls[existingIndex],
          ...toolCall,
          // Ensure function_args_str is updated if args object exists
          function_args_str: toolCall.function_args_str,
        };
      }
    });
  }

  updatedToolCalls.forEach((it, index) => {
    it.function_args_str_delta = "";
  });

  // Handle streaming arguments chunks
  if (chunk.tool_call_chunks && chunk.tool_call_chunks.length > 0) {
    chunk.tool_call_chunks.forEach((itChunk: any) => {
      let toolIndex = itChunk.index;
      if (toolIndex !== undefined) {
        // Adjust index for Claude if necessary (needs verification based on actual Claude behavior)
        if (isClaude) {
          toolIndex = toolIndex - 1;
        }

        if (updatedToolCalls[toolIndex] && typeof itChunk.args === "string") {
          // Append string arguments
          if (updatedToolCalls[toolIndex].function_args_str === "{}") {
            updatedToolCalls[toolIndex].function_args_str = ""; // Clear empty object placeholder
          } else {
            updatedToolCalls[toolIndex].action = "update_call";
          }
          updatedToolCalls[toolIndex].function_args_str += itChunk.args;
          updatedToolCalls[toolIndex].function_args_str_delta = itChunk.args;
        }
      }
    });
  }

  return updatedToolCalls;
}

/**
 * Finalizes tool calls by parsing arguments and sends tool call information via SSE.
 *
 * @param currentToolCalls The assembled tool calls.
 * @param mcpCloudServerManager Instance to check if a tool is server-side.
 * @param res The ServerResponse object for sending SSE events.
 */
export async function finalizeAndSendToolCalls(
  currentToolCalls: ToolCall[],
  mcpCloudServerManager: MCPCloudServerManager,
  handoffServerManager: HandoffServerManager,
  res: ServerResponse
): Promise<void> {
  for (let index = 0; index < currentToolCalls.length; index++) {
    const toolCall = currentToolCalls[index];
    try {
      // Attempt to parse the accumulated arguments string
      toolCall.function_args = JSON.parse(toolCall.function_args_str || "{}");
    } catch (e) {
      console.warn(
        `Failed to parse JSON arguments for tool ${toolCall.function_name} (ID: ${toolCall.tool_call_id}). Args string: ${toolCall.function_args_str}`,
        e
      );
      // Keep args as an empty object or handle error as needed
      toolCall.function_args = {};
    }

    const toolCallDelta = new IDLSSE.SSEDeltaMessage();
    toolCallDelta.op = "replace";
    toolCallDelta.path = `/message/tool_calls/${index}`;

    const chatToolCall = new IDLChat.ChatToolCall();
    chatToolCall.id = toolCall.tool_call_id;
    chatToolCall.streaming = false;
    chatToolCall.type =
      mcpCloudServerManager.hasTool(toolCall.function_name) ||
      handoffServerManager.hasTool(toolCall.function_name)
        ? IDLChat.ChatToolCallType.server_function
        : IDLChat.ChatToolCallType.client_function;
    chatToolCall._func = new IDLChat.ChatToolCallFunc();
    chatToolCall._func.name = toolCall.function_name;
    // Send the raw string arguments as received/assembled
    chatToolCall._func.arguments = toolCall.function_args_str;
    toolCallDelta.value = chatToolCall.toJSONString();

    res.write(
      `id:${Math.random()
        .toString(36)
        .substring(
          2,
          15
        )}\nevent:delta\ndata: ${toolCallDelta.toJSONString()}\n\n`
    );
  }
}

/**
 * Executes server-side tools and sends results via SSE, updating conversation history.
 *
 * @param currentToolCalls The finalized tool calls.
 * @param mcpCloudServerManager The MCP manager instance.
 * @param messages The current conversation history.
 * @param res The ServerResponse object for sending SSE events.
 * @returns A boolean indicating if any server-side function was executed.
 */
export async function executeServerSideTools(
  conversationId: string,
  currentToolCalls: ToolCall[],
  mcpCloudServerManager: MCPCloudServerManager,
  handoffServerManager: HandoffServerManager,
  messages: BaseMessage[],
  res: ServerResponse
): Promise<boolean> {
  let hasServerSideFunctionExecuted = false;

  for (let index = 0; index < currentToolCalls.length; index++) {
    const toolCall = currentToolCalls[index];

    if (mcpCloudServerManager.hasTool(toolCall.function_name)) {
      hasServerSideFunctionExecuted = true;
      let toolCallResult = "";
      try {
        toolCallResult = await mcpCloudServerManager.callTool({
          functionNameWithPrefix: toolCall.function_name,
          functionCallArguments: toolCall.function_args, // Use parsed args
          functionCallId: toolCall.tool_call_id,
        });
      } catch (error: any) {
        console.error(
          `Error executing server tool ${toolCall.function_name}:`,
          error
        );
        toolCallResult = `Error executing tool: ${error.message || error}`;
      }

      // Send tool result via SSE
      const toolResultMessage = new IDLChat.ChatMessage();
      const chatMessageMetadata = new IDLChat.ChatMessageMetadata();
      chatMessageMetadata.conversation_id = conversationId;
      chatMessageMetadata.tool_call_id = toolCall.tool_call_id;
      toolResultMessage.metadata = chatMessageMetadata;
      toolResultMessage.id = "tool_result_" + toolCall.tool_call_id;
      toolResultMessage.status =
        IDLChat.ChatMessageStatus.finished_successfully;
      toolResultMessage.author = new IDLChat.ChatAuthor();
      toolResultMessage.author.role = "tool";
      const content = new IDLChat.ChatContent();
      content.content_type = IDLChat.ChatContentType.text;
      const resultContent = new IDLChat.ChatContentPart();
      resultContent.text = toolCallResult;
      content.content_parts = [resultContent];
      toolResultMessage.content = content;
      res.write(
        `id:${Math.random()
          .toString(36)
          .substring(
            2,
            15
          )}\nevent:message\ndata: ${toolResultMessage.toJSONString()}\n\n`
      );

      // Add ToolMessage to conversation history
      messages.push(
        new ToolMessage({
          tool_call_id: toolCall.tool_call_id,
          content: toolCallResult,
        })
      );
    } else if (handoffServerManager.hasTool(toolCall.function_name)) {
      hasServerSideFunctionExecuted = true;

      const toolResultMessage = new IDLChat.ChatMessage();
      const chatMessageMetadata = new IDLChat.ChatMessageMetadata();
      chatMessageMetadata.conversation_id = conversationId;
      chatMessageMetadata.tool_call_id = toolCall.tool_call_id;
      toolResultMessage.metadata = chatMessageMetadata;
      toolResultMessage.id = "tool_result_" + toolCall.tool_call_id;
      toolResultMessage.status = IDLChat.ChatMessageStatus.in_progress;
      toolResultMessage.author = new IDLChat.ChatAuthor();
      toolResultMessage.author.role = "tool";
      const content = new IDLChat.ChatContent();
      content.content_type = IDLChat.ChatContentType.text;
      const resultContent = new IDLChat.ChatContentPart();
      resultContent.text = "";
      content.content_parts = [resultContent];
      toolResultMessage.content = content;
      res.write(
        `id:${Math.random()
          .toString(36)
          .substring(
            2,
            15
          )}\nevent:message\ndata: ${toolResultMessage.toJSONString()}\n\n`
      );

      await handoffServerManager.callTool({
        functionNameWithPrefix: toolCall.function_name,
        functionCallArguments: toolCall.function_args, // Use parsed args
        functionCallId: toolCall.tool_call_id,
        streamingCallback: (result) => {
          const deltaMessage = new IDLSSE.SSEDeltaMessage();
          deltaMessage.op = "append";
          deltaMessage.path = `/message/content/content_parts/0/text`;
          deltaMessage.value = result;
          res.write(
            `id:${Math.random()
              .toString(36)
              .substring(
                2,
                15
              )}\nevent:delta\ndata: ${deltaMessage.toJSONString()}\n\n`
          );
        },
        finishCallback: (result) => {
          const deltaMessage2 = new IDLSSE.SSEDeltaMessage();
          deltaMessage2.op = "replace";
          deltaMessage2.path = `/message/status`;
          deltaMessage2.value = "finished_successfully";
          res.write(
            `id:${Math.random()
              .toString(36)
              .substring(
                2,
                15
              )}\nevent:delta\ndata: ${deltaMessage2.toJSONString()}\n\n`
          );
          messages.push(
            new ToolMessage({
              tool_call_id: toolCall.tool_call_id,
              content: result,
            })
          );
        },
      });
    }
  }
  return hasServerSideFunctionExecuted;
}

/**
 * Adds the AI's response (including tool calls) to the message history.
 *
 * @param currentToolCalls The list of tool calls made by the AI.
 * @param currentTextContent The text content generated by the AI.
 * @param messages The conversation history array.
 * @param chatMessageId The ID of the current AI message.
 * @param isClaude Flag indicating if the model is Claude.
 */
export function addAiResponseToHistory(
  currentToolCalls: ToolCall[],
  currentTextContent: string,
  currentReasoningContent: string,
  messages: BaseMessage[],
  chatMessageId: string,
  isClaude: boolean
): void {
  if (currentToolCalls.length > 0) {
    // For Claude, add a specific AIMessage structure for tool use
    if (isClaude) {
      messages.push(
        new AIMessage({
          id: chatMessageId,
          content: currentToolCalls.map((it) => ({
            id: it.tool_call_id,
            type: "tool_use",
            name: it.function_name,
            input: it.function_args, // Use parsed args
          })) as any, // Claude tool use message might not have text content part
          // Langchain's Claude integration might expect tool_calls here instead
          // tool_calls: currentToolCalls.map(it => ({ ... }))
        })
      );
    }
    messages.push(
      new AIMessage({
        id: chatMessageId,
        content: currentTextContent, // Include text content if generated alongside tool calls
        tool_calls: currentToolCalls.map((it) => ({
          id: it.tool_call_id,
          name: it.function_name,
          args:
            (() => {
              try {
                return JSON.parse(it.function_args_str);
              } catch (error) {}
            })() ?? it.function_args, // Use parsed args
          type: "tool_call", // Explicitly set type if needed by downstream processing
        })),
        additional_kwargs: {
          reasoning_content: currentReasoningContent,
        },
      })
    );
  } else {
    // If no tool calls, just add the text content
    messages.push(
      new AIMessage({
        id: chatMessageId,
        content: currentTextContent,
        additional_kwargs: {
          reasoning_content: currentReasoningContent,
        },
      })
    );
  }
}
