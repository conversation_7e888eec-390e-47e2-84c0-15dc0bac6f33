import axios from "axios";
import * as mime from "mime-types";
import {
  BaseMessage,
  HumanMessage,
  ToolMessage,
} from "@langchain/core/messages";
import { IDLChatRequest, IDLChat } from "@byted/nurosdk-js";

/**
 * Processes incoming messages from the request body and updates the conversation history.
 * Handles text messages and file uploads (converting files to base64 data URLs).
 *
 * @param requestBody The parsed request body containing messages.
 * @param currentMessages The current array of BaseMessage objects representing the conversation history.
 * @returns The updated array of BaseMessage objects.
 */
export async function processIncomingMessages(
  requestBody: IDLChatRequest.ChatRequest,
  currentMessages: BaseMessage[]
): Promise<BaseMessage[]> {
  const updatedMessages = currentMessages;

  for (const message of requestBody.messages ?? []) {
    if (message.content?.content_parts) {
      for (const part of message.content.content_parts) {
        if (message.metadata?.tool_call_id !== undefined) {
          // Handle tool responses
          updatedMessages.push(
            new ToolMessage({
              tool_call_id: message.metadata.tool_call_id,
              content: part.text ?? "",
            })
          );
        } else {
          // Handle regular user messages (text or file)
          const prefix = part.is_referenced ? "用户引用内容：" : "";
          if (part.text) {
            const humanMsg = new HumanMessage({
              id: message.id,
              content: prefix + (part.text ?? ""),
            });
            updatedMessages.push(humanMsg);
          } else if (part.file?.url) {
            if (part.file?.file_type === IDLChat.ChatMessageFileType.IMAGE) {
              try {
                const fileUrl = part.file.url;
                const response = await axios.get(fileUrl, {
                  responseType: "arraybuffer",
                });
                const buffer = Buffer.from(response.data);
                const base64String = buffer.toString("base64");
                const mimeType =
                  response.headers["content-type"] ??
                  (mime.lookup(fileUrl.split("?")[0]) ||
                    "application/octet-stream"); // Default MIME type
                const dataUrl = `data:${mimeType};base64,${base64String}`;
                const humanMsg = new HumanMessage({
                  id: message.id,
                  content: [
                    {
                      type: "image_url",
                      image_url: {
                        url: dataUrl,
                      },
                    },
                    {
                      type: "text",
                      text: prefix
                        ? "用户引用了一张图片， 图片 URL = " + fileUrl
                        : "用户上传了一张图片，图片 URL = " + fileUrl,
                    },
                  ],
                });
                updatedMessages.push(humanMsg);
              } catch (error) {
                console.error(
                  `Error fetching or processing file from URL ${part.file.url}:`,
                  error
                );
                // Optionally push an error message or skip adding the message
                // updatedMessages.push(new SystemMessage(`Error processing file: ${part.file.url}`));
              }
            } else if (
              part.file?.file_type === IDLChat.ChatMessageFileType.VIDEO
            ) {
              const fileUrl = part.file.url;
              const humanMsg = new HumanMessage({
                id: message.id,
                content: [
                  {
                    type: "text",
                    text: prefix
                      ? "用户引用了一个视频， 视频 URL = " + fileUrl
                      : "用户上传了一个视频，视频 URL = " + fileUrl,
                  },
                ],
              });
              updatedMessages.push(humanMsg);
            }
          }
        }
      }
    }
  }

  return updatedMessages;
}
