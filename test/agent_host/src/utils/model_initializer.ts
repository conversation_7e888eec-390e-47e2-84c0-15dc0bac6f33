import { Chat<PERSON><PERSON>A<PERSON> } from "@langchain/openai";
import { ChatDeepSeek } from "@langchain/deepseek";
import { ChatAnthropic } from "@langchain/anthropic";
import { LLMOptions } from "../llm_options";

/**
 * Initializes and returns the appropriate language model instance based on the provided options
 * and whether the conversation history contains images.
 *
 * @param llmOptions The LLM configuration options.
 * @param hasImageInHistory A boolean indicating if the conversation history includes images.
 * @returns An instance of ChatOpenAI, ChatDeepSeek, or ChatAnthropic.
 */
export function initializeModel(
  llmOptions: LLMOptions,
): ChatOpenAI | ChatDeepSeek | ChatAnthropic {
  const isClaude = llmOptions.modelName.includes("claude");

  if (isClaude) {
    let endpoint = llmOptions.endpoint;
    // Anthropic SDK expects the base URL without /v1
    if (endpoint.endsWith("/v1")) {
      endpoint = endpoint.replace("/v1", "");
    }
    return new ChatAnthropic({
      model: llmOptions.modelName,
      anthropicApiUrl: endpoint,
      anthropicApiKey: llmOptions.apiKey,
      apiKey: llmOptions.apiKey, // Redundant but required by the constructor
      temperature: llmOptions.temperature,
    });
  } else if (llmOptions.modelName === "deepseek-r1") {
    // Specific handling for deepseek-r1 without images
    // Note: Ensure DEEPSEEK_API_KEY is set in the environment or passed correctly
    return new ChatDeepSeek({
      model: "ep-20250214162234-cfq82", // Specific model ID for r1
      configuration: {
        baseURL: llmOptions.endpoint,
        apiKey: llmOptions.apiKey,
      },
      temperature: llmOptions.temperature,
    });
  } else {
    // Default to ChatOpenAI for other models or when deepseek-r1 has images
    // Handles general OpenAI compatible models and image models like doubao-vlm
    return new ChatOpenAI({
      model: llmOptions.modelName,
      temperature: llmOptions.temperature,
      configuration: {
        baseURL: llmOptions.endpoint,
        apiKey: llmOptions.apiKey,
      },
    });
  }
}