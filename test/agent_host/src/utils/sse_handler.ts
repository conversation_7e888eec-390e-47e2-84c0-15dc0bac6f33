import { ServerResponse } from "http";
import { IDLChat, IDLSSE } from "@byted/nurosdk-js";
import { ToolCall, processToolCallChunks } from "./tool_processor";
import { AIMessageChunk } from "@langchain/core/messages";
import { MCPCloudServerManager } from "src/mcp-cloud-server-manager";

/**
 * Sends the initial message structure via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 * @returns The generated message ID.
 */
export function sendInitialMessage(
  res: ServerResponse,
  conversationId: string
): string {
  const msg_id = Math.random().toString(36).substring(2, 15);
  const chatMessage = new IDLChat.ChatMessage();
  const chatMessageMetadata = new IDLChat.ChatMessageMetadata();
  chatMessageMetadata.conversation_id = conversationId;
  chatMessage.metadata = chatMessageMetadata;
  chatMessage.id = msg_id;
  chatMessage.status = IDLChat.ChatMessageStatus.in_progress;
  chatMessage.author = new IDLChat.ChatAuthor();
  chatMessage.author.role = "assistant";

  res.write(
    `id:${Math.random()
      .toString(36)
      .substring(
        2,
        15
      )}\nevent:message\ndata: ${chatMessage.toJSONString()}\n\n`
  );
  return msg_id;
}

/**
 * Processes a chunk from the LLM stream, handling text, reasoning, and tool calls.
 *
 * @param chunk The chunk received from the LLM stream.
 * @param res The ServerResponse object for sending SSE deltas.
 * @param currentToolCalls The current array of assembled tool calls.
 * @param isClaude Flag indicating if the model is Claude.
 * @returns An object containing the updated text content and tool calls.
 */
export function processStreamChunk(
  chunk: AIMessageChunk, // Type depends on the specific LLM SDK
  mcpCloudServerManager: MCPCloudServerManager,
  res: ServerResponse,
  currentToolCalls: ToolCall[],
  isClaude: boolean
): {
  reasoningContentDelta?: string;
  textContentDelta: string;
  updatedToolCalls: ToolCall[];
} {
  let textContentDelta = "";
  let updatedToolCalls = [...currentToolCalls];

  const reasoningContentDelta: Optional<string> = chunk.additional_kwargs?.[
    "reasoning_content"
  ] as string;
  if (reasoningContentDelta) {
    const reasoningDelta = new IDLSSE.SSEDeltaMessage();
    reasoningDelta.op = "append";
    reasoningDelta.path = "/message/content/content_parts/0/reasoning_content";
    reasoningDelta.value = reasoningContentDelta as string;
    res.write(
      `id:${Math.random()
        .toString(36)
        .substring(
          2,
          15
        )}\nevent:delta\ndata: ${reasoningDelta.toJSONString()}\n\n`
    );
  }
  if (chunk.content) {
    // Handle text content
    const delta = new IDLSSE.SSEDeltaMessage();
    delta.op = "append";
    delta.path = "/message/content/content_parts/0/text";
    delta.value = "";

    if (isClaude && chunk.content instanceof Array) {
      chunk.content.forEach((it: any) => {
        if (it.type === "text" && it.text) {
          delta.value += it.text;
          textContentDelta += it.text;
        }
      });
    } else if (typeof chunk.content === "string") {
      delta.value = chunk.content as string;
      textContentDelta += chunk.content as string;
    }

    if (delta.value) {
      // Only write if there's text content
      res.write(
        `id:${Math.random()
          .toString(36)
          .substring(2, 15)}\nevent:delta\ndata: ${delta.toJSONString()}\n\n`
      );
    }
  }

  updatedToolCalls = processToolCallChunks(chunk, currentToolCalls, isClaude);

  for (let index = 0; index < updatedToolCalls.length; index++) {
    const toolCall = updatedToolCalls[index];
    if (toolCall.action === "init_call") {
      const toolCallDelta = new IDLSSE.SSEDeltaMessage();
      toolCallDelta.op = "add";
      toolCallDelta.path = `/message/tool_calls/${index}`;
      const chatToolCall = new IDLChat.ChatToolCall();
      chatToolCall.id = toolCall.tool_call_id;
      chatToolCall.type = mcpCloudServerManager.hasTool(toolCall.function_name)
        ? IDLChat.ChatToolCallType.server_function
        : IDLChat.ChatToolCallType.client_function;
      chatToolCall.streaming = true;
      chatToolCall._func = new IDLChat.ChatToolCallFunc();
      chatToolCall._func.name = toolCall.function_name;
      // Send the raw string arguments as received/assembled
      chatToolCall._func.arguments = toolCall.function_args_str;
      toolCallDelta.value = chatToolCall.toJSONString();
      res.write(
        `id:${Math.random()
          .toString(36)
          .substring(
            2,
            15
          )}\nevent:delta\ndata: ${toolCallDelta.toJSONString()}\n\n`
      );
    } else if (
      toolCall.action === "update_call" &&
      toolCall.function_args_str_delta
    ) {
      const toolCallDelta = new IDLSSE.SSEDeltaMessage();
      toolCallDelta.op = "append";
      toolCallDelta.path = `/message/tool_calls/${index}/func/arguments`;
      toolCallDelta.value = toolCall.function_args_str_delta;
      res.write(
        `id:${Math.random()
          .toString(36)
          .substring(
            2,
            15
          )}\nevent:delta\ndata: ${toolCallDelta.toJSONString()}\n\n`
      );
    }
  }

  return { reasoningContentDelta, textContentDelta, updatedToolCalls };
}

/**
 * Sends the final status update and stream completion messages via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 */
export function sendFinalMessages(
  res: ServerResponse,
  conversationId: string
): void {
  // Send final status delta
  const finalDelta = new IDLSSE.SSEDeltaMessage();
  finalDelta.op = "replace";
  finalDelta.path = "/message/status";
  finalDelta.value = IDLChat.ChatMessageStatus.finished_successfully;
  res.write(
    `id:${Math.random()
      .toString(36)
      .substring(2, 15)}\nevent:delta\ndata: ${finalDelta.toJSONString()}\n\n`
  );

  // Send stream complete system message
  res.write(
    `id:${Math.random()
      .toString(36)
      .substring(2, 15)}\nevent:system\ndata: ${JSON.stringify({
      type: "stream_complete",
      conversation_id: conversationId, // Include actual conversation ID
    })}\n\n`
  );

  // Send stream complete system message
  res.write(`${JSON.stringify({ ret: "0", logid: conversationId })}\n\n`);
}
