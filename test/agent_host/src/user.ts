import { Request, Response } from "express";

const aid = "1";

export class UserInfo {
  aid: string = aid;
  uid: string;
  did: string;

  constructor(uid?: string, did?: string) {
    this.uid = uid || User.generateRandomId();
    this.did = did || User.generateRandomId();
  }
}

export class User {
  static generateRandomId(length: number = 16): string {
    return Math.random()
      .toString(36)
      .substring(2, 2 + length);
  }

  static getUserInfoFromRequest(req: Request): UserInfo {
    if (req.headers["x-nuro-did"]) {
      return new UserInfo(
        req.headers["x-nuro-uid"] as string,
        req.headers["x-nuro-did"] as string
      );
    }
    if (req.headers["user-agent"]) {
      const userAgent = req.headers["user-agent"].toLowerCase();
      const browserPatterns = [
        /chrome/,
        /firefox/,
        /safari/,
        /opera/,
        /msie/,
        /trident/,
        /edge/,
      ];
      const isBrowser = browserPatterns.some((pattern) =>
        pattern.test(userAgent)
      );
      if (!isBrowser) {
        return new UserInfo("uid_default", "did_default");
      }
    } else {
      // No user-agent header, treat as not a browser
      return new UserInfo("uid_default", "did_default");
    }
    // This is a placeholder. In a real Express app, you'd use req.cookies
    // For now, we'll simulate it. This will be properly implemented in server.ts
    const cookies = req.cookies || {};
    let userInfo = cookies.userInfo ? JSON.parse(cookies.userInfo) : null;

    if (!userInfo || !userInfo.uid || !userInfo.did) {
      userInfo = new UserInfo();
    }
    return new UserInfo(userInfo.uid, userInfo.did);
  }

  static setUserInfoToResponse(res: Response, userInfo: UserInfo): void {
    if (process.env.IN_BYTEFAAS) {
      res.cookie("userInfo", JSON.stringify(userInfo), {
        maxAge: 365 * 24 * 60 * 60 * 1000,
        path: "/",
        httpOnly: true,
        secure: true,
        sameSite: "none",
      });
    } else {
      res.cookie("userInfo", JSON.stringify(userInfo), {
        maxAge: 365 * 24 * 60 * 60 * 1000,
        path: "/",
        httpOnly: true,
        sameSite: "lax",
      });
    }
  }
}
