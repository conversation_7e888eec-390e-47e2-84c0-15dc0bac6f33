import { ChatAnthropicToolType } from "@langchain/anthropic/dist/types";
import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenAI from "openai";
import {
  addZtiTokenHook,
  BytedMcpClient,
  BytedMcpClientContext,
  generateBytedServiceLoggerWithLogId,
  generateDefaultLogId,
  MCP_GATEWAY_REGION,
} from "@byted/mcp-client-sdk";
import Logger from "@byted-service/logger";
import { LLMOptions } from "./llm_options";
import { UserInfo } from "./user";

export interface MCPCloudServerConfig {
  name: string;
  url?: string;
  byteFaasConfig?: { psm: string; region: "BOE" | "CN" | "I18N" };
}

type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;

interface ServerConnection {
  config: MCPCloudServerConfig;
  client: Client | BytedMcpClient;
  transport?: SSEClientTransport;
  tools: Tool[];
  isConnected: boolean;
}

export class MCPCloudServerManager {
  private servers: Map<string, ServerConnection> = new Map();
  private toolsMapping: Map<
    string,
    { serverName: string; functionName: string }
  > = new Map();

  async registerServer(
    config: MCPCloudServerConfig,
    llmOptions: LLMOptions,
    userInfo: UserInfo,
    conversationId?: string
  ): Promise<void> {
    if (this.servers.has(config.name)) {
      throw new Error(`Server with id ${config.name} already exists`);
    }

    if (config.url) {
      const transport = new SSEClientTransport(new URL(config.url), {});
      const client = new Client(
        {
          name: config.name,
          version: "1.0.0",
        },
        {
          capabilities: {
            prompts: {},
            resources: {},
            tools: {},
          },
        }
      );

      this.servers.set(config.name, {
        config,
        client,
        transport,
        tools: [],
        isConnected: false,
      });
    } else if (config.byteFaasConfig) {
      const logId = generateDefaultLogId();
      const ctx: BytedMcpClientContext = {
        logger: generateBytedServiceLoggerWithLogId({ logId }),
        logId,
      };
      const client = new BytedMcpClient({
        byte_faas_caller: {
          transport: "http",
          psm: config.byteFaasConfig.psm,
          mcpGatewayRegion: (() => {
            switch (config.byteFaasConfig.region) {
              case "BOE":
                return MCP_GATEWAY_REGION.BOE;
              case "CN":
                return MCP_GATEWAY_REGION.CN;
              case "I18N":
                return MCP_GATEWAY_REGION.I18N;
              default:
                return MCP_GATEWAY_REGION.BOE;
            }
          })(),
          timeout: 60000,
        },
      });
      await client.connectAll({
        ctx: ctx,
      });
      client.registerBeforeCallHook({
        serverName: "byte_faas_caller",
        beforeCallHook: addZtiTokenHook,
        ctx,
      });
      await client.loadAllTools({
        ctx: ctx,
      });
      await client.registerBeforeCallHook({
        serverName: "byte_faas_caller",
        beforeCallHook: (hook: any) => {
          llmOptions.byteFaasHeaders?.forEach((header) => {
            hook.headers[header.key] = header.value;
          });
          hook.headers["x-nuro-aid"] = userInfo.aid;
          hook.headers["x-nuro-uid"] = userInfo.uid;
          hook.headers["x-nuro-did"] = userInfo.did;
          hook.headers["x-nuro-cid"] = conversationId;
        },
        ctx: ctx,
      });
      this.servers.set(config.name, {
        config,
        client,
        transport: undefined,
        tools: [],
        isConnected: false,
      });
    }
  }

  async connectServer(serverName: string): Promise<void> {
    const server = this.servers.get(serverName);
    if (!server) {
      throw new Error(`Server ${serverName} not found`);
    }

    if (!server.isConnected) {
      if (server.client instanceof Client && server.transport) {
        server.client.onclose = () => {
          console.log(`Server ${serverName} disconnected`);
          server.isConnected = false;
        };
        try {
          await server.client.connect(server.transport);
          const toolsResult = await server.client.listTools();
          server.tools = toolsResult.tools;
          toolsResult.tools.forEach((tool) => {
            this.toolsMapping.set(`${serverName}_${tool.name}`, {
              serverName,
              functionName: tool.name,
            });
          });
          server.isConnected = true;
        } catch (error) {
          throw `Failed to connect to MCP server: ${serverName}`;
        }
      }
      if (server.client instanceof BytedMcpClient) {
        const logId = generateDefaultLogId();
        const ctx: BytedMcpClientContext = {
          logger: generateBytedServiceLoggerWithLogId({ logId }),
          logId,
        };
        try {
          const tools = await server.client.loadAllTools({ ctx });
          server.tools = tools as any[];
          tools.forEach((tool) => {
            this.toolsMapping.set(`${serverName}_${tool.name}`, {
              serverName,
              functionName: tool.name,
            });
          });
          server.isConnected = true;
        } catch (error) {
          throw `Failed to connect to MCP server: ${serverName}`;
        }
      }
    }
  }

  async connectAll(): Promise<void> {
    const promises = Array.from(this.servers.keys()).map((serverName) =>
      this.connectServer(serverName)
    );
    await Promise.all(promises);
  }

  getServerTools(name: string): Tool[] {
    const server = this.servers.get(name);
    if (!server) {
      throw new Error(`Server ${name} not found`);
    }
    return server.tools;
  }

  getAllTools(): { name: string; tools: Tool[] }[] {
    return Array.from(this.servers.entries()).map(([name, server]) => ({
      name,
      tools: server.tools,
    }));
  }

  getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[] {
    let openAITools: OpenAIChatCompletionTool[] = [];
    this.getAllTools().forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        const inputSchema = tool.inputSchema;
        delete inputSchema["$schema"];
        delete inputSchema["additionalProperties"];
        openAITools.push({
          type: "function",
          function: {
            name: `${name}_${tool.name}`,
            description: tool.description,
            parameters: inputSchema,
          },
        });
      });
    });
    return openAITools;
  }

  getAllToolsAsClaudeTools(): ChatAnthropicToolType[] {
    let claudeTools: ChatAnthropicToolType[] = [];
    this.getAllTools().forEach(({ name, tools }) => {
      tools.forEach((tool) => {
        const inputSchema = tool.inputSchema;
        delete inputSchema["$schema"];
        delete inputSchema["additionalProperties"];
        claudeTools.push({
          name: `${name}_${tool.name}`,
          description: tool.description,
          input_schema: inputSchema,
        });
      });
    });
    return claudeTools;
  }

  hasTool(functionNameWithPrefix: string): boolean {
    return this.toolsMapping.has(functionNameWithPrefix);
  }

  async callTool(option: {
    functionNameWithPrefix: string;
    functionCallArguments?: any;
    functionCallId: string;
    extraArguments?: any;
    progressCallback?: (progress: string) => void;
  }): Promise<string> {
    const mapping = this.toolsMapping.get(option.functionNameWithPrefix);
    if (!mapping) {
      throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
    }

    const server = this.servers.get(mapping.serverName);
    if (!server) {
      throw new Error(`Server ${mapping.serverName} not found`);
    }

    if (!server.isConnected) {
      throw new Error(`Server ${mapping.serverName} is not connected`);
    }

    let done = false;

    if (server.client instanceof Client) {
      const result = await server.client.callTool({
        name: mapping.functionName,
        _meta: {
          progressToken: option.functionCallId,
        },
        arguments: {
          ...(option.functionCallArguments ?? {}),
          ...(option.extraArguments ?? {}),
        },
      });

      done = true;

      return `${JSON.stringify(result)}`;
    } else if (server.client instanceof BytedMcpClient) {
      const result = await server.client.callTool(mapping.functionName, {
        ...(option.functionCallArguments ?? {}),
        ...(option.extraArguments ?? {}),
      });

      done = true;

      return `${JSON.stringify(result)}`;
    } else {
      throw "unknown client type";
    }
  }
}
