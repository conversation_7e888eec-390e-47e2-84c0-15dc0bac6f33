import * as express from "express";
import * as cors from "cors";
import * as cookieParser from "cookie-parser";
import { User, UserInfo } from "./user";
import { fileRouter } from "./file_db";
import { DEFAULT_MODEL_IMAGE, getLLMOptions } from "./llm_options";
import {
  AIMessage,
  BaseMessage,
  SystemMessage,
  ToolMessage,
  HumanMessage, // Keep HumanMessage if needed elsewhere, or remove if only used in message_processor
} from "@langchain/core/messages";
import { IDLChat, IDLSSE } from "@byted/nurosdk-js";
import { MCPCloudServerManager } from "./mcp-cloud-server-manager";
import {
  conversationDBRouter,
  conversationMemoryDB,
  dumpConversationToTos, // Updated from dumpConversationToFile
  loadConversationFromTos,
  updateMessagePayload, // Updated from loadConversationFromFile
} from "./conversation_db";
import { IDLChatRequest } from "@byted/nurosdk-js";
import { processIncomingMessages } from "./utils/message_processor";
import { initializeModel } from "./utils/model_initializer";
import {
  formatClientTools,
  finalizeAndSendToolCalls,
  executeServerSideTools,
  addAiResponseToHistory,
  ToolCall, // Import ToolCall type
} from "./utils/tool_processor";
import {
  sendInitialMessage,
  processStreamChunk,
  sendFinalMessages,
} from "./utils/sse_handler";
import { HandoffServerManager } from "./handoff-server-manager";
import { MockArticleWritter } from "./mock_tools/mock_article_writter";

process.env.DEEPSEEK_API_KEY = process.env.ARK_API_KEY;

const app = express();
app.use(express.json({ limit: 1024 * 1024 * 100 }));
app.use(
  cors({
    origin: (requestOrigin, callback) => {
      callback(null, true); // Allow the origin
    },
    credentials: true,
    allowedHeaders: [
      "content-type",
      "x-llm-model",
      "x-llm-endpoint",
      "x-llm-apikey",
      "x-llm-systemprompt",
      "x-llm-temperature",
      "x-llm-mcpservers",
      "x-llm-bytefaasheaders",
    ],
    optionsSuccessStatus: 200,
  })
);
app.use(cookieParser());

// Middleware to handle user information
app.use(
  (req: express.Request, res: express.Response, next: express.NextFunction) => {
    let userInfo = User.getUserInfoFromRequest(req);
    (req as any).userInfo = userInfo;
    User.setUserInfoToResponse(res, userInfo);
    next();
  }
);

// Use the conversation router for /conversation_list and /conversation/:conversationId routes
app.use(conversationDBRouter);

// Use the file router for /upload and /files routes
app.use(fileRouter);

// Endpoint to get user info
app.get("/user_info", (req, res) => {
  const userInfo = (req as any).userInfo;
  if (userInfo) {
    res.json(userInfo);
  } else {
    // This case should ideally not be reached if the middleware is working correctly
    res.status(404).json({ error: "User information not found." });
  }
});

app.post("/set_message_payload", async (req, res) => {
  const userInfo = (req as any).userInfo as UserInfo;
  const uid = userInfo.uid;
  const { conversation_id, message_id, payload } = req.body;
  try {
    await updateMessagePayload({
      uid,
      conversationId: conversation_id,
      messageId: message_id,
      payload,
    });
    res.send(
      JSON.stringify({
        ret: "0",
        errmsg: "success",
      })
    );
  } catch (error) {
    res.send(
      JSON.stringify({
        ret: "-1",
        errmsg: "failed",
      })
    );
  }
});

app.post("/sse", async (req, res) => {
  const body = new IDLChatRequest.ChatRequest({
    JSONString: JSON.stringify(req.body),
  });
  const conversationId =
    body.conversationId || Math.random().toString(36).substring(2, 15);

  let clientClosed = false;

  // 设置SSE响应头
  res.setHeader("Content-Type", "text/event-stream");
  res.setHeader("Cache-Control", "no-cache");
  res.setHeader("Connection", "keep-alive");
  res.setHeader("X-Bytefaas-Enable-Stream", "true");
  res.on("close", () => {
    clientClosed = true;
  });

  let llmOptions = getLLMOptions(req, false);

  const userInfo = (req as any).userInfo as UserInfo;
  const uid = userInfo.uid;

  let messages: BaseMessage[] = [new SystemMessage(llmOptions.systemPrompt)];
  if (!conversationMemoryDB[uid]) {
    conversationMemoryDB[uid] = {};
  }
  if (conversationMemoryDB[uid][conversationId] === undefined) {
    await loadConversationFromTos(uid, conversationId); // Awaited and updated
  }
  if (conversationMemoryDB[uid][conversationId]) {
    messages = conversationMemoryDB[uid][conversationId];
  } else {
    conversationMemoryDB[uid][conversationId] = messages;
  }

  // Process incoming messages using the helper function
  await processIncomingMessages(body, messages);

  const hasImageInHistory =
    (messages.filter(
      (it) =>
        it instanceof HumanMessage &&
        it.content instanceof Array &&
        it.content[0].type === "image_url"
    )?.length ?? 0) > 0;

  llmOptions = getLLMOptions(req, hasImageInHistory);

  if (hasImageInHistory && llmOptions.modelName !== DEFAULT_MODEL_IMAGE) {
    // 非 double vlm 模型不支持 image_url
    messages.forEach((it) => {
      if (it instanceof HumanMessage) {
        if (it.content instanceof Array) {
          it.content = it.content.filter((it) => it.type !== "image_url");
        }
      }
    });
  }

  // Initialize the model using the helper function
  const model = initializeModel(llmOptions);
  const isClaude = llmOptions.modelName.includes("claude");

  async function runLoop(): Promise<boolean> {
    // Add return type annotation
    const mcpCloudServerManager = new MCPCloudServerManager();
    const handoffServerManager = new HandoffServerManager();

    if (llmOptions.mcpServers && llmOptions.mcpServers.length) {
      for (let index = 0; index < llmOptions.mcpServers.length; index++) {
        const server = llmOptions.mcpServers[index];
        await mcpCloudServerManager.registerServer(
          server,
          llmOptions,
          userInfo,
          conversationId
        );
      }
    } else {
      // 部署在 ByteFaas 上的一个测试服务
      // https://cloud-boe.bytedance.net/faas/mcp_servers/bb0i4fck/tools?x-resource-account=boe&x-bc-region-id=bytedance
      if (process.env.TOS_IDC === "sg1") {
      } else {
        await mcpCloudServerManager.registerServer(
          {
            name: "weather_mock",
            url: "https://bb0i4fck.mcp-boe.bytedance.net/sse",
          },
          llmOptions,
          userInfo,
          conversationId
        );
      }
    }

    // 创建一个 Mock 的流式服务，用于测试。
    handoffServerManager.registerTool(new MockArticleWritter());

    await mcpCloudServerManager.connectAll();

    // Format client tools using the helper function
    const clientTools = formatClientTools(body, isClaude);

    // console.log("model", model)
    const stream = await model.stream(messages, {
      tools: isClaude
        ? [
            ...(await mcpCloudServerManager.getAllToolsAsClaudeTools()),
            ...clientTools,
            ...handoffServerManager.getTools(),
          ]
        : [
            ...(await mcpCloudServerManager.getAllToolsAsOpenAITools()),
            ...clientTools,
            ...handoffServerManager.getTools(),
          ],
    });

    // Send initial message structure via SSE
    const msg_id = sendInitialMessage(res, conversationId);

    let currentTextContent = "";
    let currentReasoningContent = "";
    let currentToolCalls: ToolCall[] = [];

    // Process the stream using the helper function
    for await (const chunk of stream) {
      if (clientClosed) {
        console.log("Client closed connection, stopping stream processing.");
        break;
      }
      // console.log("Raw Stream Chunk:", chunk);
      const { textContentDelta, updatedToolCalls, reasoningContentDelta } =
        processStreamChunk(
          chunk,
          mcpCloudServerManager,
          res,
          currentToolCalls,
          isClaude
        );
      currentTextContent += textContentDelta;
      if (reasoningContentDelta) {
        currentReasoningContent += reasoningContentDelta;
      }
      currentToolCalls = updatedToolCalls;
    }

    // Add the AI's response (text and tool calls) to history *before* execution
    addAiResponseToHistory(
      currentToolCalls,
      currentTextContent,
      currentReasoningContent,
      messages,
      msg_id,
      isClaude
    );

    let hasServerSideFunctionExecuted = false;
    if (currentToolCalls.length > 0) {
      // Finalize parsing and send tool call info via SSE
      await finalizeAndSendToolCalls(
        currentToolCalls,
        mcpCloudServerManager,
        handoffServerManager,
        res
      );

      // Execute server-side tools and update history
      hasServerSideFunctionExecuted = await executeServerSideTools(
        conversationId,
        currentToolCalls,
        mcpCloudServerManager,
        handoffServerManager,
        messages, // Pass messages to add ToolMessage results
        res
      );
    }

    return hasServerSideFunctionExecuted; // Return true if server tools were called, to potentially loop again
  }

  // Keep running the loop as long as server-side functions are executed
  while (await runLoop()) {
    // The loop will now handle the next turn if server functions were called
    console.log("Re-running loop after server-side tool execution...");
  }

  // Send final SSE messages
  sendFinalMessages(res, conversationId);

  // End the response and save conversation
  setTimeout(async () => {
    // Made inner function async
    res.end(); // 关闭响应流，Delay 1 seconds to ensure the client receives the finally message
    await dumpConversationToTos(uid, conversationId); // Awaited and updated
  }, 1000);
});

const port = 8888;
app.listen(port, (error) => {
  if (error) {
    console.error("Error starting server:", error);
    return;
  }
  console.log(`Server is running on http://localhost:${port}`);
});
