import * as express from "express";
import { <PERSON>uff<PERSON> } from "buffer";
import { MCPCloudServerConfig } from "./mcp-cloud-server-manager"; // Added import

// Default LLM parameters
export const DEFAULT_ENDPOINT = "https://ark-cn-beijing.bytedance.net/api/v3";
export const DEFAULT_API_KEY = process.env.ARK_API_KEY;
export const DEFAULT_MODEL_TEXT = "ep-20250327120147-qt97z"; // deepseek v3
export const DEFAULT_MODEL_IMAGE = "ep-20250318173655-4jfb9"; // doubao vlm
export const DEFAULT_SYSTEM_PROMPT = "You are a helpful assistant.";
export const DEFAULT_TEMPERATURE = 0.5;

export interface LLMOptions {
  modelName: string;
  endpoint: string;
  apiKey: string;
  systemPrompt: string;
  temperature: number;
  mcpServers?: MCPCloudServerConfig[]; // Added mcpServers property
  byteFaasHeaders?: { key: string; value: string }[]; // Added byteFaasHeaders property
}

function getHeaderString(
  req: express.Request,
  key: string
): string | undefined {
  const value = req.headers[key];
  return typeof value === "string" ? value : undefined;
}

export function getLLMOptions(
  req: express.Request,
  hasImage: boolean
): LLMOptions {
  const modelNameHeader = getHeaderString(req, "x-llm-model");
  const endpointHeader = getHeaderString(req, "x-llm-endpoint");
  const apiKeyHeader = getHeaderString(req, "x-llm-apikey");
  const systemPromptHeader = getHeaderString(req, "x-llm-systemprompt");
  const temperatureHeader = getHeaderString(req, "x-llm-temperature");
  const mcpServersHeader = getHeaderString(req, "x-llm-mcpservers"); // Read the new header
  const byteFaasHeadersHeader = getHeaderString(req, "x-llm-bytefaasheaders"); // Read the new header

  const defaultModel = hasImage ? DEFAULT_MODEL_IMAGE : DEFAULT_MODEL_TEXT;

  const modelName = modelNameHeader ?? defaultModel;
  const endpoint = endpointHeader ?? DEFAULT_ENDPOINT;
  const apiKey = apiKeyHeader ?? DEFAULT_API_KEY ?? ""; // Ensure apiKey is always a string
  // 尝试对 systemPromptHeader 进行 base64 解码，如果解码失败则使用默认值
  const decodedSystemPrompt = systemPromptHeader
    ? decodeURIComponent(systemPromptHeader)
    : undefined;
  const systemPrompt = decodedSystemPrompt ?? DEFAULT_SYSTEM_PROMPT;
  const temperature = temperatureHeader
    ? parseFloat(temperatureHeader)
    : DEFAULT_TEMPERATURE;

  let mcpServers: MCPCloudServerConfig[] | undefined = undefined;
  if (mcpServersHeader) {
    try {
      mcpServers = JSON.parse(mcpServersHeader);
      // Basic validation to ensure it's an array
      if (!Array.isArray(mcpServers)) {
        console.warn(
          "Invalid x-llm-mcpservers header: not an array. Ignoring."
        );
        mcpServers = undefined;
      }
    } catch (error) {
      console.error("Failed to parse x-llm-mcpservers header:", error);
      mcpServers = undefined; // Reset to undefined on error
    }
  }
  let byteFaasHeaders: { key: string; value: string }[] | undefined = undefined;
  if (byteFaasHeadersHeader) {
    try {
      const headers = JSON.parse(
        atob(
          byteFaasHeadersHeader
            .replace(new RegExp("_P_", "g"), "+")
            .replace(new RegExp("_O_", "g"), "/")
            .replace(new RegExp("_Q_", "g"), "=")
        )
      );
      if (!Array.isArray(headers)) {
        console.warn(
          "Invalid x-llm-bytefaasheaders header: not an array. Ignoring."
        );
      } else {
        byteFaasHeaders = headers;
      }
    } catch (error) {
      console.error("Failed to parse x-llm-bytefaasheaders header:", error);
    }
  }

  return {
    modelName,
    endpoint,
    apiKey,
    systemPrompt,
    temperature,
    mcpServers, // Include mcpServers in the return object
    byteFaasHeaders,
  };
}
