import { HumanMessage } from "@langchain/core/messages";
import { HandoffTool } from "../handoff-server-manager";
import {
  DEFAULT_API_KEY,
  DEFAULT_ENDPOINT,
  DEFAULT_MODEL_TEXT,
} from "../llm_options";
import { initializeModel } from "../utils/model_initializer";

export class MockArticleWritter extends HandoffTool {
  constructor() {
    super({
      type: "function",
      function: {
        name: "article_writter",
        description:
          "Write an article based on the given topic, output 100 words.",
        parameters: {
          type: "object",
          properties: {
            topic: {
              type: "string",
              description: "The topic of the article",
            },
          },
          required: ["topic"],
        },
        strict: true,
      },
    });
  }

  async call(option: {
    functionCallArguments?: any;
    functionCallId: string;
    streamingCallback: (result: string) => void;
    finishCallback: (result: string) => void;
  }): Promise<void> {
    const model = initializeModel(
      {
        modelName: DEFAULT_MODEL_TEXT,
        endpoint: DEFAULT_ENDPOINT,
        apiKey: DEFAULT_API_KEY!,
        systemPrompt: "你的任务是根据用户给出的主题，写出 100 字的文章。",
        temperature: 0.5,
      }
    );
    const stream = await model.stream([
      new HumanMessage(
        `写一篇关于「${option.functionCallArguments?.topic}」的文章，字数不少于 100。`
      ),
    ]);
    let result = "";
    for await (const chunk of stream) {
      if (typeof chunk.content === "string") {
        option.streamingCallback(chunk.content);
        result += chunk.content;
      }
    }
    option.finishCallback(result);
  }
}
