"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fileRouter = void 0;
const express = require("express");
const path = require("path");
const tosService_1 = require("./services/tosService"); // 引入 tosClient
const crypto = require("crypto");
exports.fileRouter = express.Router();
// 文件上传接口 (接收 JSON body: { filename: string, filedata: string (base64) })
exports.fileRouter.post("/upload", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const { filename, filedata } = req.body;
    if (!filename ||
        typeof filename !== "string" ||
        !filedata ||
        typeof filedata !== "string") {
        res
            .status(400)
            .send("Invalid request body. Expecting { filename: string, filedata: string (base64) }");
        return;
    }
    try {
        const buffer = Buffer.from(filedata, "base64");
        const uniqueString = Date.now().toString() + Math.random().toString();
        const hash = crypto.createHash('sha256');
        hash.update(uniqueString);
        const randomString = hash.digest('hex').substring(0, 32); // 取前32位作为文件名的一部分
        const fileExt = path.extname(filename).split("?")[0];
        const newFilename = `${randomString}${fileExt}`;
        const tosKey = `files/${newFilename}`;
        yield tosService_1.tosClient.putObject(tosKey, buffer);
        // 返回相对于TOS存储路径的key，供客户端访问
        res.send(`/files/${newFilename}`);
    }
    catch (error) {
        console.error("Error processing file upload to TOS:", error);
        res.status(500).send("Error processing file upload to TOS.");
    }
}));
// 文件读取接口
exports.fileRouter.get("/files/:filename", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const filename = req.params.filename;
    const tosKey = `files/${filename}`;
    try {
        const result = yield tosService_1.tosClient.getObject(tosKey);
        res.setHeader("Content-Type", getMimeType(filename));
        if (result.objectBuffer) {
            res.send(result.objectBuffer);
        }
        else {
            // 如果TOS SDK返回的result.content未定义或不可读，则返回错误
            console.error("Error fetching file from TOS: No content stream available", tosKey);
            res.status(500).send("Error fetching file from TOS.");
        }
    }
    catch (error) {
        if (error.statusCode === 404 ||
            (error.message && error.message.includes("NoSuchKey"))) {
            console.error("File does not exist in TOS:", tosKey);
            return res.status(404).send("File not found.");
        }
        console.error("Error fetching file from TOS:", error);
        // 避免在发送文件出错后再次发送响应头
        if (!res.headersSent) {
            res.status(500).send("Error fetching file from TOS.");
        }
    }
}));
function getMimeType(fileName) {
    const ext = path.extname(fileName).toLowerCase();
    switch (ext) {
        case ".txt":
            return "text/plain";
        case ".html":
        case ".htm":
            return "text/html";
        case ".css":
            return "text/css";
        case ".js":
        case ".mjs":
            return "application/javascript";
        case ".json":
            return "application/json";
        case ".xml":
            return "application/xml";
        case ".pdf":
            return "application/pdf";
        case ".jpg":
        case ".jpeg":
            return "image/jpeg";
        case ".png":
            return "image/png";
        case ".gif":
            return "image/gif";
        case ".svg":
            return "image/svg+xml";
        case ".mp4":
            return "video/mp4";
        case ".webm":
            return "video/webm";
        case ".mp3":
            return "audio/mpeg";
        case ".wav":
            return "audio/wav";
        // Add more mime types as needed
        default:
            return "application/octet-stream"; // Changed default to octet-stream
    }
}
//# sourceMappingURL=file_db.js.map