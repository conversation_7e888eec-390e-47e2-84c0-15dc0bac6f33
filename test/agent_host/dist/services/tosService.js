"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.tosClient = void 0;
const tos_1 = require("@byted-service/tos");
const options = {
    clientPSM: "videocut.nuro.mockhostagent", // TOS-Server 端用来查请求来源
    bucket: "lv-mcpbase-webui-store-boe",
    signatureVersion: "sign_v1",
    accessKey: process.env.TOS_AK,
    secretKey: process.env.TOS_SK,
    idleTimeout: 60000, //(单位:毫秒) 设置为60秒; 如果不设置，默认值也是0秒，即:不超时 (Node.js版本>v13.x HTTP处理请求超时时间)
    reqTimeout: 10000, // (单位:毫秒) 设置为10秒; 如果不设置，默认值也是10秒(TOS处理请求超时时间)
    psm: "toutiao.tos.tosapi", // 明确指定访问TOS的该PSM (默认值: 'toutiao.tos.tosapi')
    consulLookUpOption: {
        env: "prod",
        cluster: "default", // 明确指定访问TOS该PSM下的某个cluster
        idc: "boe", // 明确指定访问IDC (默认值: 与consul节点相同IDC)
        addrfam: "dual-stack",
        unique: "v4", // 明确指定仅用IPv4
    },
    enableCrc64: false, // 使用Crc64进行数据一致性检验
};
// 初始化 tosClient
exports.tosClient = new tos_1.default(options); // 每个桶初始化1次即可复用 (不要反复初始化)
//# sourceMappingURL=tosService.js.map