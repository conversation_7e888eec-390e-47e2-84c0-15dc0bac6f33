"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPDeviceServerManager_HostSide = void 0;
class MCPDeviceServerManager_HostSide {
    constructor(clientTools) {
        this.clientTools = clientTools;
    }
    hasClientTool(toolName) {
        return this.clientTools.some(({ name, tools }) => tools.some((tool) => `${name}_${tool.name}` === toolName));
    }
    getAllToolsAsOpenAITools() {
        let openAITools = [];
        this.clientTools.forEach(({ name, tools }) => {
            tools.forEach((tool) => {
                openAITools.push({
                    type: "function",
                    function: {
                        name: `${name}_${tool.name}`,
                        description: tool.description,
                        parameters: tool.inputSchema,
                    },
                });
            });
        });
        return openAITools;
    }
}
exports.MCPDeviceServerManager_HostSide = MCPDeviceServerManager_HostSide;
//# sourceMappingURL=mcp-device-server-manager.js.map