import OpenAI from "openai";
type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;
export declare class HandoffTool {
    def: OpenAIChatCompletionTool;
    constructor(def: OpenAIChatCompletionTool);
    call(option: {
        functionCallArguments?: any;
        functionCallId: string;
        streamingCallback: (result: string) => void;
        finishCallback: (result: string) => void;
    }): void;
}
export declare class HandoffServerManager {
    private toolsMapping;
    registerTool(tool: HandoffTool): void;
    hasTool(name: string): boolean;
    getTools(): OpenAIChatCompletionTool[];
    callTool(option: {
        functionNameWithPrefix: string;
        functionCallArguments?: any;
        functionCallId: string;
        streamingCallback?: (result: string) => void;
        finishCallback?: (result: string) => void;
    }): Promise<void>;
}
export {};
