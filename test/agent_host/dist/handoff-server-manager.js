"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HandoffServerManager = exports.HandoffTool = void 0;
const logger_1 = require("@byted-service/logger");
const logger = new logger_1.default();
class HandoffTool {
    constructor(def) {
        this.def = def;
    }
    call(option) { }
}
exports.HandoffTool = HandoffTool;
class HandoffServerManager {
    constructor() {
        this.toolsMapping = new Map();
    }
    registerTool(tool) {
        this.toolsMapping.set(tool.def.function.name, tool);
    }
    hasTool(name) {
        return this.toolsMapping.has(name);
    }
    getTools() {
        let tools = [];
        this.toolsMapping.forEach((tool) => {
            tools.push(tool.def);
        });
        return tools;
    }
    callTool(option) {
        return __awaiter(this, void 0, void 0, function* () {
            const tool = this.toolsMapping.get(option.functionNameWithPrefix);
            if (!tool) {
                throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
            }
            yield tool.call({
                functionCallArguments: option.functionCallArguments,
                functionCallId: option.functionCallId,
                streamingCallback: option.streamingCallback,
                finishCallback: option.finishCallback,
            });
        });
    }
}
exports.HandoffServerManager = HandoffServerManager;
//# sourceMappingURL=handoff-server-manager.js.map