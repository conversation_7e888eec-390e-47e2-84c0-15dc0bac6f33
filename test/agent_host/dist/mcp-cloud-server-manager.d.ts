import { ChatAnthropicToolType } from "@langchain/anthropic/dist/types";
import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenAI from "openai";
import { LLMOptions } from "./llm_options";
import { UserInfo } from "./user";
export interface MCPCloudServerConfig {
    name: string;
    url?: string;
    byteFaasConfig?: {
        psm: string;
        region: "BOE" | "CN" | "I18N";
    };
}
type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;
export declare class MCPCloudServerManager {
    private servers;
    private toolsMapping;
    registerServer(config: MCPCloudServerConfig, llmOptions: LLMOptions, userInfo: UserInfo, conversationId?: string): Promise<void>;
    connectServer(serverName: string): Promise<void>;
    connectAll(): Promise<void>;
    getServerTools(name: string): Tool[];
    getAllTools(): {
        name: string;
        tools: Tool[];
    }[];
    getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[];
    getAllToolsAsClaudeTools(): ChatAnthropicToolType[];
    hasTool(functionNameWithPrefix: string): boolean;
    callTool(option: {
        functionNameWithPrefix: string;
        functionCallArguments?: any;
        functionCallId: string;
        extraArguments?: any;
        progressCallback?: (progress: string) => void;
    }): Promise<string>;
}
export {};
