import * as express from "express";
import { MCPCloudServerConfig } from "./mcp-cloud-server-manager";
export declare const DEFAULT_ENDPOINT = "https://ark-cn-beijing.bytedance.net/api/v3";
export declare const DEFAULT_API_KEY: string | undefined;
export declare const DEFAULT_MODEL_TEXT = "ep-20250327120147-qt97z";
export declare const DEFAULT_MODEL_IMAGE = "ep-20250318173655-4jfb9";
export declare const DEFAULT_SYSTEM_PROMPT = "You are a helpful assistant.";
export declare const DEFAULT_TEMPERATURE = 0.5;
export interface LLMOptions {
    modelName: string;
    endpoint: string;
    apiKey: string;
    systemPrompt: string;
    temperature: number;
    mcpServers?: MCPCloudServerConfig[];
    byteFaasHeaders?: {
        key: string;
        value: string;
    }[];
}
export declare function getLLMOptions(req: express.Request, hasImage: boolean): LLMOptions;
