import { Chat<PERSON>penAI } from "@langchain/openai";
import { ChatDeepSeek } from "@langchain/deepseek";
import { ChatAnthropic } from "@langchain/anthropic";
import { LLMOptions } from "../llm_options";
/**
 * Initializes and returns the appropriate language model instance based on the provided options
 * and whether the conversation history contains images.
 *
 * @param llmOptions The LLM configuration options.
 * @param hasImageInHistory A boolean indicating if the conversation history includes images.
 * @returns An instance of ChatOpenAI, ChatDeepSeek, or ChatAnthropic.
 */
export declare function initializeModel(llmOptions: LLMOptions): ChatOpenAI | ChatDeepSeek | ChatAnthropic;
