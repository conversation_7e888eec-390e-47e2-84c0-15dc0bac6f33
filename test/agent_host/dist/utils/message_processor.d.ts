import { BaseMessage } from "@langchain/core/messages";
import { IDLChatRequest } from "@byted/nurosdk-js";
/**
 * Processes incoming messages from the request body and updates the conversation history.
 * Handles text messages and file uploads (converting files to base64 data URLs).
 *
 * @param requestBody The parsed request body containing messages.
 * @param currentMessages The current array of BaseMessage objects representing the conversation history.
 * @returns The updated array of BaseMessage objects.
 */
export declare function processIncomingMessages(requestBody: IDLChatRequest.ChatRequest, currentMessages: BaseMessage[]): Promise<BaseMessage[]>;
