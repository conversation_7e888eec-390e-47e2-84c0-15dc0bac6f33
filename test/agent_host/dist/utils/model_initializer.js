"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeModel = initializeModel;
const openai_1 = require("@langchain/openai");
const deepseek_1 = require("@langchain/deepseek");
const anthropic_1 = require("@langchain/anthropic");
/**
 * Initializes and returns the appropriate language model instance based on the provided options
 * and whether the conversation history contains images.
 *
 * @param llmOptions The LLM configuration options.
 * @param hasImageInHistory A boolean indicating if the conversation history includes images.
 * @returns An instance of ChatOpenAI, ChatDeepSeek, or ChatAnthropic.
 */
function initializeModel(llmOptions) {
    const isClaude = llmOptions.modelName.includes("claude");
    if (isClaude) {
        let endpoint = llmOptions.endpoint;
        // Anthropic SDK expects the base URL without /v1
        if (endpoint.endsWith("/v1")) {
            endpoint = endpoint.replace("/v1", "");
        }
        return new anthropic_1.ChatAnthropic({
            model: llmOptions.modelName,
            anthropicApiUrl: endpoint,
            anthropicApiKey: llmOptions.apiKey,
            apiKey: llmOptions.apiKey, // Redundant but required by the constructor
            temperature: llmOptions.temperature,
        });
    }
    else if (llmOptions.modelName === "deepseek-r1") {
        // Specific handling for deepseek-r1 without images
        // Note: Ensure DEEPSEEK_API_KEY is set in the environment or passed correctly
        return new deepseek_1.ChatDeepSeek({
            model: "ep-20250214162234-cfq82", // Specific model ID for r1
            configuration: {
                baseURL: llmOptions.endpoint,
                apiKey: llmOptions.apiKey,
            },
            temperature: llmOptions.temperature,
        });
    }
    else {
        // Default to ChatOpenAI for other models or when deepseek-r1 has images
        // Handles general OpenAI compatible models and image models like doubao-vlm
        return new openai_1.ChatOpenAI({
            model: llmOptions.modelName,
            temperature: llmOptions.temperature,
            configuration: {
                baseURL: llmOptions.endpoint,
                apiKey: llmOptions.apiKey,
            },
        });
    }
}
//# sourceMappingURL=model_initializer.js.map