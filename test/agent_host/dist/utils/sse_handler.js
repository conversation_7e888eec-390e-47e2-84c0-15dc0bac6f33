"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendInitialMessage = sendInitialMessage;
exports.processStreamChunk = processStreamChunk;
exports.sendFinalMessages = sendFinalMessages;
const nurosdk_js_1 = require("@byted/nurosdk-js");
const tool_processor_1 = require("./tool_processor");
/**
 * Sends the initial message structure via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 * @returns The generated message ID.
 */
function sendInitialMessage(res, conversationId) {
    const msg_id = Math.random().toString(36).substring(2, 15);
    const chatMessage = new nurosdk_js_1.IDLChat.ChatMessage();
    const chatMessageMetadata = new nurosdk_js_1.IDLChat.ChatMessageMetadata();
    chatMessageMetadata.conversation_id = conversationId;
    chatMessage.metadata = chatMessageMetadata;
    chatMessage.id = msg_id;
    chatMessage.status = nurosdk_js_1.IDLChat.ChatMessageStatus.in_progress;
    chatMessage.author = new nurosdk_js_1.IDLChat.ChatAuthor();
    chatMessage.author.role = "assistant";
    res.write(`id:${Math.random()
        .toString(36)
        .substring(2, 15)}\nevent:message\ndata: ${chatMessage.toJSONString()}\n\n`);
    return msg_id;
}
/**
 * Processes a chunk from the LLM stream, handling text, reasoning, and tool calls.
 *
 * @param chunk The chunk received from the LLM stream.
 * @param res The ServerResponse object for sending SSE deltas.
 * @param currentToolCalls The current array of assembled tool calls.
 * @param isClaude Flag indicating if the model is Claude.
 * @returns An object containing the updated text content and tool calls.
 */
function processStreamChunk(chunk, // Type depends on the specific LLM SDK
mcpCloudServerManager, res, currentToolCalls, isClaude) {
    var _a;
    let textContentDelta = "";
    let updatedToolCalls = [...currentToolCalls];
    const reasoningContentDelta = (_a = chunk.additional_kwargs) === null || _a === void 0 ? void 0 : _a["reasoning_content"];
    if (reasoningContentDelta) {
        const reasoningDelta = new nurosdk_js_1.IDLSSE.SSEDeltaMessage();
        reasoningDelta.op = "append";
        reasoningDelta.path = "/message/content/content_parts/0/reasoning_content";
        reasoningDelta.value = reasoningContentDelta;
        res.write(`id:${Math.random()
            .toString(36)
            .substring(2, 15)}\nevent:delta\ndata: ${reasoningDelta.toJSONString()}\n\n`);
    }
    if (chunk.content) {
        // Handle text content
        const delta = new nurosdk_js_1.IDLSSE.SSEDeltaMessage();
        delta.op = "append";
        delta.path = "/message/content/content_parts/0/text";
        delta.value = "";
        if (isClaude && chunk.content instanceof Array) {
            chunk.content.forEach((it) => {
                if (it.type === "text" && it.text) {
                    delta.value += it.text;
                    textContentDelta += it.text;
                }
            });
        }
        else if (typeof chunk.content === "string") {
            delta.value = chunk.content;
            textContentDelta += chunk.content;
        }
        if (delta.value) {
            // Only write if there's text content
            res.write(`id:${Math.random()
                .toString(36)
                .substring(2, 15)}\nevent:delta\ndata: ${delta.toJSONString()}\n\n`);
        }
    }
    updatedToolCalls = (0, tool_processor_1.processToolCallChunks)(chunk, currentToolCalls, isClaude);
    for (let index = 0; index < updatedToolCalls.length; index++) {
        const toolCall = updatedToolCalls[index];
        if (toolCall.action === "init_call") {
            const toolCallDelta = new nurosdk_js_1.IDLSSE.SSEDeltaMessage();
            toolCallDelta.op = "add";
            toolCallDelta.path = `/message/tool_calls/${index}`;
            const chatToolCall = new nurosdk_js_1.IDLChat.ChatToolCall();
            chatToolCall.id = toolCall.tool_call_id;
            chatToolCall.type = mcpCloudServerManager.hasTool(toolCall.function_name)
                ? nurosdk_js_1.IDLChat.ChatToolCallType.server_function
                : nurosdk_js_1.IDLChat.ChatToolCallType.client_function;
            chatToolCall.streaming = true;
            chatToolCall._func = new nurosdk_js_1.IDLChat.ChatToolCallFunc();
            chatToolCall._func.name = toolCall.function_name;
            // Send the raw string arguments as received/assembled
            chatToolCall._func.arguments = toolCall.function_args_str;
            toolCallDelta.value = chatToolCall.toJSONString();
            res.write(`id:${Math.random()
                .toString(36)
                .substring(2, 15)}\nevent:delta\ndata: ${toolCallDelta.toJSONString()}\n\n`);
        }
        else if (toolCall.action === "update_call" &&
            toolCall.function_args_str_delta) {
            const toolCallDelta = new nurosdk_js_1.IDLSSE.SSEDeltaMessage();
            toolCallDelta.op = "append";
            toolCallDelta.path = `/message/tool_calls/${index}/func/arguments`;
            toolCallDelta.value = toolCall.function_args_str_delta;
            res.write(`id:${Math.random()
                .toString(36)
                .substring(2, 15)}\nevent:delta\ndata: ${toolCallDelta.toJSONString()}\n\n`);
        }
    }
    return { reasoningContentDelta, textContentDelta, updatedToolCalls };
}
/**
 * Sends the final status update and stream completion messages via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 */
function sendFinalMessages(res, conversationId) {
    // Send final status delta
    const finalDelta = new nurosdk_js_1.IDLSSE.SSEDeltaMessage();
    finalDelta.op = "replace";
    finalDelta.path = "/message/status";
    finalDelta.value = nurosdk_js_1.IDLChat.ChatMessageStatus.finished_successfully;
    res.write(`id:${Math.random()
        .toString(36)
        .substring(2, 15)}\nevent:delta\ndata: ${finalDelta.toJSONString()}\n\n`);
    // Send stream complete system message
    res.write(`id:${Math.random()
        .toString(36)
        .substring(2, 15)}\nevent:system\ndata: ${JSON.stringify({
        type: "stream_complete",
        conversation_id: conversationId, // Include actual conversation ID
    })}\n\n`);
    // Send stream complete system message
    res.write(`${JSON.stringify({ ret: "0", logid: conversationId })}\n\n`);
}
//# sourceMappingURL=sse_handler.js.map