import { ServerResponse } from "http";
import { ToolCall } from "./tool_processor";
import { AIMessageChunk } from "@langchain/core/messages";
import { MCPCloudServerManager } from "src/mcp-cloud-server-manager";
/**
 * Sends the initial message structure via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 * @returns The generated message ID.
 */
export declare function sendInitialMessage(res: ServerResponse, conversationId: string): string;
/**
 * Processes a chunk from the LLM stream, handling text, reasoning, and tool calls.
 *
 * @param chunk The chunk received from the LLM stream.
 * @param res The ServerResponse object for sending SSE deltas.
 * @param currentToolCalls The current array of assembled tool calls.
 * @param isClaude Flag indicating if the model is Claude.
 * @returns An object containing the updated text content and tool calls.
 */
export declare function processStreamChunk(chunk: AIMessageChunk, // Type depends on the specific LLM SDK
mcpCloudServerManager: MCPCloudServerManager, res: ServerResponse, currentToolCalls: ToolCall[], isClaude: boolean): {
    reasoningContentDelta?: string;
    textContentDelta: string;
    updatedToolCalls: ToolCall[];
};
/**
 * Sends the final status update and stream completion messages via SSE.
 *
 * @param res The ServerResponse object.
 * @param conversationId The ID of the conversation.
 */
export declare function sendFinalMessages(res: ServerResponse, conversationId: string): void;
