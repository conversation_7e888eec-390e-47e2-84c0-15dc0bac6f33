import { BaseMessage } from "@langchain/core/messages";
import { MCPCloudServerManager } from "../mcp-cloud-server-manager";
import { IDLChatRequest } from "@byted/nurosdk-js";
import { ServerResponse } from "http";
import { HandoffServerManager } from "src/handoff-server-manager";
export interface ToolCall {
    action: "init_call" | "update_call";
    tool_call_id: string;
    function_name: string;
    function_args: Record<string, any>;
    function_args_str: string;
    function_args_str_delta: string;
}
/**
 * Formats client-defined tools based on the target model (Claude or OpenAI).
 *
 * @param requestBody The request body containing potential client tools.
 * @param isClaude <PERSON> indicating if the target model is Claude.
 * @returns An array of formatted tools compatible with the target model.
 */
export declare function formatClientTools(requestBody: IDLChatRequest.ChatRequest, isClaude: boolean): any[];
/**
 * Processes tool call chunks from the LLM stream, assembling complete tool calls.
 *
 * @param chunk The current chunk from the LLM stream.
 * @param currentToolCalls The array of tool calls being assembled.
 * @param isClau<PERSON> indicating if the model is Claude (affects indexing).
 * @returns The updated array of tool calls.
 */
export declare function processToolCallChunks(chunk: any, // Type depends on the specific LLM SDK
currentToolCalls: ToolCall[], isClaude: boolean): ToolCall[];
/**
 * Finalizes tool calls by parsing arguments and sends tool call information via SSE.
 *
 * @param currentToolCalls The assembled tool calls.
 * @param mcpCloudServerManager Instance to check if a tool is server-side.
 * @param res The ServerResponse object for sending SSE events.
 */
export declare function finalizeAndSendToolCalls(currentToolCalls: ToolCall[], mcpCloudServerManager: MCPCloudServerManager, handoffServerManager: HandoffServerManager, res: ServerResponse): Promise<void>;
/**
 * Executes server-side tools and sends results via SSE, updating conversation history.
 *
 * @param currentToolCalls The finalized tool calls.
 * @param mcpCloudServerManager The MCP manager instance.
 * @param messages The current conversation history.
 * @param res The ServerResponse object for sending SSE events.
 * @returns A boolean indicating if any server-side function was executed.
 */
export declare function executeServerSideTools(conversationId: string, currentToolCalls: ToolCall[], mcpCloudServerManager: MCPCloudServerManager, handoffServerManager: HandoffServerManager, messages: BaseMessage[], res: ServerResponse): Promise<boolean>;
/**
 * Adds the AI's response (including tool calls) to the message history.
 *
 * @param currentToolCalls The list of tool calls made by the AI.
 * @param currentTextContent The text content generated by the AI.
 * @param messages The conversation history array.
 * @param chatMessageId The ID of the current AI message.
 * @param isClaude Flag indicating if the model is Claude.
 */
export declare function addAiResponseToHistory(currentToolCalls: ToolCall[], currentTextContent: string, currentReasoningContent: string, messages: BaseMessage[], chatMessageId: string, isClaude: boolean): void;
