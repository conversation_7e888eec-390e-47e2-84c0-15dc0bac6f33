{"version": 3, "file": "tool_processor.js", "sourceRoot": "", "sources": ["../../src/utils/tool_processor.ts"], "names": [], "mappings": ";;;;;;;;;;;AAuBA,8CAkCC;AAUD,sDA0EC;AASD,4DA+CC;AAWD,wDAoIC;AAWD,wDAyDC;AAxZD,uDAA+E;AAC/E,kDAAoD;AAepD;;;;;;GAMG;AACH,SAAgB,iBAAiB,CAC/B,WAAuC,EACvC,QAAiB;;IAEjB,OAAO,CACL,MAAA,MAAA,MAAA,WAAW,CAAC,QAAQ,0CAAG,CAAC,EAAE,KAAK,0CAC3B,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,iBAAiB,EAC7C,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;;QACV,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAA,EAAE,CAAC,UAAU,mCAAI,IAAI,CAAC,CAAC;QACnD,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW;YACtC,CAAC,CAAC,QAAQ,CAAC,WAAW;YACtB,CAAC,CAAC,QAAQ,CAAC;QACb,4DAA4D;QAC5D,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;QAC9B,OAAO,WAAW,CAAC,sBAAsB,CAAC,CAAC;QAE3C,IAAI,QAAQ,EAAE,CAAC;YACb,OAAO;gBACL,IAAI,EAAE,GAAG,EAAE,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,EAAE,EAAE,oCAAoC;gBACzE,WAAW,EAAE,MAAA,EAAE,CAAC,WAAW,mCAAI,QAAQ,CAAC,WAAW;gBACnD,YAAY,EAAE,WAAW;aAC1B,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO;gBACL,IAAI,EAAE,UAAU;gBAChB,QAAQ,EAAE;oBACR,IAAI,EAAE,MAAA,EAAE,CAAC,IAAI,mCAAI,QAAQ,CAAC,IAAI;oBAC9B,WAAW,EAAE,MAAA,EAAE,CAAC,WAAW,mCAAI,QAAQ,CAAC,WAAW;oBACnD,UAAU,EAAE,WAAW;iBACxB;aACF,CAAC;QACJ,CAAC;IACH,CAAC,CAAC,mCAAI,EAAE,CACX,CAAC;AACJ,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,qBAAqB,CACnC,KAAU,EAAE,uCAAuC;AACnD,gBAA4B,EAC5B,QAAiB;IAEjB,MAAM,gBAAgB,GAAG,CAAC,GAAG,gBAAgB,CAAC,CAAC;IAE/C,gEAAgE;IAChE,IACE,KAAK,CAAC,UAAU;QAChB,KAAK,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC;QAC3B,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,EAAO,EAAE,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,6CAA6C;MACzF,CAAC;QACD,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,EAAO,EAAE,EAAE;YACnC,MAAM,QAAQ,GAAa;gBACzB,MAAM,EAAE,WAAW;gBACnB,YAAY,EACV,EAAE,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,yBAAyB;gBAC1F,aAAa,EAAE,EAAE,CAAC,IAAI;gBACtB,aAAa,EAAE,EAAE,CAAC,IAAI,IAAI,EAAE,EAAE,4BAA4B;gBAC1D,iBAAiB,EACf,OAAO,EAAE,CAAC,IAAI,KAAK,QAAQ;oBACzB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC;oBACzB,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI;gBACrB,uBAAuB,EAAE,EAAE;aAC5B,CAAC;YAEF,MAAM,aAAa,GAAG,gBAAgB,CAAC,SAAS,CAC9C,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,YAAY,KAAK,QAAQ,CAAC,YAAY,CAClD,CAAC;YAEF,IAAI,aAAa,KAAK,CAAC,CAAC,EAAE,CAAC;gBACzB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAClC,CAAC;iBAAM,CAAC;gBACN,6EAA6E;gBAC7E,gBAAgB,CAAC,aAAa,CAAC,iDAC1B,gBAAgB,CAAC,aAAa,CAAC,GAC/B,QAAQ;oBACX,4DAA4D;oBAC5D,iBAAiB,EAAE,QAAQ,CAAC,iBAAiB,GAC9C,CAAC;YACJ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,gBAAgB,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,EAAE;QACrC,EAAE,CAAC,uBAAuB,GAAG,EAAE,CAAC;IAClC,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,IAAI,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChE,KAAK,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAY,EAAE,EAAE;YAC9C,IAAI,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;YAC9B,IAAI,SAAS,KAAK,SAAS,EAAE,CAAC;gBAC5B,4FAA4F;gBAC5F,IAAI,QAAQ,EAAE,CAAC;oBACb,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;gBAC5B,CAAC;gBAED,IAAI,gBAAgB,CAAC,SAAS,CAAC,IAAI,OAAO,OAAO,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;oBACpE,0BAA0B;oBAC1B,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC,iBAAiB,KAAK,IAAI,EAAE,CAAC;wBAC3D,gBAAgB,CAAC,SAAS,CAAC,CAAC,iBAAiB,GAAG,EAAE,CAAC,CAAC,iCAAiC;oBACvF,CAAC;yBAAM,CAAC;wBACN,gBAAgB,CAAC,SAAS,CAAC,CAAC,MAAM,GAAG,aAAa,CAAC;oBACrD,CAAC;oBACD,gBAAgB,CAAC,SAAS,CAAC,CAAC,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;oBAC9D,gBAAgB,CAAC,SAAS,CAAC,CAAC,uBAAuB,GAAG,OAAO,CAAC,IAAI,CAAC;gBACrE,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;;;GAMG;AACH,SAAsB,wBAAwB,CAC5C,gBAA4B,EAC5B,qBAA4C,EAC5C,oBAA0C,EAC1C,GAAmB;;QAEnB,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC;gBACH,oDAAoD;gBACpD,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,iBAAiB,IAAI,IAAI,CAAC,CAAC;YAC1E,CAAC;YAAC,OAAO,CAAC,EAAE,CAAC;gBACX,OAAO,CAAC,IAAI,CACV,2CAA2C,QAAQ,CAAC,aAAa,SAAS,QAAQ,CAAC,YAAY,mBAAmB,QAAQ,CAAC,iBAAiB,EAAE,EAC9I,CAAC,CACF,CAAC;gBACF,yDAAyD;gBACzD,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC;YAC9B,CAAC;YAED,MAAM,aAAa,GAAG,IAAI,mBAAM,CAAC,eAAe,EAAE,CAAC;YACnD,aAAa,CAAC,EAAE,GAAG,SAAS,CAAC;YAC7B,aAAa,CAAC,IAAI,GAAG,uBAAuB,KAAK,EAAE,CAAC;YAEpD,MAAM,YAAY,GAAG,IAAI,oBAAO,CAAC,YAAY,EAAE,CAAC;YAChD,YAAY,CAAC,EAAE,GAAG,QAAQ,CAAC,YAAY,CAAC;YACxC,YAAY,CAAC,SAAS,GAAG,KAAK,CAAC;YAC/B,YAAY,CAAC,IAAI;gBACf,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;oBACrD,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC;oBAClD,CAAC,CAAC,oBAAO,CAAC,gBAAgB,CAAC,eAAe;oBAC1C,CAAC,CAAC,oBAAO,CAAC,gBAAgB,CAAC,eAAe,CAAC;YAC/C,YAAY,CAAC,KAAK,GAAG,IAAI,oBAAO,CAAC,gBAAgB,EAAE,CAAC;YACpD,YAAY,CAAC,KAAK,CAAC,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC;YACjD,sDAAsD;YACtD,YAAY,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC;YAC1D,aAAa,CAAC,KAAK,GAAG,YAAY,CAAC,YAAY,EAAE,CAAC;YAElD,GAAG,CAAC,KAAK,CACP,MAAM,IAAI,CAAC,MAAM,EAAE;iBAChB,QAAQ,CAAC,EAAE,CAAC;iBACZ,SAAS,CACR,CAAC,EACD,EAAE,CACH,wBAAwB,aAAa,CAAC,YAAY,EAAE,MAAM,CAC9D,CAAC;QACJ,CAAC;IACH,CAAC;CAAA;AAED;;;;;;;;GAQG;AACH,SAAsB,sBAAsB,CAC1C,cAAsB,EACtB,gBAA4B,EAC5B,qBAA4C,EAC5C,oBAA0C,EAC1C,QAAuB,EACvB,GAAmB;;QAEnB,IAAI,6BAA6B,GAAG,KAAK,CAAC;QAE1C,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC;YAC7D,MAAM,QAAQ,GAAG,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAEzC,IAAI,qBAAqB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAC1D,6BAA6B,GAAG,IAAI,CAAC;gBACrC,IAAI,cAAc,GAAG,EAAE,CAAC;gBACxB,IAAI,CAAC;oBACH,cAAc,GAAG,MAAM,qBAAqB,CAAC,QAAQ,CAAC;wBACpD,sBAAsB,EAAE,QAAQ,CAAC,aAAa;wBAC9C,qBAAqB,EAAE,QAAQ,CAAC,aAAa,EAAE,kBAAkB;wBACjE,cAAc,EAAE,QAAQ,CAAC,YAAY;qBACtC,CAAC,CAAC;gBACL,CAAC;gBAAC,OAAO,KAAU,EAAE,CAAC;oBACpB,OAAO,CAAC,KAAK,CACX,+BAA+B,QAAQ,CAAC,aAAa,GAAG,EACxD,KAAK,CACN,CAAC;oBACF,cAAc,GAAG,yBAAyB,KAAK,CAAC,OAAO,IAAI,KAAK,EAAE,CAAC;gBACrE,CAAC;gBAED,2BAA2B;gBAC3B,MAAM,iBAAiB,GAAG,IAAI,oBAAO,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM,mBAAmB,GAAG,IAAI,oBAAO,CAAC,mBAAmB,EAAE,CAAC;gBAC9D,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC;gBACrD,mBAAmB,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;gBACzD,iBAAiB,CAAC,QAAQ,GAAG,mBAAmB,CAAC;gBACjD,iBAAiB,CAAC,EAAE,GAAG,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC;gBAC9D,iBAAiB,CAAC,MAAM;oBACtB,oBAAO,CAAC,iBAAiB,CAAC,qBAAqB,CAAC;gBAClD,iBAAiB,CAAC,MAAM,GAAG,IAAI,oBAAO,CAAC,UAAU,EAAE,CAAC;gBACpD,iBAAiB,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC;gBACvC,MAAM,OAAO,GAAG,IAAI,oBAAO,CAAC,WAAW,EAAE,CAAC;gBAC1C,OAAO,CAAC,YAAY,GAAG,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC;gBACpD,MAAM,aAAa,GAAG,IAAI,oBAAO,CAAC,eAAe,EAAE,CAAC;gBACpD,aAAa,CAAC,IAAI,GAAG,cAAc,CAAC;gBACpC,OAAO,CAAC,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;gBACxC,iBAAiB,CAAC,OAAO,GAAG,OAAO,CAAC;gBACpC,GAAG,CAAC,KAAK,CACP,MAAM,IAAI,CAAC,MAAM,EAAE;qBAChB,QAAQ,CAAC,EAAE,CAAC;qBACZ,SAAS,CACR,CAAC,EACD,EAAE,CACH,0BAA0B,iBAAiB,CAAC,YAAY,EAAE,MAAM,CACpE,CAAC;gBAEF,0CAA0C;gBAC1C,QAAQ,CAAC,IAAI,CACX,IAAI,sBAAW,CAAC;oBACd,YAAY,EAAE,QAAQ,CAAC,YAAY;oBACnC,OAAO,EAAE,cAAc;iBACxB,CAAC,CACH,CAAC;YACJ,CAAC;iBAAM,IAAI,oBAAoB,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;gBAChE,6BAA6B,GAAG,IAAI,CAAC;gBAErC,MAAM,iBAAiB,GAAG,IAAI,oBAAO,CAAC,WAAW,EAAE,CAAC;gBACpD,MAAM,mBAAmB,GAAG,IAAI,oBAAO,CAAC,mBAAmB,EAAE,CAAC;gBAC9D,mBAAmB,CAAC,eAAe,GAAG,cAAc,CAAC;gBACrD,mBAAmB,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,CAAC;gBACzD,iBAAiB,CAAC,QAAQ,GAAG,mBAAmB,CAAC;gBACjD,iBAAiB,CAAC,EAAE,GAAG,cAAc,GAAG,QAAQ,CAAC,YAAY,CAAC;gBAC9D,iBAAiB,CAAC,MAAM,GAAG,oBAAO,CAAC,iBAAiB,CAAC,WAAW,CAAC;gBACjE,iBAAiB,CAAC,MAAM,GAAG,IAAI,oBAAO,CAAC,UAAU,EAAE,CAAC;gBACpD,iBAAiB,CAAC,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC;gBACvC,MAAM,OAAO,GAAG,IAAI,oBAAO,CAAC,WAAW,EAAE,CAAC;gBAC1C,OAAO,CAAC,YAAY,GAAG,oBAAO,CAAC,eAAe,CAAC,IAAI,CAAC;gBACpD,MAAM,aAAa,GAAG,IAAI,oBAAO,CAAC,eAAe,EAAE,CAAC;gBACpD,aAAa,CAAC,IAAI,GAAG,EAAE,CAAC;gBACxB,OAAO,CAAC,aAAa,GAAG,CAAC,aAAa,CAAC,CAAC;gBACxC,iBAAiB,CAAC,OAAO,GAAG,OAAO,CAAC;gBACpC,GAAG,CAAC,KAAK,CACP,MAAM,IAAI,CAAC,MAAM,EAAE;qBAChB,QAAQ,CAAC,EAAE,CAAC;qBACZ,SAAS,CACR,CAAC,EACD,EAAE,CACH,0BAA0B,iBAAiB,CAAC,YAAY,EAAE,MAAM,CACpE,CAAC;gBAEF,MAAM,oBAAoB,CAAC,QAAQ,CAAC;oBAClC,sBAAsB,EAAE,QAAQ,CAAC,aAAa;oBAC9C,qBAAqB,EAAE,QAAQ,CAAC,aAAa,EAAE,kBAAkB;oBACjE,cAAc,EAAE,QAAQ,CAAC,YAAY;oBACrC,iBAAiB,EAAE,CAAC,MAAM,EAAE,EAAE;wBAC5B,MAAM,YAAY,GAAG,IAAI,mBAAM,CAAC,eAAe,EAAE,CAAC;wBAClD,YAAY,CAAC,EAAE,GAAG,QAAQ,CAAC;wBAC3B,YAAY,CAAC,IAAI,GAAG,uCAAuC,CAAC;wBAC5D,YAAY,CAAC,KAAK,GAAG,MAAM,CAAC;wBAC5B,GAAG,CAAC,KAAK,CACP,MAAM,IAAI,CAAC,MAAM,EAAE;6BAChB,QAAQ,CAAC,EAAE,CAAC;6BACZ,SAAS,CACR,CAAC,EACD,EAAE,CACH,wBAAwB,YAAY,CAAC,YAAY,EAAE,MAAM,CAC7D,CAAC;oBACJ,CAAC;oBACD,cAAc,EAAE,CAAC,MAAM,EAAE,EAAE;wBACzB,MAAM,aAAa,GAAG,IAAI,mBAAM,CAAC,eAAe,EAAE,CAAC;wBACnD,aAAa,CAAC,EAAE,GAAG,SAAS,CAAC;wBAC7B,aAAa,CAAC,IAAI,GAAG,iBAAiB,CAAC;wBACvC,aAAa,CAAC,KAAK,GAAG,uBAAuB,CAAC;wBAC9C,GAAG,CAAC,KAAK,CACP,MAAM,IAAI,CAAC,MAAM,EAAE;6BAChB,QAAQ,CAAC,EAAE,CAAC;6BACZ,SAAS,CACR,CAAC,EACD,EAAE,CACH,wBAAwB,aAAa,CAAC,YAAY,EAAE,MAAM,CAC9D,CAAC;wBACF,QAAQ,CAAC,IAAI,CACX,IAAI,sBAAW,CAAC;4BACd,YAAY,EAAE,QAAQ,CAAC,YAAY;4BACnC,OAAO,EAAE,MAAM;yBAChB,CAAC,CACH,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,OAAO,6BAA6B,CAAC;IACvC,CAAC;CAAA;AAED;;;;;;;;GAQG;AACH,SAAgB,sBAAsB,CACpC,gBAA4B,EAC5B,kBAA0B,EAC1B,uBAA+B,EAC/B,QAAuB,EACvB,aAAqB,EACrB,QAAiB;IAEjB,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAChC,8DAA8D;QAC9D,IAAI,QAAQ,EAAE,CAAC;YACb,QAAQ,CAAC,IAAI,CACX,IAAI,oBAAS,CAAC;gBACZ,EAAE,EAAE,aAAa;gBACjB,OAAO,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;oBACrC,EAAE,EAAE,EAAE,CAAC,YAAY;oBACnB,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,EAAE,CAAC,aAAa;oBACtB,KAAK,EAAE,EAAE,CAAC,aAAa,EAAE,kBAAkB;iBAC5C,CAAC,CAAQ,EAAE,2DAA2D;gBACvE,sEAAsE;gBACtE,oDAAoD;aACrD,CAAC,CACH,CAAC;QACJ,CAAC;QACD,QAAQ,CAAC,IAAI,CACX,IAAI,oBAAS,CAAC;YACZ,EAAE,EAAE,aAAa;YACjB,OAAO,EAAE,kBAAkB,EAAE,yDAAyD;YACtF,UAAU,EAAE,gBAAgB,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;;gBAAC,OAAA,CAAC;oBACxC,EAAE,EAAE,EAAE,CAAC,YAAY;oBACnB,IAAI,EAAE,EAAE,CAAC,aAAa;oBACtB,IAAI,EACF,MAAA,CAAC,GAAG,EAAE;wBACJ,IAAI,CAAC;4BACH,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC;wBAC1C,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC,CAAA,CAAC;oBACpB,CAAC,CAAC,EAAE,mCAAI,EAAE,CAAC,aAAa,EAAE,kBAAkB;oBAC9C,IAAI,EAAE,WAAW,EAAE,yDAAyD;iBAC7E,CAAC,CAAA;aAAA,CAAC;YACH,iBAAiB,EAAE;gBACjB,iBAAiB,EAAE,uBAAuB;aAC3C;SACF,CAAC,CACH,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,8CAA8C;QAC9C,QAAQ,CAAC,IAAI,CACX,IAAI,oBAAS,CAAC;YACZ,EAAE,EAAE,aAAa;YACjB,OAAO,EAAE,kBAAkB;YAC3B,iBAAiB,EAAE;gBACjB,iBAAiB,EAAE,uBAAuB;aAC3C;SACF,CAAC,CACH,CAAC;IACJ,CAAC;AACH,CAAC"}