"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.processIncomingMessages = processIncomingMessages;
const axios_1 = require("axios");
const mime = require("mime-types");
const messages_1 = require("@langchain/core/messages");
const nurosdk_js_1 = require("@byted/nurosdk-js");
/**
 * Processes incoming messages from the request body and updates the conversation history.
 * Handles text messages and file uploads (converting files to base64 data URLs).
 *
 * @param requestBody The parsed request body containing messages.
 * @param currentMessages The current array of BaseMessage objects representing the conversation history.
 * @returns The updated array of BaseMessage objects.
 */
function processIncomingMessages(requestBody, currentMessages) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j;
        const updatedMessages = currentMessages;
        for (const message of (_a = requestBody.messages) !== null && _a !== void 0 ? _a : []) {
            if ((_b = message.content) === null || _b === void 0 ? void 0 : _b.content_parts) {
                for (const part of message.content.content_parts) {
                    if (((_c = message.metadata) === null || _c === void 0 ? void 0 : _c.tool_call_id) !== undefined) {
                        // Handle tool responses
                        updatedMessages.push(new messages_1.ToolMessage({
                            tool_call_id: message.metadata.tool_call_id,
                            content: (_d = part.text) !== null && _d !== void 0 ? _d : "",
                        }));
                    }
                    else {
                        // Handle regular user messages (text or file)
                        if (part.text) {
                            const humanMsg = new messages_1.HumanMessage({
                                id: message.id,
                                content: (_e = part.text) !== null && _e !== void 0 ? _e : "",
                            });
                            updatedMessages.push(humanMsg);
                        }
                        else if ((_f = part.file) === null || _f === void 0 ? void 0 : _f.url) {
                            if (((_g = part.file) === null || _g === void 0 ? void 0 : _g.file_type) === nurosdk_js_1.IDLChat.ChatMessageFileType.IMAGE) {
                                try {
                                    const fileUrl = part.file.url;
                                    const response = yield axios_1.default.get(fileUrl, {
                                        responseType: "arraybuffer",
                                    });
                                    const buffer = Buffer.from(response.data);
                                    const base64String = buffer.toString("base64");
                                    const mimeType = (_h = response.headers["content-type"]) !== null && _h !== void 0 ? _h : (mime.lookup(fileUrl.split("?")[0]) ||
                                        "application/octet-stream"); // Default MIME type
                                    const dataUrl = `data:${mimeType};base64,${base64String}`;
                                    const humanMsg = new messages_1.HumanMessage({
                                        id: message.id,
                                        content: [
                                            {
                                                type: "image_url",
                                                image_url: {
                                                    url: dataUrl,
                                                },
                                            },
                                            {
                                                type: "text",
                                                text: "用户上传了一张图片，图片 URL = " + fileUrl,
                                            },
                                        ],
                                    });
                                    updatedMessages.push(humanMsg);
                                }
                                catch (error) {
                                    console.error(`Error fetching or processing file from URL ${part.file.url}:`, error);
                                    // Optionally push an error message or skip adding the message
                                    // updatedMessages.push(new SystemMessage(`Error processing file: ${part.file.url}`));
                                }
                            }
                            else if (((_j = part.file) === null || _j === void 0 ? void 0 : _j.file_type) === nurosdk_js_1.IDLChat.ChatMessageFileType.VIDEO) {
                                const fileUrl = part.file.url;
                                const humanMsg = new messages_1.HumanMessage({
                                    id: message.id,
                                    content: [
                                        {
                                            type: "text",
                                            text: "用户上传了一个视频，视频 URL = " + fileUrl,
                                        },
                                    ],
                                });
                                updatedMessages.push(humanMsg);
                            }
                        }
                    }
                }
            }
        }
        return updatedMessages;
    });
}
//# sourceMappingURL=message_processor.js.map