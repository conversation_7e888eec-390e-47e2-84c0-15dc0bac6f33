"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_TEMPERATURE = exports.DEFAULT_SYSTEM_PROMPT = exports.DEFAULT_MODEL_IMAGE = exports.DEFAULT_MODEL_TEXT = exports.DEFAULT_API_KEY = exports.DEFAULT_ENDPOINT = void 0;
exports.getLLMOptions = getLLMOptions;
// Default LLM parameters
exports.DEFAULT_ENDPOINT = "https://ark-cn-beijing.bytedance.net/api/v3";
exports.DEFAULT_API_KEY = process.env.ARK_API_KEY;
exports.DEFAULT_MODEL_TEXT = "ep-20250327120147-qt97z"; // deepseek v3
exports.DEFAULT_MODEL_IMAGE = "ep-20250318173655-4jfb9"; // doubao vlm
exports.DEFAULT_SYSTEM_PROMPT = "You are a helpful assistant.";
exports.DEFAULT_TEMPERATURE = 0.5;
function getHeaderString(req, key) {
    const value = req.headers[key];
    return typeof value === "string" ? value : undefined;
}
function getLLMOptions(req, hasImage) {
    var _a;
    const modelNameHeader = getHeaderString(req, "x-llm-model");
    const endpointHeader = getHeaderString(req, "x-llm-endpoint");
    const apiKeyHeader = getHeaderString(req, "x-llm-apikey");
    const systemPromptHeader = getHeaderString(req, "x-llm-systemprompt");
    const temperatureHeader = getHeaderString(req, "x-llm-temperature");
    const mcpServersHeader = getHeaderString(req, "x-llm-mcpservers"); // Read the new header
    const byteFaasHeadersHeader = getHeaderString(req, "x-llm-bytefaasheaders"); // Read the new header
    const defaultModel = hasImage ? exports.DEFAULT_MODEL_IMAGE : exports.DEFAULT_MODEL_TEXT;
    const modelName = modelNameHeader !== null && modelNameHeader !== void 0 ? modelNameHeader : defaultModel;
    const endpoint = endpointHeader !== null && endpointHeader !== void 0 ? endpointHeader : exports.DEFAULT_ENDPOINT;
    const apiKey = (_a = apiKeyHeader !== null && apiKeyHeader !== void 0 ? apiKeyHeader : exports.DEFAULT_API_KEY) !== null && _a !== void 0 ? _a : ""; // Ensure apiKey is always a string
    // 尝试对 systemPromptHeader 进行 base64 解码，如果解码失败则使用默认值
    const decodedSystemPrompt = systemPromptHeader
        ? decodeURIComponent(systemPromptHeader)
        : undefined;
    const systemPrompt = decodedSystemPrompt !== null && decodedSystemPrompt !== void 0 ? decodedSystemPrompt : exports.DEFAULT_SYSTEM_PROMPT;
    const temperature = temperatureHeader
        ? parseFloat(temperatureHeader)
        : exports.DEFAULT_TEMPERATURE;
    let mcpServers = undefined;
    if (mcpServersHeader) {
        try {
            mcpServers = JSON.parse(mcpServersHeader);
            // Basic validation to ensure it's an array
            if (!Array.isArray(mcpServers)) {
                console.warn("Invalid x-llm-mcpservers header: not an array. Ignoring.");
                mcpServers = undefined;
            }
        }
        catch (error) {
            console.error("Failed to parse x-llm-mcpservers header:", error);
            mcpServers = undefined; // Reset to undefined on error
        }
    }
    let byteFaasHeaders = undefined;
    if (byteFaasHeadersHeader) {
        try {
            const headers = JSON.parse(atob(byteFaasHeadersHeader
                .replace(new RegExp("_P_", "g"), "+")
                .replace(new RegExp("_O_", "g"), "/")
                .replace(new RegExp("_Q_", "g"), "=")));
            if (!Array.isArray(headers)) {
                console.warn("Invalid x-llm-bytefaasheaders header: not an array. Ignoring.");
            }
            else {
                byteFaasHeaders = headers;
            }
        }
        catch (error) {
            console.error("Failed to parse x-llm-bytefaasheaders header:", error);
        }
    }
    return {
        modelName,
        endpoint,
        apiKey,
        systemPrompt,
        temperature,
        mcpServers, // Include mcpServers in the return object
        byteFaasHeaders,
    };
}
//# sourceMappingURL=llm_options.js.map