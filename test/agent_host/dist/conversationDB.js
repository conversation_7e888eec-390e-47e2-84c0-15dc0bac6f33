"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationMemoryDB = void 0;
exports.dumpConversationToFile = dumpConversationToFile;
exports.loadConversationFromFile = loadConversationFromFile;
const fs = require("fs");
const path = require("path");
const messages_1 = require("@langchain/core/messages");
const filesDir = path.join(__dirname, "../conversation_db");
if (!fs.existsSync(filesDir)) {
    fs.mkdirSync(filesDir, { recursive: true });
}
const indexFilePath = path.join(filesDir, "conversation_index.json");
exports.conversationMemoryDB = {};
function readConversationIndex() {
    if (fs.existsSync(indexFilePath)) {
        try {
            const fileContent = fs.readFileSync(indexFilePath, "utf-8");
            return JSON.parse(fileContent);
        }
        catch (error) {
            console.error("Error reading or parsing conversation index file:", error);
            return [];
        }
    }
    else {
        return [];
    }
}
function writeConversationIndex(index) {
    try {
        fs.writeFileSync(indexFilePath, JSON.stringify(index, null, 2));
    }
    catch (error) {
        console.error("Error writing conversation index file:", error);
    }
}
function updateConversationIndex(conversationId, messages) {
    const index = readConversationIndex();
    const existingEntryIndex = index.findIndex((entry) => entry.conversationId === conversationId);
    let firstUserMessageText = "";
    for (const msg of messages) {
        // Find the first non-system message's content
        if (!(msg instanceof messages_1.SystemMessage) && typeof msg.content === "string") {
            firstUserMessageText = msg.content.substring(0, 100); // Limit length
            break;
        }
        // Handle cases where content might be an array (e.g., HumanMessage with image)
        // For simplicity, we'll just take the first text part if available
        if (!(msg instanceof messages_1.SystemMessage) && Array.isArray(msg.content)) {
            firstUserMessageText = msg.text;
        }
    }
    const now = new Date().toISOString();
    if (existingEntryIndex !== -1) {
        // Update existing entry
        index[existingEntryIndex].updatedAt = now;
        // Optionally update firstMessageText if it was empty before
        if (!index[existingEntryIndex].firstMessageText && firstUserMessageText) {
            index[existingEntryIndex].firstMessageText = firstUserMessageText;
        }
    }
    else {
        // Add new entry
        index.push({
            conversationId,
            updatedAt: now,
            firstMessageText: firstUserMessageText,
        });
    }
    writeConversationIndex(index);
}
function dumpConversationToFile(conversationId) {
    const messages = exports.conversationMemoryDB[conversationId];
    if (messages) {
        // Update the index first
        updateConversationIndex(conversationId, messages);
        // Then dump the conversation content
        const serialized = (0, messages_1.mapChatMessagesToStoredMessages)(messages);
        const filePath = path.join(filesDir, `${conversationId}.json`);
        fs.writeFile(filePath, JSON.stringify(serialized, null, 2), (err) => {
            if (err) {
                console.error("Error writing conversation file:", err);
            }
        });
    }
}
function loadConversationFromFile(conversationId) {
    const filePath = path.join(filesDir, `${conversationId}.json`);
    if (fs.existsSync(filePath)) {
        const fileContent = fs.readFileSync(filePath, "utf-8");
        const serialized = JSON.parse(fileContent);
        const deserialized = (0, messages_1.mapStoredMessagesToChatMessages)(serialized);
        exports.conversationMemoryDB[conversationId] = deserialized;
    }
}
//# sourceMappingURL=conversationDB.js.map