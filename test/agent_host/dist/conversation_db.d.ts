import { BaseMessage } from "@langchain/core/messages";
export declare const conversationMemoryDB: Record<string, Record<string, BaseMessage[]>>;
export declare const conversationDBRouter: import("express-serve-static-core").Router;
export declare function dumpConversationToTos(// Renamed and made async
uid: string, conversationId: string): Promise<void>;
export declare function loadConversationFromTos(// Renamed and made async
uid: string, conversationId: string): Promise<void>;
export declare function updateMessagePayload(option: {
    uid: string;
    conversationId: string;
    messageId: string;
    payload: string;
}): Promise<void>;
