{"version": 3, "file": "llm_options.js", "sourceRoot": "", "sources": ["../src/llm_options.ts"], "names": [], "mappings": ";;;AA8BA,sCA0EC;AApGD,yBAAyB;AACZ,QAAA,gBAAgB,GAAG,6CAA6C,CAAC;AACjE,QAAA,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC;AAC1C,QAAA,kBAAkB,GAAG,yBAAyB,CAAC,CAAC,cAAc;AAC9D,QAAA,mBAAmB,GAAG,yBAAyB,CAAC,CAAC,aAAa;AAC9D,QAAA,qBAAqB,GAAG,8BAA8B,CAAC;AACvD,QAAA,mBAAmB,GAAG,GAAG,CAAC;AAYvC,SAAS,eAAe,CACtB,GAAoB,EACpB,GAAW;IAEX,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IAC/B,OAAO,OAAO,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC;AACvD,CAAC;AAED,SAAgB,aAAa,CAC3B,GAAoB,EACpB,QAAiB;;IAEjB,MAAM,eAAe,GAAG,eAAe,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;IAC5D,MAAM,cAAc,GAAG,eAAe,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;IAC9D,MAAM,YAAY,GAAG,eAAe,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;IAC1D,MAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;IACtE,MAAM,iBAAiB,GAAG,eAAe,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;IACpE,MAAM,gBAAgB,GAAG,eAAe,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC,CAAC,sBAAsB;IACzF,MAAM,qBAAqB,GAAG,eAAe,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC,CAAC,sBAAsB;IAEnG,MAAM,YAAY,GAAG,QAAQ,CAAC,CAAC,CAAC,2BAAmB,CAAC,CAAC,CAAC,0BAAkB,CAAC;IAEzE,MAAM,SAAS,GAAG,eAAe,aAAf,eAAe,cAAf,eAAe,GAAI,YAAY,CAAC;IAClD,MAAM,QAAQ,GAAG,cAAc,aAAd,cAAc,cAAd,cAAc,GAAI,wBAAgB,CAAC;IACpD,MAAM,MAAM,GAAG,MAAA,YAAY,aAAZ,YAAY,cAAZ,YAAY,GAAI,uBAAe,mCAAI,EAAE,CAAC,CAAC,mCAAmC;IACzF,mDAAmD;IACnD,MAAM,mBAAmB,GAAG,kBAAkB;QAC5C,CAAC,CAAC,kBAAkB,CAAC,kBAAkB,CAAC;QACxC,CAAC,CAAC,SAAS,CAAC;IACd,MAAM,YAAY,GAAG,mBAAmB,aAAnB,mBAAmB,cAAnB,mBAAmB,GAAI,6BAAqB,CAAC;IAClE,MAAM,WAAW,GAAG,iBAAiB;QACnC,CAAC,CAAC,UAAU,CAAC,iBAAiB,CAAC;QAC/B,CAAC,CAAC,2BAAmB,CAAC;IAExB,IAAI,UAAU,GAAuC,SAAS,CAAC;IAC/D,IAAI,gBAAgB,EAAE,CAAC;QACrB,IAAI,CAAC;YACH,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAC1C,2CAA2C;YAC3C,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC/B,OAAO,CAAC,IAAI,CACV,0DAA0D,CAC3D,CAAC;gBACF,UAAU,GAAG,SAAS,CAAC;YACzB,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0CAA0C,EAAE,KAAK,CAAC,CAAC;YACjE,UAAU,GAAG,SAAS,CAAC,CAAC,8BAA8B;QACxD,CAAC;IACH,CAAC;IACD,IAAI,eAAe,GAAiD,SAAS,CAAC;IAC9E,IAAI,qBAAqB,EAAE,CAAC;QAC1B,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CACxB,IAAI,CACF,qBAAqB;iBAClB,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;iBACpC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC;iBACpC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CACxC,CACF,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,OAAO,CAAC,IAAI,CACV,+DAA+D,CAChE,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,eAAe,GAAG,OAAO,CAAC;YAC5B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+CAA+C,EAAE,KAAK,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;IAED,OAAO;QACL,SAAS;QACT,QAAQ;QACR,MAAM;QACN,YAAY;QACZ,WAAW;QACX,UAAU,EAAE,0CAA0C;QACtD,eAAe;KAChB,CAAC;AACJ,CAAC"}