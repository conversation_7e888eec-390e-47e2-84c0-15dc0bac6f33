import { Tool } from "@modelcontextprotocol/sdk/types.js";
import OpenAI from "openai";
type OpenAIChatCompletionTool = OpenAI.ChatCompletionTool;
export declare class MCPDeviceServerManager_HostSide {
    readonly clientTools: {
        name: string;
        tools: Tool[];
    }[];
    constructor(clientTools: {
        name: string;
        tools: Tool[];
    }[]);
    hasClientTool(toolName: string): boolean;
    getAllToolsAsOpenAITools(): OpenAIChatCompletionTool[];
}
export {};
