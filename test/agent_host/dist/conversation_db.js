"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.conversationDBRouter = exports.conversationMemoryDB = void 0;
exports.dumpConversationToTos = dumpConversationToTos;
exports.loadConversationFromTos = loadConversationFromTos;
exports.updateMessagePayload = updateMessagePayload;
const express = require("express");
// import * as fs from "fs"; // Removed fs
// import * as path from "path"; // Removed path
const tosService_1 = require("./services/tosService"); // Added tosClient
const messages_1 = require("@langchain/core/messages");
// const baseFilesDir = path.join(__dirname, "../conversation_db"); // Removed
// function getUserFilesDir(uid: string): string { // Removed
//   const userDir = path.join(baseFilesDir, uid);
//   if (!fs.existsSync(userDir)) {
//     fs.mkdirSync(userDir, { recursive: true });
//   }
//   return userDir;
// }
function getUserIndexObjectKey(uid) {
    // Renamed and updated
    return `${uid}/conversation_index.json`;
}
function getConversationObjectKey(uid, conversationId) {
    // Renamed and updated
    return `${uid}/${conversationId}.json`;
}
exports.conversationMemoryDB = {};
exports.conversationDBRouter = express.Router();
exports.conversationDBRouter.post("/conversation_list", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Made async
    const userInfo = req.userInfo;
    if (!userInfo || !userInfo.uid) {
        return res.status(400).send("User ID (uid) is missing from userInfo.");
    }
    try {
        const index = yield readConversationIndex(userInfo.uid); // Awaited
        res.json(index);
    }
    catch (error) {
        console.error(`Error fetching conversation list for uid ${userInfo.uid}:`, error);
        res.status(500).send("Error fetching conversation list.");
    }
}));
exports.conversationDBRouter.post("/conversation/:conversationId/:uid", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    // Made async
    const conversationId = req.params.conversationId;
    const uid = req.params.uid;
    const objectKey = getConversationObjectKey(uid, conversationId);
    try {
        const result = yield tosService_1.tosClient.getObject(objectKey);
        if (result && result.objectBuffer) {
            res.send(result.objectBuffer.toString("utf-8"));
        }
        else {
            res.status(404).send("Conversation not found");
        }
    }
    catch (error) {
        // Assuming tosClient.getObject throws an error for non-existent keys (e.g., NoSuchKey)
        // This error handling might need adjustment based on the specific errors thrown by tosClient
        if (error.name === "NoSuchKey" || error.statusCode === 404) {
            // Example error check
            res.status(404).send("Conversation not found");
        }
        else {
            console.error(`Error fetching conversation ${conversationId} for uid ${uid} from TOS:`, error);
            res.status(500).send("Error fetching conversation.");
        }
    }
}));
function readConversationIndex(uid) {
    return __awaiter(this, void 0, void 0, function* () {
        // Made async
        const objectKey = getUserIndexObjectKey(uid);
        try {
            const result = yield tosService_1.tosClient.getObject(objectKey);
            if (result && result.objectBuffer) {
                return JSON.parse(result.objectBuffer.toString("utf-8"));
            }
            return []; // Should not happen if object exists and has content, but as a fallback
        }
        catch (error) {
            return []; // Return empty index on other errors
        }
    });
}
function writeConversationIndex(uid, index) {
    return __awaiter(this, void 0, void 0, function* () {
        // Made async
        const objectKey = getUserIndexObjectKey(uid);
        try {
            yield tosService_1.tosClient.putObject(objectKey, Buffer.from(JSON.stringify(index, null, 2)));
        }
        catch (error) {
            console.error(`Error writing conversation index to TOS for uid ${uid}, key ${objectKey}:`, error);
            // Potentially re-throw or handle more gracefully depending on requirements
        }
    });
}
function updateConversationIndex(// Made async
uid, conversationId, messages) {
    return __awaiter(this, void 0, void 0, function* () {
        // Added Promise<void>
        const index = yield readConversationIndex(uid); // Awaited
        const existingEntryIndex = index.findIndex((entry) => entry.conversationId === conversationId);
        let firstUserMessageText = "";
        for (const msg of messages) {
            if (!(msg instanceof messages_1.SystemMessage) && typeof msg.content === "string") {
                firstUserMessageText = msg.content.substring(0, 100);
                break;
            }
            if (!(msg instanceof messages_1.SystemMessage) && Array.isArray(msg.content)) {
                const textPart = msg.content.find((part) => part.type === "text");
                if (textPart &&
                    typeof textPart.text === "string") {
                    firstUserMessageText = textPart.text.substring(0, 100);
                    break;
                }
                else if (typeof msg.text === "string") {
                    // Adjusted to (msg as any).text for safety
                    firstUserMessageText = msg.text.substring(0, 100);
                    break;
                }
            }
        }
        const now = new Date().toISOString(); // Using ISOString for consistency
        if (existingEntryIndex !== -1) {
            index[existingEntryIndex].updatedAt = now;
            if (!index[existingEntryIndex].firstMessageText && firstUserMessageText) {
                index[existingEntryIndex].firstMessageText = firstUserMessageText;
            }
        }
        else {
            index.push({
                conversationId,
                uid,
                updatedAt: now,
                firstMessageText: firstUserMessageText,
            });
        }
        yield writeConversationIndex(uid, index); // Awaited
    });
}
function dumpConversationToTos(// Renamed and made async
uid, conversationId) {
    return __awaiter(this, void 0, void 0, function* () {
        // Added Promise<void>
        if (!exports.conversationMemoryDB[uid] ||
            !exports.conversationMemoryDB[uid][conversationId]) {
            console.warn(`No messages in memory for uid ${uid}, conversationId ${conversationId} to dump.`);
            return;
        }
        const messages = exports.conversationMemoryDB[uid][conversationId];
        if (messages) {
            yield updateConversationIndex(uid, conversationId, messages); // Awaited
            const serialized = (0, messages_1.mapChatMessagesToStoredMessages)(messages);
            const objectKey = getConversationObjectKey(uid, conversationId);
            try {
                yield tosService_1.tosClient.putObject(objectKey, Buffer.from(JSON.stringify(serialized, null, 2)));
            }
            catch (err) {
                console.error(`Error writing conversation to TOS for uid ${uid}, conversationId ${conversationId}, key ${objectKey}:`, err);
            }
        }
    });
}
function loadConversationFromTos(// Renamed and made async
uid, conversationId) {
    return __awaiter(this, void 0, void 0, function* () {
        // Added Promise<void>
        const objectKey = getConversationObjectKey(uid, conversationId);
        try {
            const result = yield tosService_1.tosClient.getObject(objectKey);
            if (result && result.objectBuffer) {
                const serialized = JSON.parse(result.objectBuffer.toString("utf-8"));
                const deserialized = (0, messages_1.mapStoredMessagesToChatMessages)(serialized);
                if (!exports.conversationMemoryDB[uid]) {
                    exports.conversationMemoryDB[uid] = {};
                }
                exports.conversationMemoryDB[uid][conversationId] = deserialized;
            }
        }
        catch (error) {
            // If the object does not exist, tosClient.getObject might throw an error (e.g., NoSuchKey)
            if (error.name === "NoSuchKey" || error.statusCode === 404) {
                // Example error check
                // Conversation file doesn't exist, which is a valid scenario (e.g., new conversation)
                // No need to log an error here, just means no data to load.
                return;
            }
        }
    });
}
function updateMessagePayload(option) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        const { uid, conversationId, messageId, payload } = option;
        // 1. Load conversation from TOS (this will also update in-memory DB)
        yield loadConversationFromTos(uid, conversationId);
        const messages = (_a = exports.conversationMemoryDB[uid]) === null || _a === void 0 ? void 0 : _a[conversationId];
        if (!messages) {
            console.error(`Conversation not found in memory for uid ${uid}, conversationId ${conversationId} after attempting to load.`);
            // Optionally, you could throw an error here or handle it as per your application's needs
            return;
        }
        // 2. Find the message by messageId
        const messageIndex = messages.findIndex((msg) => msg.id === messageId);
        if (messageIndex === -1) {
            console.error(`Message with id ${messageId} not found in conversation ${conversationId} for uid ${uid}.`);
            // Optionally, throw an error or handle
            return;
        }
        // 3. Update the additional_kwargs.payload
        const messageToUpdate = messages[messageIndex];
        if (!messageToUpdate.additional_kwargs) {
            messageToUpdate.additional_kwargs = {};
        }
        messageToUpdate.additional_kwargs.payload = payload;
        // Update the message in the array
        exports.conversationMemoryDB[uid][conversationId][messageIndex] = messageToUpdate;
        // 4. Save the updated conversation back to TOS (this also updates the index via dumpConversationToTos's internal call to updateConversationIndex)
        yield dumpConversationToTos(uid, conversationId);
        // 5. Explicitly update the conversation index again to ensure the updatedAt timestamp is fresh if dumpConversationToTos doesn't cover all cases
        // or if a more direct update is preferred after a specific message modification.
        // However, dumpConversationToTos already calls updateConversationIndex. If the goal is just to reflect the change,
        // the call within dumpConversationToTos should be sufficient. If a separate, more immediate index update is needed
        // for other reasons (e.g., specific logic tied to payload updates), it can be done here.
        // For now, relying on dumpConversationToTos's existing behavior.
        // await updateConversationIndex(uid, conversationId, messages); // This might be redundant if dumpConversationToTos handles it
        console.log(`Payload updated for message ${messageId} in conversation ${conversationId} for uid ${uid}.`);
    });
}
//# sourceMappingURL=conversation_db.js.map