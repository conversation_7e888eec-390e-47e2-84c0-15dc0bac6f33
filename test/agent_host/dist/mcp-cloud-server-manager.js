"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPCloudServerManager = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/client/index.js");
const sse_js_1 = require("@modelcontextprotocol/sdk/client/sse.js");
const mcp_client_sdk_1 = require("@byted/mcp-client-sdk");
class MCPCloudServerManager {
    constructor() {
        this.servers = new Map();
        this.toolsMapping = new Map();
    }
    registerServer(config, llmOptions, userInfo, conversationId) {
        return __awaiter(this, void 0, void 0, function* () {
            if (this.servers.has(config.name)) {
                throw new Error(`Server with id ${config.name} already exists`);
            }
            if (config.url) {
                const transport = new sse_js_1.SSEClientTransport(new URL(config.url), {});
                const client = new index_js_1.Client({
                    name: config.name,
                    version: "1.0.0",
                }, {
                    capabilities: {
                        prompts: {},
                        resources: {},
                        tools: {},
                    },
                });
                this.servers.set(config.name, {
                    config,
                    client,
                    transport,
                    tools: [],
                    isConnected: false,
                });
            }
            else if (config.byteFaasConfig) {
                const logId = (0, mcp_client_sdk_1.generateDefaultLogId)();
                const ctx = {
                    logger: (0, mcp_client_sdk_1.generateBytedServiceLoggerWithLogId)({ logId }),
                    logId,
                };
                const client = new mcp_client_sdk_1.BytedMcpClient({
                    byte_faas_caller: {
                        transport: "http",
                        psm: config.byteFaasConfig.psm,
                        mcpGatewayRegion: (() => {
                            switch (config.byteFaasConfig.region) {
                                case "BOE":
                                    return mcp_client_sdk_1.MCP_GATEWAY_REGION.BOE;
                                case "CN":
                                    return mcp_client_sdk_1.MCP_GATEWAY_REGION.CN;
                                case "I18N":
                                    return mcp_client_sdk_1.MCP_GATEWAY_REGION.I18N;
                                default:
                                    return mcp_client_sdk_1.MCP_GATEWAY_REGION.BOE;
                            }
                        })(),
                        timeout: 60000,
                    },
                });
                yield client.connectAll({
                    ctx: ctx,
                });
                client.registerBeforeCallHook({
                    serverName: "byte_faas_caller",
                    beforeCallHook: mcp_client_sdk_1.addZtiTokenHook,
                    ctx,
                });
                yield client.loadAllTools({
                    ctx: ctx,
                });
                yield client.registerBeforeCallHook({
                    serverName: "byte_faas_caller",
                    beforeCallHook: (hook) => {
                        var _a;
                        (_a = llmOptions.byteFaasHeaders) === null || _a === void 0 ? void 0 : _a.forEach((header) => {
                            hook.headers[header.key] = header.value;
                        });
                        hook.headers["x-nuro-aid"] = userInfo.aid;
                        hook.headers["x-nuro-uid"] = userInfo.uid;
                        hook.headers["x-nuro-did"] = userInfo.did;
                        hook.headers["x-nuro-cid"] = conversationId;
                    },
                    ctx: ctx,
                });
                this.servers.set(config.name, {
                    config,
                    client,
                    transport: undefined,
                    tools: [],
                    isConnected: false,
                });
            }
        });
    }
    connectServer(serverName) {
        return __awaiter(this, void 0, void 0, function* () {
            const server = this.servers.get(serverName);
            if (!server) {
                throw new Error(`Server ${serverName} not found`);
            }
            if (!server.isConnected) {
                if (server.client instanceof index_js_1.Client && server.transport) {
                    server.client.onclose = () => {
                        console.log(`Server ${serverName} disconnected`);
                        server.isConnected = false;
                    };
                    try {
                        yield server.client.connect(server.transport);
                        const toolsResult = yield server.client.listTools();
                        server.tools = toolsResult.tools;
                        toolsResult.tools.forEach((tool) => {
                            this.toolsMapping.set(`${serverName}_${tool.name}`, {
                                serverName,
                                functionName: tool.name,
                            });
                        });
                        server.isConnected = true;
                    }
                    catch (error) {
                        throw `Failed to connect to MCP server: ${serverName}`;
                    }
                }
                if (server.client instanceof mcp_client_sdk_1.BytedMcpClient) {
                    const logId = (0, mcp_client_sdk_1.generateDefaultLogId)();
                    const ctx = {
                        logger: (0, mcp_client_sdk_1.generateBytedServiceLoggerWithLogId)({ logId }),
                        logId,
                    };
                    try {
                        const tools = yield server.client.loadAllTools({ ctx });
                        server.tools = tools;
                        tools.forEach((tool) => {
                            this.toolsMapping.set(`${serverName}_${tool.name}`, {
                                serverName,
                                functionName: tool.name,
                            });
                        });
                        server.isConnected = true;
                    }
                    catch (error) {
                        throw `Failed to connect to MCP server: ${serverName}`;
                    }
                }
            }
        });
    }
    connectAll() {
        return __awaiter(this, void 0, void 0, function* () {
            const promises = Array.from(this.servers.keys()).map((serverName) => this.connectServer(serverName));
            yield Promise.all(promises);
        });
    }
    getServerTools(name) {
        const server = this.servers.get(name);
        if (!server) {
            throw new Error(`Server ${name} not found`);
        }
        return server.tools;
    }
    getAllTools() {
        return Array.from(this.servers.entries()).map(([name, server]) => ({
            name,
            tools: server.tools,
        }));
    }
    getAllToolsAsOpenAITools() {
        let openAITools = [];
        this.getAllTools().forEach(({ name, tools }) => {
            tools.forEach((tool) => {
                const inputSchema = tool.inputSchema;
                delete inputSchema["$schema"];
                delete inputSchema["additionalProperties"];
                openAITools.push({
                    type: "function",
                    function: {
                        name: `${name}_${tool.name}`,
                        description: tool.description,
                        parameters: inputSchema,
                    },
                });
            });
        });
        return openAITools;
    }
    getAllToolsAsClaudeTools() {
        let claudeTools = [];
        this.getAllTools().forEach(({ name, tools }) => {
            tools.forEach((tool) => {
                const inputSchema = tool.inputSchema;
                delete inputSchema["$schema"];
                delete inputSchema["additionalProperties"];
                claudeTools.push({
                    name: `${name}_${tool.name}`,
                    description: tool.description,
                    input_schema: inputSchema,
                });
            });
        });
        return claudeTools;
    }
    hasTool(functionNameWithPrefix) {
        return this.toolsMapping.has(functionNameWithPrefix);
    }
    callTool(option) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c, _d;
            const mapping = this.toolsMapping.get(option.functionNameWithPrefix);
            if (!mapping) {
                throw new Error(`Tool ${option.functionNameWithPrefix} not found`);
            }
            const server = this.servers.get(mapping.serverName);
            if (!server) {
                throw new Error(`Server ${mapping.serverName} not found`);
            }
            if (!server.isConnected) {
                throw new Error(`Server ${mapping.serverName} is not connected`);
            }
            let done = false;
            if (server.client instanceof index_js_1.Client) {
                const result = yield server.client.callTool({
                    name: mapping.functionName,
                    _meta: {
                        progressToken: option.functionCallId,
                    },
                    arguments: Object.assign(Object.assign({}, ((_a = option.functionCallArguments) !== null && _a !== void 0 ? _a : {})), ((_b = option.extraArguments) !== null && _b !== void 0 ? _b : {})),
                });
                done = true;
                return `${JSON.stringify(result)}`;
            }
            else if (server.client instanceof mcp_client_sdk_1.BytedMcpClient) {
                const result = yield server.client.callTool(mapping.functionName, Object.assign(Object.assign({}, ((_c = option.functionCallArguments) !== null && _c !== void 0 ? _c : {})), ((_d = option.extraArguments) !== null && _d !== void 0 ? _d : {})));
                done = true;
                return `${JSON.stringify(result)}`;
            }
            else {
                throw "unknown client type";
            }
        });
    }
}
exports.MCPCloudServerManager = MCPCloudServerManager;
//# sourceMappingURL=mcp-cloud-server-manager.js.map