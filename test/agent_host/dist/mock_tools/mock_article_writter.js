"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MockArticleWritter = void 0;
const messages_1 = require("@langchain/core/messages");
const handoff_server_manager_1 = require("../handoff-server-manager");
const llm_options_1 = require("../llm_options");
const model_initializer_1 = require("../utils/model_initializer");
class MockArticleWritter extends handoff_server_manager_1.HandoffTool {
    constructor() {
        super({
            type: "function",
            function: {
                name: "article_writter",
                description: "Write an article based on the given topic, output 100 words.",
                parameters: {
                    type: "object",
                    properties: {
                        topic: {
                            type: "string",
                            description: "The topic of the article",
                        },
                    },
                    required: ["topic"],
                },
                strict: true,
            },
        });
    }
    call(option) {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, e_1, _b, _c;
            var _d;
            const model = (0, model_initializer_1.initializeModel)({
                modelName: llm_options_1.DEFAULT_MODEL_TEXT,
                endpoint: llm_options_1.DEFAULT_ENDPOINT,
                apiKey: llm_options_1.DEFAULT_API_KEY,
                systemPrompt: "你的任务是根据用户给出的主题，写出 100 字的文章。",
                temperature: 0.5,
            });
            const stream = yield model.stream([
                new messages_1.HumanMessage(`写一篇关于「${(_d = option.functionCallArguments) === null || _d === void 0 ? void 0 : _d.topic}」的文章，字数不少于 100。`),
            ]);
            let result = "";
            try {
                for (var _e = true, stream_1 = __asyncValues(stream), stream_1_1; stream_1_1 = yield stream_1.next(), _a = stream_1_1.done, !_a; _e = true) {
                    _c = stream_1_1.value;
                    _e = false;
                    const chunk = _c;
                    if (typeof chunk.content === "string") {
                        option.streamingCallback(chunk.content);
                        result += chunk.content;
                    }
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_e && !_a && (_b = stream_1.return)) yield _b.call(stream_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
            option.finishCallback(result);
        });
    }
}
exports.MockArticleWritter = MockArticleWritter;
//# sourceMappingURL=mock_article_writter.js.map