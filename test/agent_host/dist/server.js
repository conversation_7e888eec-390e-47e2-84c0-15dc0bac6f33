"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __asyncValues = (this && this.__asyncValues) || function (o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function () { return this; }, i);
    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }
    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }
};
Object.defineProperty(exports, "__esModule", { value: true });
const express = require("express");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const user_1 = require("./user");
const file_db_1 = require("./file_db");
const llm_options_1 = require("./llm_options");
const messages_1 = require("@langchain/core/messages");
const mcp_cloud_server_manager_1 = require("./mcp-cloud-server-manager");
const conversation_db_1 = require("./conversation_db");
const nurosdk_js_1 = require("@byted/nurosdk-js");
const message_processor_1 = require("./utils/message_processor");
const model_initializer_1 = require("./utils/model_initializer");
const tool_processor_1 = require("./utils/tool_processor");
const sse_handler_1 = require("./utils/sse_handler");
const handoff_server_manager_1 = require("./handoff-server-manager");
const mock_article_writter_1 = require("./mock_tools/mock_article_writter");
process.env.DEEPSEEK_API_KEY = process.env.ARK_API_KEY;
const app = express();
app.use(express.json({ limit: 1024 * 1024 * 100 }));
app.use(cors({
    origin: (requestOrigin, callback) => {
        callback(null, true); // Allow the origin
    },
    credentials: true,
    allowedHeaders: [
        "content-type",
        "x-llm-model",
        "x-llm-endpoint",
        "x-llm-apikey",
        "x-llm-systemprompt",
        "x-llm-temperature",
        "x-llm-mcpservers",
        "x-llm-bytefaasheaders",
    ],
    optionsSuccessStatus: 200,
}));
app.use(cookieParser());
// Middleware to handle user information
app.use((req, res, next) => {
    let userInfo = user_1.User.getUserInfoFromRequest(req);
    req.userInfo = userInfo;
    user_1.User.setUserInfoToResponse(res, userInfo);
    next();
});
// Use the conversation router for /conversation_list and /conversation/:conversationId routes
app.use(conversation_db_1.conversationDBRouter);
// Use the file router for /upload and /files routes
app.use(file_db_1.fileRouter);
// Endpoint to get user info
app.get("/user_info", (req, res) => {
    const userInfo = req.userInfo;
    if (userInfo) {
        res.json(userInfo);
    }
    else {
        // This case should ideally not be reached if the middleware is working correctly
        res.status(404).json({ error: "User information not found." });
    }
});
app.post("/set_message_payload", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    const userInfo = req.userInfo;
    const uid = userInfo.uid;
    const { conversation_id, message_id, payload } = req.body;
    try {
        yield (0, conversation_db_1.updateMessagePayload)({
            uid,
            conversationId: conversation_id,
            messageId: message_id,
            payload,
        });
        res.send(JSON.stringify({
            ret: "0",
            errmsg: "success",
        }));
    }
    catch (error) {
        res.send(JSON.stringify({
            ret: "-1",
            errmsg: "failed",
        }));
    }
}));
app.post("/sse", (req, res) => __awaiter(void 0, void 0, void 0, function* () {
    var _a, _b;
    const body = new nurosdk_js_1.IDLChatRequest.ChatRequest({
        JSONString: JSON.stringify(req.body),
    });
    const conversationId = body.conversationId || Math.random().toString(36).substring(2, 15);
    let clientClosed = false;
    // 设置SSE响应头
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("X-Bytefaas-Enable-Stream", "true");
    res.on("close", () => {
        clientClosed = true;
    });
    let llmOptions = (0, llm_options_1.getLLMOptions)(req, false);
    const userInfo = req.userInfo;
    const uid = userInfo.uid;
    let messages = [new messages_1.SystemMessage(llmOptions.systemPrompt)];
    if (!conversation_db_1.conversationMemoryDB[uid]) {
        conversation_db_1.conversationMemoryDB[uid] = {};
    }
    if (conversation_db_1.conversationMemoryDB[uid][conversationId] === undefined) {
        yield (0, conversation_db_1.loadConversationFromTos)(uid, conversationId); // Awaited and updated
    }
    if (conversation_db_1.conversationMemoryDB[uid][conversationId]) {
        messages = conversation_db_1.conversationMemoryDB[uid][conversationId];
    }
    else {
        conversation_db_1.conversationMemoryDB[uid][conversationId] = messages;
    }
    // Process incoming messages using the helper function
    yield (0, message_processor_1.processIncomingMessages)(body, messages);
    const hasImageInHistory = ((_b = (_a = messages.filter((it) => it instanceof messages_1.HumanMessage &&
        it.content instanceof Array &&
        it.content[0].type === "image_url")) === null || _a === void 0 ? void 0 : _a.length) !== null && _b !== void 0 ? _b : 0) > 0;
    llmOptions = (0, llm_options_1.getLLMOptions)(req, hasImageInHistory);
    if (hasImageInHistory && llmOptions.modelName !== llm_options_1.DEFAULT_MODEL_IMAGE) {
        // 非 double vlm 模型不支持 image_url
        messages.forEach((it) => {
            if (it instanceof messages_1.HumanMessage) {
                if (it.content instanceof Array) {
                    it.content = it.content.filter((it) => it.type !== "image_url");
                }
            }
        });
    }
    // Initialize the model using the helper function
    const model = (0, model_initializer_1.initializeModel)(llmOptions);
    const isClaude = llmOptions.modelName.includes("claude");
    function runLoop() {
        return __awaiter(this, void 0, void 0, function* () {
            var _a, e_1, _b, _c;
            // Add return type annotation
            const mcpCloudServerManager = new mcp_cloud_server_manager_1.MCPCloudServerManager();
            const handoffServerManager = new handoff_server_manager_1.HandoffServerManager();
            if (llmOptions.mcpServers && llmOptions.mcpServers.length) {
                for (let index = 0; index < llmOptions.mcpServers.length; index++) {
                    const server = llmOptions.mcpServers[index];
                    yield mcpCloudServerManager.registerServer(server, llmOptions, userInfo, conversationId);
                }
            }
            else {
                // 部署在 ByteFaas 上的一个测试服务
                // https://cloud-boe.bytedance.net/faas/mcp_servers/bb0i4fck/tools?x-resource-account=boe&x-bc-region-id=bytedance
                yield mcpCloudServerManager.registerServer({
                    name: "weather_mock",
                    url: "https://bb0i4fck.mcp-boe.bytedance.net/sse",
                }, llmOptions, userInfo, conversationId);
            }
            // 创建一个 Mock 的流式服务，用于测试。
            handoffServerManager.registerTool(new mock_article_writter_1.MockArticleWritter());
            yield mcpCloudServerManager.connectAll();
            // Format client tools using the helper function
            const clientTools = (0, tool_processor_1.formatClientTools)(body, isClaude);
            // console.log("model", model)
            const stream = yield model.stream(messages, {
                tools: isClaude
                    ? [
                        ...(yield mcpCloudServerManager.getAllToolsAsClaudeTools()),
                        ...clientTools,
                        ...handoffServerManager.getTools(),
                    ]
                    : [
                        ...(yield mcpCloudServerManager.getAllToolsAsOpenAITools()),
                        ...clientTools,
                        ...handoffServerManager.getTools(),
                    ],
            });
            // Send initial message structure via SSE
            const msg_id = (0, sse_handler_1.sendInitialMessage)(res, conversationId);
            let currentTextContent = "";
            let currentReasoningContent = "";
            let currentToolCalls = [];
            try {
                // Process the stream using the helper function
                for (var _d = true, stream_1 = __asyncValues(stream), stream_1_1; stream_1_1 = yield stream_1.next(), _a = stream_1_1.done, !_a; _d = true) {
                    _c = stream_1_1.value;
                    _d = false;
                    const chunk = _c;
                    if (clientClosed) {
                        console.log("Client closed connection, stopping stream processing.");
                        break;
                    }
                    // console.log("Raw Stream Chunk:", chunk);
                    const { textContentDelta, updatedToolCalls, reasoningContentDelta } = (0, sse_handler_1.processStreamChunk)(chunk, mcpCloudServerManager, res, currentToolCalls, isClaude);
                    currentTextContent += textContentDelta;
                    if (reasoningContentDelta) {
                        currentReasoningContent += reasoningContentDelta;
                    }
                    currentToolCalls = updatedToolCalls;
                }
            }
            catch (e_1_1) { e_1 = { error: e_1_1 }; }
            finally {
                try {
                    if (!_d && !_a && (_b = stream_1.return)) yield _b.call(stream_1);
                }
                finally { if (e_1) throw e_1.error; }
            }
            // Add the AI's response (text and tool calls) to history *before* execution
            (0, tool_processor_1.addAiResponseToHistory)(currentToolCalls, currentTextContent, currentReasoningContent, messages, msg_id, isClaude);
            let hasServerSideFunctionExecuted = false;
            if (currentToolCalls.length > 0) {
                // Finalize parsing and send tool call info via SSE
                yield (0, tool_processor_1.finalizeAndSendToolCalls)(currentToolCalls, mcpCloudServerManager, handoffServerManager, res);
                // Execute server-side tools and update history
                hasServerSideFunctionExecuted = yield (0, tool_processor_1.executeServerSideTools)(conversationId, currentToolCalls, mcpCloudServerManager, handoffServerManager, messages, // Pass messages to add ToolMessage results
                res);
            }
            return hasServerSideFunctionExecuted; // Return true if server tools were called, to potentially loop again
        });
    }
    // Keep running the loop as long as server-side functions are executed
    while (yield runLoop()) {
        // The loop will now handle the next turn if server functions were called
        console.log("Re-running loop after server-side tool execution...");
    }
    // Send final SSE messages
    (0, sse_handler_1.sendFinalMessages)(res, conversationId);
    // End the response and save conversation
    setTimeout(() => __awaiter(void 0, void 0, void 0, function* () {
        // Made inner function async
        res.end(); // 关闭响应流，Delay 1 seconds to ensure the client receives the finally message
        yield (0, conversation_db_1.dumpConversationToTos)(uid, conversationId); // Awaited and updated
    }), 1000);
}));
const port = 8888;
app.listen(port, (error) => {
    if (error) {
        console.error("Error starting server:", error);
        return;
    }
    console.log(`Server is running on http://localhost:${port}`);
});
//# sourceMappingURL=server.js.map