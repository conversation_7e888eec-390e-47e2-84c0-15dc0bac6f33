{"version": 3, "file": "mcp-cloud-server-manager.js", "sourceRoot": "", "sources": ["../src/mcp-cloud-server-manager.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,wEAAmE;AACnE,oEAA6E;AAG7E,0DAO+B;AAqB/B,MAAa,qBAAqB;IAAlC;QACU,YAAO,GAAkC,IAAI,GAAG,EAAE,CAAC;QACnD,iBAAY,GAGhB,IAAI,GAAG,EAAE,CAAC;IAkQhB,CAAC;IAhQO,cAAc,CAClB,MAA4B,EAC5B,UAAsB,EACtB,QAAkB,EAClB,cAAuB;;YAEvB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,kBAAkB,MAAM,CAAC,IAAI,iBAAiB,CAAC,CAAC;YAClE,CAAC;YAED,IAAI,MAAM,CAAC,GAAG,EAAE,CAAC;gBACf,MAAM,SAAS,GAAG,IAAI,2BAAkB,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC;gBAClE,MAAM,MAAM,GAAG,IAAI,iBAAM,CACvB;oBACE,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,OAAO,EAAE,OAAO;iBACjB,EACD;oBACE,YAAY,EAAE;wBACZ,OAAO,EAAE,EAAE;wBACX,SAAS,EAAE,EAAE;wBACb,KAAK,EAAE,EAAE;qBACV;iBACF,CACF,CAAC;gBAEF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC5B,MAAM;oBACN,MAAM;oBACN,SAAS;oBACT,KAAK,EAAE,EAAE;oBACT,WAAW,EAAE,KAAK;iBACnB,CAAC,CAAC;YACL,CAAC;iBAAM,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBACjC,MAAM,KAAK,GAAG,IAAA,qCAAoB,GAAE,CAAC;gBACrC,MAAM,GAAG,GAA0B;oBACjC,MAAM,EAAE,IAAA,oDAAmC,EAAC,EAAE,KAAK,EAAE,CAAC;oBACtD,KAAK;iBACN,CAAC;gBACF,MAAM,MAAM,GAAG,IAAI,+BAAc,CAAC;oBAChC,gBAAgB,EAAE;wBAChB,SAAS,EAAE,MAAM;wBACjB,GAAG,EAAE,MAAM,CAAC,cAAc,CAAC,GAAG;wBAC9B,gBAAgB,EAAE,CAAC,GAAG,EAAE;4BACtB,QAAQ,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;gCACrC,KAAK,KAAK;oCACR,OAAO,mCAAkB,CAAC,GAAG,CAAC;gCAChC,KAAK,IAAI;oCACP,OAAO,mCAAkB,CAAC,EAAE,CAAC;gCAC/B,KAAK,MAAM;oCACT,OAAO,mCAAkB,CAAC,IAAI,CAAC;gCACjC;oCACE,OAAO,mCAAkB,CAAC,GAAG,CAAC;4BAClC,CAAC;wBACH,CAAC,CAAC,EAAE;wBACJ,OAAO,EAAE,KAAK;qBACf;iBACF,CAAC,CAAC;gBACH,MAAM,MAAM,CAAC,UAAU,CAAC;oBACtB,GAAG,EAAE,GAAG;iBACT,CAAC,CAAC;gBACH,MAAM,CAAC,sBAAsB,CAAC;oBAC5B,UAAU,EAAE,kBAAkB;oBAC9B,cAAc,EAAE,gCAAe;oBAC/B,GAAG;iBACJ,CAAC,CAAC;gBACH,MAAM,MAAM,CAAC,YAAY,CAAC;oBACxB,GAAG,EAAE,GAAG;iBACT,CAAC,CAAC;gBACH,MAAM,MAAM,CAAC,sBAAsB,CAAC;oBAClC,UAAU,EAAE,kBAAkB;oBAC9B,cAAc,EAAE,CAAC,IAAS,EAAE,EAAE;;wBAC5B,MAAA,UAAU,CAAC,eAAe,0CAAE,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE;4BAC7C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC;wBAC1C,CAAC,CAAC,CAAC;wBACH,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC;wBAC1C,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,cAAc,CAAC;oBAC9C,CAAC;oBACD,GAAG,EAAE,GAAG;iBACT,CAAC,CAAC;gBACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE;oBAC5B,MAAM;oBACN,MAAM;oBACN,SAAS,EAAE,SAAS;oBACpB,KAAK,EAAE,EAAE;oBACT,WAAW,EAAE,KAAK;iBACnB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;KAAA;IAEK,aAAa,CAAC,UAAkB;;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;YAC5C,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,UAAU,UAAU,YAAY,CAAC,CAAC;YACpD,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACxB,IAAI,MAAM,CAAC,MAAM,YAAY,iBAAM,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;oBACxD,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE;wBAC3B,OAAO,CAAC,GAAG,CAAC,UAAU,UAAU,eAAe,CAAC,CAAC;wBACjD,MAAM,CAAC,WAAW,GAAG,KAAK,CAAC;oBAC7B,CAAC,CAAC;oBACF,IAAI,CAAC;wBACH,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;wBAC9C,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC;wBACpD,MAAM,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC;wBACjC,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BACjC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;gCAClD,UAAU;gCACV,YAAY,EAAE,IAAI,CAAC,IAAI;6BACxB,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;oBAC5B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,oCAAoC,UAAU,EAAE,CAAC;oBACzD,CAAC;gBACH,CAAC;gBACD,IAAI,MAAM,CAAC,MAAM,YAAY,+BAAc,EAAE,CAAC;oBAC5C,MAAM,KAAK,GAAG,IAAA,qCAAoB,GAAE,CAAC;oBACrC,MAAM,GAAG,GAA0B;wBACjC,MAAM,EAAE,IAAA,oDAAmC,EAAC,EAAE,KAAK,EAAE,CAAC;wBACtD,KAAK;qBACN,CAAC;oBACF,IAAI,CAAC;wBACH,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;wBACxD,MAAM,CAAC,KAAK,GAAG,KAAc,CAAC;wBAC9B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;4BACrB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,UAAU,IAAI,IAAI,CAAC,IAAI,EAAE,EAAE;gCAClD,UAAU;gCACV,YAAY,EAAE,IAAI,CAAC,IAAI;6BACxB,CAAC,CAAC;wBACL,CAAC,CAAC,CAAC;wBACH,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC;oBAC5B,CAAC;oBAAC,OAAO,KAAK,EAAE,CAAC;wBACf,MAAM,oCAAoC,UAAU,EAAE,CAAC;oBACzD,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;KAAA;IAEK,UAAU;;YACd,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE,CAClE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAC/B,CAAC;YACF,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC9B,CAAC;KAAA;IAED,cAAc,CAAC,IAAY;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACtC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,YAAY,CAAC,CAAC;QAC9C,CAAC;QACD,OAAO,MAAM,CAAC,KAAK,CAAC;IACtB,CAAC;IAED,WAAW;QACT,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;YACjE,IAAI;YACJ,KAAK,EAAE,MAAM,CAAC,KAAK;SACpB,CAAC,CAAC,CAAC;IACN,CAAC;IAED,wBAAwB;QACtB,IAAI,WAAW,GAA+B,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YAC7C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACrC,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;gBAC9B,OAAO,WAAW,CAAC,sBAAsB,CAAC,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;wBAC5B,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,UAAU,EAAE,WAAW;qBACxB;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,wBAAwB;QACtB,IAAI,WAAW,GAA4B,EAAE,CAAC;QAC9C,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YAC7C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrB,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;gBACrC,OAAO,WAAW,CAAC,SAAS,CAAC,CAAC;gBAC9B,OAAO,WAAW,CAAC,sBAAsB,CAAC,CAAC;gBAC3C,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;oBAC5B,WAAW,EAAE,IAAI,CAAC,WAAW;oBAC7B,YAAY,EAAE,WAAW;iBAC1B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,sBAA8B;QACpC,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;IACvD,CAAC;IAEK,QAAQ,CAAC,MAMd;;;YACC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAC;YACrE,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,QAAQ,MAAM,CAAC,sBAAsB,YAAY,CAAC,CAAC;YACrE,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACpD,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,MAAM,IAAI,KAAK,CAAC,UAAU,OAAO,CAAC,UAAU,YAAY,CAAC,CAAC;YAC5D,CAAC;YAED,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACxB,MAAM,IAAI,KAAK,CAAC,UAAU,OAAO,CAAC,UAAU,mBAAmB,CAAC,CAAC;YACnE,CAAC;YAED,IAAI,IAAI,GAAG,KAAK,CAAC;YAEjB,IAAI,MAAM,CAAC,MAAM,YAAY,iBAAM,EAAE,CAAC;gBACpC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;oBAC1C,IAAI,EAAE,OAAO,CAAC,YAAY;oBAC1B,KAAK,EAAE;wBACL,aAAa,EAAE,MAAM,CAAC,cAAc;qBACrC;oBACD,SAAS,kCACJ,CAAC,MAAA,MAAM,CAAC,qBAAqB,mCAAI,EAAE,CAAC,GACpC,CAAC,MAAA,MAAM,CAAC,cAAc,mCAAI,EAAE,CAAC,CACjC;iBACF,CAAC,CAAC;gBAEH,IAAI,GAAG,IAAI,CAAC;gBAEZ,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACrC,CAAC;iBAAM,IAAI,MAAM,CAAC,MAAM,YAAY,+BAAc,EAAE,CAAC;gBACnD,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,kCAC3D,CAAC,MAAA,MAAM,CAAC,qBAAqB,mCAAI,EAAE,CAAC,GACpC,CAAC,MAAA,MAAM,CAAC,cAAc,mCAAI,EAAE,CAAC,EAChC,CAAC;gBAEH,IAAI,GAAG,IAAI,CAAC;gBAEZ,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,MAAM,qBAAqB,CAAC;YAC9B,CAAC;QACH,CAAC;KAAA;CACF;AAvQD,sDAuQC"}