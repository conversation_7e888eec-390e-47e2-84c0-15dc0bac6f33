{"version": 3, "file": "mcp-device-server-manager.js", "sourceRoot": "", "sources": ["../src/mcp-device-server-manager.ts"], "names": [], "mappings": ";;;AAKA,MAAa,+BAA+B;IAC1C,YAAqB,WAA8C;QAA9C,gBAAW,GAAX,WAAW,CAAmC;IAAG,CAAC;IAEvE,aAAa,CAAC,QAAgB;QAC5B,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;IAC/G,CAAC;IAED,wBAAwB;QACtB,IAAI,WAAW,GAA+B,EAAE,CAAC;QACjD,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;YAC3C,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE;gBACrB,WAAW,CAAC,IAAI,CAAC;oBACf,IAAI,EAAE,UAAU;oBAChB,QAAQ,EAAE;wBACR,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;wBAC5B,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,UAAU,EAAE,IAAI,CAAC,WAAW;qBAC7B;iBACF,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,OAAO,WAAW,CAAC;IACrB,CAAC;CACF;AAvBD,0EAuBC"}