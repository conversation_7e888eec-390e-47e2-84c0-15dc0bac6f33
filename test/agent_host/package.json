{"name": "@byted/nuro-sdk-test-agent-host", "version": "1.0.0", "main": "index.js", "scripts": {"start": "tsc && node dist/server.js", "build": "tsc", "serve": "node dist/server.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@byted-service/logger": "^2.4.1", "@byted-service/tos": "^4.3.8", "@byted/mcp-client-sdk": "^0.0.20250428-1", "@byted/nurosdk-js": "1.1.0-alpha.48", "@byted/tsnfoundation": "0.2.0", "@bytefaas/nodejs-framework-httpnative": "^1.1.0", "@langchain/anthropic": "^0.3.18", "@langchain/core": "^0.3.43", "@langchain/deepseek": "^0.0.1", "@langchain/openai": "^0.5.3", "@modelcontextprotocol/sdk": "^1.8.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "express": "^5.1.0", "langchain": "^0.3.20", "ts-node": "^10.9.2"}, "devDependencies": {"@types/cookie-parser": "^1.4.8", "@types/mime-types": "^2.1.4", "@types/multer": "^1.4.12", "axios": "^1.8.4", "mime-types": "^3.0.1", "multer": "^1.4.5-lts.2"}}