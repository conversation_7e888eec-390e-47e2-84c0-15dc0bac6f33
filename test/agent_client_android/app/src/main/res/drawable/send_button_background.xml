<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_enabled="true">
        <shape android:shape="rectangle">
            <solid android:color="?attr/colorPrimary"/>
            <corners android:radius="18dp"/>
        </shape>
    </item>
    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <solid android:color="#CCCCCC"/> <!-- Disabled color -->
            <corners android:radius="18dp"/>
        </shape>
    </item>
</selector>