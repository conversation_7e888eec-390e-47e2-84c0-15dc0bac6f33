<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <LinearLayout
        android:id="@+id/bubbleView_assistant"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/assistant_bubble_background"
        android:orientation="vertical"
        android:paddingStart="12dp"
        android:paddingTop="10dp"
        android:paddingEnd="12dp"
        android:paddingBottom="10dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.75">

        <TextView
            android:id="@+id/messageLabel_assistant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@android:color/black"
            android:textSize="16sp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>