<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/reasoning_container_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#90EE90"
        app:cardCornerRadius="10dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <FrameLayout
                android:id="@+id/reasoning_icon_view"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/reasoning_icon_background"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/reasoning_icon_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="R"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </FrameLayout>

            <TextView
                android:id="@+id/reasoning_title_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Reasoning"
                android:textColor="#333333" 
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="@id/reasoning_icon_view"
                app:layout_constraintBottom_toBottomOf="@id/reasoning_icon_view"
                app:layout_constraintStart_toEndOf="@id/reasoning_icon_view"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/reasoning_message_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:textColor="#4D4D4D" 
                android:textSize="14sp"
                app:layout_constraintTop_toBottomOf="@id/reasoning_icon_view"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="This is a sample reasoning message content that can be quite long and wrap to multiple lines." />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>