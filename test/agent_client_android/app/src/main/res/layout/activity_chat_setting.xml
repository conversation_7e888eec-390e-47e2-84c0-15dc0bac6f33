<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/scrollView"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    tools:context=".appui.ChatSettingActivity">

    <LinearLayout
        android:id="@+id/contentView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <RadioGroup
            android:id="@+id/segmentedControl"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <RadioButton
                android:id="@+id/rb_llm"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_segment_background"
                android:button="@null"
                android:gravity="center"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="LLM"
                android:textColor="@drawable/selector_segment_text_color"
                android:checked="true" />

            <RadioButton
                android:id="@+id/rb_mcp"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_segment_background"
                android:button="@null"
                android:gravity="center"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="MCP"
                android:textColor="@drawable/selector_segment_text_color" />

            <RadioButton
                android:id="@+id/rb_host_agent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@drawable/selector_segment_background"
                android:button="@null"
                android:gravity="center"
                android:paddingTop="8dp"
                android:paddingBottom="8dp"
                android:text="Host Agent"
                android:textColor="@drawable/selector_segment_text_color" />
        </RadioGroup>

        <!-- LLM Section -->
        <LinearLayout
            android:id="@+id/llmSectionContainerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="visible">

            <TextView
                style="@style/SettingLabel"
                android:text="Model Name (x-llm-model)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/modelNameTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="e.g., gpt-4, deepseek-r1">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/SettingLabel"
                android:layout_marginTop="12dp"
                android:text="Endpoint (x-llm-endpoint)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/endpointTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="e.g., https://api.example.com/v1">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/SettingLabel"
                android:layout_marginTop="12dp"
                android:text="API Key (x-llm-apikey)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/apiKeyTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Enter your API key">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/SettingLabel"
                android:layout_marginTop="12dp"
                android:text="System Prompt (x-llm-systemprompt)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/systemPromptTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="Enter system prompt here...">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="top"
                    android:inputType="textMultiLine"
                    android:minLines="3" />
            </com.google.android.material.textfield.TextInputLayout>

            <TextView
                style="@style/SettingLabel"
                android:layout_marginTop="12dp"
                android:text="Temperature (x-llm-temperature)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/temperatureTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="e.g., 0.7 (leave empty for default)">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="numberDecimal" />
            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- MCP Section -->
        <LinearLayout
            android:id="@+id/mcpSectionContainerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                android:id="@+id/mcpServersLabel"
                style="@style/SettingLabel"
                android:text="MCP Servers (x-llm-mcpservers)" />

            <LinearLayout
                android:id="@+id/mcpServersStackView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical" />

            <Button
                android:id="@+id/addMcpServerButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="start"
                android:layout_marginTop="12dp"
                android:text="Add Server"
                android:backgroundTint="@android:color/holo_green_light" />

        </LinearLayout>

        <!-- Host Agent Section -->
        <LinearLayout
            android:id="@+id/hostAgentSectionContainerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone">

            <TextView
                style="@style/SettingLabel"
                android:text="Host Agent URL (x-hostagent-url)" />

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/hostAgentUrlTextInputLayout"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="e.g., http://localhost:8000">

                <com.google.android.material.textfield.TextInputEditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="24dp">

            <Button
                android:id="@+id/cancelButton"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="取消"
                android:layout_marginEnd="8dp"/>

            <Button
                android:id="@+id/saveButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存"
                android:layout_marginStart="8dp"/>
        </LinearLayout>

    </LinearLayout>
</ScrollView>