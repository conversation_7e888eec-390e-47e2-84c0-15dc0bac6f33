<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingTop="4dp"
    android:paddingBottom="4dp"
    android:paddingStart="8dp"
    android:paddingEnd="8dp">

    <LinearLayout
        android:id="@+id/user_bubble_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="end"
        android:background="@drawable/user_bubble_background" 
        android:orientation="vertical"
        android:paddingStart="12dp"
        android:paddingTop="10dp"
        android:paddingEnd="12dp"
        android:paddingBottom="10dp"
        android:layout_marginStart="50dp"> 

        <LinearLayout
            android:id="@+id/user_content_stack_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            
            <!-- ImageView instances will be added here programmatically -->

            <TextView
                android:id="@+id/user_message_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                tools:text="This is a sample user message."/>
        </LinearLayout>
    </LinearLayout>

</LinearLayout>