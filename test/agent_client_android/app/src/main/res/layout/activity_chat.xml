<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="?attr/colorPrimary"
        android:elevation="4dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Light">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <ProgressBar
                android:id="@+id/loadingIndicator"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:indeterminateTint="@android:color/white"
                android:visibility="gone" />
        </LinearLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/tableView"
        android:layout_below="@id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@+id/messageInputParentContainer" />

    <LinearLayout
        android:id="@+id/messageInputParentContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:paddingStart="8dp"
        android:paddingEnd="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:background="@android:color/white">

        <HorizontalScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scrollbars="none">
            <LinearLayout
                android:id="@+id/selectedImagesContainerView"
                android:layout_width="wrap_content"
                android:layout_height="50dp" 
                android:orientation="horizontal"
                android:visibility="gone"
                android:layout_marginBottom="4dp"/>
        </HorizontalScrollView>

        <LinearLayout
            android:id="@+id/messageInputContainerView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/chat_input_background" 
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="12dp"
            android:paddingEnd="8dp"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <ImageButton
                android:id="@+id/attachmentButton"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Attach file"
                android:src="@drawable/ic_attachment" 
                app:tint="@android:color/darker_gray" />

            <EditText
                android:id="@+id/messageTextField"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@android:color/transparent"
                android:hint="请输入你的想法..."
                android:inputType="textMultiLine|textCapSentences"
                android:maxLines="5"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textSize="16sp" />

            <Button
                android:id="@+id/sendButton"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:background="@drawable/send_button_background"
                android:text="发送"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="16sp"
                android:textStyle="bold"
                android:enabled="false"/>

        </LinearLayout>
    </LinearLayout>


</RelativeLayout>