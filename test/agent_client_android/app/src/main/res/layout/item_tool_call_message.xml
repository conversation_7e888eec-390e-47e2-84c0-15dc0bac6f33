<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/tool_call_container_view"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="#FFDAB9"
        app:cardCornerRadius="10dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="12dp">

            <FrameLayout
                android:id="@+id/tool_call_icon_view"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:background="@drawable/tool_call_icon_background"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <TextView
                    android:id="@+id/tool_call_icon_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:text="T"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    android:textStyle="bold" />
            </FrameLayout>

            <TextView
                android:id="@+id/tool_call_title_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:textColor="#333333" 
                android:textSize="15sp"
                android:textStyle="bold"
                app:layout_constraintTop_toTopOf="@id/tool_call_icon_view"
                app:layout_constraintBottom_toBottomOf="@id/tool_call_icon_view"
                app:layout_constraintStart_toEndOf="@id/tool_call_icon_view"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="Tool Call - GetWeather" />

            <TextView
                android:id="@+id/tool_call_params_title_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="工具调用参数"
                android:textColor="@android:color/darker_gray"
                android:textSize="13sp"
                app:layout_constraintTop_toBottomOf="@id/tool_call_icon_view"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tool_call_params_content_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="#4D4D4D" 
                android:textSize="13sp"
                android:fontFamily="monospace"
                app:layout_constraintTop_toBottomOf="@id/tool_call_params_title_label"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:text="" />

            <TextView
                android:id="@+id/tool_call_result_title_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="工具调用结果"
                android:textColor="@android:color/darker_gray"
                android:textSize="13sp"
                app:layout_constraintTop_toBottomOf="@id/tool_call_params_content_label"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <TextView
                android:id="@+id/tool_call_result_content_label"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:textColor="#1A1A1A" 
                android:textSize="14sp"
                app:layout_constraintTop_toBottomOf="@id/tool_call_result_title_label"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                tools:text="" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>