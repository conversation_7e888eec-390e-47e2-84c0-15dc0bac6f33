package com.tinyscript.tsnsample.appui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroToolCallMessage
import com.tinyscript.nurosample.R

class ToolCallMessageCell(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val containerView: View = itemView.findViewById(R.id.tool_call_container_view)
    private val iconView: View = itemView.findViewById(R.id.tool_call_icon_view)
    private val iconTextView: TextView = itemView.findViewById(R.id.tool_call_icon_text)
    private val titleLabel: TextView = itemView.findViewById(R.id.tool_call_title_label)
    private val paramsTitleLabel: TextView = itemView.findViewById(R.id.tool_call_params_title_label)
    private val paramsContentLabel: TextView = itemView.findViewById(R.id.tool_call_params_content_label)
    private val resultTitleLabel: TextView = itemView.findViewById(R.id.tool_call_result_title_label)
    private val resultContentLabel: TextView = itemView.findViewById(R.id.tool_call_result_content_label)

    fun bind(message: NuroToolCallMessage) {
        titleLabel.text = "Tool Call - ${message.toolName}"
        paramsContentLabel.text = message.toolArgs
        iconTextView.text = "T"

        if (!message.toolResult.isNullOrEmpty()) {
            resultTitleLabel.visibility = View.VISIBLE
            resultContentLabel.visibility = View.VISIBLE
            resultContentLabel.text = message.toolResult
        } else {
            resultTitleLabel.visibility = View.GONE
            resultContentLabel.visibility = View.GONE
        }
    }

    companion object {
        fun create(parent: ViewGroup): ToolCallMessageCell {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_tool_call_message, parent, false)
            return ToolCallMessageCell(view)
        }
    }
}