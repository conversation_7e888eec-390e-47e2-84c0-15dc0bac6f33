package com.tinyscript.tsnsample.appui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroReasoningMessage
import com.tinyscript.nurosample.R

class ReasoningMessageCell(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val containerView: View = itemView.findViewById(R.id.reasoning_container_view)
    private val iconView: View = itemView.findViewById(R.id.reasoning_icon_view)
    private val iconTextView: TextView = itemView.findViewById(R.id.reasoning_icon_text)
    private val titleLabel: TextView = itemView.findViewById(R.id.reasoning_title_label)
    private val messageLabel: TextView = itemView.findViewById(R.id.reasoning_message_label)

    fun bind(message: NuroReasoningMessage) {
        titleLabel.text = "Reasoning"
        messageLabel.text = message.text ?: ""
        iconTextView.text = "R"
        // Additional styling can be applied here if needed
    }

    companion object {
        fun create(parent: ViewGroup): ReasoningMessageCell {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_reasoning_message, parent, false)
            return ReasoningMessageCell(view)
        }
    }
}