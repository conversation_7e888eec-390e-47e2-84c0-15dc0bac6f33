package com.tinyscript.nurosample

import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.EditText
import androidx.activity.ComponentActivity
import com.bytedance.nurosdk.EventStreamAdapter
import com.bytedance.nurosdk.MCPToolCallResult
import com.bytedance.nurosdk.MCPToolCallTextContent
import com.bytedance.nurosdk.MCPToolDefineObjectProperty
import com.bytedance.nurosdk.MCPToolDefineStringProperty
import com.bytedance.nurosdk.NuroAssistantMessage
import com.bytedance.nurosdk.NuroConversationManager
import com.bytedance.nurosdk.NuroLogLevel
import com.bytedance.nurosdk.NuroLogger
import com.bytedance.nurosdk.NuroLoggerAdapter
import com.bytedance.nurosdk.NuroMCPManager
import com.bytedance.nurosdk.NuroReasoningMessage
import com.bytedance.nurosdk.NuroSetting
import com.bytedance.nurosdk.NuroToolCallMessage
import com.bytedance.nurosdk.NuroUserMessage
import com.bytedance.nurosdk.NuroUtils
import com.bytedance.nurosdk.SSETransport
import com.bytedance.nurosdk.TOSFileUploadAdapter
import com.bytedance.nurosdk.mcp.MCPLiteServer
import com.tinyscript.tsnsample.test_jsonrepair
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.BufferedSource
import java.io.IOException

class MainActivity : ComponentActivity() {

    companion object {
        val TAG = "DEMO"
        val TOKEN:String = "demo_token"
    }

    var edit: EditText? = null
    var send: Button? = null
    var interrupt: Button? = null

    val conversationManager = NuroConversationManager()

    var call: Call? = null

    fun installES() {
        EventStreamAdapter.fetch = { c ->
            val client = OkHttpClient()

            val request = Request.Builder()
                .url(c.endpoint)
                .method(
                    c.method,
                    RequestBody.create(MediaType.parse("application/json"), c.data ?: "")
                )
                .headers(Headers.of(c.headers ?: mutableMapOf()))
                .build()

            call = client.newCall(request)

            call?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.i(TAG, "请求失败: ${e.message}")
                }

                override fun onResponse(call: Call, response: Response) {
                    if (!response.isSuccessful) {
                        Log.i(TAG, "响应失败: ${response.code()}")
                        return
                    }

                    val source: BufferedSource = response.body()?.source() ?: run {
                        c.onError?.invoke(-1, "unknown error")
                        return
                    }

                    try {
                        val buffer = ByteArray(1024)
                        while (!source.exhausted()) {
                            val bytesRead = source.read(buffer)
                            if (bytesRead == -1) break
                            val chunk = String(buffer, 0, bytesRead, Charsets.UTF_8)
                            c.onChunk?.invoke(chunk)
                        }
                        c.onFinish?.invoke()
                    } catch (e: Exception) {
                        c.onError?.invoke(-1, "unknown error")
                    }
                }
            })

            TOKEN
        }

        EventStreamAdapter.cancel = { cancelToken ->
            Log.i(TAG, "cancel cancelToken:" + cancelToken)
            // 取消请求
            call?.cancel()
            call = null
        }

        TOSFileUploadAdapter.upload = { c ->
            "" // cancel token
        }

        TOSFileUploadAdapter.cancel = { cancelToken ->
            // 取消请求
        }
    }

    fun installLogger() {
        NuroLoggerAdapter.info = { tag, message ->
            Log.i(tag, message)
        }
        NuroLoggerAdapter.error = { tag, message ->
            Log.e(tag, message)
        }
        NuroLoggerAdapter.debug = { tag, message ->
            Log.d(tag, message)
        }
        NuroLogger.setLogLevel(NuroLogLevel.ERROR)
    }

    private fun createTheMCPServer(): MCPLiteServer {
        val server = MCPLiteServer("user", "1.0.0")
        server.tool(
            "get_location",
            "get user current location",
                MCPToolDefineObjectProperty()
                    .defProperty(
                        "reason",
                        MCPToolDefineStringProperty().defDescription("为什么你需要获取用户的地理位置")
                    )
                    .defRequired(mutableListOf("reason")),
            toolCallHandler = { params, resultCallback ->
                val result = MCPToolCallResult()
                result.content = mutableListOf(
                    MCPToolCallTextContent.create("用户当前位置是佛山")
                )
                resultCallback(result)
            }
        )
        return server
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        NuroSetting.needDisplayServerFunctionMessage = true
        test_jsonrepair()
        setContentView(R.layout.main_activity_layout)
        edit = findViewById(R.id.sent_msg_edit)
        send = findViewById(R.id.sent_msg_btn)
        interrupt = findViewById(R.id.interrupt_btn)
        send?.setOnClickListener {
            val userMessage =
                NuroUserMessage(NuroUtils.randomUUIDString(), edit?.text.toString() ?: "")
//        val userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), "用户今天和昨天在哪里")
            conversationManager.sendUserMessage(userMessage)
        }

        interrupt?.setOnClickListener {
            conversationManager.interruptResponse()
        }


        installES()
        installLogger()
        val transport = SSETransport("https://7c026eti.cn-boe2-fn.bytedance.net/sse", mutableMapOf())
        conversationManager.connect(transport)

        conversationManager.mcpManager = NuroMCPManager()
        conversationManager.mcpManager?.registerServer(
            createTheMCPServer().asNuroMCPServerConfig()
        )
        conversationManager.enableMCPTools()
        conversationManager.conversation.addStateUpdateListener {status->
            Log.i(TAG, "Conversation state updated:" + status)
        }

        conversationManager.conversation.addSystemDataListener {
            Log.i(TAG, "Conversation system updated:" + it.toJSONString())
        }

        conversationManager.conversation.addMessageUpdateListener { it, op ->
            Log.i(
                TAG,
                "message-->class:${it} :id: ${it.id},type: ${it.type},updated: ${it.updated}, op: ${op.value}"
            )

            if (it is NuroUserMessage) {
                Log.i(TAG, "User message:text: ${it.text}")
            }
            if (it is NuroAssistantMessage) {
                Log.i(TAG, "Assistant message: test:${it.text}")
            }
            if (it is NuroReasoningMessage) {
                Log.i(TAG, "Reasoning message:text: ${it.text}")
            }
            if (it is NuroToolCallMessage) {
                Log.i(
                    TAG,
                    "Tool call message: toolCallId: ${it.toolCallId},toolType: ${it.toolType} ,name: ${it.toolName}, args: ${it.toolArgs}, result: ${it.toolResult}"
                )
            }
        }


    }
}