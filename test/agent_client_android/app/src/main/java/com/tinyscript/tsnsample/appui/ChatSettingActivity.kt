package com.tinyscript.tsnsample.appui

import android.content.Context
import android.content.SharedPreferences
import android.graphics.Color
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.text.InputType
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.widget.Button
import android.widget.EditText
import android.widget.LinearLayout
import android.widget.RadioGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.google.android.material.textfield.TextInputLayout
import com.tinyscript.nurosample.R
import org.json.JSONArray
import org.json.JSONObject

class ChatSettingActivity : AppCompatActivity() {

    private lateinit var scrollView: View
    private lateinit var contentView: LinearLayout

    private lateinit var segmentedControl: RadioGroup

    // LLM Section UI Elements
    private lateinit var llmSectionContainerView: LinearLayout
    private lateinit var modelNameEditText: EditText
    private lateinit var endpointEditText: EditText
    private lateinit var apiKeyEditText: EditText
    private lateinit var systemPromptEditText: EditText
    private lateinit var temperatureEditText: EditText

    // MCP Section UI Elements
    private lateinit var mcpSectionContainerView: LinearLayout
    private lateinit var mcpServersLabel: TextView
    private lateinit var mcpServersStackView: LinearLayout // Equivalent to UIStackView
    private lateinit var addMcpServerButton: Button

    // Host Agent Section UI Elements
    private lateinit var hostAgentSectionContainerView: LinearLayout
    private lateinit var hostAgentUrlEditText: EditText

    private lateinit var cancelButton: Button
    private lateinit var saveButton: Button

    private lateinit var sharedPreferences: SharedPreferences

    companion object {
        const val PREFS_NAME = "ChatSettings"
        const val KEY_SELECTED_TAB = "selectedTab"
        // LLM Keys
        const val KEY_MODEL_NAME = "x-llm-model"
        const val KEY_ENDPOINT = "x-llm-endpoint"
        const val KEY_API_KEY = "x-llm-apikey"
        const val KEY_SYSTEM_PROMPT = "x-llm-systemprompt"
        const val KEY_TEMPERATURE = "x-llm-temperature"
        // MCP Keys
        const val KEY_MCP_SERVERS_JSON = "x-llm-mcpservers"
        // Host Agent Keys
        const val KEY_HOST_AGENT_URL = "x-hostagent-url"

        var onSettingSaved: (() -> Unit)? = null
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_chat_setting) // XML layout file

        supportActionBar?.title = "设置"
        supportActionBar?.setDisplayHomeAsUpEnabled(true)

        sharedPreferences = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        initializeViews()
        setupSegmentedControlListener()
        setupButtonListeners()
        loadSettings()
        updateSectionVisibility(segmentedControl.checkedRadioButtonId)
    }

    private fun initializeViews() {
        scrollView = findViewById(R.id.scrollView)
        contentView = findViewById(R.id.contentView)
        segmentedControl = findViewById(R.id.segmentedControl)

        // LLM Section
        llmSectionContainerView = findViewById(R.id.llmSectionContainerView)
        modelNameEditText = findViewById<TextInputLayout>(R.id.modelNameTextInputLayout).editText!!
        endpointEditText = findViewById<TextInputLayout>(R.id.endpointTextInputLayout).editText!!
        apiKeyEditText = findViewById<TextInputLayout>(R.id.apiKeyTextInputLayout).editText!!
        systemPromptEditText = findViewById<TextInputLayout>(R.id.systemPromptTextInputLayout).editText!!
        temperatureEditText = findViewById<TextInputLayout>(R.id.temperatureTextInputLayout).editText!!

        // Set single line and editor action listener for relevant EditTexts
        setEditTextSingleLineAndHideKeyboardOnEnter(modelNameEditText)
        setEditTextSingleLineAndHideKeyboardOnEnter(endpointEditText)
        setEditTextSingleLineAndHideKeyboardOnEnter(apiKeyEditText)
        setEditTextSingleLineAndHideKeyboardOnEnter(temperatureEditText)

        // MCP Section
        mcpSectionContainerView = findViewById(R.id.mcpSectionContainerView)
        mcpServersLabel = findViewById(R.id.mcpServersLabel)
        mcpServersStackView = findViewById(R.id.mcpServersStackView)
        addMcpServerButton = findViewById(R.id.addMcpServerButton)

        // Host Agent Section
        hostAgentSectionContainerView = findViewById(R.id.hostAgentSectionContainerView)
        hostAgentUrlEditText = findViewById<TextInputLayout>(R.id.hostAgentUrlTextInputLayout).editText!!
        setEditTextSingleLineAndHideKeyboardOnEnter(hostAgentUrlEditText)

        cancelButton = findViewById(R.id.cancelButton)
        saveButton = findViewById(R.id.saveButton)

        // Placeholder behavior for systemPromptEditText
        setupTextViewPlaceholder(systemPromptEditText, "Enter system prompt here...")
    }

    private fun setupSegmentedControlListener() {
        segmentedControl.setOnCheckedChangeListener { _, checkedId ->
            updateSectionVisibility(checkedId)
        }
    }

    private fun updateSectionVisibility(checkedId: Int) {
        llmSectionContainerView.visibility = if (checkedId == R.id.rb_llm) View.VISIBLE else View.GONE
        mcpSectionContainerView.visibility = if (checkedId == R.id.rb_mcp) View.VISIBLE else View.GONE
        hostAgentSectionContainerView.visibility = if (checkedId == R.id.rb_host_agent) View.VISIBLE else View.GONE
    }

    private fun setupButtonListeners() {
        addMcpServerButton.setOnClickListener {
            addMcpServerRow(null, null)
        }

        cancelButton.setOnClickListener {
            finish() // Close the activity
        }

        saveButton.setOnClickListener {
            saveSettings()
            onSettingSaved?.invoke()
            finish() // Close the activity
        }
    }

    private fun addMcpServerRow(name: String?, url: String?) {
        val inflater = LayoutInflater.from(this)
        val rowView = inflater.inflate(R.layout.item_mcp_server_row, mcpServersStackView, false) as LinearLayout

        val nameEditText = rowView.findViewById<TextInputLayout>(R.id.mcpServerNameTextInputLayout).editText!!
        val urlEditText = rowView.findViewById<TextInputLayout>(R.id.mcpServerUrlTextInputLayout).editText!!

        setEditTextSingleLineAndHideKeyboardOnEnter(nameEditText)
        setEditTextSingleLineAndHideKeyboardOnEnter(urlEditText)
        val deleteButton = rowView.findViewById<Button>(R.id.deleteMcpServerButton)

        nameEditText.setText(name)
        urlEditText.setText(url)

        deleteButton.setOnClickListener {
            mcpServersStackView.removeView(rowView)
        }
        mcpServersStackView.addView(rowView)
    }

    private fun loadSettings() {
        val selectedTabId = sharedPreferences.getInt(KEY_SELECTED_TAB, R.id.rb_llm)
        segmentedControl.check(selectedTabId)
        updateSectionVisibility(selectedTabId)

        // LLM Settings
        modelNameEditText.setText(sharedPreferences.getString(KEY_MODEL_NAME, ""))
        endpointEditText.setText(sharedPreferences.getString(KEY_ENDPOINT, ""))
        apiKeyEditText.setText(sharedPreferences.getString(KEY_API_KEY, ""))
        systemPromptEditText.setText(sharedPreferences.getString(KEY_SYSTEM_PROMPT, ""))
        if (systemPromptEditText.text.isEmpty()) {
            systemPromptEditText.setText("Enter system prompt here...")
            systemPromptEditText.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray))
        } else {
            systemPromptEditText.setTextColor(ContextCompat.getColor(this, android.R.color.black))
        }
        temperatureEditText.setText(sharedPreferences.getString(KEY_TEMPERATURE, ""))

        // MCP Settings
        mcpServersStackView.removeAllViews() // Clear existing rows before loading
        val mcpServersJsonString = sharedPreferences.getString(KEY_MCP_SERVERS_JSON, null)
        if (mcpServersJsonString != null) {
            try {
                val jsonArray = JSONArray(mcpServersJsonString)
                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)
                    val name = jsonObject.optString("name")
                    val url = jsonObject.optString("url")
                    addMcpServerRow(name, url)
                }
            } catch (e: Exception) {
                // Handle JSON parsing error, e.g., log it or clear the invalid setting
                e.printStackTrace()
            }
        }

        // Host Agent Settings
        hostAgentUrlEditText.setText(sharedPreferences.getString(KEY_HOST_AGENT_URL, ""))
    }

    private fun saveSettings() {
        val editor = sharedPreferences.edit()

        editor.putInt(KEY_SELECTED_TAB, segmentedControl.checkedRadioButtonId)

        // LLM Settings
        editor.putString(KEY_MODEL_NAME, modelNameEditText.text.toString())
        editor.putString(KEY_ENDPOINT, endpointEditText.text.toString())
        editor.putString(KEY_API_KEY, apiKeyEditText.text.toString())
        if (systemPromptEditText.text.toString() == "Enter system prompt here...") {
            editor.putString(KEY_SYSTEM_PROMPT, "")
        } else {
            editor.putString(KEY_SYSTEM_PROMPT, systemPromptEditText.text.toString())
        }
        editor.putString(KEY_TEMPERATURE, temperatureEditText.text.toString())

        // MCP Settings
        val mcpServersArray = JSONArray()
        for (i in 0 until mcpServersStackView.childCount) {
            val rowView = mcpServersStackView.getChildAt(i) as LinearLayout
            val nameEditText = rowView.findViewById<TextInputLayout>(R.id.mcpServerNameTextInputLayout).editText!!
            val urlEditText = rowView.findViewById<TextInputLayout>(R.id.mcpServerUrlTextInputLayout).editText!!
            val serverObject = JSONObject()
            serverObject.put("name", nameEditText.text.toString())
            serverObject.put("url", urlEditText.text.toString())
            mcpServersArray.put(serverObject)
        }
        editor.putString(KEY_MCP_SERVERS_JSON, mcpServersArray.toString())

        // Host Agent Settings
        editor.putString(KEY_HOST_AGENT_URL, hostAgentUrlEditText.text.toString())

        editor.apply()
    }

    // UITextViewDelegate equivalent for placeholder
    private fun setupTextViewPlaceholder(editText: EditText, placeholder: String) {
        if (editText.text.isEmpty()) {
            editText.setText(placeholder)
            editText.setTextColor(ContextCompat.getColor(this, android.R.color.darker_gray)) // Placeholder color
        }

        editText.setOnFocusChangeListener { _, hasFocus ->
            if (hasFocus && editText.text.toString() == placeholder) {
                editText.setText("")
                editText.setTextColor(ContextCompat.getColor(this, android.R.color.black)) // Normal text color
            }
        }

        editText.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                if (!editText.hasFocus() && s.isNullOrEmpty()) {
                    editText.setText(placeholder)
                    editText.setTextColor(Color.BLACK)
                }
            }
        })
    }

    private fun setEditTextSingleLineAndHideKeyboardOnEnter(editText: EditText) {
        editText.maxLines = 1
        editText.inputType = InputType.TYPE_CLASS_TEXT // Ensure it's a regular text input, not multiline
        editText.setOnEditorActionListener { v, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_DONE || actionId == EditorInfo.IME_ACTION_NEXT) {
                val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(v.windowToken, 0)
                v.clearFocus()
                true
            } else {
                false
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == android.R.id.home) {
            finish()
            return true
        }
        return super.onOptionsItemSelected(item)
    }
}