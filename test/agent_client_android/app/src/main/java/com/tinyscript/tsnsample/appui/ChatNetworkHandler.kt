package com.tinyscript.tsnsample.appui

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Base64
import android.util.Log
import com.bytedance.nurosdk.EventStreamAdapter
import com.bytedance.nurosdk.NuroUtils
import com.bytedance.nurosdk.TOSFileUploadAdapter
import com.tinyscript.nurosample.MainActivity.Companion.TAG
import com.tinyscript.nurosample.MainActivity.Companion.TOKEN
import okhttp3.Call
import okhttp3.Callback
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okio.BufferedSource
import org.json.JSONObject
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.util.concurrent.ConcurrentHashMap

class ChatNetworkHandler(private val context: Context) {

    private var sseCall: Call? = null
    private val uploaderEndpoint = "https://7c026eti.cn-boe2-fn.bytedance.net" // Example: Replace with your actual endpoint
    private val activeUploadTasks: ConcurrentHashMap<String, Call> = ConcurrentHashMap()
    private val httpClient = OkHttpClient()

    fun setupEventStream() {
        EventStreamAdapter.fetch = { sseConfig ->
            val request = Request.Builder()
                .url(sseConfig.endpoint)
                .method(
                    sseConfig.method,
                    RequestBody.create(MediaType.parse("application/json"), sseConfig.data ?: "")
                )
                .headers(Headers.of(sseConfig.headers ?: mutableMapOf()))
                .build()

            sseCall = httpClient.newCall(request)

            sseCall?.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    Log.i(TAG, "SSE请求失败: ${e.message}")
                    // Consider invoking a callback to notify the activity/viewModel
                }

                override fun onResponse(call: Call, response: Response) {
                    if (!response.isSuccessful) {
                        Log.i(TAG, "SSE响应失败: ${response.code()}")
                        // Consider invoking a callback
                        return
                    }

                    val source: BufferedSource = response.body()?.source() ?: run {
                        sseConfig.onError?.invoke(-1, "unknown error")
                        return
                    }

                    try {
                        val buffer = ByteArray(1024)
                        while (!source.exhausted()) {
                            val bytesRead = source.read(buffer)
                            if (bytesRead == -1) break
                            val chunk = String(buffer, 0, bytesRead, Charsets.UTF_8)
                            sseConfig.onChunk?.invoke(chunk)
                        }
                        sseConfig.onFinish?.invoke()
                    } catch (e: Exception) {
                        sseConfig.onError?.invoke(-1, "unknown error")
                    }
                }
            })
            TOKEN // This seems to be a placeholder or specific token, ensure its context is correct
        }

        EventStreamAdapter.cancel = { cancelToken ->
            Log.i(TAG, "SSE cancel cancelToken:" + cancelToken)
            sseCall?.cancel()
            sseCall = null
        }
    }

    fun setupTosFileUpload() {
        TOSFileUploadAdapter.upload = upload@{ tosConfig ->
            val localPath = tosConfig.localFile.localPath
            val fileUri = tosConfig.localFile.localFileObject as? Uri

            if (fileUri == null) {
                tosConfig.onError?.invoke(-1, "upload failed: localFileObject is not Uri")
                return@upload "invalid_data_token"
            }

            var bitmap: Bitmap?
            try {
                context.contentResolver.openInputStream(fileUri).use { inputStream ->
                    if (inputStream == null) {
                        tosConfig.onError?.invoke(-1, "upload failed: could not open input stream")
                        return@upload "stream_error_token"
                    }
                    bitmap = BitmapFactory.decodeStream(inputStream)
                }
            } catch (e: IOException) {
                Log.e(TAG, "Error decoding bitmap", e)
                tosConfig.onError?.invoke(-1, "upload failed: error decoding bitmap")
                return@upload "bitmap_decode_error_token"
            }

            if (bitmap == null) {
                tosConfig.onError?.invoke(-1, "upload failed: bitmap is null")
                return@upload "bitmap_null_token"
            }

            val byteArrayOutputStream = ByteArrayOutputStream()
            bitmap!!.compress(Bitmap.CompressFormat.JPEG, 80, byteArrayOutputStream)
            val imageBytes = byteArrayOutputStream.toByteArray()
            val base64String = Base64.encodeToString(imageBytes, Base64.NO_WRAP)

            val uploadUrl = "$uploaderEndpoint/upload"

            val jsonBody = JSONObject()
            jsonBody.put("filename", "$localPath.jpg") // Consider more robust naming
            jsonBody.put("filedata", base64String)

            val request = Request.Builder()
                .url(uploadUrl)
                .post(RequestBody.create(MediaType.parse("application/json"), jsonBody.toString()))
                .build()

            val callToken = NuroUtils.randomUUIDString()
            val uploadCall = httpClient.newCall(request)
            activeUploadTasks[callToken] = uploadCall

            uploadCall.enqueue(object : Callback {
                override fun onFailure(call: Call, e: IOException) {
                    activeUploadTasks.remove(callToken)
                    // Ensure UI updates are on the main thread if calling back to Activity
                    if (e.message?.contains("Canceled", ignoreCase = true) == true || e is java.net.SocketException && e.message?.contains("Socket closed", ignoreCase = true) == true) {
                        Log.i(TAG, "Upload task $callToken was cancelled.")
                        tosConfig.onError?.invoke(-1, "upload cancelled")
                    } else {
                        Log.e(TAG, "Upload failed for $localPath", e)
                        tosConfig.onError?.invoke(-1, "upload failed: ${e.message}")
                    }
                }

                override fun onResponse(call: Call, response: Response) {
                    activeUploadTasks.remove(callToken)
                    if (response.isSuccessful) {
                        val responseBody = response.body()?.string()
                        if (responseBody != null) {
                            tosConfig.nuroFile.url = uploaderEndpoint + responseBody
                            tosConfig.onFinish?.invoke()
                        } else {
                            tosConfig.onError?.invoke(-1, "upload failed: could not parse response data")
                        }
                    } else {
                        var errorMessage = "upload failed with status code: ${response.code()}"
                        val errorBody = response.body()?.string()
                        if (errorBody != null) {
                            errorMessage += " - $errorBody"
                        }
                        tosConfig.onError?.invoke(-1, errorMessage)
                    }
                    response.body()?.close()
                }
            })
            callToken
        }

        TOSFileUploadAdapter.cancel = { cancelToken ->
            val taskToCancel = activeUploadTasks[cancelToken]
            if (taskToCancel != null) {
                taskToCancel.cancel()
                activeUploadTasks.remove(cancelToken)
                Log.i(TAG, "Upload task $cancelToken cancellation requested.")
            } else {
                Log.w(TAG, "No active upload task found for token: $cancelToken")
            }
        }
    }

    fun cancelAllTasks() {
        sseCall?.cancel()
        activeUploadTasks.values.forEach { it.cancel() }
        activeUploadTasks.clear()
    }
}