package com.tinyscript.tsnsample.appui

import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroAssistantMessage
import com.bytedance.nurosdk.NuroMessage
import com.bytedance.nurosdk.NuroReasoningMessage
import com.bytedance.nurosdk.NuroToolCallMessage
import com.bytedance.nurosdk.NuroUserMessage

class ChatAdapter(private val messages: List<NuroMessage>) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    companion object {
        private const val VIEW_TYPE_USER = 1
        private const val VIEW_TYPE_ASSISTANT = 2
        private const val VIEW_TYPE_TOOL_CALL = 3
        private const val VIEW_TYPE_REASONING = 4
        private const val VIEW_TYPE_UNKNOWN = 0 // Fallback for unknown types
    }

    override fun getItemViewType(position: Int): Int {
        return when (messages[position].type) {
            "user" -> VIEW_TYPE_USER
            "assistant" -> VIEW_TYPE_ASSISTANT
            "tool_call" -> VIEW_TYPE_TOOL_CALL
            "reasoning" -> VIEW_TYPE_REASONING
            else -> VIEW_TYPE_UNKNOWN
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_USER -> UserMessageCell.create(parent)
            VIEW_TYPE_ASSISTANT -> AssistantMessageCell.create(parent)
            VIEW_TYPE_TOOL_CALL -> ToolCallMessageCell.create(parent)
            VIEW_TYPE_REASONING -> ReasoningMessageCell.create(parent)
            else -> UnknownMessageCell.create(parent) // Handle unknown type
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val message = messages[position]
        when (holder) {
            is UserMessageCell -> holder.bind(message as NuroUserMessage)
            is AssistantMessageCell -> holder.bind(message as NuroAssistantMessage)
            is ToolCallMessageCell -> holder.bind(message as NuroToolCallMessage)
            is ReasoningMessageCell -> holder.bind(message as NuroReasoningMessage)
            is UnknownMessageCell -> holder.bind(message) // Bind unknown message type
        }
    }

    override fun getItemCount(): Int = messages.size
}