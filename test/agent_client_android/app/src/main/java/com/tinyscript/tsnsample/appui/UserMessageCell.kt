package com.tinyscript.tsnsample.appui

import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.bytedance.nurosdk.NuroUserMessage
import com.tinyscript.nurosample.R

class UserMessageCell(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val bubbleView: LinearLayout = itemView.findViewById(R.id.user_bubble_view)
    private val contentStackView: LinearLayout = itemView.findViewById(R.id.user_content_stack_view)
    private val messageLabel: TextView = itemView.findViewById(R.id.user_message_label)

    fun bind(message: NuroUserMessage) {
        // Clear previously added image views
        val viewsToRemove = mutableListOf<View>()
        for (i in 0 until contentStackView.childCount) {
            val child = contentStackView.getChildAt(i)
            if (child is ImageView) {
                viewsToRemove.add(child)
            }
        }
        viewsToRemove.forEach { contentStackView.removeView(it) }

        messageLabel.text = message.text
        messageLabel.isVisible = !message.text.isNullOrEmpty()

        val imageFiles = message.files?.filter { it.type.value == "image" } ?: emptyList()

        if (imageFiles.isNotEmpty()) {
            imageFiles.forEachIndexed { index, file ->
                val imageView = ImageView(itemView.context).apply {
                    layoutParams = LinearLayout.LayoutParams(
                        LinearLayout.LayoutParams.WRAP_CONTENT,
                        200.dpToPx() // Max height for each image
                    ).apply {
                        // Add spacing if there's text or multiple images
                        if (index > 0 || !messageLabel.text.isNullOrEmpty()) {
                            topMargin = 8.dpToPx()
                        }
                    }
                    scaleType = ImageView.ScaleType.FIT_CENTER
                    adjustViewBounds = true
                    isVisible = false // Initially hidden until image loads
                }
                contentStackView.addView(imageView, index) // Add before messageLabel if it's managed differently or ensure order

                file.localFile?.localFileObject?.let {
                    when (it) {
                        is android.graphics.Bitmap -> {
                            imageView.setImageBitmap(it)
                            imageView.isVisible = true
                        }
                        is android.net.Uri -> {
                            imageView.setImageURI(it)
                            imageView.isVisible = true
                        }
                    }
                } ?: file.url?.let {
                    Glide.with(itemView.context)
                        .load(it)
                        .into(object : com.bumptech.glide.request.target.CustomViewTarget<ImageView, android.graphics.drawable.Drawable>(imageView) {
                            override fun onLoadFailed(errorDrawable: android.graphics.drawable.Drawable?) {
                                // Handle error, e.g., show placeholder or hide
                                imageView.isVisible = false
                            }
                            override fun onResourceReady(resource: android.graphics.drawable.Drawable, transition: com.bumptech.glide.request.transition.Transition<in android.graphics.drawable.Drawable>?) {
                                imageView.setImageDrawable(resource)
                                imageView.isVisible = true
                            }
                            override fun onResourceCleared(placeholder: android.graphics.drawable.Drawable?) {
                                // Called when the resource is cleared
                            }
                        })
                }
            }
        }

        val hasVisibleContent = messageLabel.isVisible || imageFiles.isNotEmpty()
        bubbleView.isVisible = hasVisibleContent

        // Styling for user message bubble
        val bubbleBg = GradientDrawable()
        bubbleBg.setColor(Color.parseColor("#007AFF")) // System Blue
        bubbleBg.cornerRadii = floatArrayOf(
            18f.dpToPx(), 18f.dpToPx(), // Top-left
            18f.dpToPx(), 18f.dpToPx(), // Top-right
            0f, 0f,                   // Bottom-right (pointy for user)
            18f.dpToPx(), 18f.dpToPx()  // Bottom-left
        )
        bubbleView.background = bubbleBg
        messageLabel.setTextColor(Color.WHITE)

        // Align to right
        val params = bubbleView.layoutParams as LinearLayout.LayoutParams
        params.gravity = Gravity.END
        bubbleView.layoutParams = params
    }

    // Helper to convert dp to pixels
    private fun Int.dpToPx(): Int = (this * itemView.context.resources.displayMetrics.density).toInt()
    private fun Float.dpToPx(): Float = (this * itemView.context.resources.displayMetrics.density)

    companion object {
        fun create(parent: ViewGroup): UserMessageCell {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_user_message, parent, false)
            return UserMessageCell(view)
        }
    }
}

// Extension function for String to check if it's an image type URL
fun String.isImageType(): Boolean {
    val imageExtensions = listOf("jpg", "jpeg", "png", "gif", "bmp", "tiff", "webp")
    return try {
        val uri = android.net.Uri.parse(this)
        val pathExtension = uri.lastPathSegment?.substringAfterLast('.', "")?.lowercase()
        imageExtensions.contains(pathExtension)
    } catch (e: Exception) {
        false
    }
}