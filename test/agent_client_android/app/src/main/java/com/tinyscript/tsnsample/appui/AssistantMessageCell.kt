package com.tinyscript.tsnsample.appui

import android.graphics.Color
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroAssistantMessage
import com.tinyscript.nurosample.R // 确保你的包名正确，并且 R 文件已生成

class AssistantMessageCell(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val bubbleView: View = itemView.findViewById(R.id.bubbleView_assistant)
    private val messageLabel: TextView = itemView.findViewById(R.id.messageLabel_assistant)

    fun bind(message: NuroAssistantMessage) {
        messageLabel.text = message.text ?: ""
        // 背景和圆角由 R.drawable.assistant_bubble_background 处理
        // 文字颜色在 XML 中设置

        // 对齐方式由 item_assistant_message.xml 中的约束处理
        // bubbleView 被约束到父布局的起始位置，最大宽度为父布局的75%
    }

    companion object {
        const val IDENTIFIER = "AssistantMessageCell"

        fun create(parent: ViewGroup): AssistantMessageCell {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_assistant_message, parent, false) // 假设布局文件名为 item_assistant_message.xml
            return AssistantMessageCell(view)
        }
    }
}