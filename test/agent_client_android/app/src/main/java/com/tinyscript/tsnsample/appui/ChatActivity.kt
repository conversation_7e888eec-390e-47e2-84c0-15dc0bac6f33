package com.tinyscript.tsnsample.appui

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Menu
import android.view.MenuItem
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.Toolbar
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroConversationManager
import com.bytedance.nurosdk.NuroConversationState
import com.bytedance.nurosdk.NuroFile
import com.bytedance.nurosdk.NuroFileType
import com.bytedance.nurosdk.NuroLocalFile
import com.bytedance.nurosdk.NuroMessage
import com.bytedance.nurosdk.NuroUserMessage
import com.bytedance.nurosdk.NuroUtils
import com.bytedance.nurosdk.SSETransport
import com.tinyscript.nurosample.R

class ChatActivity : AppCompatActivity() {

    var chatNetworkHandler: ChatNetworkHandler? = null

    private var conversationManager: NuroConversationManager? = null
    private var conversationId: String? = null

    private lateinit var tableView: RecyclerView
    private lateinit var messageInputContainerView: LinearLayout
    private lateinit var attachmentButton: ImageButton
    private lateinit var messageTextField: EditText
    private lateinit var sendButton: Button
    private lateinit var selectedImagesContainerView: LinearLayout
    private lateinit var loadingIndicator: ProgressBar

    private lateinit var chatAdapter: ChatAdapter
    private val messages: MutableList<NuroMessage> = mutableListOf()
    private val selectedImageUris: MutableList<Uri> = mutableListOf()

    private val imagePickerLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {
        result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                addSelectedImage(uri)
            }
            // For multi-select (not directly supported by ACTION_GET_CONTENT as easily as single select)
            // If using ACTION_PICK with MediaStore.EXTRA_PICK_IMAGES, handle clipData here
            result.data?.clipData?.let {
                clipData ->
                for (i in 0 until clipData.itemCount) {
                    addSelectedImage(clipData.getItemAt(i).uri)
                }
            }
        }
    }

    private val requestPermissionLauncher = registerForActivityResult(ActivityResultContracts.RequestPermission()) {
        isGranted: Boolean ->
        if (isGranted) {
            openImagePicker()
        } else {
            Toast.makeText(this, "Permission denied to read external storage", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        chatNetworkHandler = ChatNetworkHandler(this)
        chatNetworkHandler?.setupEventStream()
        chatNetworkHandler?.setupTosFileUpload()

        setContentView(R.layout.activity_chat)

        val toolbar: Toolbar = findViewById(R.id.toolbar)
        setSupportActionBar(toolbar)

        supportActionBar?.setDisplayHomeAsUpEnabled(false) // 通常不需要返回按钮，因为有新建对话按钮

        conversationId = intent.getStringExtra("conversationId")

        initializeViews()
        setupRecyclerView()
        setupListeners()

        installConversationManager()

        if (conversationId != null) {
            // Load conversation data if needed, though manager handles this
        }

        updateSendButtonState()

        // Handle keyboard visibility changes (simplified, for more complex scenarios consider WindowInsets)
        tableView.setOnTouchListener { v, _ ->
            hideKeyboard(v)
            false
        }
    }

    private fun initializeViews() {
        tableView = findViewById(R.id.tableView)
        messageInputContainerView = findViewById(R.id.messageInputContainerView)
        attachmentButton = findViewById(R.id.attachmentButton)
        messageTextField = findViewById(R.id.messageTextField)
        sendButton = findViewById(R.id.sendButton)
        selectedImagesContainerView = findViewById(R.id.selectedImagesContainerView)
        loadingIndicator = findViewById(R.id.loadingIndicator)
    }

    private fun setupRecyclerView() {
        chatAdapter = ChatAdapter(messages)
        tableView.adapter = chatAdapter
        tableView.layoutManager = LinearLayoutManager(this).apply {
            stackFromEnd = true
        }
        tableView.itemAnimator = null // Disable default animations if not needed
    }

    private fun setupListeners() {
        attachmentButton.setOnClickListener {
            checkPermissionAndOpenImagePicker()
        }

        sendButton.setOnClickListener {
            sendButtonTapped()
        }

        messageTextField.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                updateSendButtonState()
            }
        })

        ChatSettingActivity.onSettingSaved = {
            val settings = loadSettings()
            val hostAgentEndpoint = settings["x-hostagent-endpoint"] ?: ""
            val transport = SSETransport(if (hostAgentEndpoint.isEmpty()) "https://7c026eti.cn-boe2-fn.bytedance.net/sse" else hostAgentEndpoint, settings.toMutableMap())
            conversationManager?.connect(transport)
        }
    }

    private fun installConversationManager() {
        com.bytedance.nurosdk.NuroSetting.needDisplayServerFunctionMessage = true

        val settings = loadSettings()
        val hostAgentEndpoint = settings["x-hostagent-endpoint"] ?: ""
        val transport = SSETransport(if (hostAgentEndpoint.isEmpty()) "https://7c026eti.cn-boe2-fn.bytedance.net/sse" else hostAgentEndpoint, settings.toMutableMap())
        val newConversationManager = NuroConversationManager()
        newConversationManager.connect(transport)

        newConversationManager.conversation.addStateUpdateListener { state ->
            Log.d("ChatActivity", "Conversation state updated: $state")
            runOnUiThread {
                updateUIForConversationState(state)
            }
        }

        newConversationManager.conversation.addMessageUpdateListener { message, op ->
            Log.d("ChatActivity", "Message update: $op, Message: ${message.id}")
            // NuroSDK's internal list is the source of truth
            messages.clear()
            messages.addAll(newConversationManager.conversation.messages)
            runOnUiThread {
                chatAdapter.notifyDataSetChanged()
                if (messages.isNotEmpty()) {
                    scrollToBottom(true)
                }
            }
        }
        this.conversationManager = newConversationManager
        // If there's an existing conversationId, try to resume it (NuroSDK might handle this internally or need explicit call)
        // For a new session, NuroSDK usually starts a new conversation automatically.
    }

    private fun loadSettings(): Map<String, String> {
        val sharedPreferences = getSharedPreferences(ChatSettingActivity.PREFS_NAME, Context.MODE_PRIVATE)
        val settingsMap = mutableMapOf<String, String>()

        settingsMap[ChatSettingActivity.KEY_MODEL_NAME] = sharedPreferences.getString(ChatSettingActivity.KEY_MODEL_NAME, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_ENDPOINT] = sharedPreferences.getString(ChatSettingActivity.KEY_ENDPOINT, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_API_KEY] = sharedPreferences.getString(ChatSettingActivity.KEY_API_KEY, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_SYSTEM_PROMPT] = sharedPreferences.getString(ChatSettingActivity.KEY_SYSTEM_PROMPT, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_TEMPERATURE] = sharedPreferences.getString(ChatSettingActivity.KEY_TEMPERATURE, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_HOST_AGENT_URL] = sharedPreferences.getString(ChatSettingActivity.KEY_HOST_AGENT_URL, "") ?: ""
        settingsMap[ChatSettingActivity.KEY_MCP_SERVERS_JSON] = sharedPreferences.getString(ChatSettingActivity.KEY_MCP_SERVERS_JSON, "") ?: ""

        // Filter out empty values, as the iOS version does
        return settingsMap.filterValues { it.isNotEmpty() }
    }

    private fun updateUIForConversationState(state: NuroConversationState) {
        when (state) {
            NuroConversationState.preparing -> {
                loadingIndicator.visibility = View.VISIBLE
                sendButton.isEnabled = false
                messageTextField.isEnabled = false
            }
            NuroConversationState.readyToSendMessage -> {
                loadingIndicator.visibility = View.GONE
                sendButton.isEnabled = messageTextField.text.isNotEmpty() || selectedImageUris.isNotEmpty()
                messageTextField.isEnabled = true
            }
            NuroConversationState.streamingResponse -> {
                loadingIndicator.visibility = View.VISIBLE
                sendButton.isEnabled = false
                // messageTextField.isEnabled = false // Keep enabled to allow typing next message
            }
        }
    }

    private fun sendButtonTapped() {
        val text = messageTextField.text.toString().trim()
        if (text.isEmpty() && selectedImageUris.isEmpty()) {
            return
        }

        val files = mutableListOf<NuroFile>()
        selectedImageUris.forEach { uri ->
            val nuroLocalFile = NuroLocalFile(localPath = uri.toString(), localFileObject = uri)
            files.add(NuroFile(NuroFileType.image, null, nuroLocalFile))
        }
        val userMessage = NuroUserMessage(NuroUtils.randomUUIDString(), text, files)
        conversationManager?.sendUserMessage(userMessage)

        messageTextField.setText("")
        selectedImageUris.clear()
        selectedImagesContainerView.removeAllViews()
        selectedImagesContainerView.visibility = View.GONE
        updateSendButtonState()
        hideKeyboard(messageTextField)
    }

    private fun updateSendButtonState() {
        sendButton.isEnabled = messageTextField.text.isNotEmpty() || selectedImageUris.isNotEmpty()
    }

    private fun scrollToBottom(animated: Boolean) {
        if (messages.isNotEmpty()) {
            if (animated) {
                tableView.smoothScrollToPosition(messages.size - 1)
            } else {
                tableView.scrollToPosition(messages.size - 1)
            }
        }
    }

    private fun hideKeyboard(view: View) {
        val imm = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    private fun checkPermissionAndOpenImagePicker() {
        val permission = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            Manifest.permission.READ_MEDIA_IMAGES
        } else {
            Manifest.permission.READ_EXTERNAL_STORAGE
        }

        when {
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED -> {
                openImagePicker()
            }
            ActivityCompat.shouldShowRequestPermissionRationale(this, permission) -> {
                // Show an explanation to the user *asynchronously*
                Toast.makeText(this, "Storage permission is needed to select images.", Toast.LENGTH_LONG).show()
                requestPermissionLauncher.launch(permission)
            }
            else -> {
                requestPermissionLauncher.launch(permission)
            }
        }
    }

    private fun openImagePicker() {
        val intent = Intent(Intent.ACTION_GET_CONTENT)
        intent.type = "image/*"
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        imagePickerLauncher.launch(Intent.createChooser(intent, "Select Pictures"))
    }

    private fun addSelectedImage(uri: Uri) {
        if (selectedImageUris.size >= 5) { // Limit number of selected images
            Toast.makeText(this, "You can select up to 5 images.", Toast.LENGTH_SHORT).show()
            return
        }
        selectedImageUris.add(uri)
        displaySelectedImages()
        updateSendButtonState()
    }

    private fun displaySelectedImages() {
        selectedImagesContainerView.removeAllViews()
        if (selectedImageUris.isNotEmpty()) {
            selectedImagesContainerView.visibility = View.VISIBLE
            val imageSizePx = 80.dpToPx() // Define a square size in dp and convert to pixels
            val deleteButtonSizePx = 24.dpToPx() // Define delete button size
            val deleteButtonPaddingPx = 4.dpToPx() // Define padding for the delete button

            selectedImageUris.forEach { imageUri ->
                val imageContainer = FrameLayout(this)
                val containerLayoutParams = LinearLayout.LayoutParams(
                    imageSizePx, // width
                    imageSizePx  // height
                )
                containerLayoutParams.setMargins(8.dpToPx(), 8.dpToPx(), 8.dpToPx(), 8.dpToPx())
                imageContainer.layoutParams = containerLayoutParams

                val imageView = ImageView(this)
                val imageLayoutParams = FrameLayout.LayoutParams(
                    FrameLayout.LayoutParams.MATCH_PARENT,
                    FrameLayout.LayoutParams.MATCH_PARENT
                )
                imageView.layoutParams = imageLayoutParams
                imageView.setImageURI(imageUri)
                imageView.scaleType = ImageView.ScaleType.CENTER_CROP // This is aspectFill
                imageContainer.addView(imageView)

                val deleteButton = ImageButton(this)
                val deleteLayoutParams = FrameLayout.LayoutParams(
                    deleteButtonSizePx,
                    deleteButtonSizePx
                )
                deleteLayoutParams.gravity = android.view.Gravity.TOP or android.view.Gravity.END
                deleteLayoutParams.setMargins(deleteButtonPaddingPx, deleteButtonPaddingPx, deleteButtonPaddingPx, deleteButtonPaddingPx)
                deleteButton.layoutParams = deleteLayoutParams
                // Use a more modern 'close' or 'cancel' icon
                deleteButton.setImageResource(android.R.drawable.ic_menu_close_clear_cancel) 
                deleteButton.setBackgroundResource(R.drawable.circle_background_transparent) // Add a subtle background for better visibility if needed
                deleteButton.setPadding(deleteButtonPaddingPx, deleteButtonPaddingPx, deleteButtonPaddingPx, deleteButtonPaddingPx)
                deleteButton.setOnClickListener {
                    selectedImageUris.remove(imageUri)
                    displaySelectedImages() // Re-render the selected images
                    updateSendButtonState()
                }
                imageContainer.addView(deleteButton)

                selectedImagesContainerView.addView(imageContainer)
            }
        } else {
            selectedImagesContainerView.visibility = View.GONE
        }
    }

    private fun Int.dpToPx(): Int = (this * resources.displayMetrics.density).toInt()


    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_chat, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.action_new_conversation -> {
                // Start a new ChatActivity instance
                val intent = Intent(this, ChatActivity::class.java)
                // Optionally, clear task and start new if you want a completely fresh stack
                // intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                startActivity(intent)
                finish() // Finish current activity
                true
            }
            R.id.action_settings -> {
                val intent = Intent(this, ChatSettingActivity::class.java)
                startActivity(intent)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        ChatSettingActivity.onSettingSaved = null // Clean up static reference
    }
}