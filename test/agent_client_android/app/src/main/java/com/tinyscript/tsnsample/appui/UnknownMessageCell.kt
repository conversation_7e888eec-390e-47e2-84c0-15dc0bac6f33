package com.tinyscript.tsnsample.appui

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bytedance.nurosdk.NuroMessage
import com.tinyscript.nurosample.R

class UnknownMessageCell(itemView: View) : RecyclerView.ViewHolder(itemView) {

    private val messageLabel: TextView = itemView.findViewById(R.id.unknown_message_text)

    fun bind(message: NuroMessage) {
        messageLabel.text = "Unknown message type: ${message.type}"
        // You can add more details or specific formatting for unknown messages here
    }

    companion object {
        fun create(parent: ViewGroup): UnknownMessageCell {
            val view = LayoutInflater.from(parent.context)
                .inflate(R.layout.item_unknown_message, parent, false)
            return UnknownMessageCell(view)
        }
    }
}