package com.tinyscript.tsnsample

import byted.tsn.foundation.length
import com.bytedance.nurosdk.MCPToolCallResult
import com.bytedance.nurosdk.MCPToolCallTextContent
import com.bytedance.nurosdk.MCPToolDefineObjectProperty
import com.bytedance.nurosdk.MCPToolDefineStringProperty
import com.bytedance.nurosdk.NuroSetting
import com.bytedance.nurosdk.mcp.MCPLiteServer
import com.bytedance.nurosdk_unittest.TestContext
import com.bytedance.nurosdk_unittest.runTests
import org.junit.Test

class AllTests {

    fun installDevice() {
        NuroSetting.needDisplayServerFunctionMessage = true
        val localServer = MCPLiteServer("local", "1.0.0")
        localServer.tool(
            "get_location",
            "获取用户当前位置",
            MCPToolDefineObjectProperty()
                .defProperty("reason", MCPToolDefineStringProperty().defDescription("为什么需要获取地理位置，理由是什么。"))
                .defRequired(mutableListOf("reason"))
        ) { params, resultCallback ->
            val paramMap = params as? Map<*, *>
            val reason = paramMap?.get("reason") as? String

            if (reason == "不需要用户确认") {
                val result = MCPToolCallResult()
                result.content = mutableListOf(
                    MCPToolCallTextContent.create("佛山市")
                )
                resultCallback(result)
                return@tool
            }

            val result = MCPToolCallResult()
            resultCallback(result) // 留空是因为我们需要通过自定义工具界面，请求用户授权获取用户的地理位置信息。
        }
    }

    @Test
    fun testAll() {
        installDevice()
        runTests {
            if (TestContext.globalContext.getFailedTests().length > 0) {
                TestContext.globalContext.getFailedTests().forEach {
                    println("Test ${it.functionName} failed: ${it.errorMessage}")
                }
                assert(false) { "Test Failed." }
            }
        }
    }
}