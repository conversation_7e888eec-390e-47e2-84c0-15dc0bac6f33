pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenCentral()
        gradlePluginPortal()
        maven("https://jitpack.io")
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        google()
        mavenCentral()
        maven { url = uri("https://maven.byted.org/repository/android_public/") }
        maven("https://jitpack.io")
    }
}

rootProject.name = "nurosample"
include(":app")
include(":nurosdk")
include(":nurosdk_unittest")
project(":nurosdk").projectDir = file("../../kotlin")
project(":nurosdk_unittest").projectDir = file("../../unittest/kotlin")
