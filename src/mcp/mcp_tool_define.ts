import { IS_JS, TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

export class MCPToolDefine {
  name: string;
  description: string;
  inputSchema?: MCPToolDefineObjectProperty;

  constructor(
    name: string,
    description: string,
    inputSchema?: MCPToolDefineObjectProperty,
  ) {
    this.name = name;
    this.description = description;
    this.inputSchema = inputSchema;
  }
}

export class MCPToolDefineProperty extends TSNSerializable {
  @TSNJSON("type") type: string = "";
  @TSNJSON("title") title?: string = undefined;
  @TSNJSON("description") description?: string = undefined;
  @TSNJSON("deprecated") deprecated?: boolean = undefined;

  public defTitle(value: string): MCPToolDefineProperty {
    this.title = value;
    return this;
  }

  public defDescription(value: string): MCPToolDefineProperty {
    this.description = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineProperty {
    this.deprecated = value;
    return this;
  }
}

export class MCPToolDefineObjectProperty extends MCPToolDefineProperty {
  @TSNJSON("properties", () => MCPToolDefineProperty, "Record")
  properties: Record<string, MCPToolDefineProperty> = {};
  @TSNJSON("required") required: string[] = [];

  afterInit(): void {
    super.afterInit();
    this.type = "object";
  }

  public defDescription(value: string): MCPToolDefineObjectProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineObjectProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineObjectProperty {
    this.deprecated = value;
    return this;
  }

  public defProperty(
    name: string,
    value: MCPToolDefineProperty,
  ): MCPToolDefineObjectProperty {
    this.properties[name] = value;
    return this;
  }

  public defRequired(required: string[]): MCPToolDefineObjectProperty {
    this.required = required;
    return this;
  }
}

export class MCPToolDefineStringProperty extends MCPToolDefineProperty {
  @TSNJSON("default") _default?: string = undefined;
  @TSNJSON("examples") examples?: string[] = undefined;
  @TSNJSON("enum") _enum?: string[] = undefined;
  @TSNJSON("const") _const?: string = undefined;
  @TSNJSON("minLength") minLength?: Int = undefined;
  @TSNJSON("maxLength") maxLength?: Int = undefined;
  @TSNJSON("pattern") pattern?: string = undefined;

  afterInit(): void {
    super.afterInit();
    this.type = "string";
  }

  public defDescription(value: string): MCPToolDefineStringProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineStringProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineStringProperty {
    this.deprecated = value;
    return this;
  }

  public defDefault(value: string): MCPToolDefineStringProperty {
    this._default = value;
    return this;
  }

  public defExamples(values: string[]): MCPToolDefineStringProperty {
    this.examples = values;
    return this;
  }

  public defEnum(values: string[]): MCPToolDefineStringProperty {
    this._enum = values;
    return this;
  }

  public defConst(value: string): MCPToolDefineStringProperty {
    this._const = value;
    return this;
  }

  public defMinLength(value: Int): MCPToolDefineStringProperty {
    this.minLength = value;
    return this;
  }

  public defMaxLength(value: Int): MCPToolDefineStringProperty {
    this.maxLength = value;
    return this;
  }

  public defPattern(value: string): MCPToolDefineStringProperty {
    this.pattern = value;
    return this;
  }
}

export class MCPToolDefineIntegerProperty extends MCPToolDefineProperty {
  @TSNJSON("default") _default?: Int = undefined;
  @TSNJSON("examples") examples?: Int[] = undefined;
  @TSNJSON("enum") _enum?: Int[] = undefined;
  @TSNJSON("const") _const?: Int = undefined;
  @TSNJSON("minimum") minimum?: Int = undefined;
  @TSNJSON("maximum") maximum?: Int = undefined;

  afterInit(): void {
    super.afterInit();
    this.type = "integer";
  }

  public defDescription(value: string): MCPToolDefineIntegerProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineIntegerProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineIntegerProperty {
    this.deprecated = value;
    return this;
  }

  public defDefault(value: Int): MCPToolDefineIntegerProperty {
    this._default = value;
    return this;
  }

  public defExamples(values: Int[]): MCPToolDefineIntegerProperty {
    this.examples = values;
    return this;
  }

  public defEnum(values: Int[]): MCPToolDefineIntegerProperty {
    this._enum = values;
    return this;
  }

  public defConst(value: Int): MCPToolDefineIntegerProperty {
    this._const = value;
    return this;
  }

  public defMinimum(value: Int): MCPToolDefineIntegerProperty {
    this.minimum = value;
    return this;
  }

  public defMaximum(value: Int): MCPToolDefineIntegerProperty {
    this.maximum = value;
    return this;
  }
}

export class MCPToolDefineNumberProperty extends MCPToolDefineProperty {
  @TSNJSON("default") _default?: Double = undefined;
  @TSNJSON("examples") examples?: Double[] = undefined;
  @TSNJSON("enum") _enum?: Double[] = undefined;
  @TSNJSON("const") _const?: Double = undefined;
  @TSNJSON("minimum") minimum?: Double = undefined;
  @TSNJSON("maximum") maximum?: Double = undefined;
  afterInit(): void {
    super.afterInit();
    this.type = "number";
  }

  public defDescription(value: string): MCPToolDefineNumberProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineNumberProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineNumberProperty {
    this.deprecated = value;
    return this;
  }

  public defDefault(value: Double): MCPToolDefineNumberProperty {
    this._default = value;
    return this;
  }

  public defExamples(values: Double[]): MCPToolDefineNumberProperty {
    this.examples = values;
    return this;
  }

  public defEnum(values: Double[]): MCPToolDefineNumberProperty {
    this._enum = values;
    return this;
  }

  public defConst(value: Double): MCPToolDefineNumberProperty {
    this._const = value;
    return this;
  }

  public defMinimum(value: Double): MCPToolDefineNumberProperty {
    this.minimum = value;
    return this;
  }

  public defMaximum(value: Double): MCPToolDefineNumberProperty {
    this.maximum = value;
    return this;
  }
}

export class MCPToolDefineBooleanProperty extends MCPToolDefineProperty {
  @TSNJSON("default") _default?: boolean = undefined;
  afterInit(): void {
    super.afterInit();
    this.type = "boolean";
  }

  public defDescription(value: string): MCPToolDefineBooleanProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineBooleanProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineBooleanProperty {
    this.deprecated = value;
    return this;
  }

  defDefault(value: boolean): MCPToolDefineBooleanProperty {
    this._default = value;
    return this;
  }
}

export class MCPToolDefineArrayProperty extends MCPToolDefineProperty {
  @TSNJSON("items", () => MCPToolDefineProperty) items?: MCPToolDefineProperty =
    undefined;
  @TSNJSON("enum") _enum?: any[] = undefined;
  @TSNJSON("maxItems") maxItems?: Int = undefined;
  @TSNJSON("minItems") minItems?: Int = undefined;

  afterInit(): void {
    super.afterInit();
    this.type = "array";
  }

  public defDescription(value: string): MCPToolDefineArrayProperty {
    this.description = value;
    return this;
  }

  public defTitle(value: string): MCPToolDefineArrayProperty {
    this.title = value;
    return this;
  }

  public defDeprecated(value: boolean): MCPToolDefineArrayProperty {
    this.deprecated = value;
    return this;
  }

  public defItems(value: MCPToolDefineProperty): MCPToolDefineArrayProperty {
    this.items = value;
    return this;
  }

  public defEnum(values: any[]): MCPToolDefineArrayProperty {
    this._enum = values;
    return this;
  }

  public defMaxItems(value: Int): MCPToolDefineArrayProperty {
    this.maxItems = value;
    return this;
  }

  public defMinItems(value: Int): MCPToolDefineArrayProperty {
    this.minItems = value;
    return this;
  }
}

export function transformToZodSchema(
  Zod: any,
  value?: MCPToolDefineProperty,
  isRoot: boolean = true,
): Record<string, any> {
  if (value === undefined) {
    return {};
  }

  if (IS_JS) {
    let schema: any;

    switch (value.type) {
      case "object":
        const objProp = value as MCPToolDefineObjectProperty;
        const shape: Record<string, any> = {};
        for (const key in objProp.properties) {
          shape[key] = transformToZodSchema(
            Zod,
            objProp.properties[key]!,
            false,
          );
          // Mark as optional if not in required list
          if (objProp.required.indexOf(key) < 0) {
            shape[key] = shape[key]!.optional();
          }
        }
        schema = isRoot ? shape : Zod.z.object(shape as any);
        break;
      case "string":
        const strProp = value as MCPToolDefineStringProperty;
        let strSchema = Zod.string();
        if (strProp.minLength !== undefined) {
          strSchema = strSchema.min(strProp.minLength);
        }
        if (strProp.maxLength !== undefined) {
          strSchema = strSchema.max(strProp.maxLength);
        }
        // if (strProp.pattern !== undefined) {
        //   strSchema = strSchema.regex(new window.RegExp(strProp.pattern));
        // }
        if (strProp._enum !== undefined) {
          // Zod enums require at least one value
          if (strProp._enum.length > 0) {
            schema = Zod.enum(strProp._enum as [string, ...string[]]);
          } else {
            // Handle empty enum case if necessary, maybe default to string?
            schema = Zod.string();
          }
        } else {
          schema = strSchema;
        }
        if (strProp._const !== undefined) {
          schema = Zod.literal(strProp._const);
        }
        if (strProp._default !== undefined) {
          schema = schema.default(strProp._default);
        }
        break;
      case "integer":
        const intProp = value as MCPToolDefineIntegerProperty;
        let intSchema = Zod.number().int();
        if (intProp.minimum !== undefined) {
          intSchema = intSchema.min(intProp.minimum);
        }
        if (intProp.maximum !== undefined) {
          intSchema = intSchema.max(intProp.maximum);
        }
        if (intProp._enum !== undefined) {
          // Zod doesn't have a direct number enum, use union of literals
          if (intProp._enum.length > 0) {
            const literals = intProp._enum.map((val) => Zod.literal(val));
            schema = Zod.union(literals as any); // Need at least two for union
          } else {
            schema = intSchema;
          }
        } else {
          schema = intSchema;
        }
        if (intProp._const !== undefined) {
          schema = Zod.literal(intProp._const);
        }
        if (intProp._default !== undefined) {
          schema = schema.default(intProp._default);
        }
        break;
      case "number":
        const numProp = value as MCPToolDefineNumberProperty;
        let numSchema = Zod.number();
        if (numProp.minimum !== undefined) {
          numSchema = numSchema.min(numProp.minimum);
        }
        if (numProp.maximum !== undefined) {
          numSchema = numSchema.max(numProp.maximum);
        }
        if (numProp._enum !== undefined) {
          // Zod doesn't have a direct number enum, use union of literals
          if (numProp._enum.length > 0) {
            const literals = numProp._enum.map((val) => Zod.literal(val));
            schema = Zod.union(literals as any); // Need at least two for union
          } else {
            schema = numSchema;
          }
        } else {
          schema = numSchema;
        }
        if (numProp._const !== undefined) {
          schema = Zod.literal(numProp._const);
        }
        if (numProp._default !== undefined) {
          schema = schema.default(numProp._default);
        }
        break;
      case "boolean":
        const boolProp = value as MCPToolDefineBooleanProperty;
        schema = Zod.boolean();
        if (boolProp._default !== undefined) {
          schema = schema.default(boolProp._default);
        }
        break;
      case "array":
        const arrProp = value as MCPToolDefineArrayProperty;
        let itemSchema = Zod.any(); // Default to any if items is not defined
        if (arrProp.items) {
          itemSchema = transformToZodSchema(Zod, arrProp.items, false) as any;
        }
        let arrSchema = Zod.array(itemSchema);
        if (arrProp.minItems !== undefined) {
          arrSchema = arrSchema.min(arrProp.minItems);
        }
        if (arrProp.maxItems !== undefined) {
          arrSchema = arrSchema.max(arrProp.maxItems);
        }
        // Note: Zod doesn't directly support array enums like JSON schema.
        // You might need custom validation logic if strict enum checking is needed for arrays.
        schema = arrSchema;
        break;
      default:
        // Fallback for unknown types or handle specific cases like 'null'
        schema = Zod.any();
        break;
    }

    // Apply common properties like description
    if (value.description) {
      schema = schema.describe(value.description);
    }

    return schema;
  } else {
    return {};
  }
}
