import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

export class MCPToolCallContent extends TSNSerializable {
  @TSNJSON("type") type: string = "text";
}

export class MCPToolCallResource extends MCPToolCallContent {
  @TSNJSON("uri") uri?: string = undefined;
  @TSNJSON("name") name?: string = undefined;
  @TSNJSON("text") text?: string = undefined;
  @TSNJSON("mimeType") mimeType?: string = undefined;
}

export class MCPToolCallResourceContent extends MCPToolCallContent {
  @TSNJSON("resource", () => MCPToolCallResource)
  resource?: MCPToolCallResource = new MCPToolCallResource();

  afterInit(): void {
    super.afterInit();
    this.type = "resource";
  }

  static create(
    uri: string,
    name: string,
    text?: string,
    mimeType?: string,
  ): MCPToolCallResourceContent {
    const content = new MCPToolCallResourceContent();
    const resource = new MCPToolCallResource();
    resource.uri = uri;
    resource.name = name;
    resource.text = text;
    resource.mimeType = mimeType;
    content.resource = resource;
    return content;
  }
}

export class MCPToolCallResult extends TSNSerializable {
  @TSNJSON("content", () => MCPToolCallContent) content: MCPToolCallContent[] =
    [];

  afterParse(): void {
    this.content = this.content.map((it) => {
      if (it.type === "text") {
        return new MCPToolCallTextContent({ JSONObject: it.rawData });
      } else if (it.type === "resource") {
        return new MCPToolCallResourceContent({ JSONObject: it.rawData });
      } else {
        return it;
      }
    });
  }
}

export class MCPToolCallTextContent extends MCPToolCallContent {
  @TSNJSON("text") text: string = "";

  afterInit(): void {
    super.afterInit();
    this.type = "text";
  }

  static create(text: string): MCPToolCallTextContent {
    const content = new MCPToolCallTextContent();
    content.text = text;
    return content;
  }
}
