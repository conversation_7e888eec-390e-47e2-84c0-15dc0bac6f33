// @ts-nocheck
const { McpServer } = require("@modelcontextprotocol/sdk/server/mcp.js");
const { Client } = require("@modelcontextprotocol/sdk/client/index.js");
const McpClient = Client;
const { InMemoryTransport } = require("@modelcontextprotocol/sdk/inMemory.js");
import { NuroMCPClientAdapter, NuroMCPToolItem } from "../nurosdk";
declare let JSON: any;

export class NuroMCPClientAdapterImpl implements NuroMCPClientAdapter {
  private readonly mcpClient: any;

  constructor(
    readonly serverName: string,
    readonly mcpServer: any,
  ) {
    const transports = InMemoryTransport.createLinkedPair();
    const clientTransport = transports[0];
    const serverTransport = transports[1];
    this.mcpServer.connect(serverTransport);
    const mcpClient = new McpClient({
      name: this.serverName,
      version: "1.0.0",
    });
    mcpClient.connect(clientTransport);
    this.mcpClient = mcpClient;
  }

  async listTools(callback: (tools: NuroMCPToolItem[]) => void): Promise<void> {
    const tools = (await this.mcpClient.listTools()).tools;
    callback(
      tools.map((tool) => {
        return new NuroMCPToolItem(
          this.serverName,
          tool.name,
          tool.description ?? "",
          JSON.stringify(tool.inputSchema),
          JSON.stringify(tool),
        );
      }),
    );
  }

  callTool(
    toolCallId: string,
    toolName: string,
    toolArgs: string,
    callback: (result: string) => void,
  ): void {
    (
      this.mcpClient.callTool({
        name: toolName,
        arguments: (() => {
          try {
            return JSON.parse(toolArgs);
          } catch (error) {
            return {};
          }
        })(),
      }) as any
    ).then((result: any) => {
      callback(JSON.stringify(result));
    });
  }
}
