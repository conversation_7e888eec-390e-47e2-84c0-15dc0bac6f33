import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";
import { ChatMessage } from "./chat_message";
import { NuroSetting } from "../nuro/setting";

export class ChatRequest extends TSNSerializable {
  /**
   * 填当前 Conversation 唯一的 UUID，如果是新的 Conversation 则为空
   */
  @TSNJSON("conversation_id") conversationId?: string = undefined;
  /**
   * 填上一个消息的 UUID
   */
  @TSNJSON("parent_message_id") parentMessageId?: string = undefined;
  /**
   * 消息列表
   */
  @TSNJSON("messages", () => ChatMessage)
  messages?: ChatMessage[] = undefined;

  /**
   * system_prompt,eg:{\"mode\":\"潮玩二创\"}
   */
  @TSNJSON("system_prompt") systemPrompt?: string = undefined;

  /**
   * 版本
   */
  @TSNJSON("version") version?: string = NuroSetting.version;
}
