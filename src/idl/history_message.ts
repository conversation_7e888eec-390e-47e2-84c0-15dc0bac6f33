import {
  T<PERSON><PERSON><PERSON><PERSON>,
  T<PERSON><PERSON>apU<PERSON><PERSON>,
  TSNNumberConverter,
  TSNSerializable,
} from "@byted/tsnfoundation";
import { ChatMessage } from "./chat_message";

export class HistoryConversation extends TSNSerializable {
  @TSNJSON("title") title?: string = undefined;

  @TSNJSON("create_time") create_time: Int = 0;

  @TSNJSON("update_time") update_time: Int = 0;

  @TSNJSON("conversation_id") conversation_id: string = "";

  @TSNJSON("mapping", () => ChatMessage, "Record") conversationMapping?: Record<
    string,
    ChatMessage
  > = undefined;

  @TSNJSON("summary") summary?: string = undefined;
}

export class HistoryMessages extends TSNSerializable {
  @TSNJSON("conversation", () => HistoryConversation)
  conversation?: HistoryConversation = undefined;
  @TSNJSON("total") total?: Int = undefined;
  @TSNJSON("has_more") hasMore?: boolean = undefined;

  getChatMessages(): Array<ChatMessage> {
    let result: Array<ChatMessage> = [];
    let _mapping = this.conversation?.conversationMapping;
    if (_mapping !== undefined) {
      let toolCallRoleMessages: Array<ChatMessage> = [];
      TSNMapUtils.forEach(_mapping, (id, message) => {
        let role = message.author?.role;
        if (role !== undefined && role === "tool") {
          toolCallRoleMessages.push(message);
          return;
        }
        result.push(message);
      });
      toolCallRoleMessages.forEach((message) => {
        result.push(message);
      });
    } else {
      return [];
    }
    return result;
  }
}
