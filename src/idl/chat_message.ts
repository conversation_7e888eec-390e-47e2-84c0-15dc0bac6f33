import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";
import { SSEDeltaMessage } from "./sse_message";

export class ChatPatchable extends TSNSerializable {
  applyPatch(delta: SSEDeltaMessage, pathComponents?: string[]): void {
    let _pathComponents =
      pathComponents ??
      (() => {
        const deltaPath = delta.path;
        if (deltaPath === undefined) {
          let v: string[] = [];
          return v;
        }
        let p = deltaPath.split("/");
        if (p[0] === "") {
          p.shift();
        }
        if (p[0] === "message") {
          p.shift();
        }
        return p;
      })();
    const currentPath = _pathComponents.shift();
    if (currentPath !== undefined) {
      this.applyPatchPath(currentPath, delta, _pathComponents);
    }
  }

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {}

  applyToArrayAny<T>(
    delta: SSEDeltaMessage,
    pathComponents: string[],
    newValueBuilder: (value?: string) => T,
    originValue?: T[],
  ): T[] {
    let _pathComponents = pathComponents;
    const indexPath = _pathComponents.shift();
    if (indexPath === undefined) {
      return [];
    }
    const index = parseInt(indexPath);
    let parts = originValue ?? [];
    if (parts[index] === undefined) {
      for (let j = parts.length; j < index + 1; j++) {
        if (parts[j] === undefined) {
          parts.push(newValueBuilder(undefined));
        }
      }
    }
    if (
      (delta.op === "replace" || delta.op === "add") &&
      _pathComponents.length === 0
    ) {
      parts[index] = newValueBuilder(delta.value);
    }
    const newValue = parts[index];
    if (newValue instanceof ChatPatchable) {
      newValue.applyPatch(delta, _pathComponents);
    }
    return parts;
  }

  applyToArrayString(
    delta: SSEDeltaMessage,
    pathComponents: string[],
    originValue?: string[],
  ): string[] {
    let _pathComponents = pathComponents;
    const indexPath = _pathComponents.shift();
    if (indexPath === undefined) {
      return [];
    }
    const index = parseInt(indexPath);
    let parts = originValue ?? [];
    if (parts[index] === undefined) {
      for (let j = parts.length; j < index + 1; j++) {
        if (parts[j] === undefined) {
          parts.push("");
        }
      }
    }
    if (
      (delta.op === "replace" || delta.op === "add") &&
      _pathComponents.length === 0
    ) {
      if (delta.value !== undefined) {
        parts[index] = delta.value;
      }
    } else if (delta.op === "append") {
      parts[index] = (parts[index] ?? "") + (delta.value ?? "");
    }
    return parts;
  }

  applyToString(delta: SSEDeltaMessage, originValue?: string): string {
    if (delta.op === "replace") {
      if (delta.value !== undefined) {
        return delta.value;
      }
    } else if (delta.op === "append") {
      return (originValue ?? "") + (delta.value ?? "");
    }
    return originValue ?? "";
  }
}

export enum ChatMessageStatus {
  /**
   * 消息状态, finished_successfully
   */
  finished_successfully = "finished_successfully",
  /**
   * 消息状态,  in_progress
   */
  in_progress = "in_progress",

  /**
   * 推流前异常错误
   * 推流失败，比如第一条事件就推送失败了
   */
  interrupt_status = "interrupt_status",
  /**
   * 推流前异常错误
   * pe策略失败
   */
  pe_policy_failed_status = "pe_policy_failed_status",
  /**
   * 推流前异常错误
   * 获取流失败
   */
  chat_stream_failed_status = "chat_stream_failed_status",

  /**
   * 安全审核拦截
   * 输入文本审核拦截
   */
  input_text_block_status = "input_text_block_status",
  /**
   * 安全审核拦截
   * 输出文本审核拦截
   */
  output_text_block_status = "output_text_block_status",

  /**
   * 推流异常状态
   * 推送思考内容截止
   */
  send_reasoning_content_stop_status = "send_reasoning_content_stop_status",
  /**
   * 推流异常状态
   * 推送内容截止
   */
  send_content_stop_status = "send_content_stop_status",
}

export enum ChatContentType {
  text = "text",
  image_url = "image_url",
}

export enum ChatMessageFileType {
  /**
   * txt
   */
  TXT = 1,
  /**
   * pdf
   */
  PDF = 2,
  /**
   * doc
   */
  DOC = 3,
  /**
   * docx
   */
  DOCX = 4,
  /**
   * image
   */
  IMAGE = 5,
  /**
   * video
   */
  VIDEO = 6,
  /**
   * audio
   */
  AUDIO = 7,
}

export class ChatMessageImageMetadata extends ChatPatchable {
  @TSNJSON("image_width") image_width?: Int = undefined;
  @TSNJSON("image_height") image_height?: Int = undefined;
  @TSNJSON("image_format") image_format?: string = undefined;
  @TSNJSON("image_prompt") image_prompt?: string = undefined;
}

export class ChatMessageFileURI extends ChatPatchable {
  @TSNJSON("uri") uri?: string = undefined;
  @TSNJSON("file_type") file_type?: ChatMessageFileType = undefined;
  @TSNJSON("file_name") file_name?: string = undefined;
  @TSNJSON("url") url?: string = undefined;
  @TSNJSON("image_metadata", () => ChatMessageImageMetadata)
  image_metadata?: ChatMessageImageMetadata = undefined;
  @TSNJSON("extra") extra?: Record<string, any> = undefined;
  @TSNJSON("file_description") file_description?: string = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "uri") {
      this.uri = this.applyToString(delta, this.uri);
    } else if (name === "file_type") {
      if (delta.op === "replace") {
        if (delta.value !== undefined) {
          this.file_type = ChatMessage.convertValueToChatMessageFileType(
            parseInt(delta.value),
          );
        }
      }
    } else if (name === "file_name") {
      this.file_name = this.applyToString(delta, this.file_name);
    } else if (name === "url") {
      this.url = this.applyToString(delta, this.url);
    } else if (name === "image_metadata") {
      if (this.image_metadata === undefined) {
        this.image_metadata = new ChatMessageImageMetadata();
      }
      this.image_metadata?.applyPatch(delta, pathComponents);
    } else if (name === "file_description") {
      this.file_description = this.applyToString(delta, this.file_description);
    }
  }
}

export class ChatContentPart extends ChatPatchable {
  @TSNJSON("text") text?: string = undefined;
  @TSNJSON("file", () => ChatMessageFileURI) file?: ChatMessageFileURI =
    undefined;
  @TSNJSON("is_referenced") is_referenced?: boolean = undefined;
  @TSNJSON("reasoning_content") reasoning_content?: string = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "text") {
      this.text = this.applyToString(delta, this.text);
    } else if (name === "reasoning_content") {
      this.reasoning_content = this.applyToString(
        delta,
        this.reasoning_content,
      );
    } else if (name === "file") {
      if (this.file === undefined) {
        this.file = new ChatMessageFileURI({ JSONString: delta.value ?? "{}" });
      }
      this.file?.applyPatch(delta, pathComponents);
    } else if (name === "is_referenced") {
      if (delta.value === "true") {
        this.is_referenced = true;
      }
    }
  }
}

export class ChatContent extends ChatPatchable {
  /**
   * 内容类型：text、image_url
   */
  @TSNJSON("content_type") content_type?: ChatContentType = undefined;
  /**
   * 消息内容
   */
  @TSNJSON("content_parts", () => ChatContentPart)
  content_parts?: ChatContentPart[] = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "content_parts") {
      this.content_parts = this.applyToArrayAny(
        delta,
        pathComponents,
        (value) => {
          if (value !== undefined && value.indexOf("{") === 0) {
            return new ChatContentPart({ JSONString: value ?? "{}" });
          } else {
            return new ChatContentPart();
          }
        },
        this.content_parts,
      );
    }
  }
}

export enum ChatToolCallType {
  client_function = "client_function",
  server_function = "server_function",
}

export class ChatToolCallFunc extends ChatPatchable {
  /**
   * 函数名称
   */
  @TSNJSON("name") name?: string = undefined;
  /**
   * 函数调用参数
   */
  @TSNJSON("arguments") arguments?: string = undefined;
  /**
   * 内部使用，上一次成功修复的 JSON 参数，流式传输使用。
   */
  _lastRepairedArguments?: string = "{}";

  /**
   * 其他附带的内容
   */
  @TSNJSON("extra") extra?: Record<string, string> = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "arguments") {
      this.arguments = this.applyToString(delta, this.arguments);
    }
  }
}

/**
 * 工具调用定义
 */
export class ChatToolCall extends ChatPatchable {
  /**
   * 工具调用ID
   */
  @TSNJSON("id") id?: string = undefined;
  /**
   * 类型：client_function、server_function
   */
  @TSNJSON("type") type?: ChatToolCallType = undefined;
  /**
   * 是否正在流式返回工具参数
   */
  @TSNJSON("streaming") streaming?: boolean = undefined;
  /**
   * func
   */
  @TSNJSON("func", () => ChatToolCallFunc) _func?: ChatToolCallFunc = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "func") {
      if (this._func === undefined) {
        this._func = new ChatToolCallFunc();
      }
      this._func?.applyPatch(delta, pathComponents);
    } else if (name === "streaming") {
      if (delta.op === "replace") {
        if (delta.value !== undefined) {
          this.streaming = delta.value === "true";
        }
      }
    }
  }
}

export enum ChatToolType {
  client_function = "client_function",
  server_function = "server_function",
}

export class ChatTool extends ChatPatchable {
  /**
   * 工具ID
   */
  @TSNJSON("id") id?: string = undefined;
  /**
   * 工具类型
   */
  @TSNJSON("type") type?: ChatToolType = undefined;
  /**
   * 工具名称
   */
  @TSNJSON("name") name?: string = undefined;
  /**
   * 工具描述
   */
  @TSNJSON("description") description?: string = undefined;
  /**
   * 工具参数（JSONSchema）
   */
  @TSNJSON("parameters") parameters?: string = undefined;
}

export class ChatAuthor extends ChatPatchable {
  /**
   * 角色
   */
  @TSNJSON("role") role?: string = undefined;
  /**
   * 名称
   */
  @TSNJSON("name") name?: string = undefined;
}

export class FinishDetails extends ChatPatchable {
  /**
   * 类型,eg: stop, interrupted
   */
  @TSNJSON("type") type: string = "";

  isEqualToObject(obj: any): boolean {
    if (obj instanceof FinishDetails) {
      return obj.type === this.type;
    }
    return false;
  }
}

export class ChatMessageMetadata extends ChatPatchable {
  /**
   * 是否在会话中隐藏，有些消息不需要展示
   */
  @TSNJSON("is_visually_hidden_from_conversation")
  is_visually_hidden_from_conversation: boolean = false;
  /**
   * 对话ID
   */
  @TSNJSON("conversation_id") conversation_id: string = "";
  /**
   * 父消息ID
   */
  @TSNJSON("parent_message_id") parent_message_id: string = "";

  /**
   * 工具调用ID
   */
  @TSNJSON("tool_call_id") tool_call_id?: string = undefined;

  /**
   * 完成详情(FinishDetails结构见公共结构)
   */
  @TSNJSON("finish_details", () => FinishDetails)
  finish_details?: FinishDetails = undefined;

  /**
   * 用户GUI操作
   */
  @TSNJSON("action") action?: string = undefined;

  /**
   * 回复类型：follow_up、answer
   * 1. follow_up: 表示推荐问
   * 2. answer表示：模型正常回答
   */
  @TSNJSON("message_type") message_type?: string = undefined;

  /**
   * 模型想起
   */
  @TSNJSON("model_detail") model_detail?: string = undefined;

  /**
   * payload
   */
  @TSNJSON("payload") payload?: string = undefined;

  /**
   * 是否对话已经完结，不是的话需要 续连
   */
  @TSNJSON("end_turn") end_turn?: boolean = undefined;

  /**
   * 埋点透传字段
   */
  @TSNJSON("metrics_extra") metricsExtra?: string = undefined;

  isEqualToObject(obj: any): boolean {
    if (obj instanceof ChatMessageMetadata) {
      let finish_details_is_equal = true;
      if (
        obj.finish_details !== undefined &&
        this.finish_details !== undefined
      ) {
        finish_details_is_equal = obj.finish_details.isEqualToObject(
          this.finish_details,
        );
      } else if (
        obj.finish_details !== undefined &&
        this.finish_details === undefined
      ) {
        finish_details_is_equal = false;
      } else if (
        obj.finish_details === undefined &&
        this.finish_details !== undefined
      ) {
        finish_details_is_equal = false;
      }
      return (
        obj.conversation_id === this.conversation_id &&
        obj.parent_message_id === this.parent_message_id &&
        obj.tool_call_id === this.tool_call_id &&
        obj.is_visually_hidden_from_conversation ===
          this.is_visually_hidden_from_conversation &&
        obj.action === this.action &&
        obj.message_type === this.message_type &&
        obj.model_detail === this.model_detail &&
        obj.payload === this.payload &&
        obj.end_turn === this.end_turn &&
        finish_details_is_equal === true
      );
    }
    return false;
  }
}

export class ChatMessage extends ChatPatchable {
  /**
   * 消息作者
   */
  @TSNJSON("author", () => ChatAuthor) author?: ChatAuthor = undefined;
  /**
   * 消息元数据
   */
  @TSNJSON("metadata", () => ChatMessageMetadata)
  metadata?: ChatMessageMetadata = undefined;
  /**
   * 消息状态, finished_successfully, in_progress
   */
  @TSNJSON("status") status?: ChatMessageStatus = undefined;
  /**
   * 消息ID, UUID
   */
  @TSNJSON("id") id?: string = undefined;
  /**
   * 消息内容, 展示给用户查看
   */
  @TSNJSON("content", () => ChatContent) content?: ChatContent = undefined;
  /**
   * 更新时间，unix毫秒时间戳
   */
  @TSNJSON("update_time") update_time?: Int = undefined;
  /**
   * 创建时间, unix毫秒时间戳
   */
  @TSNJSON("create_time", undefined, "Int64") create_time?: Int64 = 0;
  /**
   * 是否结束本轮对话，一轮对话可能由多个message构成
   */
  @TSNJSON("end_turn") end_turn?: boolean = undefined;
  /**
   * 下发到端上需要执行的操作，端上被视为Agent的工具，可以被进行工具调用
   */
  @TSNJSON("tool_calls", () => ChatToolCall) tool_calls?: ChatToolCall[] =
    undefined;
  @TSNJSON("tools", () => ChatTool) tools?: ChatTool[] = undefined;

  applyPatchPath(
    name: string,
    delta: SSEDeltaMessage,
    pathComponents: string[],
  ): void {
    if (name === "content") {
      if (this.content === undefined) {
        this.content = new ChatContent();
      }
      this.content?.applyPatch(delta, pathComponents);
    } else if (name === "status") {
      if (delta.op === "replace") {
        if (delta.value !== undefined) {
          this.status = ChatMessage.convertValueToChatMessageStatus(
            delta.value,
          );
        }
      }
    } else if (name === "tool_calls") {
      this.tool_calls = this.applyToArrayAny(
        delta,
        pathComponents,
        (value) => {
          if (value !== undefined && value.indexOf("{") === 0) {
            return new ChatToolCall({ JSONString: value });
          }
          return new ChatToolCall();
        },
        this.tool_calls,
      );
    } else if (name == "end_turn") {
      if (delta.op === "replace") {
        if (delta.value === "true") {
          this.end_turn = true;
        } else {
          this.end_turn = false;
        }
      }
    }
  }

  static convertValueToChatMessageStatus(value: string): ChatMessageStatus {
    switch (value) {
      case "finished_successfully":
        return ChatMessageStatus.finished_successfully;
      case "in_progress":
        return ChatMessageStatus.in_progress;
      case "interrupt_status":
        return ChatMessageStatus.interrupt_status;
      case "pe_policy_failed_status":
        return ChatMessageStatus.pe_policy_failed_status;
      case "chat_stream_failed_status":
        return ChatMessageStatus.chat_stream_failed_status;
      case "input_text_block_status":
        return ChatMessageStatus.input_text_block_status;
      case "output_text_block_status":
        return ChatMessageStatus.output_text_block_status;
      case "send_reasoning_content_stop_status":
        return ChatMessageStatus.send_reasoning_content_stop_status;
      case "send_content_stop_status":
        return ChatMessageStatus.send_content_stop_status;
      default:
        return ChatMessageStatus.finished_successfully;
    }
  }

  static convertValueToChatMessageFileType(value: Int): ChatMessageFileType {
    switch (value) {
      case 1:
        return ChatMessageFileType.TXT;
      case 2:
        return ChatMessageFileType.PDF;
      case 3:
        return ChatMessageFileType.DOC;
      case 4:
        return ChatMessageFileType.DOCX;
      case 5:
        return ChatMessageFileType.IMAGE;
      case 6:
        return ChatMessageFileType.VIDEO;
      case 7:
        return ChatMessageFileType.AUDIO;
      default:
        return ChatMessageFileType.IMAGE;
    }
  }
}
