import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

export class SSEDeltaMessage extends TSNSerializable {
  @TSNJSON("op") op?: string = undefined;
  @TSNJSON("path") path?: string = undefined;
  @TSNJSON("value") value?: string = undefined;
  @TSNJSON("original_value") original_value?: string = undefined;
}

export class SSEFinalMessage extends TSNSerializable {
  @TSNJSON("ret") ret?: string = undefined;
  @TSNJSON("errmsg") errmsg?: string = undefined;
  @TSNJSON("systime") systime?: string = undefined;
  @TSNJSON("logid") logid?: string = undefined;
}
