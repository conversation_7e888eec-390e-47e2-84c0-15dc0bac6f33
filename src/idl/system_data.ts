import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

export enum SystemDataType {
  title_generation = "title_generation",
  stream_complete = "stream_complete",
  heartbeat = "heartbeat",
  summary = "summary",
}

export class SystemData extends TSNSerializable {
  @TSNJSON("type") type?: SystemDataType = undefined;

  @TSNJSON("conversation_id") conversation_id?: string = undefined;

  @TSNJSON("title") title?: string = undefined;

  @TSNJSON("content") content?: string = undefined;
}
