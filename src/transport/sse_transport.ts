import { NuroConversationState } from "../nuro/conversation";
import {
  NuroAssistantMessage,
  NuroFileType,
  NuroMessage,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUserMessageStatus,
  ResumeData,
} from "../nuro/message";
import type { NuroConversationManager } from "../nuro/conversation_manager";
import { CommonTransport } from "./transport";
import { ChatRequest } from "../idl/chat_request";
import { EventStreamAdapter, EventStreamConfig, SSEImpl } from "./sse_impl";
import {
  ChatAuthor,
  ChatContent,
  ChatContentPart,
  ChatMessage,
  ChatMessageFileType,
  ChatMessageFileURI,
  ChatMessageImageMetadata,
  ChatMessageMetadata,
  ChatTool,
  ChatToolType,
} from "../idl/chat_message";
import { SSEDeltaMessage, SSEFinalMessage } from "../idl/sse_message";
import { NuroMCPToolItem } from "../nuro/mcp";
import { ConvertType, MessageProcessor } from "../nuro/message_processor";
import { NuroLogger } from "./logger";
import { TSNConsole, TSNMapUtils, TSNOpenClass } from "@byted/tsnfoundation";
import { SystemData, SystemDataType } from "../idl/system_data";
import { NuroSetting } from "../nuro/setting";
import { NuroMockManager } from "../nuro/mocker";

@TSNOpenClass
export class SSETransport extends CommonTransport {
  private _conversationManager?: Weak<NuroConversationManager>;
  private endpoint: string;
  private headers: Record<string, string> = {};
  private currentChunked: string = "";
  private currentChatMessage: Optional<ChatMessage>;
  private messageDataType: string = ""; // reasoning, text, tool.

  constructor(endpoint: string, headers?: Record<string, string>) {
    super();
    this.endpoint = endpoint;
    if (headers !== undefined) {
      this.headers = headers;
    }
  }

  setConversationManager(conversationManager: NuroConversationManager): void {
    this._conversationManager = conversationManager;
  }

  sendMessage(message: NuroMessage, tools: NuroMCPToolItem[]): void {
    this.sendMessages([message], tools);
  }

  resumeMessage() {
    let headers: Record<string, string> = {
      "Content-Type": "application/json",
    };
    for (let key in this.headers) {
      headers[key] = this.headers[key] ?? "";
    }
    let resumeData: ResumeData = new ResumeData();
    resumeData.conversationId =
      this._conversationManager?.conversation.conversationId ?? "";
    let length = this._conversationManager?.conversation.messages.length ?? 0;
    if (length > 0) {
      let message =
        this._conversationManager?.conversation.messages[length - 1];
      let rawId = "";
      if (message !== undefined) {
        rawId = message.getResumeMsgId();
      }
      resumeData.messageId = rawId;
    }

    this.currentChunked = "";
    this.sendSSERequest(
      EventStreamAdapter.reconnectEndpoint,
      resumeData.toJSONString(),
      headers,
      () => {
        // onChunkStart
      },
      () => {
        // onSendMessageFailed
        MessageProcessor.markInProgressMessagesAsFailed(
          this._conversationManager,
        );

        this._conversationManager?.conversation.updateState(
          NuroConversationState.readyToSendMessage,
        );
      },
      () => {
        MessageProcessor.markInProgressMessagesAsCancel(
          this._conversationManager,
        );
        this._conversationManager?.conversation.updateState(
          NuroConversationState.readyToSendMessage,
        );
      },
    );
  }

  sendMessages(messages: NuroMessage[], tools: NuroMCPToolItem[]): void {
    if (messages.length === 0) {
      this._conversationManager?.conversation.updateState(
        NuroConversationState.readyToSendMessage,
      );
      return;
    }

    this._conversationManager?.conversation.messages.forEach((message) => {
      if (message.isFinalStatus() === false) {
        if (message instanceof NuroToolCallMessage) {
          message.setMsgStatus(NuroToolCallMessageStatus.skipped);
        }
      }
    });

    for (let index = 0; index < messages.length; index++) {
      const msg = messages[index]!;
      if (msg instanceof NuroUserMessage) {
        msg.setMsgStatus(NuroUserMessageStatus.sending);
      }
    }
    const chatRequest = new ChatRequest();

    chatRequest.conversationId =
      this._conversationManager?.conversation.conversationId;

    chatRequest.parentMessageId = (() => {
      const messages = this._conversationManager?.conversation.messages;
      if (messages !== undefined && messages.length > 0) {
        for (let index = messages.length - 1; index >= 0; index = index - 1) {
          const msg = messages[index];
          if (msg !== undefined) {
            if (
              msg instanceof NuroAssistantMessage ||
              msg instanceof NuroToolCallMessage
            ) {
              return msg._rawId;
            }
          }
        }
      }
      return undefined;
    })();

    if (chatRequest.parentMessageId == undefined) {
      chatRequest.systemPrompt =
        this._conversationManager?.conversation.systemPrompt;
    }
    chatRequest.messages = [];
    var previousMessageId: Optional<string> = undefined;
    for (let index = 0; index < messages.length; index++) {
      const message = messages[index]!;
      const chatMessage = new ChatMessage();
      chatMessage.id = message.id;
      const chatAuthor = new ChatAuthor();
      chatAuthor.role =
        message instanceof NuroToolCallMessage ? "tool" : "user";
      chatMessage.author = chatAuthor;
      const content = new ChatContent();
      const metadata = new ChatMessageMetadata();
      if (message instanceof NuroUserMessage) {
        let contentParts: ChatContentPart[] = [];
        if (message.text !== undefined && message.text.length > 0) {
          let contentPart: ChatContentPart = new ChatContentPart();
          contentPart.text = message.text;
          contentParts.push(contentPart);
        }
        if (message.files !== undefined && message.files.length > 0) {
          message.files.forEach((it) => {
            let contentPart: ChatContentPart = new ChatContentPart();
            let file = new ChatMessageFileURI();
            file.file_type =
              it.type === NuroFileType.image
                ? ChatMessageFileType.IMAGE
                : ChatMessageFileType.VIDEO;
            file.url = it.url;
            file.uri = it.uri;
            file.extra = it.extra;
            file.file_description = it.file_description;
            let nuroImageMetadata = it.metadata?.image_metadata ?? undefined;
            if (
              file.file_type === ChatMessageFileType.IMAGE &&
              nuroImageMetadata !== undefined
            ) {
              let imageMetadata = new ChatMessageImageMetadata();
              imageMetadata.image_width = nuroImageMetadata.width;
              imageMetadata.image_height = nuroImageMetadata.height;
              imageMetadata.image_prompt = nuroImageMetadata.prompt;
              imageMetadata.image_format = nuroImageMetadata.format;
              file.image_metadata = imageMetadata;
            }
            contentPart.file = file;
            contentParts.push(contentPart);
          });
        }
        metadata.metricsExtra = message.metadata.metricsExtra;
        if (
          message.referenceInfo !== undefined &&
          message.referenceInfo.length > 0
        ) {
          message.referenceInfo.forEach((it) => {
            let contentPart: ChatContentPart = new ChatContentPart();
            if (it.text !== undefined && it.text.length > 0) {
              contentPart.text = it.text;
            }
            if (it.file !== undefined) {
              let file = new ChatMessageFileURI();
              file.file_type =
                it.file.type === NuroFileType.image
                  ? ChatMessageFileType.IMAGE
                  : ChatMessageFileType.VIDEO;
              file.url = it.file.url;
              file.uri = it.file.uri;
              file.extra = it.file.extra;
              file.file_description = it.file.file_description;
              let nuroImageMetadata =
                it.file.metadata?.image_metadata ?? undefined;
              if (
                file.file_type === ChatMessageFileType.IMAGE &&
                nuroImageMetadata !== undefined
              ) {
                let imageMetadata = new ChatMessageImageMetadata();
                imageMetadata.image_width = nuroImageMetadata.width;
                imageMetadata.image_height = nuroImageMetadata.height;
                imageMetadata.image_prompt = nuroImageMetadata.prompt;
                imageMetadata.image_format = nuroImageMetadata.format;
                file.image_metadata = imageMetadata;
              }
              contentPart.file = file;
            }
            contentPart.is_referenced = true;
            contentParts.push(contentPart);
          });
        }
        content.content_parts = contentParts;
      } else if (message instanceof NuroToolCallMessage) {
        let tollmsg: NuroToolCallMessage = message as NuroToolCallMessage;
        let contentPart: ChatContentPart = new ChatContentPart();
        contentPart.text = tollmsg.toolResult ?? "";
        content.content_parts = [contentPart];
        metadata.tool_call_id = tollmsg.toolCallId;
      }

      chatMessage.content = content;
      chatMessage.tools = tools.map((it) => {
        const toolItem = new ChatTool();
        toolItem.id = it.name;
        toolItem.type = ChatToolType.client_function;
        toolItem.name = it.serverName + "_" + it.name;
        toolItem.description = it.description;
        toolItem.parameters = it.inputSchema;
        return toolItem;
      });

      metadata.conversation_id = chatRequest.conversationId ?? "";
      metadata.parent_message_id =
        previousMessageId ?? chatRequest.parentMessageId ?? "";
      chatMessage.metadata = metadata;
      chatMessage.create_time = message.createTime;
      chatRequest.messages?.push(chatMessage);
      previousMessageId = message.id;
    }
    let headers: Record<string, string> = {
      "Content-Type": "application/json",
    };
    for (let key in this.headers) {
      headers[key] = this.headers[key] ?? "";
    }
    this.currentChunked = "";
    this.sendSSERequest(
      this.endpoint,
      chatRequest.toJSONString(),
      headers,
      () => {
        // onChunkStart
      },
      () => {
        // onSendMessageFailed
        MessageProcessor.markInProgressMessagesAsFailed(
          this._conversationManager,
        );

        this._conversationManager?.conversation.updateState(
          NuroConversationState.readyToSendMessage,
        );
      },
      () => {
        MessageProcessor.markInProgressMessagesAsCancel(
          this._conversationManager,
        );
        this._conversationManager?.conversation.updateState(
          NuroConversationState.readyToSendMessage,
        );
      },
    );
  }

  sendSSERequest(
    endpoint: Optional<string>,
    chatRequest: Optional<string>,
    headers: Record<string, string>,
    onChunkStart: () => void,
    onMessageFailed: () => void,
    onConversationCancel: () => void,
  ): void {
    if (NuroMockManager.isMocking()) {
      return;
    }
    if (endpoint === undefined) {
      return;
    }
    const esConfig = new EventStreamConfig();
    esConfig.endpoint = endpoint;
    esConfig.method = "POST";
    esConfig.headers = headers;
    esConfig.data = chatRequest ?? "";
    let chunkedStarted = false;
    esConfig.onChunk = (text: string) => {
      NuroLogger.debug("SSETransport", () => `received chunk, ${text}`);
      if (chunkedStarted === false) {
        chunkedStarted = true;
        onChunkStart();
      }
      this.currentChunked = this.currentChunked + text;
      this.flushChuck(false);
    };
    esConfig.onFinish = () => {
      NuroLogger.debug("SSETransport", () => `received finish`);
      this.flushChuck(true);
    };
    esConfig.onError = (code: Int, message?: string) => {
      NuroLogger.debug(
        "SSETransport",
        () =>
          `received error, code = ${code.toString()}, message = ${message ?? ""}`,
      );
      this.flushChuck(true);
      onMessageFailed();
    };
    esConfig.onCancel = (code: Int, message?: string) => {
      NuroLogger.debug("SSETransport", () => `received cancel`);
      this.flushChuck(true);
      onConversationCancel();
    };
    NuroLogger.debug("SSETransport", () => `send sse request, ${chatRequest}`);
    this.token = SSEImpl.fetch(esConfig);
  }

  flushChuck(ended: boolean): void {
    let originalParts = this.currentChunked.split("\n\n");

    let partsLength = originalParts.length;
    let currentIndex = -1;
    originalParts.forEach((part) => {
      currentIndex = currentIndex + 1;
      if (currentIndex === partsLength - 1) {
        if (ended === false) {
          // last part
          this.currentChunked = part;
          return;
        }
      }

      if (
        part.indexOf("logid") > 0 &&
        part.indexOf("ret") > 0 &&
        part.indexOf("{") === 0
      ) {
        let res: SSEFinalMessage = new SSEFinalMessage({
          JSONString: part,
        });
        if (res.ret !== undefined) {
          if (res.ret === "0") {
            MessageProcessor.markLastUserMessageAsFinished(
              this._conversationManager,
            );
          } else {
            MessageProcessor.markInProgressMessagesAsFailed(
              this._conversationManager,
            );
          }
          this._conversationManager?.conversation.updateState(
            NuroConversationState.readyToSendMessage,
          );
          return;
        }
      }

      let id: string = "";
      let event: string = "";
      let data: string = "";
      part.split("\n").forEach((line) => {
        if (line.indexOf("id:") === 0) {
          id = line.substr(3).trim();
        } else if (line.indexOf("event:") === 0) {
          event = line.substr(6).trim();
        } else if (line.indexOf("data:") === 0) {
          data = line.substr(5).trim();
        }
        if (id !== "" && event !== "" && data !== "") {
          this.onSSEEvent(id, event, data);
        }
      });
    });

    if (ended === true) {
      this.currentChunked = "";
    }
  }

  onSSEEvent(id: string, event: string, data: string): void {
    if (event === "message") {
      NuroLogger.debug("SSETransport", () => `received message, ${data}`);
      const _chatMessage = new ChatMessage({ JSONString: data });
      const coversationId = _chatMessage.metadata?.conversation_id;
      this.currentChatMessage = _chatMessage;
      if (
        this._conversationManager !== undefined &&
        coversationId !== undefined
      ) {
        this._conversationManager.conversation.conversationId = coversationId;
      }
      const contentParts = _chatMessage.content?.content_parts;
      if (contentParts !== undefined && contentParts.length > 0) {
        MessageProcessor.convertChatMessageToNuroMessage(
          this._conversationManager,
          _chatMessage,
          ConvertType.new_message,
        ).forEach((it) => {
          MessageProcessor.markLastUserMessageAsFinished(
            this._conversationManager,
          );
          this._conversationManager?.receivedMessage(it);
        });
      }
    } else if (event === "delta") {
      NuroLogger.debug("SSETransport", () => `received delta, ${data}`);
      let delta = new SSEDeltaMessage({ JSONString: data });
      let deltaPath = delta.path;
      if (
        deltaPath !== undefined &&
        deltaPath.indexOf("/message/content/content_parts/") >= 0
      ) {
        this.messageDataType = "assistant";
      } else if (
        deltaPath !== undefined &&
        deltaPath.indexOf("/message/tool_calls/") >= 0
      ) {
        // 输出 tool 代表 reasoning 结束
        MessageProcessor.markMessagesAsFinished(this._conversationManager);
        this.messageDataType = "tool_call";
      }
      if (this.currentChatMessage !== undefined) {
        this.currentChatMessage.applyPatch(delta, undefined);
        MessageProcessor.convertChatMessageToNuroMessage(
          this._conversationManager,
          this.currentChatMessage,
          ConvertType.new_message,
        ).forEach((it) => {
          MessageProcessor.markLastUserMessageAsFinished(
            this._conversationManager,
          );
          this._conversationManager?.receivedMessage(it);
        });
      }
    } else if (event === "system" && data.indexOf("stream_error") > 0) {
      NuroLogger.error(
        "SSETransport",
        () => `received system stream_error, ${data}`,
      );
      MessageProcessor.markInProgressMessagesAsFailed(
        this._conversationManager,
      );
      // failed, so change to ready to send message.
    } else if (event === "system") {
      let sysData = new SystemData({ JSONString: data });
      NuroLogger.debug("SSETransport", () => `received system data, ${data}`);
      if (sysData.type === SystemDataType.stream_complete) {
        MessageProcessor.markMessagesAsFinished(this._conversationManager);
      }
      if (
        sysData.type === SystemDataType.summary &&
        this._conversationManager !== undefined &&
        this._conversationManager.conversation !== undefined
      ) {
        this._conversationManager.conversation.summary = sysData.content;
      }
      let listeners =
        this._conversationManager?.conversation.systemDataListeners;
      if (listeners !== undefined) {
        TSNMapUtils.forEach(listeners, (key, listener) => {
          listener(sysData);
        });
      }
    }
  }
}
