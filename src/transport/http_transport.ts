import { NuroLogger } from "./logger";
import { EventStreamConfig, SSEImpl } from "./sse_impl";
import { TSNJSON, TSNSerializable } from "@byted/tsnfoundation";

export class HttpTransport {
  private currentChunked: string = "";

  sendRequest(
    endpoint: Optional<string>,
    data: Optional<string> = "",
    headers: Record<string, string> = {},
    successCallback: (result: string) => void,
    failCallback: (code: string, reason?: string) => void,
  ): void {
    if (endpoint === undefined) {
      return;
    }

    const esConfig = new EventStreamConfig();
    esConfig.endpoint = endpoint;
    esConfig.method = "POST";
    esConfig.headers = headers;
    esConfig.data = data ?? "";
    esConfig.onChunk = (text: string) => {
      this.currentChunked = this.currentChunked + text;
    };
    esConfig.onFinish = () => {
      let result = new HttpData({ JSONString: this.currentChunked });
      if (result.ret == "0") {
        NuroLogger.debug(
          "HttpTransport",
          () =>
            `sendRequest: onFinish: ret = ${result.ret}, endpoint = ${endpoint}, data = ${data}`,
        );
        successCallback(this.currentChunked);
      } else {
        NuroLogger.error(
          "HttpTransport",
          () =>
            `sendRequest: onFinish: ret = ${result.ret}, errmsg = ${result.errmsg}, endpoint = ${endpoint}, data = ${data}`,
        );
        failCallback(result.ret, result.errmsg);
      }
    };
    esConfig.onError = (code: Int, message?: string) => {
      NuroLogger.error(
        "HttpTransport",
        () =>
          `sendRequest: onError: code = ${code}, message = ${message}, endpoint = ${endpoint}, data = ${data}`,
      );
      failCallback(code.toString(), message);
    };
    NuroLogger.debug(
      "HttpTransport",
      () =>
        `sendRequest: endpoint = ${endpoint}, data = ${data}, method = ${esConfig.method}`,
    );
    SSEImpl.fetch(esConfig);
  }
}

export class HttpData extends TSNSerializable {
  @TSNJSON("ret") ret: string = "0";
  @TSNJSON("errmsg") errmsg?: string = undefined;
  @TSNJSON("systime") systime?: string = undefined;
  @TSNJSON("logid") logid?: string = undefined;
}
