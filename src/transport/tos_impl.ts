import { NuroFile, NuroLocalFile } from "../nurosdk";

export class TOSFileUploadConfig {
  nuroFile: NuroFile;
  localFile: NuroLocalFile;
  onFinish: Optional<() => void>;
  onError: Optional<(code: Int, message?: string) => void>;
  onCancel: Optional<(code: Int, message?: string) => void>;

  constructor(nuroFile: NuroFile, localFile: NuroLocalFile) {
    this.nuroFile = nuroFile;
    this.localFile = localFile;
  }
}

export class TOSFileUploadAdapter {
  static upload: Optional<(config: TOSFileUploadConfig) => string>;
  static cancel: Optional<(cancelToken: string) => void>;
}

export class TOSFileUploadImpl {
  static upload(config: TOSFileUploadConfig): Optional<string> {
    const uploader = TOSFileUploadAdapter.upload;
    if (uploader !== undefined) {
      return uploader(config);
    }
    return undefined;
  }

  static cancel(cancelToken: string): void {
    const canceler = TOSFileUploadAdapter.cancel;
    if (canceler !== undefined) {
      canceler(cancelToken);
    }
  }
}
