export class EventStreamConfig {
  endpoint: string = "";
  method: string = "POST";
  headers?: Record<string, string>;
  data?: string;
  onStart: Optional<() => void>;
  onChunk: Optional<(text: string) => void>;
  onError: Optional<(code: Int, message?: string) => void>;
  onFinish: Optional<() => void>;
  onCancel: Optional<(code: Int, message?: string) => void>;
}

export class EventStreamAdapter {
  static fetch: Optional<(config: EventStreamConfig) => string>;
  static cancel: Optional<(cancelToken: string) => void>;
  static reconnectEndpoint: Optional<string>; // 重连的 endpoint
  static interruptEndpoint: Optional<string>; // 中断的 endpoint
  static payloadEndpoint: Optional<string>; // payload// 的 endpoint
}

export class SSEImpl {
  static fetch(config: EventStreamConfig): Optional<string> {
    const fetcher = EventStreamAdapter.fetch;
    if (fetcher !== undefined) {
      return fetcher(config);
    }
    return undefined;
  }

  static cancel(cancelToken: string): void {
    const canceler = EventStreamAdapter.cancel;
    if (canceler !== undefined) {
      canceler(cancelToken);
    }
  }
}
