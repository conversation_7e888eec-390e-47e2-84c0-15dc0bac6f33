// Define log levels
export enum NuroLogLevel {
  DEBUG = 0,
  INFO = 1,
  ERROR = 2,
  NONE = 3, // No logs
}

export class NuroLoggerAdapter {
  static debug: Optional<(tag: string, msg: string) => void>;
  static info: Optional<(tag: string, msg: string) => void>;
  static error: Optional<(tag: string, msg: string) => void>;
}

export class NuroLogger {
  private static currentLevel: NuroLogLevel = NuroLogLevel.ERROR; // Default log level

  static setLogLevel(level: NuroLogLevel): void {
    NuroLogger.currentLevel = level;
  }
  static debug(tag: string, msg: () => string): void {
    if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.DEBUG.valueOf()) {
      const logger = NuroLoggerAdapter.debug;
      if (logger !== undefined) {
        logger(tag, `[DEBUG] ${msg()}`);
      }
    }
  }
  static info(tag: string, msg: () => string): void {
    if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.INFO.valueOf()) {
      const logger = NuroLoggerAdapter.info;
      if (logger !== undefined) {
        logger(tag, `[INFO] ${msg()}`);
      }
    }
  }
  static error(tag: string, msg: () => string): void {
    if (NuroLogger.currentLevel.valueOf() <= NuroLogLevel.ERROR.valueOf()) {
      const logger = NuroLoggerAdapter.error;
      if (logger !== undefined) {
        logger(tag, `[ERROR] ${msg()}`);
      }
    }
  }
}
