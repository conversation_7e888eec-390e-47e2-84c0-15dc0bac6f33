import { NuroMCPToolItem } from "../nurosdk";
import type { NuroConversationManager } from "../nuro/conversation_manager";
import type { NuroMessage, NuroUserMessage } from "../nuro/message";
import { TSNOpenClass } from "@byted/tsnfoundation";

@TSNOpenClass
export abstract class CommonTransport {
  abstract setConversationManager(
    conversationManager: NuroConversationManager,
  ): void;
  abstract sendMessage(message: NuroMessage, tools: NuroMCPToolItem[]): void;
  abstract sendMessages(
    messages: NuroMessage[],
    tools: NuroMCPToolItem[],
  ): void;
  token: Optional<string> = undefined;
  abstract resumeMessage(): void;
}
