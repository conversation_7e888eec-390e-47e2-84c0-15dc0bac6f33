import { TSNConsole, TSNMapUtils } from "@byted/tsnfoundation";
import {
  NuroMessage,
  NuroTaskMessageType,
  NuroToolCallMessage,
  NuroCanvasMessage,
  NuroUserMessage,
  NuroToolCallMessageStatus,
  NuroAssistantMessage,
} from "./message";
import { NuroUtils } from "./utils";
import {
  EventStreamAdapter,
  type NuroConversationManager,
  NuroLogger,
  NuroMessageOp,
  NuroMessageOut,
  NuroTaskOp,
  NuroTaskOut,
} from "../nurosdk";
import { SystemData } from "../idl/system_data";
import { ChatToolCallType } from "../idl/chat_message";
import { NuroTask, NuroTaskChecker, NuroTaskStatus } from "./task";
import { NuroSetting } from "./setting";

export enum NuroConversationState {
  /**
   * The conversation is preparing to send the first message.
   * UI should show a loading indicator, should not allow user to send a message.
   */
  preparing = "preparing",

  /**
   * The conversation is streaming the response.
   * UI should show the response with streaming, should not allow user to send new message.
   * UI can display a pause button allow user to stop streaming.
   */
  streamingResponse = "streamingResponse",

  /**
   * The conversation is ready to send the first message.
   * UI should allow the user to send the first message.
   */
  readyToSendMessage = "readyToSendMessage",
}

export class NuroConversation {
  conversationId: string;
  systemPrompt?: string;
  conversationState: NuroConversationState = NuroConversationState.preparing;
  messages: Array<NuroMessage> = [];
  tasks: Array<NuroTask> = [];
  taskChecker?: NuroTaskChecker = undefined;
  summary?: string = undefined;

  constructor(conversationId: string) {
    this.conversationId = conversationId;
  }
  private stateUpdateListeners: Record<
    string,
    (state: NuroConversationState) => void
  > = {};
  private messageUpdateListeners: Record<
    string,
    (message: NuroMessage, op: NuroMessageOp) => void
  > = {};
  private taskUpdateListeners: Record<string, (tasks: NuroTaskOut[]) => void> =
    {};

  systemDataListeners: Record<string, (systemData: SystemData) => void> = {};

  addStateUpdateListener(
    listener: (state: NuroConversationState) => void,
  ): string {
    const uuid = NuroUtils.randomUUIDString();
    this.stateUpdateListeners[uuid] = listener;
    return uuid;
  }

  removeStateUpdateListener(token: string) {
    this.stateUpdateListeners[token] = undefined;
  }

  removeAllStateUpdateListeners() {
    this.stateUpdateListeners = {};
  }

  addSystemDataListener(listener: (systemData: SystemData) => void): string {
    const uuid = NuroUtils.randomUUIDString();
    this.systemDataListeners[uuid] = listener;
    return uuid;
  }

  removeSystemDataListener(token: string) {
    this.systemDataListeners[token] = undefined;
  }

  removeSystemDataListeners() {
    this.systemDataListeners = {};
  }

  addMessageUpdateListener(
    listener: (message: NuroMessage, op: NuroMessageOp) => void,
  ): string {
    const uuid = NuroUtils.randomUUIDString();
    this.messageUpdateListeners[uuid] = listener;
    return uuid;
  }

  removeMessageUpdateListener(token: string) {
    this.messageUpdateListeners[token] = undefined;
  }

  removeAllMessageUpdateListeners() {
    this.messageUpdateListeners = {};
  }

  addTaskUpdateListener(listener: (tasks: NuroTaskOut[]) => void): string {
    const uuid = NuroUtils.randomUUIDString();
    this.taskUpdateListeners[uuid] = listener;
    return uuid;
  }

  removeTaskUpdateListener(token: string) {
    this.taskUpdateListeners[token] = undefined;
  }

  removeAllTaskUpdateListeners() {
    this.taskUpdateListeners = {};
  }

  needResume(): boolean {
    if (EventStreamAdapter.reconnectEndpoint === undefined) {
      return false;
    }

    if (NuroSetting.version === "3.0.0") {
      // 3.0.0按任务来分
      let length = this.tasks.length;
      if (length <= 0) {
        return false;
      }
      let lastTask = this.tasks[length - 1];
      return lastTask?.taskStatus === NuroTaskStatus.running;
    } else {
      let length = this.messages.length;

      if (length <= 0) {
        return false;
      }
      let lastMessage = this.messages[length - 1];

      return lastMessage?.needResume() ?? false;
    }
  }

  updateState(state: NuroConversationState): void {
    NuroLogger.info(
      "NuroConversation",
      () =>
        `updateConversationState: state = ${state}, conversationId = ${this.conversationId}`,
    );
    this.conversationState = state;
    TSNMapUtils.forEach(this.stateUpdateListeners, (key, listener) => {
      listener(state);
    });
  }

  updateMessage(message: NuroMessage, op: NuroMessageOp): void {
    NuroLogger.info(
      "NuroConversation",
      () =>
        `updateMessage: conversationId = ${this.conversationId}, msgType = ${message.type}, id = ${message.id}, op = ${op}`,
    );
    //  this.updateStateWhenMsgUpdate();
    TSNMapUtils.forEach(this.messageUpdateListeners, (key, listener) => {
      listener(message, op);
    });

    let messageOut = new NuroMessageOut(message, op);
    this.processTask([messageOut], true);
  }

  processTask(msgOut: NuroMessageOut[], needUpdate: boolean = true): void {
    const taskChecker = this.taskChecker;
    if (taskChecker !== undefined) {
      const newTasks: Array<NuroTask> = [];
      let currentTask: Optional<NuroTask>;
      this.messages.forEach((it, index) => {
        if (taskChecker.isArtifactMessage(it)) {
          if (currentTask === undefined) {
            const newTask = new NuroTask(it.id);
            currentTask = newTask;
            newTask.taskStatus = NuroTaskStatus.running;
            newTasks.push(newTask);
          }
          if (taskChecker.isShieldMessage(it)) {
            currentTask?.addMessage(it, NuroTaskMessageType.shieldMessage);
          } else {
            currentTask?.addMessage(it, NuroTaskMessageType.artifactMessage);
          }
        } else {
          if (
            currentTask !== undefined &&
            currentTask.artifactMessages.length > 0
          ) {
            currentTask = undefined; // reset currentTask because it has artifactMessages, and received a new message.
          }
          if (currentTask === undefined) {
            const newTask = new NuroTask(it.id);
            currentTask = newTask;
            newTask.taskStatus = NuroTaskStatus.running;
            newTasks.push(newTask);
          }
          if (currentTask !== undefined) {
            if (taskChecker.isPromptMessage(it) === true) {
              if (currentTask.middlewareMessages.length <= 0) {
                currentTask.addMessage(it, NuroTaskMessageType.promptMessage);
              } else {
                const newTask = new NuroTask(it.id);
                newTask.taskStatus = NuroTaskStatus.running;
                newTask.addMessage(it, NuroTaskMessageType.promptMessage);
                newTasks.push(newTask);
                currentTask = newTask;
              }
            } else {
              currentTask.addMessage(it, NuroTaskMessageType.middlewareMessage);
            }
          }
        }
        if (currentTask !== undefined) {
          if (it.isCancelledStatus()) {
            currentTask.taskStatus = NuroTaskStatus.cancelled;
            currentTask = undefined;
          } else if (it.isFailedStatus()) {
            currentTask.taskStatus = NuroTaskStatus.failed;
            currentTask = undefined;
          } else {
            if (index === this.messages.length - 1) {
              if (it instanceof NuroAssistantMessage) {
                if (it.isFinalTurn()) {
                  currentTask.taskStatus = NuroTaskStatus.finished;
                }
              } else if (it instanceof NuroToolCallMessage) {
                if ((it as NuroToolCallMessage)?.isClientToolSkipped()) {
                  currentTask.taskStatus = NuroTaskStatus.finished;
                }
              }
            }
          }
        }
      });

      newTasks.forEach((task) => task.recalculateMessageHash());
      const newTasksLength = newTasks.length;
      let changeTasks: Array<NuroTaskOut> = [];

      for (let i = 0; i < newTasksLength; i++) {
        if (i < this.tasks.length) {
          if (newTasks[i]!.messageHash !== this.tasks[i]!.messageHash) {
            this.tasks[i] = newTasks[i]!;
            if (needUpdate) {
              let taskOut = new NuroTaskOut(this.tasks[i]!, NuroTaskOp.update);
              changeTasks.push(taskOut);
            }
          }
        } else {
          this.tasks.push(newTasks[i]!);
          if (needUpdate) {
            let taskOut = new NuroTaskOut(
              this.tasks[this.tasks.length - 1]!,
              NuroTaskOp.add,
            );
            changeTasks.push(taskOut);
          }
        }
      }

      if (this.tasks.length > newTasksLength) {
        const tasksToRemoveCount = this.tasks.length - newTasksLength;
        const removedTasks = this.tasks.splice(
          newTasksLength,
          tasksToRemoveCount,
        );
        removedTasks.forEach((removedTask) => {
          if (needUpdate) {
            let taskOut = new NuroTaskOut(removedTask, NuroTaskOp.delete);
            changeTasks.push(taskOut);
          }
        });
      }
      changeTasks.forEach((changeTask) => {
        changeTask.updateMessage(msgOut);
      });

      TSNMapUtils.forEach(this.taskUpdateListeners, (key, listener) => {
        listener(changeTasks);
      });
    }
  }

  findToolCallMessageByToolCallId(
    toolCallId: string,
  ): Optional<NuroToolCallMessage> {
    let result: Optional<NuroToolCallMessage> = undefined;
    this.messages.forEach((it) => {
      if (it instanceof NuroToolCallMessage) {
        if (it.toolCallId === toolCallId) {
          result = it;
        }
      }
    });
    return result;
  }

  findLastOpenNuroCanvasMessage(
    messageList: Optional<NuroMessage[]>,
  ): Optional<NuroCanvasMessage> {
    if (messageList === undefined) {
      TSNConsole.log(" message list is empty! ");
      return undefined;
    }
    let result: Optional<NuroCanvasMessage> = undefined;
    let index = messageList.length - 1;
    while (index >= 0) {
      if (messageList[index] instanceof NuroCanvasMessage) {
        let message = messageList[index] as NuroCanvasMessage;
        if (message.isCanvasOpen()) {
          result = message;
          break;
        }
        break;
      }
      index--;
    }
    return result;
  }

  findLOpenNuroCanvasMessage(
    messageList: Optional<NuroMessage[]>,
  ): Optional<NuroCanvasMessage[]> {
    if (messageList === undefined) {
      TSNConsole.log(" message list is empty! ");
      return undefined;
    }
    let result: Optional<NuroCanvasMessage[]> = undefined;
    let index = messageList.length - 1;
    while (index >= 0) {
      if (messageList[index] instanceof NuroCanvasMessage) {
        let message = messageList[index] as NuroCanvasMessage;
        if (message.isCanvasOpen()) {
          if (result === undefined) {
            result = [];
          }
          result.push(message);
        }
      }
      index--;
    }
    return result;
  }

  findOpenNuroCanvasMessageById(
    canvasMsgId: string,
    messageList: Optional<NuroMessage[]>,
  ): Optional<NuroCanvasMessage> {
    if (messageList === undefined) {
      TSNConsole.log(" message list is empty! ");
      return undefined;
    }
    let result: Optional<NuroCanvasMessage> = undefined;
    let index = this.messages.length - 1;

    while (index >= 0) {
      if (this.messages[index] instanceof NuroCanvasMessage) {
        let message = this.messages[index] as NuroCanvasMessage;
        if (message.id === canvasMsgId) {
          result = message;
          break;
        }
      }
      index--;
    }
    return result;
  }

  findLastUserMessage(): Optional<NuroUserMessage> {
    let length = this.messages.length;

    for (let i = length - 1; i >= 0; i = i - 1) {
      let msg = this.messages[i];
      if (msg instanceof NuroUserMessage) {
        return msg;
      }
    }
    return undefined;
  }
}
