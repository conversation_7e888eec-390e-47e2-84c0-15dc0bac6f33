import {
  NuroAssistantMessage,
  NuroMessage,
  NuroTaskMessageType,
  NuroToolCallMessage,
  NuroUserMessage,
  NuroCanvasMessage,
  NuroCanvasStatus,
} from "./message";
import { ChatToolCallType } from "../idl/chat_message";
import { TSNOpenClass } from "@byted/tsnfoundation";
import { NuroSetting } from "./setting";

export enum NuroMessageOp {
  add = "add",
  update = "update",
  delete = "delete",
}

export enum NuroTaskOp {
  add = "add",
  update = "update",
  delete = "delete",
}

export enum NuroTaskStatus {
  none = "none",
  running = "running",
  finished = "finished",
  failed = "failed",
  cancelled = "cancelled",
}

export class NuroMessageOut {
  message: NuroMessage;
  messageOp: NuroMessageOp;
  constructor(message: NuroMessage, op: NuroMessageOp) {
    this.message = message;
    this.messageOp = op;
  }
}

export class NuroTaskOut {
  task: NuroTask;
  taskOp: NuroTaskOp;
  messages: NuroMessageOut[] = [];

  constructor(task: NuroTask, taskOp: NuroTaskOp) {
    this.task = task;
    this.taskOp = taskOp;
  }

  updateMessage(messages: NuroMessageOut[]) {
    messages.forEach((msg) => {
      if (this.task.containMessage(msg.message)) {
        this.messages.push(msg);
      } else {
        this.messages.push(
          new NuroMessageOut(msg.message, NuroMessageOp.delete),
        );
      }
    });
  }
}

export class NuroTask {
  /**
   * 任务ID
   */
  taskId: string;

  /**
   * 任务状态
   */
  taskStatus: NuroTaskStatus = NuroTaskStatus.none;

  /**
   * 消息哈希
   * 用于判断消息是否发生了变化
   */
  messageHash: string = "";

  /**
   * 用户输入的问题
   * 可能包含多个 UserMessage
   */
  promptMessages: Array<NuroMessage> = [];

  /**
   * 中间消息
   * 可能包含多个 ReasoningMessage、AssistantMessage、ToolCallMessage
   * 这些消息表示了生成产物的过程
   */
  middlewareMessages: Array<NuroMessage> = [];

  /**
   * 产物消息
   * 可能包含多个 AssistantMessage、ToolCallMessage
   */
  artifactMessages: Array<NuroMessage> = [];

  /**
   *
   * 屏蔽掉的消息信息
   */
  shieldMessages: Array<NuroMessage> = [];

  constructor(taskId: string) {
    this.taskId = taskId;
  }

  recalculateMessageHash() {
    let messageHashPart = this.promptMessages
      .concat(this.middlewareMessages)
      .concat(this.shieldMessages)
      .concat(this.artifactMessages);

    const messageHash = messageHashPart
      .map((it) => it.id + "_" + it.updated.toString(10))
      .join(",");
    this.messageHash = messageHash;
  }

  displayingMiddlewareMessages(): Array<NuroMessage> {
    let addFirstAssistant: boolean = false;
    return this.middlewareMessages.filter((msg: NuroMessage) => {
      if (msg instanceof NuroAssistantMessage) {
        if (addFirstAssistant === false) {
          addFirstAssistant = true;
          return true;
        }
      } else if (msg instanceof NuroToolCallMessage) {
        if (msg.toolType === ChatToolCallType.client_function.valueOf()) {
          return true;
        }
      }
      return false;
    });
  }

  isDisplayingMiddlewareMessage(msg: NuroMessage): boolean {
    let showMsgs: Array<NuroMessage> = this.displayingMiddlewareMessages();
    return showMsgs.some((it: NuroMessage) => {
      it.id === msg.id;
    });
  }

  addMessage(msg: NuroMessage, taskMsgType: NuroTaskMessageType) {
    msg._task = this;
    msg.taskMessageType = taskMsgType;
    if (taskMsgType === NuroTaskMessageType.promptMessage) {
      this.promptMessages.push(msg);
    } else if (taskMsgType === NuroTaskMessageType.middlewareMessage) {
      this.middlewareMessages.push(msg);
    } else if (taskMsgType === NuroTaskMessageType.artifactMessage) {
      this.artifactMessages.push(msg);
    } else if (taskMsgType === NuroTaskMessageType.shieldMessage) {
      this.shieldMessages.push(msg);
    }
  }

  containMessage(msg: NuroMessage): boolean {
    for (let i = 0; i < this.promptMessages.length; i++) {
      if (this.promptMessages[i]?.id === msg.id) {
        return true;
      }
    }

    for (let i = 0; i < this.middlewareMessages.length; i++) {
      if (this.middlewareMessages[i]?.id === msg.id) {
        return true;
      }
    }

    for (let i = 0; i < this.artifactMessages.length; i++) {
      if (this.artifactMessages[i]?.id === msg.id) {
        return true;
      }
    }

    for (let i = 0; i < this.shieldMessages.length; i++) {
      if (this.shieldMessages[i]?.id === msg.id) {
        return true;
      }
    }

    return false;
  }

  needResume(): boolean {
    return this.taskStatus === NuroTaskStatus.running;
  }
}

@TSNOpenClass
export class NuroTaskChecker {
  public isPromptMessage(message: NuroMessage): boolean {
    return message instanceof NuroUserMessage;
  }

  public isArtifactMessage(message: NuroMessage): boolean {
    return !(message instanceof NuroUserMessage);
  }

  public isShieldMessage(message: NuroMessage): boolean {
    if (message instanceof NuroToolCallMessage) {
      return (
        NuroSetting.shieldToolCall.indexOf(
          (message as NuroToolCallMessage).toolName,
        ) >= 0
      );
    }
    return false;
  }
}

export class NuroCanvasChecker {
  public isNuroCanvasMessage(message: NuroToolCallMessage): boolean {
    return false;
  }
}
