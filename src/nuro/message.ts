import type { NuroConversationManager } from "./conversation_manager";
import { HttpTransport } from "../transport/http_transport";
import {
  TSNJSON,
  TSNNumberConverter,
  TSNSerializable,
} from "@byted/tsnfoundation";
import {
  ChatMessageMetadata,
  ChatMessageStatus,
  ChatToolCallType,
} from "../idl/chat_message";
import {
  MCPToolCallResult,
  MCPToolCallTextContent,
} from "../mcp/mcp_tool_result";
import { EventStreamAdapter } from "../transport/sse_impl";
import type { NuroTask } from "./task";
import { NuroMockManager } from "./mocker";

export enum ChatMessageError {
  /**
   * 推流前异常错误
   * 推流失败，比如第一条事件就推送失败了
   */
  interrupt_status = "interrupt_status",
  /**
   * 推流前异常错误
   * pe策略失败
   */
  pe_policy_failed_status = "pe_policy_failed_status",
  /**
   * 推流前异常错误
   * 获取流失败
   */
  chat_stream_failed_status = "chat_stream_failed_status",

  /**
   * 安全审核拦截
   * 输入文本审核拦截
   */
  input_text_block_status = "input_text_block_status",
  /**
   * 安全审核拦截
   * 输出文本审核拦截
   */
  output_text_block_status = "output_text_block_status",

  /**
   * 推流异常状态
   * 推送思考内容截止
   */
  send_reasoning_content_stop_status = "send_reasoning_content_stop_status",
  /**
   * 推流异常状态
   * 推送内容截止
   */
  send_content_stop_status = "send_content_stop_status",

  /**
   * sdk返回值错误
   * sse请求错误
   */
  send_failed = "send_failed",

  /**
   * sdk返回值错误
   * 上传文件失败
   */
  upload_failed = "upload_failed",
}

export enum NuroUserMessageStatus {
  /**
   * 刚创建状态
   */
  none = "none",
  /**
   * 正在上传图片/视频
   */
  uploading_files = "uploading_files",
  /**
   * 正在发送
   */
  sending = "sending",
  /**
   * 收到返回的 AssistantMessage
   * 收到一点点立即变状态，不会等AssistantMessage完整收到再改变改状态
   */
  finished_successfully = "finished_successfully",

  /**
   * 失败
   */
  failed = "failed",

  /**
   * 取消
   */
  cancelled = "cancelled",
}

export enum NuroAssistantMessageStatus {
  /**
   * 刚创建状态
   */
  none = "none",
  /**
   * 正在回传数据流
   */
  streaming = "streaming",
  /**
   * 数据流返回完成
   */
  finished_successfully = "finished_successfully",

  /**
   * 失败
   */
  failed = "failed",
  /**
   * 取消
   */
  cancelled = "cancelled",
}

export enum NuroReasoningMessageStatus {
  /**
   * 刚创建状态
   */
  none = "none",
  /**
   * 正在回传数据流
   */
  streaming = "streaming",
  /**
   * 数据流返回完成
   */
  finished_successfully = "finished_successfully",
  /**
   * 失败
   */
  failed = "failed",
  /**
   * 取消
   */
  cancelled = "cancelled",
}

export enum NuroToolCallMessageStatus {
  /**
   * 刚创建状态
   */
  none = "none",

  /**
   * 工具参数正在流式传输中，上层业务不应允许用户操作本工具。
   */
  streaming = "streaming",

  /**
   * 流式传输失败，可能是 LLM 或 Host Agent 或网络异常原因。
   */
  streaming_failed = "streaming_failed",

  /**
   * 流式传输取消，可能是用户主动取消
   */
  streaming_cancelled = "streaming_cancelled",

  /**
   * 正在调用本地或远端方法
   * 此处执行过程中，toolResult 有可能被流式更新。
   */
  invoking = "invoking",

  /**
   * 本地方法无法直接返回结果
   * 等 sendToolResultMessage 方法调用返回结果
   */
  wait_user_response = "wait_user_response",

  /**
   * 用户发送其他消息，不再通过sendToolResultMessage 回调结果
   */
  skipped = "skipped",

  /**
   * 方法成功回调结果
   */
  finished_successfully = "finished_successfully",
}

export enum NuroMessageType {
  user = "user",
  assistant = "assistant",
  reasoning = "reasoning",
  toolcall = "tool_call",
  canvas = "canvas",
}

/**
 *  PayLoadData
 */
export class PayLoadData extends TSNSerializable {
  @TSNJSON("conversation_id") conversationId: string = "";
  @TSNJSON("message_id") messageId: string = "";
  @TSNJSON("payload") payload: string = ""; //传submit_id
}

/**
 *  InterruptData
 */
export class InterruptData extends TSNSerializable {
  @TSNJSON("conversation_id") conversationId: string = "";
  @TSNJSON("message_id") messageId: string = "";
}

/**
 *  ResumeData
 */
export class ResumeData extends TSNSerializable {
  @TSNJSON("conversation_id") conversationId: string = "";
  @TSNJSON("message_id") messageId: string = "";
  @TSNJSON("from_first_message") from_first_message?: boolean = true;
}

export enum NuroTaskMessageType {
  promptMessage = "promptMessage",
  middlewareMessage = "middlewareMessage",
  artifactMessage = "artifactMessage",
  shieldMessage = "shieldMessage",
}

export class NuroMessage {
  id: string;
  type: string;
  updated: Int = 0;
  createTime: Int64 = 0;
  errorMsg?: string;
  metadata: ChatMessageMetadata = new ChatMessageMetadata();
  taskMessageType?: NuroTaskMessageType;
  // 此消息是否为最后一轮
  endTurn?: boolean;

  /**
   * 原始的消息 id，用于标识服务端消息的唯一性，请勿在前端使用！！！
   */
  _rawId: string = "";
  /**
   * 会话管理器，用于发送消息，上层业务勿用。
   */
  _conversationManager: Weak<NuroConversationManager>;

  /**
   * 消息对应的task
   */
  _task: Weak<NuroTask>;

  /**
   * 消息索引值，用于排序
   */
  _messageIndex: Int = 0;

  constructor(id: string, type: string) {
    this.id = id;
    this.type = type;
    this.updated = 0;
  }

  setMessagePayload(
    payload: string,
    successCallback: (result: string) => void,
    failCallback: (code: string, reason?: string) => void,
  ): void {
    if (EventStreamAdapter.payloadEndpoint === undefined) {
      return;
    }
    this.metadata.payload = payload;
    let payLoadData: PayLoadData = new PayLoadData();
    payLoadData.messageId = this._rawId;
    payLoadData.conversationId =
      this._conversationManager?.conversation.conversationId ?? "";
    payLoadData.payload = payload;

    new HttpTransport().sendRequest(
      EventStreamAdapter.payloadEndpoint,
      payLoadData.toJSONString() ?? "",
      {},
      successCallback,
      (code: string, reason?: string) => {
        failCallback(code, reason);
      },
    );
  }

  baseCopy(message: NuroMessage): NuroMessage {
    message.updated = this.updated;
    message.createTime = this.createTime;
    message.errorMsg = this.errorMsg;
    message.metadata = this.metadata;
    message._rawId = this._rawId;
    message._conversationManager = this._conversationManager;
    message.taskMessageType = this.taskMessageType;
    message._task = this._task;
    message.endTurn = this.endTurn;
    return message;
  }

  copy(): NuroMessage {
    const message = new NuroMessage(this.id, this.type);
    this.baseCopy(message);
    return message;
  }

  isDisplay(): boolean {
    if (this.taskMessageType === NuroTaskMessageType.middlewareMessage) {
      let show = this._task?.isDisplayingMiddlewareMessage(this) ?? true;
      return show;
    } else {
      return true;
    }
  }

  isFinalStatus(): boolean {
    return true;
  }

  isCancelledStatus(): boolean {
    return true;
  }

  isFailedStatus(): boolean {
    return true;
  }

  isEqualToObject(message: any): boolean {
    if (message instanceof NuroMessage) {
      return (
        this.id === message.id &&
        this.type === message.type &&
        this.createTime === message.createTime &&
        this.errorMsg === message.errorMsg &&
        this.metadata.isEqualToObject(message.metadata) === true &&
        this.taskMessageType === message.taskMessageType &&
        this.endTurn === message.endTurn
      );
    }
    return false;
  }

  /**
   * 获取这条消息 对应的 这轮回话里面的 相关消息（包含 NuroUserMessage）
   */
  getMessageGroup(): NuroMessage[] {
    let msgs: NuroMessage[] =
      this._conversationManager?.conversation.messages ?? [];
    let targetIndex = -1;
    for (let i = 0; i < msgs.length; i = i + 1) {
      let id = msgs[i]?.id ?? "";
      if (id === this.id) {
        targetIndex = i;
        break;
      }
    }
    if (targetIndex === -1) return [];

    // 向前查找：包含第一个遇到的 type='a'
    let startIndex = 0;
    for (let i = targetIndex; i >= 0; i = i - 1) {
      if (msgs[i] instanceof NuroUserMessage) {
        startIndex = i;
        break;
      }
      // 若到头部仍未找到 a，从索引 0 开始
      if (i === 0) startIndex = 0;
    }

    // 向后查找：不包含第一个遇到的 type='a'
    let endIndex = msgs.length;
    for (let i = targetIndex + 1; i < msgs.length; i = i + 1) {
      if (msgs[i] instanceof NuroUserMessage) {
        endIndex = i;
        break;
      }
    }

    return msgs.slice(startIndex, endIndex);
  }

  needResume(): boolean {
    if (this === undefined) {
      return false;
    }

    if (this instanceof NuroUserMessage) {
      return false;
    }

    return !this.isFinalStatus();
  }

  getResumeMsgId(): string {
    return this._rawId;
  }
}

export enum NuroFileType {
  image = "image",
  video = "video",
}

export class NuroLocalFile {
  localPath: string;
  localFileObject?: any;
  constructor(localPath: string, localFileObject?: any) {
    this.localPath = localPath;
    this.localFileObject = localFileObject;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroLocalFile) {
      return this.localPath === other.localPath;
    }
    return false;
  }
}

export class NuroImageMetadata {
  width?: Int;
  height?: Int;
  format?: string;
  prompt?: string;

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroImageMetadata) {
      return (
        this.width === other.width &&
        this.height === other.height &&
        this.format === other.format &&
        this.prompt === other.prompt
      );
    }
    return false;
  }
}

export class NuroFileMetadata {
  // 图片
  image_metadata?: NuroImageMetadata;

  constructor(imageMetadata: NuroImageMetadata) {
    this.image_metadata = imageMetadata;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroFileMetadata) {
      if (
        this.image_metadata === undefined &&
        other.image_metadata === undefined
      ) {
        return true;
      } else if (
        this.image_metadata !== undefined &&
        other.image_metadata !== undefined
      ) {
        return (
          this.image_metadata?.isEqualToObject(other.image_metadata) === true
        );
      } else {
        return false;
      }
    }
    return false;
  }
}

export class NuroFile {
  /**
   * image, video, etc.
   */
  type: NuroFileType;
  /**
   * url of the file.
   */
  url?: string;
  /**
   * uri of the file.
   */
  uri?: string;
  /**
   *
   */
  extra?: Record<string, any>;
  /**
   * 本地文件信息，当文件被用户选择后，并且未被上传时，会有该字段。
   * 该信息将透传至 TOS Uploader 方法，用于上传文件，文件上传后，url 字段会被更新。
   */
  localFile?: NuroLocalFile;
  /**
   * 增加imagemeta信息
   */
  mimeType?: string;

  /**
   * 文件描述, 业务需要添加的描述，可以用来描述相对关系
   */
  file_description?: string;

  /**
   * metadata, 用来存prompt等信息
   */
  metadata?: NuroFileMetadata;

  constructor(
    type: NuroFileType,
    url?: string,
    mimeType?: string,
    localFile: Optional<NuroLocalFile> = undefined,
    file_description?: string,
  ) {
    this.type = type;
    this.url = url;
    this.mimeType = mimeType;
    this.localFile = localFile;
    this.file_description = file_description;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroFile) {
      let localFileEqual = false;
      if (this.localFile !== undefined && other.localFile !== undefined) {
        localFileEqual =
          this.localFile.isEqualToObject(other.localFile) === true;
      } else if (
        this.localFile === undefined &&
        other.localFile === undefined
      ) {
        localFileEqual = true;
      }
      let localMetadataEqual = false;
      if (this.metadata !== undefined && other.metadata !== undefined) {
        localMetadataEqual =
          this.metadata.isEqualToObject(other.metadata) === true;
      } else if (this.metadata === undefined && other.metadata === undefined) {
        localMetadataEqual = true;
      }

      return (
        this.type === other.type &&
        this.url === other.url &&
        this.uri === other.uri &&
        this.file_description === other.file_description &&
        this.mimeType === other.mimeType &&
        localMetadataEqual &&
        localFileEqual
      );
    }
    return false;
  }
}

export enum ReferenceRole {
  User,
  Assistant,
}

export class RefContent {
  text?: string;
  file?: NuroFile;

  constructor(text?: string, file?: NuroFile) {
    this.text = text;
    this.file = file;
  }
}

export class NuroUserMessage extends NuroMessage {
  text?: string;
  files?: NuroFile[];
  messageStatus: NuroUserMessageStatus;
  referenceInfo?: RefContent[];
  constructor(
    id: string,
    text?: string,
    files: Optional<NuroFile[]> = undefined,
    messageStatus: NuroUserMessageStatus = NuroUserMessageStatus.none,
    referenceInfo: Optional<RefContent[]> = undefined,
  ) {
    super(id, NuroMessageType.user);
    this.text = text;
    this.files = files;
    this.createTime = TSNNumberConverter.toInt64(Date.now());
    this.messageStatus = messageStatus;
    this.referenceInfo = referenceInfo;
  }

  copy(): NuroMessage {
    const message = new NuroUserMessage(
      this.id,
      this.text,
      this.files,
      this.messageStatus,
      this.referenceInfo,
    );
    this.baseCopy(message);
    return message;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroUserMessage) {
      let filesEqual = true;
      if (
        this.files !== undefined &&
        other.files !== undefined &&
        this.files.length === other.files.length
      ) {
        for (let i = 0; i < this.files.length; i++) {
          if (this.files[i]!.isEqualToObject(other.files[i]!) !== true) {
            filesEqual = false;
            break;
          }
        }
      } else if (this.files?.length !== other.files?.length) {
        filesEqual = false;
      }

      return (
        super.isEqualToObject(other) === true &&
        this.text === other.text &&
        this.messageStatus === other.messageStatus &&
        filesEqual
      );
    }
    return false;
  }

  setMsgStatus(status: NuroUserMessageStatus): void {
    let newmsg = this.copy();
    if (newmsg instanceof NuroUserMessage) {
      newmsg.messageStatus = status;
    }
    this._conversationManager?.receivedMessage(newmsg);
  }

  isFinalStatus(): boolean {
    return (
      this.messageStatus === NuroUserMessageStatus.finished_successfully ||
      this.messageStatus === NuroUserMessageStatus.failed ||
      this.messageStatus === NuroUserMessageStatus.cancelled
    );
  }

  isCancelledStatus(): boolean {
    return this.messageStatus === NuroUserMessageStatus.cancelled;
  }

  isFailedStatus(): boolean {
    return this.messageStatus === NuroUserMessageStatus.failed;
  }
}

export class NuroReasoningMessage extends NuroMessage {
  text: string;
  messageStatus: NuroReasoningMessageStatus;
  constructor(
    id: string,
    text: string,
    messageStatus: NuroReasoningMessageStatus = NuroReasoningMessageStatus.none,
  ) {
    super(id, NuroMessageType.reasoning);
    this.text = text;
    this.messageStatus = messageStatus;
  }

  copy(): NuroMessage {
    const message = new NuroReasoningMessage(
      this.id,
      this.text,
      this.messageStatus,
    );
    this.baseCopy(message);
    return message;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroReasoningMessage) {
      return (
        super.isEqualToObject(other) === true &&
        this.text === other.text &&
        this.messageStatus === other.messageStatus
      );
    }
    return false;
  }

  isFinalStatus(): boolean {
    return (
      this.messageStatus === NuroReasoningMessageStatus.finished_successfully ||
      this.messageStatus === NuroReasoningMessageStatus.failed ||
      this.messageStatus === NuroReasoningMessageStatus.cancelled
    );
  }

  isCancelledStatus(): boolean {
    return this.messageStatus === NuroReasoningMessageStatus.cancelled;
  }

  isFailedStatus(): boolean {
    return this.messageStatus === NuroReasoningMessageStatus.failed;
  }

  setMsgStatus(status: NuroReasoningMessageStatus): void {
    let newmsg = this.copy();
    if (newmsg instanceof NuroReasoningMessage) {
      newmsg.messageStatus = status;
    }
    this._conversationManager?.receivedMessage(newmsg);
  }

  setStatus(status?: ChatMessageStatus): void {
    if (status === undefined) {
      return;
    }
    switch (status) {
      case ChatMessageStatus.finished_successfully: {
        this.messageStatus = NuroReasoningMessageStatus.finished_successfully;
        break;
      }
      case ChatMessageStatus.in_progress: {
        this.messageStatus = NuroReasoningMessageStatus.streaming;
        break;
      }
      default: {
        this.messageStatus = NuroReasoningMessageStatus.failed;
        this.errorMsg = status.valueOf();
        break;
      }
    }
  }
}

export class NuroAssistantMessage extends NuroMessage {
  name?: string;
  text?: string;
  files?: NuroFile[];
  messageStatus: NuroAssistantMessageStatus;
  relateToolCalls: Array<NuroToolCallMessage> = [];
  constructor(
    id: string,
    name?: string,
    text?: string,
    files: Optional<NuroFile[]> = undefined,
    messageStatus: NuroAssistantMessageStatus = NuroAssistantMessageStatus.none,
  ) {
    super(id, NuroMessageType.assistant);
    this.text = text;
    this.files = files;
    this.name = name;
    this.messageStatus = messageStatus;
  }

  copy(): NuroMessage {
    const message = new NuroAssistantMessage(
      this.id,
      this.name,
      this.text,
      this.files,
      this.messageStatus,
    );
    message.relateToolCalls = this.relateToolCalls;
    this.baseCopy(message);
    return message;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroAssistantMessage) {
      let filesEqual = true;
      if (
        this.files !== undefined &&
        other.files !== undefined &&
        this.files.length === other.files.length
      ) {
        for (let i = 0; i < this.files.length; i++) {
          if (this.files[i]!.isEqualToObject(other.files[i]!) !== true) {
            filesEqual = false;
            break;
          }
        }
      } else if (this.files?.length !== other.files?.length) {
        filesEqual = false;
      }

      return (
        super.isEqualToObject(other) === true &&
        this.name === other.name &&
        this.text === other.text &&
        this.messageStatus === other.messageStatus &&
        filesEqual
      );
    }
    return false;
  }

  setMsgStatus(status: NuroAssistantMessageStatus): void {
    let newmsg = this.copy();
    if (newmsg instanceof NuroAssistantMessage) {
      newmsg.messageStatus = status;
    }
    this._conversationManager?.receivedMessage(newmsg);
  }

  isFinalStatus(): boolean {
    return (
      this.messageStatus === NuroAssistantMessageStatus.finished_successfully ||
      this.messageStatus === NuroAssistantMessageStatus.failed ||
      this.messageStatus === NuroAssistantMessageStatus.cancelled
    );
  }

  isCancelledStatus(): boolean {
    return this.messageStatus === NuroAssistantMessageStatus.cancelled;
  }

  isFailedStatus(): boolean {
    return this.messageStatus === NuroAssistantMessageStatus.failed;
  }

  setStatus(status?: ChatMessageStatus): void {
    if (status === undefined) {
      return;
    }
    switch (status) {
      case ChatMessageStatus.finished_successfully: {
        this.messageStatus = NuroAssistantMessageStatus.finished_successfully;
        break;
      }
      case ChatMessageStatus.in_progress: {
        this.messageStatus = NuroAssistantMessageStatus.streaming;
        break;
      }
      default: {
        this.messageStatus = NuroAssistantMessageStatus.failed;
        this.errorMsg = status.valueOf();
        break;
      }
    }
  }

  // 标识是否是对话结束
  isFinalTurn(): boolean {
    if (this.endTurn === true || this.metadata?.end_turn === true) {
      return true;
    }
    return false;
  }
}

export class NuroToolCallMessage extends NuroMessage {
  /**
   * functionType: server_function, client_function.
   */
  toolCallId: string;
  toolType: string;
  toolName: string; // text2image_form
  toolArgs?: string; // <- json
  toolResult?: string;
  toolExtra?: Record<string, string>;
  messageStatus: NuroToolCallMessageStatus;

  constructor(
    id: string,
    toolCallId: string,
    toolType: string,
    toolName: string,
    toolArgs?: string,
    toolExtra?: Record<string, string>,
    toolResult?: string,
    messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none,
  ) {
    super(id, NuroMessageType.toolcall);
    this.toolCallId = toolCallId;
    this.toolType = toolType;
    this.toolName = toolName;
    this.toolArgs = toolArgs;
    this.toolExtra = toolExtra;
    this.toolResult = toolResult;
    this.messageStatus = messageStatus;
    if (toolResult !== undefined && toolResult.length > 0) {
      const toolCallResult = new MCPToolCallResult({ JSONString: toolResult });
      if (
        toolCallResult.content.length > 0 &&
        this.messageStatus !== NuroToolCallMessageStatus.invoking
      ) {
        this.messageStatus = NuroToolCallMessageStatus.finished_successfully;
      }
    }
  }

  sendToolCallResult(
    result: string,
    userMessage: Optional<NuroUserMessage> = undefined,
  ): void {
    this.toolResult = result;
    if (
      this.toolResult !== undefined &&
      this.toolResult !== "" &&
      this._conversationManager !== undefined
    ) {
      this.setMsgStatus(NuroToolCallMessageStatus.finished_successfully);
    }
    this._conversationManager?.sendToolResultMessage(this, userMessage);
  }

  sendToolCallResultFromMCPFormat(
    callResult: MCPToolCallResult,
    userMessage: Optional<NuroUserMessage> = undefined,
  ): void {
    if (NuroMockManager.isMocking()) {
      return;
    }
    this.sendToolCallResult(callResult.toJSONString() ?? "", userMessage);
  }

  decodeToolCallResultAsPlainText(): Optional<string> {
    if (this.toolResult !== undefined) {
      if (this.toolResult.trim().indexOf("{") === 0) {
        const formattedResult = this.decodeToolCallResultToMCPFormat();
        let content = "";
        for (let index = 0; index < formattedResult.content.length; index++) {
          const element = formattedResult.content[index];
          if (element instanceof MCPToolCallTextContent) {
            content += element.text;
          }
        }
        return content;
      } else {
        return this.toolResult;
      }
    } else {
      return undefined;
    }
  }

  decodeToolCallResultToMCPFormat(): MCPToolCallResult {
    return new MCPToolCallResult({ JSONString: this.toolResult ?? "{}" });
  }

  setMsgStatus(status: NuroToolCallMessageStatus): void {
    let newmsg = this.copy();
    if (newmsg instanceof NuroToolCallMessage) {
      newmsg.messageStatus = status;
    }
    this._conversationManager?.receivedMessage(newmsg);
  }

  isFinalStatus(): boolean {
    return (
      this.messageStatus === NuroToolCallMessageStatus.skipped ||
      this.messageStatus === NuroToolCallMessageStatus.finished_successfully ||
      this.messageStatus === NuroToolCallMessageStatus.streaming_failed ||
      this.messageStatus === NuroToolCallMessageStatus.streaming_cancelled
    );
  }

  isCancelledStatus(): boolean {
    return this.messageStatus === NuroToolCallMessageStatus.streaming_cancelled;
  }

  isFailedStatus(): boolean {
    return this.messageStatus === NuroToolCallMessageStatus.streaming_failed;
  }

  copy(): NuroToolCallMessage {
    const message = new NuroToolCallMessage(
      this.id,
      this.toolCallId,
      this.toolType,
      this.toolName,
      this.toolArgs,
      this.toolExtra,
      this.toolResult,
      this.messageStatus,
    );
    this.baseCopy(message);
    return message;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroToolCallMessage) {
      return (
        super.isEqualToObject(other) === true &&
        this.toolCallId === other.toolCallId &&
        this.toolType === other.toolType &&
        this.toolName === other.toolName &&
        this.toolArgs === other.toolArgs &&
        this.toolExtra === other.toolExtra &&
        this.toolResult === other.toolResult &&
        this.messageStatus === other.messageStatus &&
        this.endTurn === other.endTurn
      );
    }
    return false;
  }

  isClientToolSkipped(): boolean {
    if (this.toolType === ChatToolCallType.client_function) {
      return this.messageStatus === NuroToolCallMessageStatus.skipped;
    }
    return false;
  }
}

export const CANVAS_DEFAULT: Int = -10000;
export const CANVAS_ADD_TO_END: Int = -10001;

export function getNodeFromNuroToolCallMsg(
  id: string,
  nodeIndex: Int,
  toolCall: NuroToolCallMessage,
): NuroCanvasNode {
  const node = new NuroCanvasNode(
    id,
    nodeIndex,
    toolCall.toolCallId,
    toolCall.toolType,
    toolCall.toolName,
    toolCall.toolArgs,
    toolCall.toolExtra,
    toolCall.toolResult,
    toolCall.messageStatus,
  );
  node.nodeIndex = nodeIndex;
  node._rawId = toolCall._rawId ?? "";
  node._conversationManager = toolCall._conversationManager;
  node.createTime = toolCall.createTime ?? 0;
  node._messageIndex = toolCall._messageIndex;
  node.messageStatus = toolCall.messageStatus;
  node.endTurn = toolCall.endTurn;
  return node;
}

// NuroCanvasNode，代表中间插入的节点信息
export class NuroCanvasNode extends NuroToolCallMessage {
  nodeIndex: Int = CANVAS_DEFAULT; // 标识插入节点的相对位置

  constructor(
    id: string,
    nodeIndex: Int,
    toolCallId: string,
    toolType: string,
    toolName: string,
    toolArgs?: string,
    toolExtra?: Record<string, string>,
    toolResult?: string,
    messageStatus: NuroToolCallMessageStatus = NuroToolCallMessageStatus.none,
  ) {
    super(
      id,
      toolCallId,
      toolType,
      toolName,
      toolArgs,
      toolExtra,
      toolResult,
      messageStatus,
    );
    this.nodeIndex = nodeIndex;
  }

  copy(): NuroCanvasNode {
    const message = new NuroCanvasNode(
      this.id,
      this.nodeIndex,
      this.toolCallId,
      this.toolType,
      this.toolName,
      this.toolArgs,
      this.toolExtra,
      this.toolResult,
      this.messageStatus,
    );
    this.baseCopy(message);
    return message;
  }

  isCancelledStatus(): boolean {
    // 生成节点没有取消状态
    return super.isCancelledStatus();
  }

  isFailedStatus(): boolean {
    // 生成节点没有fail状态
    return super.isFailedStatus();
  }

  isFinalStatus(): boolean {
    return super.isFinalStatus();
  }

  getResumeMsgId(): string {
    return this._rawId;
  }

  isEqualToObject(other: any): boolean {
    if (other instanceof NuroCanvasNode) {
      return (
        super.isEqualToObject(other) === true &&
        this.nodeIndex === other.nodeIndex
      );
    }
    return false;
  }
}

export enum NuroCanvasStatus {
  none,
  init,
  streaming,
  cancel, // 用户取消
  end,
}

export class NuroCanvasMessage extends NuroMessage {
  status: NuroCanvasStatus = NuroCanvasStatus.none;
  startNode?: NuroToolCallMessage;
  endNode?: NuroToolCallMessage;
  // 标识对应的图片
  nodes?: NuroCanvasNode[] = [];

  constructor(id: string) {
    super(id, NuroMessageType.canvas);
  }

  updateStartNode(node: NuroToolCallMessage) {
    this.startNode = node;
  }

  updateCanvasStatus(status: NuroCanvasStatus) {
    this.status = status;
    this.setMsgStatus(this.status);
  }

  updateEndNode(node: NuroToolCallMessage) {
    this.endNode = node;
  }

  /*
   ** 增加一个finish接口，因为收到end的时候，有可能还在streaming
   */
  finish() {
    this.status = NuroCanvasStatus.end;
    this.setMsgStatus(NuroCanvasStatus.end);
  }

  addOrReplaceNode(node: NuroCanvasNode): void {
    // 如果之前的流关闭了，不能再写入,先简单做个判断
    if (this.nodes === undefined) {
      this.nodes = [];
    }

    if (node.nodeIndex > this.nodes?.length) {
      this.nodes.push(node);
    } else {
      if (node.nodeIndex === CANVAS_ADD_TO_END) {
        let length = this.nodes.length;
        this.nodes[length] = node;
      } else {
        let index = node.nodeIndex - 1;
        this.nodes[index] = node;
      }
    }
  }

  findNodeByToolCallId(toolCallId: string): Optional<NuroCanvasNode> {
    if (this.nodes === undefined) {
      return undefined;
    }

    let targetNode: Optional<NuroCanvasNode> = undefined;

    this.nodes.forEach((it) => {
      if (it.toolCallId === toolCallId) {
        targetNode = it;
      }
    });
    return targetNode;
  }

  findEndNode(toolCallId: string): Optional<NuroToolCallMessage> {
    if (this.endNode === undefined) {
      return undefined;
    }
    if (this.endNode.toolCallId === toolCallId) {
      return this.endNode;
    }
    return undefined;
  }

  findStartNode(toolCallId: string): Optional<NuroToolCallMessage> {
    if (this.startNode === undefined) {
      return undefined;
    }
    if (this.startNode.toolCallId === toolCallId) {
      return this.startNode;
    }
    return undefined;
  }

  isCanvasOpen(): boolean {
    return (
      this.status !== NuroCanvasStatus.end &&
      this.status !== NuroCanvasStatus.cancel
    );
  }

  copy(): NuroCanvasMessage {
    const message = new NuroCanvasMessage(this.id);
    message.status = this.status;
    message.nodes = this.nodes?.map((it) => it.copy());
    message.startNode = this.startNode?.copy();
    message.endNode = this.endNode?.copy();
    this.baseCopy(message);
    return message;
  }

  isCancelledStatus(): boolean {
    // 没有cancel状态，生成过程中，交互需要禁止
    return this.status === NuroCanvasStatus.cancel;
  }

  isFailedStatus(): boolean {
    // 画布消息不维护失败状态，由业务自行判断。
    return false;
  }

  isFinalStatus(): boolean {
    return this.status === NuroCanvasStatus.end;
  }

  setMsgStatus(status: NuroCanvasStatus): void {
    this.status = status;
    let newmsg = this.copy();
    if (newmsg instanceof NuroCanvasMessage) {
      newmsg.status = status;
    }
    this._conversationManager?.receivedMessage(newmsg);
  }

  getResumeMsgId(): string {
    let result = "";

    if (this.startNode !== undefined) {
      if (this.startNode.needResume()) {
        result = this.startNode._rawId;
      }
    }
    if (this.nodes !== undefined) {
      this.nodes.forEach((node) => {
        if (node !== undefined) {
          if (node.needResume()) {
            result = node._rawId;
          }
        }
      });
    }
    if (this.endNode !== undefined) {
      if (this.endNode.needResume()) {
        result = this.endNode._rawId;
      }
    }
    // 如果都没有，取第一个startNode
    if (result === "") {
      if (this.startNode !== undefined) {
        result = this.startNode._rawId;
      }
    }

    return result;
  }

  isEqualToObject(message: any): boolean {
    if (message instanceof NuroCanvasMessage) {
      let result =
        super.isEqualToObject(message) === true &&
        this.status === message.status &&
        this.startNode?.isEqualToObject(message.startNode) === true &&
        this.endNode?.isEqualToObject(message.endNode) === true;
      if (result === false) {
        return false;
      }
      if (this.nodes === undefined) {
        if (message.nodes !== undefined) {
          return false;
        } else {
          return true;
        }
      } else if (this.nodes.length === 0) {
        if (message.nodes === undefined) {
          return false;
        } else if (message.nodes.length > 0) {
          return false;
        }
        return true;
      } else {
        if (message.nodes === undefined) {
          return false;
        } else if (message.nodes.length === 0) {
          return false;
        } else if (this.nodes.length !== message.nodes.length) {
          return false;
        } else {
          for (let i = 0; i < this.nodes.length; i++) {
            if (this.nodes[i]?.isEqualToObject(message.nodes[i]) === false) {
              return false;
            }
          }
          return true;
        }
      }
    }
    return false;
  }
}
