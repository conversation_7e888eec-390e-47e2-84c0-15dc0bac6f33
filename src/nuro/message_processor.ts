import {
  ChatMessageError,
  NuroAssistantMessage,
  NuroAssistantMessageStatus,
  NuroFile,
  NuroFileType,
  NuroMessage,
  NuroReasoningMessage,
  NuroReasoningMessageStatus,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUserMessageStatus,
  NuroCanvasMessage,
  NuroCanvasNode,
  RefContent,
  NuroCanvasStatus,
  getNodeFromNuroToolCallMsg,
  NuroImageMetadata,
  NuroFileMetadata,
  CANVAS_DEFAULT,
  CANVAS_ADD_TO_END,
} from "../nuro/message";
import {
  ChatMessage,
  ChatMessageMetadata,
  ChatMessageStatus,
  ChatToolCallType,
} from "../idl/chat_message";
import type { NuroConversationManager } from "./conversation_manager";
import { NuroSetting } from "./setting";
import {
  MCPToolCallResult,
  MCPToolCallTextContent,
} from "../mcp/mcp_tool_result";
import { NuroUtils } from "./utils";
import { TSNConsole } from "@byted/tsnfoundation";

export enum ConvertType {
  new_message = "new_message",
  history = "history",
}

export class MessageProcessor {
  static convertChatMessageToNuroMessage(
    conversationManager: Optional<NuroConversationManager>,
    chatMessage: ChatMessage,
    type: ConvertType,
  ): NuroMessage[] {
    const is_visually_hidden_from_conversation =
      chatMessage.metadata?.is_visually_hidden_from_conversation;
    if (
      is_visually_hidden_from_conversation !== undefined &&
      is_visually_hidden_from_conversation === true
    ) {
      // Server 端要求该消息不展示在会话中，所以不返回任何消息。
      return [];
    }
    const role = chatMessage.author?.role;
    const name = chatMessage.author?.name;
    let msgs: NuroMessage[] = [];
    if (role !== undefined) {
      if (role === "user") {
        let text = "";
        let files: NuroFile[] = [];
        let referenceInfo: RefContent[] = [];
        chatMessage.content?.content_parts?.forEach((it) => {
          if (it.text !== undefined && it.text.length > 0) {
            if (it.is_referenced === true) {
              referenceInfo.push({
                text: it.text,
              });
            } else {
              text = text + it.text.trim();
            }
          }
          if (it.file !== undefined) {
            let file = new NuroFile(NuroFileType.image, it.file.url, undefined);
            file.uri = it.file.uri;
            if (it.file.image_metadata !== undefined) {
              let fileImageMetadata = new NuroImageMetadata();
              fileImageMetadata.width = it.file.image_metadata.image_width;
              fileImageMetadata.height = it.file.image_metadata.image_height;
              fileImageMetadata.prompt = it.file.image_metadata.image_prompt;
              fileImageMetadata.format = it.file.image_metadata.image_format;
              let fileMetadata = new NuroFileMetadata(fileImageMetadata);
              file.metadata = fileMetadata;
            }
            if (it.is_referenced === true) {
              referenceInfo.push({ file: file });
            } else {
              files.push(file);
            }
          }
        });
        let msg = new NuroUserMessage(
          chatMessage.id ?? "",
          text,
          files.length > 0 ? files : undefined,
          undefined,
          referenceInfo.length > 0 ? referenceInfo : undefined,
        );
        msg._conversationManager = conversationManager;
        msg.createTime = chatMessage.create_time ?? 0;
        msg._rawId = chatMessage.id ?? "";
        msg._messageIndex = msgs.length;
        if (chatMessage.status === ChatMessageStatus.in_progress) {
          msg.messageStatus = NuroUserMessageStatus.sending;
        } else {
          msg.messageStatus = NuroUserMessageStatus.finished_successfully;
        }
        if (chatMessage.metadata !== undefined) {
          msg.metadata = chatMessage.metadata;
        }
        msgs.push(msg);
      } else if (role === "assistant") {
        let reasoning = "";
        let text = "";
        let files: NuroFile[] = [];
        chatMessage.content?.content_parts?.forEach((it) => {
          if (
            it.reasoning_content !== undefined &&
            it.reasoning_content.length > 0
          ) {
            reasoning = reasoning + it.reasoning_content;
          }
          if (it.text !== undefined && it.text.length > 0) {
            text = text + it.text.trim();
          }
          if (it.file !== undefined) {
            let file = new NuroFile(NuroFileType.image, it.file.url, undefined);
            file.uri = it.file.uri;
            if (it.file.image_metadata !== undefined) {
              let fileImageMetadata = new NuroImageMetadata();
              fileImageMetadata.width = it.file.image_metadata.image_width;
              fileImageMetadata.height = it.file.image_metadata.image_height;
              fileImageMetadata.prompt = it.file.image_metadata.image_prompt;
              fileImageMetadata.format = it.file.image_metadata.image_format;
              let fileMetadata = new NuroFileMetadata(fileImageMetadata);
              file.metadata = fileMetadata;
            }
            files.push(file);
          }
        });
        if (reasoning.length > 0) {
          let reasoningMsg = new NuroReasoningMessage(
            (chatMessage.id ?? "") + "_reasoning",
            reasoning,
          );
          reasoningMsg._conversationManager = conversationManager;
          reasoningMsg.createTime = chatMessage.create_time ?? 0;
          reasoningMsg._rawId = chatMessage.id ?? "";
          reasoningMsg._messageIndex = msgs.length;
          if (text.length > 0 || files.length > 0) {
            reasoningMsg.messageStatus =
              NuroReasoningMessageStatus.finished_successfully;
          } else {
            reasoningMsg.setStatus(chatMessage.status);
          }
          if (chatMessage.metadata !== undefined) {
            reasoningMsg.metadata = new ChatMessageMetadata({
              JSONString: chatMessage.metadata.toJSONString() ?? "{}",
            });
          }
          msgs.push(reasoningMsg);
        }
        let assistantMsg: Optional<NuroAssistantMessage>;
        let isLastTurnMsg = false;
        if (chatMessage.end_turn === true) {
          isLastTurnMsg = true;
          let canvasMsgList =
            conversationManager?.conversation.findLOpenNuroCanvasMessage(
              msgs,
            ) ??
            conversationManager?.conversation.findLOpenNuroCanvasMessage(
              conversationManager?.conversation.messages,
            );
          if (canvasMsgList !== undefined) {
            canvasMsgList.forEach((it) => {
              it.setMsgStatus(NuroCanvasStatus.end);
              msgs.push(it);
            });
          }
        }
        if (text.length > 0 || files.length > 0 || isLastTurnMsg === true) {
          let msg = new NuroAssistantMessage(
            (chatMessage.id ?? "") + "_assistant",
            name,
            text,
            files.length > 0 ? files : undefined,
          );
          msg._conversationManager = conversationManager;
          msg.createTime = chatMessage.create_time ?? 0;
          msg._rawId = chatMessage.id ?? "";
          msg._messageIndex = msgs.length;
          msg.setStatus(chatMessage.status);
          if (chatMessage.metadata !== undefined) {
            msg.metadata = new ChatMessageMetadata({
              JSONString: chatMessage.metadata.toJSONString() ?? "{}",
            });
          }
          if (isLastTurnMsg) {
            msg.endTurn = true;
          }
          assistantMsg = msg;
          msgs.push(msg);
        }
        chatMessage.tool_calls?.forEach((it) => {
          let itFunc = it._func;
          if (itFunc === undefined) {
            return;
          }
          if (
            it.type === ChatToolCallType.server_function &&
            NuroSetting.needDisplayServerFunctionMessage === false
          ) {
            return;
          }

          let jsonrepairSrc = itFunc.arguments ?? "";
          let jsonrepairResult = NuroUtils.jsonrepair(jsonrepairSrc);
          if (jsonrepairResult === undefined) {
            jsonrepairResult = itFunc._lastRepairedArguments ?? jsonrepairSrc;
          } else if (jsonrepairResult === "") {
            jsonrepairResult = itFunc._lastRepairedArguments ?? jsonrepairSrc;
          } else {
            itFunc._lastRepairedArguments = jsonrepairResult;
          }
          let toolName = itFunc.name ?? "";
          // 先统一转成toolCallMessage
          let toolCall = new NuroToolCallMessage(
            (chatMessage.id ?? "") + "_toolcall_" + (it.id ?? ""),
            it.id ?? "",
            it.type?.valueOf() ?? ChatToolCallType.client_function.valueOf(),
            toolName,
            jsonrepairResult,
            itFunc.extra,
            "",
          );

          toolCall._conversationManager = conversationManager;
          toolCall.createTime = chatMessage.create_time ?? 0;
          toolCall._rawId = chatMessage.id ?? "";
          toolCall._messageIndex = msgs.length;

          if (type === ConvertType.new_message) {
            toolCall.messageStatus = NuroToolCallMessageStatus.invoking;
            if (it.streaming === true) {
              if (
                chatMessage.status ===
                ChatMessageStatus.send_content_stop_status
              ) {
                toolCall.messageStatus =
                  NuroToolCallMessageStatus.streaming_failed;
              } else {
                toolCall.messageStatus = NuroToolCallMessageStatus.streaming;
              }
            }
          } else {
            toolCall.messageStatus = NuroToolCallMessageStatus.skipped;
          }

          // 判断画布
          if (toolName.indexOf(NuroSetting.canvasSettings.startNode) >= 0) {
            // 如果收到一个打开画布的指令，就新建一个画布消息 等到streaming=false的时候，才能新建,否则流失输出会收到多条消息
            let canvasMsgId = (chatMessage.id ?? "") + "_canvas";
            let existCanvasMsg =
              conversationManager?.conversation?.findOpenNuroCanvasMessageById(
                canvasMsgId,
                msgs,
              ) ??
              conversationManager?.conversation?.findOpenNuroCanvasMessageById(
                canvasMsgId,
                conversationManager?.conversation.messages,
              );

            let msg: Optional<NuroCanvasMessage> = undefined;
            if (existCanvasMsg === undefined) {
              msg = new NuroCanvasMessage(canvasMsgId);
              msg._conversationManager = conversationManager;
              msg.createTime = chatMessage.create_time ?? 0;
              msg._rawId = chatMessage.id ?? "";
              msg._messageIndex = msgs.length;
            } else {
              msg = existCanvasMsg.copy();
            }
            if (chatMessage.metadata !== undefined) {
              msg.metadata = new ChatMessageMetadata({
                JSONString: chatMessage.metadata.toJSONString() ?? "{}",
              });
            }
            if (type === ConvertType.history) {
              msg.status = NuroCanvasStatus.streaming;
            } else {
              msg.updateCanvasStatus(NuroCanvasStatus.streaming);
            }
            msg.updateStartNode(toolCall);
            msgs.push(msg);
          } else if (
            toolName.indexOf(NuroSetting.canvasSettings.endNode) >= 0
          ) {
            // 如果收到一个关闭画布的指令，找到最近的NuroCanvasMessage,设置它的status为end
            let msg =
              conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                msgs,
              ) ??
              conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                conversationManager?.conversation.messages,
              );
            if (msg !== undefined) {
              let update_msg = msg.copy() as NuroCanvasMessage;
              msg.endNode = toolCall;
              update_msg.updateEndNode(toolCall);
              msgs.push(update_msg);
            }
          } else if (
            NuroSetting.canvasSettings.nodes.some(
              (node) => node.indexOf(toolName) >= 0,
            )
          ) {
            // 如果收到一个插入内容的指令，找到最近的NuroCanvasMessage,执行插入Node操作
            let msg =
              conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                msgs,
              ) ??
              conversationManager?.conversation.findLastOpenNuroCanvasMessage(
                conversationManager?.conversation.messages,
              );
            if (msg !== undefined) {
              let update_msg = msg.copy() as NuroCanvasMessage;
              let nodeIndex = CANVAS_DEFAULT;
              if (itFunc.arguments !== undefined) {
                if (NuroUtils.isJSONValid(itFunc.arguments)) {
                  nodeIndex =
                    JSON.parse(itFunc.arguments).canvas_content_id ??
                    CANVAS_DEFAULT;
                } else {
                  // 有可能被转义影响到了，处理一下, 临时修复逻辑，单独起一个分支处理，减少影响, 后面随着canvas的升级干掉
                  let repairResult =
                    NuroUtils.completeQuotes(itFunc.arguments) ?? "";
                  if (NuroUtils.isJSONValid(repairResult)) {
                    nodeIndex =
                      JSON.parse(itFunc.arguments).canvas_content_id ??
                      CANVAS_DEFAULT;
                  }
                }
              }
              if (nodeIndex !== CANVAS_DEFAULT) {
                const targetNode = getNodeFromNuroToolCallMsg(
                  toolCall.id,
                  nodeIndex,
                  toolCall,
                );

                update_msg.addOrReplaceNode(targetNode);
                msgs.push(update_msg);
              } else {
                // 历史消息中，可能用户主动interrupt了，导致toolArgs不能解析出完整数据，但是还是需要update这个tool,并且把msg 关掉
                // 此时这个节点不需要展示
                if (type === ConvertType.history) {
                  const node = getNodeFromNuroToolCallMsg(
                    toolCall.id,
                    CANVAS_ADD_TO_END,
                    toolCall,
                  );
                  update_msg.addOrReplaceNode(node);
                  msgs.push(update_msg);
                }
              }
            } else {
              assistantMsg?.relateToolCalls.push(toolCall);
              msgs.push(toolCall);
            }
          } else {
            // 没有可用画布，直接作为toolCall
            assistantMsg?.relateToolCalls.push(toolCall);
            msgs.push(toolCall);
          }
        });
      } else if (role === "tool") {
        let content_parts = chatMessage.content?.content_parts ?? [];
        let toolCallId = chatMessage.metadata?.tool_call_id;
        let canvasMessageList =
          conversationManager?.conversation.findLOpenNuroCanvasMessage(msgs) ??
          conversationManager?.conversation.findLOpenNuroCanvasMessage(
            conversationManager?.conversation.messages,
          );
        let len = content_parts.length;
        if (len > 0) {
          if (toolCallId !== undefined) {
            let part = content_parts[0];
            // 确认一下是否为画布工具，如果是画布的工具，需要到画布的消息中去更新
            if (part !== undefined) {
              let toolResult: Optional<string> = part.text;
              // 找到对应的工具调用消息，更新它的结果
              let updateCanvasMsg: Optional<NuroCanvasMessage> = undefined;
              if (canvasMessageList !== undefined) {
                canvasMessageList.forEach((lastCanvasMsg) => {
                  let endNode = lastCanvasMsg.findEndNode(toolCallId ?? "");
                  let startNode = lastCanvasMsg.findStartNode(toolCallId ?? "");
                  if (endNode !== undefined) {
                    updateCanvasMsg = lastCanvasMsg.copy() as NuroCanvasMessage;
                    if (toolResult !== undefined) {
                      endNode.toolResult = toolResult;
                    }
                    updateCanvasMsg.updateEndNode(endNode.copy());
                    if (
                      chatMessage.status ===
                      ChatMessageStatus.finished_successfully
                    ) {
                      if (type === ConvertType.history) {
                        updateCanvasMsg.status = NuroCanvasStatus.end;
                      } else {
                        updateCanvasMsg.finish();
                      }
                    }
                    msgs.push(updateCanvasMsg);
                  } else if (startNode !== undefined) {
                    updateCanvasMsg = lastCanvasMsg.copy() as NuroCanvasMessage;
                    if (toolResult !== undefined) {
                      startNode.toolResult = toolResult;
                    }
                    updateCanvasMsg.updateStartNode(startNode.copy());
                    msgs.push(updateCanvasMsg);
                  } else {
                    let node = lastCanvasMsg.findNodeByToolCallId(
                      toolCallId ?? "",
                    );
                    if (node !== undefined) {
                      updateCanvasMsg =
                        lastCanvasMsg.copy() as NuroCanvasMessage;
                      let targetNode = node.copy() as NuroCanvasNode;
                      if (
                        toolResult !== undefined &&
                        toolResult.trim().indexOf("{") !== 0
                      ) {
                        // 不是一个合法的 JSON，兼容处理成 mcp text
                        let r = new MCPToolCallResult();
                        r.content = [
                          MCPToolCallTextContent.create(toolResult ?? ""),
                        ];
                        targetNode.toolResult = r.toJSONString();
                      } else {
                        const firstJson = this.extractFirstJsonObject(
                          toolResult ?? "",
                        );
                        targetNode.toolResult = firstJson ?? "";
                      }

                      if (
                        chatMessage.status === ChatMessageStatus.in_progress
                      ) {
                        targetNode.messageStatus =
                          NuroToolCallMessageStatus.invoking;
                      } else {
                        targetNode.messageStatus =
                          NuroToolCallMessageStatus.finished_successfully;
                      }
                      updateCanvasMsg.addOrReplaceNode(targetNode);
                      msgs.push(updateCanvasMsg);
                    } else {
                      TSNConsole.log(
                        "[NuroCanvasMsgToolCall] cannot find node with toolCallId " +
                          toolCallId,
                      );
                    }
                  }
                });
              }
              // 如果画布没找到，再到消息中找
              if (updateCanvasMsg === undefined) {
                let toolMsg =
                  conversationManager?.conversation.findToolCallMessageByToolCallId(
                    toolCallId,
                  );
                if (
                  toolMsg !== undefined &&
                  toolMsg instanceof NuroToolCallMessage
                ) {
                  const toolMsgCopy = toolMsg.copy() as NuroToolCallMessage;
                  if (
                    toolResult !== undefined &&
                    toolResult.trim().indexOf("{") !== 0
                  ) {
                    // 不是一个合法的 JSON，兼容处理成 mcp text
                    let r = new MCPToolCallResult();
                    r.content = [MCPToolCallTextContent.create(toolResult)];
                    toolMsgCopy.toolResult = r.toJSONString();
                  } else {
                    toolMsgCopy.toolResult = toolResult;
                  }
                  if (chatMessage.status === ChatMessageStatus.in_progress) {
                    toolMsgCopy.messageStatus =
                      NuroToolCallMessageStatus.invoking;
                  } else {
                    toolMsgCopy.messageStatus =
                      NuroToolCallMessageStatus.finished_successfully;
                  }
                  msgs.push(toolMsgCopy);
                }
              }
            }
          }
        }
      }
    }

    return msgs;
  }

  static markMessagesAsFinished(
    conversationManager: Optional<NuroConversationManager>,
  ): void {
    if (conversationManager === undefined) {
      return;
    }
    conversationManager.conversation.messages.forEach((it) => {
      if (it.isFinalStatus() === false) {
        if (it instanceof NuroReasoningMessage) {
          it.setMsgStatus(NuroReasoningMessageStatus.finished_successfully);
        }
        if (it instanceof NuroAssistantMessage) {
          it.setMsgStatus(NuroAssistantMessageStatus.finished_successfully);
        }
      }
    });
  }

  static markLastUserMessageAsFinished(
    conversationManager: Optional<NuroConversationManager>,
  ): void {
    if (conversationManager === undefined) {
      return;
    }
    conversationManager.conversation.messages.forEach((it) => {
      if (it instanceof NuroUserMessage) {
        if (it.isFinalStatus() === false) {
          it.setMsgStatus(NuroUserMessageStatus.finished_successfully);
        }
      }
    });
  }

  static markInProgressMessagesAsFailed(
    conversationManager: Optional<NuroConversationManager>,
  ): void {
    if (conversationManager === undefined) {
      return;
    }
    let errorMsg = ChatMessageError.send_failed.valueOf();
    conversationManager.conversation.messages.forEach((it) => {
      if (it.isFinalStatus() === false) {
        it.errorMsg = errorMsg;
        if (it instanceof NuroUserMessage) {
          it.setMsgStatus(NuroUserMessageStatus.failed);
        }
        if (it instanceof NuroReasoningMessage) {
          it.setMsgStatus(NuroReasoningMessageStatus.failed);
        }
        if (it instanceof NuroAssistantMessage) {
          it.setMsgStatus(NuroAssistantMessageStatus.failed);
        }

        if (it instanceof NuroToolCallMessage) {
          if (it.messageStatus === NuroToolCallMessageStatus.streaming) {
            it.setMsgStatus(NuroToolCallMessageStatus.streaming_failed);
          } else {
            it.setMsgStatus(NuroToolCallMessageStatus.skipped);
          }
        }
      }
    });
  }

  static markInProgressMessagesAsCancel(
    conversationManager: Optional<NuroConversationManager>,
  ): void {
    if (conversationManager === undefined) {
      return;
    }
    conversationManager.conversation.messages.forEach((it) => {
      if (it.isFinalStatus() === false) {
        if (it instanceof NuroUserMessage) {
          it.setMsgStatus(NuroUserMessageStatus.cancelled);
        }
        if (it instanceof NuroReasoningMessage) {
          it.setMsgStatus(NuroReasoningMessageStatus.cancelled);
        }
        if (it instanceof NuroAssistantMessage) {
          it.setMsgStatus(NuroAssistantMessageStatus.cancelled);
        }
        if (it instanceof NuroToolCallMessage) {
          if (
            it.messageStatus === NuroToolCallMessageStatus.streaming ||
            it.messageStatus ===
              NuroToolCallMessageStatus.streaming_cancelled ||
            it.messageStatus === NuroToolCallMessageStatus.wait_user_response
          ) {
            it.setMsgStatus(NuroToolCallMessageStatus.streaming_cancelled);
          } else {
            it.setMsgStatus(NuroToolCallMessageStatus.skipped);
          }
        }
        if (it instanceof NuroCanvasMessage) {
          it.setMsgStatus(NuroCanvasStatus.cancel);
        }
      }
    });
  }

  static isUserMessage(message: NuroMessage): boolean {
    return message instanceof NuroUserMessage;
  }

  static isToolCallMessage(message: NuroMessage): boolean {
    return message instanceof NuroToolCallMessage;
  }

  static extractFirstJsonObject(jsonString: string): string {
    let balance = 0;
    let startIndex = -1;

    for (let i = 0; i < jsonString.length; i++) {
      // 由于 jsonString 是 String 类型，不能直接用数字索引，需要先转换为字符串字面量
      if (jsonString.charAt(i) === "{") {
        if (balance === 0) {
          startIndex = i;
        }
        balance++;
      } else if (jsonString.charAt(i) === "}") {
        balance--;
        if (balance === 0 && startIndex !== -1) {
          return jsonString.substring(startIndex, i + 1);
        }
      }
    }
    return ""; // 没有找到完整的 JSON 对象
  }
}
