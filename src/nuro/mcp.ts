import {
  TSNConsole,
  TSNMapUtils,
  TSNNumberConverter,
} from "@byted/tsnfoundation";

export interface NuroMCPClientAdapter {
  listTools(callback: (tools: NuroMCPToolItem[]) => void): void;
  callTool(
    toolCallId: string,
    toolName: string,
    toolArgs: Optional<string>,
    callback: (result: string) => void,
  ): void;
}

export class NuroMCPServerConfig {
  name: string;
  adapter: NuroMCPClientAdapter;

  constructor(name: string, adapter: NuroMCPClientAdapter) {
    this.name = name;
    this.adapter = adapter;
  }
}

export class NuroMCPToolItem {
  /**
   * 服务名称
   */
  serverName: string;
  /**
   * 工具名称
   */
  name: string;
  /**
   * 工具描述
   */
  description: string;
  /**
   * JSON Schema
   */
  inputSchema: string;

  constructor(
    serverName: string,
    name: string,
    description: string,
    inputSchema: string,
  ) {
    this.serverName = serverName;
    this.name = name;
    this.description = description;
    this.inputSchema = inputSchema;
  }
}

export class NuroMCPManager {
  private servers: Record<string, NuroMCPServerConfig> = {};
  private toolsServerMapping: Record<string, string> = {};

  registerServer(config: NuroMCPServerConfig): void {
    this.servers[config.name] = config;
  }

  getAllTools(callback: (tools: NuroMCPToolItem[]) => void): void {
    const allTools: NuroMCPToolItem[] = [];
    let taskCount = TSNMapUtils.size(this.servers);
    if (taskCount === 0) {
      callback(allTools);
      return;
    }
    TSNMapUtils.forEach(this.servers, (serverName, server) => {
      server.adapter.listTools((tools) => {
        tools.forEach((tool) => {
          allTools.push(tool);
          this.toolsServerMapping[serverName + "_" + tool.name] = serverName;
        });
        taskCount = taskCount - 1;
        if (taskCount === 0) {
          TSNConsole.log(allTools);
          callback(allTools);
        }
      });
    });
  }

  callTool(
    functionNameWithPrefix: string,
    functionCallArguments: Optional<string>,
    functionCallId: string,
    callback: (result: string) => void,
  ): void {
    const serverName = this.toolsServerMapping[functionNameWithPrefix];
    if (serverName === undefined) {
      return;
    }
    const server = this.servers[serverName];
    if (server === undefined) {
      return;
    }
    server.adapter.callTool(
      functionCallId,
      functionNameWithPrefix.substr(serverName.length + 1),
      functionCallArguments,
      (result) => {
        callback(result);
      },
    );
  }
}
