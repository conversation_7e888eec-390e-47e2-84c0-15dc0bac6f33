import type { NuroUserMessage } from "./message";
import type { NuroConversationManager } from "./conversation_manager";

export interface INuroMocker {
  checkMock(
    userMessage: NuroUserMessage,
    manager: NuroConversationManager,
  ): boolean;
}

export class NuroMockManager {
  private static mocker: Optional<INuroMocker> = undefined;

  static setMocker(mocker: Optional<INuroMocker>) {
    NuroMockManager.mocker = mocker;
  }

  static checkMock(
    userMessage: NuroUserMessage,
    manager: NuroConversationManager,
  ): boolean {
    return NuroMockManager.mocker?.checkMock(userMessage, manager) ?? false;
  }

  static isMocking(): boolean {
    return NuroMockManager.mocker !== undefined;
  }
}
