export class NuroSetting {
  /**
   * 是否需要展示 Server 端的 Function 调用消息
   * 如果为 true，则会在 Conversation.messages 中带有对应的 ToolCallMessage。
   */
  static needDisplayServerFunctionMessage: boolean = true;

  /**
   * 请求版本
   */
  static version: string = "3.0.0";
  /**
   *
   */
  static canvasSettings: CanvasSettings = {
    startNode: "", // 默认值
    endNode: "", // 默认值
    nodes: [], // 默认值
  };

  /**
   * agent之间的屏蔽toolCall
   */
  static shieldToolCall: string[] = ["handoff_to_planner", "handoff_to_host"];
}

export interface CanvasSettings {
  startNode: string;
  endNode: string;
  nodes: string[];
}
