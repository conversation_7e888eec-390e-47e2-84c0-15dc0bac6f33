import {
  IS_KT,
  TSN<PERSON>apUtils,
  TSNNumberConverter,
  TSNConsole,
} from "@byted/tsnfoundation";
import { CommonTransport } from "../transport/transport";
import { NuroConversation, NuroConversationState } from "./conversation";
import { NuroMCPManager } from "./mcp";
import {
  ChatMessageError,
  InterruptData,
  NuroFile,
  NuroMessage,
  NuroToolCallMessage,
  NuroToolCallMessageStatus,
  NuroUserMessage,
  NuroUserMessageStatus,
  NuroCanvasMessage,
} from "./message";
import { TOSFileUploadConfig, TOSFileUploadImpl } from "../transport/tos_impl";
import { HistoryMessages } from "../idl/history_message";
import { ConvertType, MessageProcessor } from "./message_processor";
import { EventStreamAdapter, SSEImpl } from "../transport/sse_impl";
import { NuroUtils } from "./utils";
import {
  MCPTool<PERSON>all<PERSON><PERSON>ult,
  MCPToolCallTextContent,
} from "../mcp/mcp_tool_result";
import {
  ChatContentPart,
  ChatMessageStatus,
  ChatToolCallType,
} from "../idl/chat_message";
import { HttpTransport } from "../transport/http_transport";
import { NuroMockManager } from "./mocker";
import { NuroMessageOp, NuroTaskStatus } from "./task";

export class NuroConversationManager {
  readonly conversation: NuroConversation;
  private activeTransport?: CommonTransport;
  /**
   * Client Side MCP
   */
  mcpManager?: NuroMCPManager;
  private toolCalled: Record<string, boolean> = {};

  constructor(conversationId: string = NuroUtils.randomUUIDString()) {
    this.conversation = new NuroConversation(conversationId);
  }

  /**
   * 注册需要链接的后端通道
   * @param transport
   */
  connect(transport: CommonTransport): void {
    this.activeTransport = transport;
    transport.setConversationManager(this);
    this.conversation.conversationState =
      NuroConversationState.readyToSendMessage;
  }

  /**
   * 使能本地工具调用
   */
  enableMCPTools(): void {
    let _self: Weak<NuroConversationManager> = this;
    this.conversation.addStateUpdateListener((state) => {
      if (_self === undefined) {
        return;
      }
      if (state === NuroConversationState.readyToSendMessage) {
        let msgs: NuroToolCallMessage[] = [];
        this.conversation.messages.forEach((msg) => {
          if (msg instanceof NuroToolCallMessage) {
            if (msg.messageStatus === NuroToolCallMessageStatus.invoking)
              if (msg.toolType === ChatToolCallType.client_function.valueOf()) {
                if (msg.toolResult === "") {
                  msgs.push(msg);
                } else if (msg.toolResult === undefined) {
                  msgs.push(msg);
                }
              }
          }
        });
        if (msgs.length > 0) {
          _self.onToolCall(msgs);
        }
      }
    });
  }

  /**
   * 解析会话历史数据
   * @param str 会话历史数据 jsonstr
   */
  decodeConversationFromJSONString(
    str: string,
    needResume: boolean = false,
  ): void {
    let history = new HistoryMessages({ JSONString: str });
    this.conversation.conversationId =
      history.conversation?.conversation_id ?? "";
    let messages = history.getChatMessages();
    this.conversation.summary = history.conversation?.summary ?? "";

    // Sort messages by createTime
    messages.sort((a, b) => {
      const timeA = TSNNumberConverter.toDouble(a.create_time ?? 0);
      const timeB = TSNNumberConverter.toDouble(b.create_time ?? 0);
      if (timeA < timeB) {
        return -1;
      } else if (timeA > timeB) {
        return 1;
      } else {
        return 0;
      }
    });

    let nuroMessagesR: Record<string, NuroMessage> = {};
    messages.forEach((message) => {
      let msgs = MessageProcessor.convertChatMessageToNuroMessage(
        this,
        message,
        ConvertType.history,
      );
      msgs.forEach((message) => {
        nuroMessagesR[message.id] = message;
      });
      let nuroMessages: Array<NuroMessage> = TSNMapUtils.values(nuroMessagesR);
      let length: Int = nuroMessages.length;
      if (length > 1) {
        let parentid = nuroMessages[0]?.id ?? "";
        for (let index = 1; index < length; index = index + 1) {
          let msg = nuroMessages[index];
          if (msg !== undefined) {
            msg.metadata.parent_message_id = parentid;
            parentid = msg.id;
          }
        }
      }
      // this.conversation.messages = nuroMessages;
      // this.conversation.processTask([], false);
      nuroMessages.forEach((msg) => {
        this.receivedMessage(msg, false);
      });
    });
    // 恢复历史数据
    if (needResume) {
      this.resumeMessageIfNeed();
    }
  }

  /**
   * 会话开始时需要提前填入的信息
   * @param value
   */
  setSystemPrompt(value: string): void {
    this.conversation.systemPrompt = value;
  }

  resumeMessageIfNeed(): void {
    if (this.conversation.needResume() === false) {
      return;
    }
    this.conversation.updateState(NuroConversationState.streamingResponse);
    this.activeTransport?.resumeMessage();
  }

  sendUserMessage(message: Optional<NuroUserMessage>): void {
    if (message === undefined) {
      return;
    }
    if (NuroMockManager.checkMock(message, this)) {
      return;
    }

    if (message._conversationManager === undefined) {
      message._conversationManager = this;
    }
    this.conversation.updateState(NuroConversationState.streamingResponse);

    message.setMsgStatus(NuroUserMessageStatus.none);
    if (message.files !== undefined && message.files.length > 0) {
      let _files: NuroFile[] = [];
      for (let index = 0; index < message.files.length; index++) {
        const file = message.files[index]!;
        _files.push(file);
      }
      message.setMsgStatus(NuroUserMessageStatus.uploading_files);
      this.uploadFiles(
        _files,
        () => {
          this._doSendUserMessage(message);
        },
        () => {
          message.errorMsg = ChatMessageError.upload_failed.valueOf();
          message.setMsgStatus(NuroUserMessageStatus.failed);
          this.conversation.updateState(
            NuroConversationState.readyToSendMessage,
          );
        },
      );
    } else {
      this._doSendUserMessage(message);
    }
  }

  private _doSendUserMessage(message: NuroUserMessage): void {
    if (this.mcpManager !== undefined) {
      this.mcpManager.getAllTools((tools) => {
        this.activeTransport?.sendMessage(message, tools);
      });
    } else {
      this.activeTransport?.sendMessage(message, []);
    }
  }

  private noUpload(file: NuroFile): boolean {
    return file.url !== undefined && file.uri !== undefined;
  }

  private uploadFiles(
    files: NuroFile[],
    successCallback: () => void,
    failCallback: () => void,
  ): void {
    let uploadQueueFiles = files;
    let file = uploadQueueFiles[0];
    if (file !== undefined) {
      let noUp: boolean = this.noUpload(file);
      if (noUp === true) {
        uploadQueueFiles.shift();
        this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
      } else {
        if (file.localFile !== undefined) {
          let config = new TOSFileUploadConfig(file, file.localFile);
          config.onFinish = () => {
            uploadQueueFiles.shift();
            this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
          };
          config.onError = (code, message) => {
            failCallback();
          };
          TOSFileUploadImpl.upload(config);
        } else {
          uploadQueueFiles.shift();
          this.uploadFiles(uploadQueueFiles, successCallback, failCallback);
        }
      }
    } else {
      successCallback();
    }
  }

  /**
   * 本地工具方法 结果回调
   * @param message 回调的工具
   * @param userMessage 额外需要带的用户消息
   */
  sendToolResultMessage(
    message: NuroToolCallMessage,
    userMessage: Optional<NuroUserMessage> = undefined,
  ): void {
    this.conversation.updateState(NuroConversationState.streamingResponse);
    let msgs: NuroMessage[] = [];
    msgs.push(message);
    if (userMessage !== undefined) {
      msgs.push(userMessage);
    }
    if (this.mcpManager !== undefined) {
      this.mcpManager.getAllTools((tools) => {
        this.activeTransport?.sendMessages(msgs, tools);
      });
    } else {
      this.activeTransport?.sendMessages(msgs, []);
    }
  }

  sendToolResultMessages(messages: NuroToolCallMessage[]): void {
    messages.forEach((message) => {
      this.receivedMessage(message);
    });
    this.conversation.updateState(NuroConversationState.streamingResponse);
    if (this.mcpManager !== undefined) {
      this.mcpManager.getAllTools((tools) => {
        if (IS_KT) {
          this.activeTransport?.sendMessages(messages as NuroMessage[], tools);
        } else {
          this.activeTransport?.sendMessages(messages, tools);
        }
      });
    } else {
      if (IS_KT) {
        this.activeTransport?.sendMessages(messages as NuroMessage[], []);
      } else {
        this.activeTransport?.sendMessages(messages, []);
      }
    }
  }

  /**
   * 重试操作
   * @param message 需要被重试的消息
   *
   * @param message 是否需要删除重试消息
   */
  regenerateMessage(message: NuroMessage, needDelete: boolean): void {
    let hasTool = false;
    let index: Int = -1;
    let userMessage: Optional<NuroUserMessage> = undefined;
    let indexuser: Int = -1;
    for (let i = this.conversation.messages.length - 1; i > -1; i = i - 1) {
      let msg = this.conversation.messages[i];
      if (msg === undefined) {
        continue;
      }
      if (msg.id === message.id && msg.type === message.type) {
        index = i;
      }

      if (index > -1) {
        if (msg instanceof NuroToolCallMessage) {
          hasTool = true;
        } else if (msg instanceof NuroUserMessage) {
          userMessage = msg;
          indexuser = i;
          break;
        }
      }
    }
    if (index !== -1) {
      if (userMessage !== undefined) {
        if (needDelete === true) {
          this.conversation.messages.splice(index, 1);
          this.notifyConversationUpdate(message, NuroMessageOp.delete);
        }
        if (needDelete === true && index !== indexuser) {
          this.conversation.messages.splice(indexuser, 1);
          this.notifyConversationUpdate(userMessage, NuroMessageOp.delete);
        }
        let newMessage: Optional<NuroUserMessage>;
        if (hasTool === true && needDelete === false) {
          newMessage = new NuroUserMessage(
            NuroUtils.randomUUIDString(),
            "再次生成",
          );
        } else {
          newMessage = userMessage.copy() as NuroUserMessage;
          if (newMessage !== undefined) {
            newMessage.id = NuroUtils.randomUUIDString();
          }
        }
        this.sendUserMessage(newMessage);
      }
    }
  }

  /**
   * 中断 当前会话请求
   */
  interruptResponse(): void {
    let token = this.activeTransport?.token;
    MessageProcessor.markInProgressMessagesAsCancel(this);
    if (token !== undefined) {
      SSEImpl.cancel(token);
    }
    let interruptData: InterruptData = new InterruptData();
    interruptData.conversationId = this.conversation.conversationId ?? "";
    let lastUserMessage = this.conversation.findLastUserMessage();
    interruptData.messageId = lastUserMessage?.id ?? "";
    new HttpTransport().sendRequest(
      EventStreamAdapter.interruptEndpoint,
      interruptData.toJSONString(),
      {},
      (result: string) => {},
      (code: string, reason?: string) => {},
    );
  }

  receivedMessage(_message: NuroMessage, fromSSE: boolean = true): void {
    let message = _message.copy();
    message._conversationManager = this;
    let index = -1;
    for (let i = 0; i < this.conversation.messages.length; i++) {
      if (
        this.conversation.messages[i]?.id === message.id &&
        this.conversation.messages[i]?.type === message.type
      ) {
        index = i;
        break;
      }
    }
    if (index >= 0) {
      // message already received, current is a streaming message.
      let oldmsg = this.conversation.messages[index];
      if (oldmsg !== undefined) {
        if (oldmsg.isEqualToObject(message) === true) {
          return;
        }
        if (oldmsg.isFinalStatus() === true && fromSSE === true) {
          return;
        }
        message.updated = oldmsg.updated + 1;
      }
      this.conversation.messages[index] = message;
      this.notifyConversationUpdate(message, NuroMessageOp.update);
    } else {
      let length: Int = this.conversation.messages.length;
      if (length > 0) {
        message.metadata.parent_message_id =
          this.conversation.messages[length - 1]?.id ?? "";
      }
      this.conversation.messages.push(message);
      this.notifyConversationUpdate(message, NuroMessageOp.add);
    }
  }

  notifyConversationUpdate(message: NuroMessage, op: NuroMessageOp): void {
    this.conversation.updateMessage(message, op);
  }

  onToolCall(toolCallMessages: NuroToolCallMessage[]): void {
    var _toolCallMessages = toolCallMessages;
    let toolCallCount = toolCallMessages.length;
    for (let index = 0; index < toolCallMessages.length; index++) {
      const toolCallMessage = toolCallMessages[index]!;
      if (this.toolCalled[toolCallMessage.id] === true) {
        toolCallCount = toolCallCount - 1;
        return;
      }
      this.toolCalled[toolCallMessage.id] = true;
      this.mcpManager?.callTool(
        toolCallMessage.toolName,
        toolCallMessage.toolArgs,
        toolCallMessage.id,
        (result) => {
          const toolCallResult = new MCPToolCallResult({ JSONString: result });
          if (toolCallResult.content.length <= 0) {
            toolCallMessage._conversationManager = this;
            toolCallMessage.setMsgStatus(
              NuroToolCallMessageStatus.wait_user_response,
            );
            // 无内容，代表需要等候用户操作，由 UI 再次发送 result。
            // toolCallCount 也不作减 1 处理，因为 UI 会主动发送 result，不由此函数处理。
          } else {
            let toolMsgCopy = toolCallMessage.copy() as NuroToolCallMessage;
            toolMsgCopy.toolResult = result;
            toolMsgCopy.messageStatus =
              NuroToolCallMessageStatus.finished_successfully;
            _toolCallMessages[index] = toolMsgCopy;
            toolCallCount = toolCallCount - 1;
            if (toolCallCount <= 0) {
              this.sendToolResultMessages(_toolCallMessages);
            }
          }
        },
      );
    }
  }
}
