const fs = require("fs");
const path = require("path");

// --- Added by AI Assistant ---
// This script removes the reference path line from js/nuro/utils.d.ts

const utilsDtsPath = path.join(__dirname, "../js/nuro/utils.d.ts");
const lineToRemove = '/// <reference path="../../src/nuro/utils.uni.d.ts" />';

try {
  let content = fs.readFileSync(utilsDtsPath, "utf8");
  const lines = content.split("\n");
  const filteredLines = lines.filter(
    (line) => !line.trim().startsWith(lineToRemove),
  );
  const newContent = filteredLines.join("\n");

  if (content !== newContent) {
    fs.writeFileSync(utilsDtsPath, newContent, "utf8");
    console.log(`Successfully removed reference line from ${utilsDtsPath}`);
  } else {
    console.log(
      `Reference line not found in ${utilsDtsPath}, no changes made.`,
    );
  }
} catch (error) {
  console.error(`Error processing ${utilsDtsPath}:`, error);
  process.exit(1); // Exit if there's an error reading/writing the file
}

// --- End of AI Assistant addition ---
