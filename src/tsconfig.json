{
  "compilerOptions": {
    "target": "ES6",
    "module": "CommonJS",
    "jsx": "react",
    "noLib": true,
    "experimentalDecorators": true,
    "types": [],
    "strict": true,
    "sourceMap": true,
    "declaration": true,
    "outDir": "../js" // JS Output
  },
  "include": ["**/*.ts", "**/*.tsx"],
  "exclude": [],
  "swift": {
    "out": "../swift",
    "package": "NuroSDK",
    "macros": {},
    "exclude": []
  },
  "kotlin": {
    "out": "../kotlin/src/main/java/com/bytedance/nurosdk",
    "package": "com.bytedance.nurosdk",
    "macros": {},
    "exclude": []
  }
}
